{"enabled": true, "name": "Code Quality Analyzer", "description": "Monitors source code files for changes and analyzes modified code for potential improvements including code smells, design patterns, and best practices. Generates suggestions for improving code quality while maintaining existing functionality.", "version": "1", "when": {"type": "fileEdited", "patterns": ["src/**/*.js", "src/**/*.jsx", "*.js", "*.jsx", "src/**/*.ts", "src/**/*.tsx", "*.ts", "*.tsx"]}, "then": {"type": "askAgent", "prompt": "Analyze the modified code files for potential improvements. Focus on:\n\n1. **Code Smells**: Identify any code smells like long functions, duplicate code, large classes, or complex conditionals\n2. **Design Patterns**: Suggest appropriate design patterns that could improve the code structure\n3. **Best Practices**: Check for adherence to JavaScript/React best practices including:\n   - Proper error handling\n   - Component composition\n   - Hook usage patterns\n   - State management efficiency\n   - Performance optimizations\n4. **Readability**: Suggest improvements for code clarity, naming conventions, and documentation\n5. **Maintainability**: Identify areas that could be refactored for easier maintenance\n6. **Performance**: Highlight potential performance bottlenecks and optimization opportunities\n\nFor each suggestion, provide:\n- Clear explanation of the issue\n- Specific code example showing the improvement\n- Reasoning for why the change would be beneficial\n- Impact on functionality (ensure no breaking changes)\n\nConsider the React/Vite/TailwindCSS tech stack and maintain compatibility with the existing BAT-7 psychological assessment system architecture."}}