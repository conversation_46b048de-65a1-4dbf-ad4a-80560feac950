# Documento de Diseño

## Resumen

El Dashboard de Reportes y Análisis Mejorado será una interfaz integral de visualización de datos que se integrará con el sistema BAT-7 existente. Proporcionará capacidades analíticas avanzadas a través de gráficos interactivos, métricas estadísticas y funcionalidades de exportación personalizables. El diseño aprovecha la arquitectura React existente y se integra con la base de datos Supabase para ofrecer insights en tiempo real.

## Arquitectura

### Integración con Dashboard Existente

El sistema ya cuenta con un dashboard robusto en `/src/pages/admin/Dashboard.jsx` con múltiples vistas y servicios. Las mejoras se integrarán extendiendo la arquitectura existente:

```
┌─────────────────────────────────────────────────────────────┐
│                 Dashboard Existente (React)                 │
├─────────────────────────────────────────────────────────────┤
│  Vistas Actuales       │  Nuevas Vistas Mejoradas          │
│  - ExecutiveView       │  - EnhancedAnalyticsView           │
│  - K<PERSON>View            │  - PatientProgressView             │
│  - General<PERSON>iew         │  - InteractiveChartsView           │
│  - TrendsView          │  - CustomDashboardView             │
│  - ComparativeView     │  - ScheduledReportsView            │
│  - StatisticalView     │                                    │
│  - ExportView          │                                    │
├─────────────────────────────────────────────────────────────┤
│              Servicios Existentes + Extensiones             │
│  - DashboardService    │  - Enhanced Analytics Service      │
│  - useDashboardData    │  - Patient Progress Service        │
│  - Componentes Charts  │  - Advanced Export Service         │
│                        │  - Report Scheduler Service        │
├─────────────────────────────────────────────────────────────┤
│                    Supabase Backend                         │
│  - Tablas Existentes   │  - Nuevas Vistas Analíticas       │
│  - Datos BAT-7         │  - Funciones de Agregación         │
│                        │  - Triggers para Métricas          │
└─────────────────────────────────────────────────────────────┘
```

### Extensión de Arquitectura Existente

**Componentes Existentes a Extender:**
- `useDashboardData.js` - Hook para datos del dashboard
- `DashboardService.js` - Servicio principal de datos
- `/components/dashboard/views/` - Vistas del dashboard
- `/components/dashboard/` - Componentes de visualización

**Nuevas Adiciones:**
- Nuevas vistas en `/src/components/dashboard/views/enhanced/`
- Servicios especializados en `/src/services/analytics/`
- Componentes de gráficos avanzados con Recharts
- Utilidades de exportación y programación

## Componentes e Interfaces

### Componentes Principales

#### 1. AnalyticsDashboard (Componente Principal)
```jsx
interface AnalyticsDashboardProps {
  userRole: 'admin' | 'psychologist';
  institutionId?: string;
  psychologistId?: string;
}
```

#### 2. InstitutionalOverview
```jsx
interface InstitutionalOverviewProps {
  timeRange: TimeRange;
  institutionId: string;
  filters: DashboardFilters;
}
```

#### 3. PatientProgressTracker
```jsx
interface PatientProgressTrackerProps {
  patientId: string;
  assessmentHistory: Assessment[];
  comparisonMode: 'individual' | 'institutional' | 'national';
}
```

#### 4. InteractiveChartContainer
```jsx
interface InteractiveChartContainerProps {
  chartType: 'line' | 'bar' | 'area' | 'scatter' | 'radar';
  data: ChartData[];
  config: ChartConfiguration;
  exportable: boolean;
}
```

#### 5. CustomizableDashboard
```jsx
interface CustomizableDashboardProps {
  userId: string;
  savedLayouts: DashboardLayout[];
  availableWidgets: WidgetDefinition[];
}
```

### Interfaces de Datos

#### Tipos de Datos Principales
```typescript
interface AnalyticsData {
  institutionalMetrics: InstitutionalMetrics;
  patientProgress: PatientProgressData[];
  systemUsage: UsageMetrics;
  comparativeAnalysis: ComparisonData;
}

interface InstitutionalMetrics {
  totalAssessments: number;
  completionRate: number;
  averageScores: AptitudeScores;
  trendsOverTime: TimeSeries[];
  psychologistPerformance: PsychologistMetrics[];
}

interface PatientProgressData {
  patientId: string;
  assessmentHistory: AssessmentResult[];
  scoreProgression: AptitudeProgression[];
  percentileComparisons: PercentileData;
  significantChanges: ChangeIndicator[];
}

interface ChartConfiguration {
  title: string;
  xAxis: AxisConfig;
  yAxis: AxisConfig;
  colors: ColorScheme;
  interactivity: InteractivityOptions;
  exportOptions: ExportConfig;
}
```

### Componentes de Visualización

#### Biblioteca de Gráficos: Recharts
Se utilizará Recharts como biblioteca principal de visualización por:
- Integración nativa con React
- Componentes declarativos y modulares
- Soporte para interactividad y tooltips
- Capacidades de personalización avanzadas
- Accesibilidad integrada

#### Tipos de Gráficos Implementados

1. **LineChart**: Progreso temporal de pacientes y tendencias institucionales
2. **BarChart**: Comparaciones entre grupos y distribuciones de puntajes
3. **AreaChart**: Volumen de evaluaciones y métricas acumulativas
4. **ScatterChart**: Correlaciones entre aptitudes
5. **RadarChart**: Perfiles de aptitudes individuales
6. **ComposedChart**: Visualizaciones híbridas para análisis complejos

## Modelos de Datos

### Nuevas Vistas de Base de Datos

#### Vista de Métricas Institucionales
```sql
CREATE VIEW institutional_analytics AS
SELECT 
  i.id as institution_id,
  i.nombre as institution_name,
  COUNT(e.id) as total_assessments,
  AVG(CASE WHEN e.estado = 'completada' THEN 1 ELSE 0 END) as completion_rate,
  AVG(r.puntuacion_directa) as avg_direct_score,
  AVG(r.percentil) as avg_percentile,
  DATE_TRUNC('month', e.fecha_inicio) as assessment_month
FROM instituciones i
LEFT JOIN psicologos p ON p.institucion_id = i.id
LEFT JOIN evaluaciones e ON e.psicologo_id = p.id
LEFT JOIN resultados r ON r.evaluacion_id = e.id
GROUP BY i.id, i.nombre, DATE_TRUNC('month', e.fecha_inicio);
```

#### Vista de Progreso de Pacientes
```sql
CREATE VIEW patient_progress_analytics AS
SELECT 
  pac.id as patient_id,
  pac.nombre as patient_name,
  e.id as evaluation_id,
  e.fecha_inicio,
  e.fecha_fin,
  apt.nombre as aptitude_name,
  r.puntuacion_directa,
  r.percentil,
  LAG(r.percentil) OVER (
    PARTITION BY pac.id, apt.id 
    ORDER BY e.fecha_inicio
  ) as previous_percentile
FROM pacientes pac
JOIN evaluaciones e ON e.paciente_id = pac.id
JOIN resultados r ON r.evaluacion_id = e.id
JOIN aptitudes apt ON apt.id = r.aptitud_id
WHERE e.estado = 'completada'
ORDER BY pac.id, apt.id, e.fecha_inicio;
```

### Estructuras de Datos para Analytics

#### Agregaciones Temporales
```typescript
interface TimeSeriesData {
  timestamp: Date;
  value: number;
  category?: string;
  metadata?: Record<string, any>;
}

interface AggregatedMetrics {
  daily: TimeSeriesData[];
  weekly: TimeSeriesData[];
  monthly: TimeSeriesData[];
  yearly: TimeSeriesData[];
}
```

#### Datos de Comparación
```typescript
interface ComparisonGroup {
  groupId: string;
  groupName: string;
  sampleSize: number;
  statistics: {
    mean: number;
    median: number;
    standardDeviation: number;
    percentiles: Record<number, number>;
  };
  aptitudeBreakdown: Record<string, number>;
}
```

## Manejo de Errores

### Estrategias de Manejo de Errores

#### 1. Errores de Carga de Datos
```typescript
interface DataLoadingError {
  type: 'NETWORK_ERROR' | 'DATA_PARSING_ERROR' | 'PERMISSION_ERROR';
  message: string;
  retryable: boolean;
  fallbackData?: any;
}
```

#### 2. Errores de Visualización
- Fallback a gráficos más simples si los datos son insuficientes
- Mensajes informativos para conjuntos de datos vacíos
- Indicadores de carga durante procesamiento de datos grandes

#### 3. Errores de Exportación
- Validación de datos antes de exportar
- Manejo de timeouts en generación de reportes grandes
- Notificaciones de estado para procesos asíncronos

### Componentes de Error

#### ErrorBoundary para Analytics
```jsx
class AnalyticsErrorBoundary extends React.Component {
  constructor(props) {
    super(props);
    this.state = { hasError: false, errorInfo: null };
  }

  static getDerivedStateFromError(error) {
    return { hasError: true };
  }

  componentDidCatch(error, errorInfo) {
    // Log error to monitoring service
    this.setState({ errorInfo });
  }

  render() {
    if (this.state.hasError) {
      return <AnalyticsErrorFallback errorInfo={this.state.errorInfo} />;
    }
    return this.props.children;
  }
}
```

## Estrategia de Testing

### Tipos de Testing

#### 1. Unit Testing
- Funciones de cálculo estadístico
- Utilidades de formateo de datos
- Componentes de visualización individuales
- Servicios de agregación de datos

#### 2. Integration Testing
- Flujo completo de carga y visualización de datos
- Integración con servicios de Supabase
- Funcionalidad de exportación end-to-end
- Interacciones entre componentes del dashboard

#### 3. Visual Testing
- Renderizado correcto de gráficos con diferentes conjuntos de datos
- Responsividad en diferentes tamaños de pantalla
- Temas y personalización visual
- Estados de carga y error

#### 4. Performance Testing
- Tiempo de carga con grandes conjuntos de datos
- Rendimiento de agregaciones complejas
- Memoria utilizada por visualizaciones
- Optimización de re-renderizado

### Herramientas de Testing

#### Testing Framework
```javascript
// Ejemplo de test para componente de gráfico
import { render, screen, waitFor } from '@testing-library/react';
import { AnalyticsChart } from '../AnalyticsChart';

describe('AnalyticsChart', () => {
  it('renders chart with provided data', async () => {
    const mockData = [
      { name: 'Test 1', value: 100 },
      { name: 'Test 2', value: 200 }
    ];
    
    render(<AnalyticsChart data={mockData} type="bar" />);
    
    await waitFor(() => {
      expect(screen.getByRole('img')).toBeInTheDocument();
    });
  });

  it('handles empty data gracefully', () => {
    render(<AnalyticsChart data={[]} type="bar" />);
    expect(screen.getByText(/No hay datos disponibles/)).toBeInTheDocument();
  });
});
```

#### Mock Services
```javascript
// Mock para servicio de analytics
export const mockAnalyticsService = {
  getInstitutionalMetrics: jest.fn().mockResolvedValue({
    totalAssessments: 150,
    completionRate: 0.85,
    averageScores: { V: 75, E: 80, A: 70 }
  }),
  
  getPatientProgress: jest.fn().mockResolvedValue([
    {
      patientId: '1',
      assessmentHistory: [],
      scoreProgression: []
    }
  ])
};
```

### Criterios de Aceptación para Testing

1. **Cobertura de Código**: Mínimo 80% para componentes críticos
2. **Performance**: Carga inicial < 3 segundos con 1000 registros
3. **Accesibilidad**: Cumplimiento WCAG 2.1 AA
4. **Compatibilidad**: Soporte para navegadores modernos (Chrome, Firefox, Safari, Edge)
5. **Responsividad**: Funcionalidad completa en dispositivos móviles y tablets