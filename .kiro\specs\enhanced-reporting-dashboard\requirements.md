Adding the detail about using real data from a Supabase database and the necessity of constant synchronization is a crucial clarification. It emphasizes a core non-functional requirement related to data integrity and freshness.

Here's how we can integrate this into the prompt, specifically under the "Deployment Strategy" and "Cross-Cutting Aspects" sections, and potentially refining the "Sync Tests" module.

-----

## **Prompt: Refine and Enhance a Requirements Document for the BAT-7 Dashboard**

Your task is to review, refine, and significantly enhance the provided "Requirements Document" for the **Enhanced Reporting and Analytics Dashboard** of the BAT-7 psychological assessment system.

The primary goal is to ensure the document is **exceptionally clear, precise, unambiguous, and easily understandable** for all stakeholders, including product owners, developers, and quality assurance testers. Focus on making each requirement and its acceptance criteria as specific, testable, and measurable as possible. Crucially, emphasize that **all data displayed and processed by the dashboard must be real data, synchronized directly from the Supabase database, not fictitious or static data.**

### **Instructions:**

1.  **Review for Clarity and Precision:**

      * Identify and eliminate any vague language, technical jargon, or ambiguity within the user stories, requirements, and acceptance criteria.
      * Rephrase sentences to be direct, concise, and actionable.
      * Ensure every acceptance criterion is **testable and measurable**, providing clear conditions for verification.

2.  **Ensure Completeness and Consistency:**

      * Identify any **missing details** that would hinder development or prevent comprehensive testing. This includes specifics on data types, expected interactions, and edge cases.
      * Verify consistency in terminology, formatting, and the use of "SHALL" for mandatory requirements throughout the document.

3.  **Improve Readability and Structure:**

      * Suggest any structural changes that would enhance document navigation (e.g., more granular headings, effective use of bullet points, clear numbering).
      * Ensure a logical and intuitive flow from user story to detailed acceptance criteria.

4.  **Incorporate Architectural and Cross-Cutting Concerns:**

      * Integrate the provided **Deployment Strategy** into a dedicated section, clarifying the architectural setup and technologies used, specifically noting the **Supabase integration for real data**.
      * Expand the **Detailed Module and Service Description** to include user roles and more specific interaction details.
      * Develop a comprehensive **Cross-Cutting Aspects** section, addressing non-functional requirements such as security, performance, usability, maintainability, and crucially, **data integrity and real-time synchronization**.

-----

### **Provided Requirements Document:**

```
# Requirements Document

## Introduction

The Enhanced Reporting and Analytics Dashboard will provide comprehensive data visualization and analytical capabilities for the BAT-7 psychological assessment system. This feature will enable administrators and psychologists to gain deeper insights into assessment patterns, institutional performance, and individual patient progress through interactive charts, statistical summaries, and customizable reports.

---

## 1. Deployment Strategy

The solution will leverage a **containerized microservices architecture** using **Docker** for robust and scalable backend services. The frontend and backend components will be deployed as follows:

* **Frontend (User Dashboard):** To be deployed on Content Delivery Network (CDN) platforms such as **Vercel or Netlify**. These platforms ensure optimal performance and rapid delivery of the user interface globally.
* **Backend (API, Business Logic, and Services):** To be hosted on **AWS, GCP, or DigitalOcean**. This environment will run Docker containers for the following services:
    * **API Service:** Responsible for exposing endpoints for frontend interaction.
    * **Synchronization Service:** Manages data integration and updates (e.g., from external assessment systems).
    * **Reporting Service:** Processes and generates detailed reports.
* **Database:** The primary data source will be **Supabase (PostgreSQL)**. This managed database service, hosted on **Supabase's infrastructure**, will serve as the single source of truth for all BAT-7 assessment data. **All data displayed on the dashboard must be real data synchronized directly from this Supabase database, ensuring consistency and accuracy.**

---

## 2. Detailed Module and Service Description

This section outlines the core modules of the dashboard, detailing their primary functions, dependencies, and the expected visual and reporting outputs.

| ID | Module | Role/Audience | Primary Function | Integration and Dependencies | Graphics & Reports (Expected Interactions) |
| :--- | :--- | :--- | :--- | :--- | :--- |
| M-01 | **🔧 Sync Tests** | Administrator | Verify the integrity and state of **BAT-7 data synchronization from Supabase**. | - **Depends directly on the Supabase database** for data source and the **Synchronization Service** for process status.<br>- **Alimenta de datos al Módulo 'Estado del Sistema' (M-10)** para el monitoreo general. | - **Synchronization Log:** Detailed table with history (date of execution, status [Success/Failure], number of records processed, duration, **source of synchronization, e.g., Supabase API pull**).<br>- **Status Cards:** Clear indicators of the **latest synchronization** (Date/Time, Status) and potential error messages. |
| M-02 | **📊 Executive Summary** | Administrator | Provide a strategic, high-level overview with the **most important and actionable findings** on overall performance. | - **Consumes processed and aggregated real data** directly from the Backend API (which, in turn, draws from M-04, M-05, M-06, **sourced from Supabase**).<br>- **Alimenta el Informe Ejecutivo** en el Módulo 'Exportación' (M-08). | - **Gauge Charts:** For **key performance indicators (KPIs)** (e.g., overall completion rate, average general score).<br>- **Insight Cards:** Dynamically generated key conclusions (e.g., "15% decrease in Verbal aptitude over the last quarter", "Increase in assessment completion rate").<br>- **Minigráficos (Sparklines):** Summarized trends for key metrics (e.g., assessment volume per month). |
| M-03 | **📈 Critical KPIs** | Administrator | Monitor predefined **Key Performance Indicators (KPIs)** for the institution. | - **Consumes real data processed** by the Backend API (including M-04, M-05, M-06, **sourced from Supabase**).<br>- **Can generate alerts** displayed in the 'Executive Summary' (M-02) and/or 'System Status' (M-10) Modules. | - **KPI Cards:** Display current value, target (if applicable), and percentage deviation.<br>- **Bullet Charts:** For visual comparison of current value vs. target for each KPI.<br>- **Performance Tables:** Breakdown of KPIs by psychologist, department, etc. |
| M-04 | **📋 Overview** | Both | Present **global descriptive statistics** of the assessed population and conducted tests. | - **Consumes aggregated real data** directly from the Backend API (**sourced from Supabase**). | - **Bar Charts:** Distribution of assessed individuals by institution, education level, age range.<br>- **Pie/Donut Charts:** Distribution by gender, assessment category.<br>- **Numeric Panel:** Cards with key metrics (e.g., Total Assessed Individuals, Total Completed Tests, Average Age). |
| M-05 | **📉 Trend Analysis** | Both | Visualize the **evolution of metrics and scores** to identify patterns over time. | - **Consumes real data processed** by the Backend API (**sourced from Supabase**).<br>- **Requires dynamic date range filters** (day, week, month, quarter, year) and granularidad selection. | - **Line Charts (Interactive):** Evolution of average scores (Raw Scores (PD) and Percentile Scores (PC)) per aptitude, assessment volume.<br>- **Stacked Area Charts:** Evolution of performance level distribution (Low, Medium, High) per aptitude.<br>- **Zoom and Period Selection Options.** |
| M-06 | **⚖️ Comparative Analysis** | Psychologist, Administrator | Perform **benchmarking** and compare the assessment results between different population segments or groups. | - **Consumes real data processed** by the Backend API (**sourced from Supabase**).<br>- **Requires dynamic group selection** based on defined criteria (demographics, institutional, etc.). | - **Radar Charts:** To compare aptitude profiles between 2 or more selected groups (e.g., Males vs. Females, Group A vs. Group B).<br>- **Grouped/Stacked Bar Charts:** Compare average aptitudes by categories (e.g., by gender, by institution, by psychologist).<br>- **Statistical Significance:** Indication of statistically significant differences (p-values, confidence intervals). |
| M-07 | **📊 Statistical Analysis** | Both | Provide **detailed measures of central tendency, dispersion, and distribution** for aptitude scores. | - **Consumes real data processed** by the Backend API, allowing filters by aptitude, group and date range (**sourced from Supabase**). | - **Statistical Tables:** Details of mean, median, mode, standard deviation, variance, percentiles (25th, 50th, 75th).<br>- **Boxplots (Diagramas de Caja):** Visualizar the dispersion and presence of outliers for each aptitude.<br>- **Histograms:** Display the frequency distribution of scores for each aptitude. |
| M-08 | **📤 Export** | Both | Generate and download **structured reports** in various formats for sharing with stakeholders. | - **Depends on the Reporting Service** for final composition.<br>- **Consumes real aggregated and detailed data** from all analytical modules (M-02, M-06, M-09) to compose reports (**all sourced from Supabase**). | - **PDF:** Export of predefined reports (e.g., Executive Report [based on M-02], Individual Report [based on M-09], Comparative Report [based on M-06]).<br>- **Excel/CSV:** Export of raw data and detailed statistical tables.<br>- **PowerPoint:** Presentation with key graphics from the Executive Summary and Critical KPIs for meetings. |
| M-09 | **👤 Individual Report** | Psychologist | Provide an **in-depth and detailed analysis of a single assessed individual's results** over time. | - **Consumes real data for a specific patient** via the Backend API (**sourced from Supabase**).<br>- **Forms the basis for exporting** the Individual Report through the 'Export' Module (M-08). | - **Aptitude Profile (Radar or Bar Chart):** Visualization of the assessed individual's relative strengths and weaknesses across the 7 BAT-7 aptitudes.<br>- **Score Table:** Details of Raw Scores (PD), Percentile Scores (PC), and Performance Level for each assessment and aptitude.<br>- **Textual Report:** Qualitative interpretations and personalized recommendations based on results and trends. |
| M-10 | **🖥️ System Status** | Administrator | Technical dashboard to **monitor the operational status and performance** of all platform components. | - **Consumes data from the Synchronization Service (M-01)** and **real-time monitoring** of the Backend API and Database (**including direct Supabase connectivity status**). | - **Status Indicators (Traffic Lights/Icons):** "Green/Yellow/Red" status for API, Database (**Supabase connectivity**), Synchronization Service, and other critical services.<br>- **Error Log:** Table of recent critical system errors with date, description, and severity.<br>- **Module Table:** Version and operational status of each functional module. |

---

## 3. Requirements

### Requirement 1

**User Story:** As an administrator, I want to view comprehensive institutional analytics, so that I can monitor overall assessment performance and identify trends across my organization.

#### Acceptance Criteria

1.  WHEN an administrator accesses the analytics dashboard THEN the system SHALL display institutional overview metrics including total assessments completed, **average scores (PD and PC) per aptitude**, and **assessment completion rates**, all based on **real data from Supabase**.
2.  WHEN viewing institutional metrics THEN the system SHALL show data for configurable time periods (last 30 days, 3 months, 6 months, 1 year), **with all data retrieved live from Supabase or its synchronized cache**.
3.  WHEN displaying institutional data THEN the system SHALL present **line charts showing assessment volume trends** over time, **reflecting real assessment activity**.
4.  IF the institution has multiple psychologists THEN the system SHALL display **comparative performance metrics across psychologists (e.g., average completion time, average patient scores, assessment volume)**, derived from **real psychologist-attributed data in Supabase**.

### Requirement 2

**User Story:** As a psychologist, I want to analyze individual patient progress over time, so that I can track improvement and identify areas needing attention.

#### Acceptance Criteria

1.  WHEN a psychologist selects a patient THEN the system SHALL display a comprehensive progress timeline showing all completed assessments, **including assessment date and overall score summary, sourced directly from the patient's real data in Supabase**.
2.  WHEN viewing patient progress THEN the system SHALL show **line charts of score trends (PD and PC)** for each of the seven BAT-7 aptitudes (V, E, A, CON, R, N, M, O), **based on the patient's actual assessment history in Supabase**.
3.  WHEN displaying patient data THEN the system SHALL **clearly highlight statistically significant score changes** between assessments (e.g., based on a predefined threshold or statistical significance test), **using real patient score data**.
4.  WHEN analyzing patient progress THEN the system SHALL provide **percentile comparisons against institutional and national averages (with national averages sourced from [specify source if known])**, with all comparisons based on **real, current data from Supabase and external benchmarks**.

### Requirement 3

**User Story:** As an administrator, I want to generate detailed assessment reports, so that I can share insights with stakeholders and make data-driven decisions.

#### Acceptance Criteria

1.  WHEN generating reports THEN the system SHALL allow selection of multiple filter criteria including date ranges, **patient groups (predefined or custom)**, psychologists, and aptitude types, **all applied to real data from Supabase**.
2.  WHEN creating a report THEN the system SHALL provide export options in **PDF (formatted report), Excel (raw data and summary tables), and CSV (raw data)** formats, with all exported data being **actual, verified data from Supabase**.
3.  WHEN exporting data THEN the system SHALL include both **raw scores (PD) and percentile scores (PC) with appropriate statistical summaries (e.g., mean, median, standard deviation) for selected aggregations**, ensuring all figures are **derived from real Supabase data**.
4.  IF generating institutional reports THEN the system SHALL include **anonymized aggregate data** while maintaining patient privacy (e.g., by removing Personally Identifiable Information and aggregating to minimum group sizes), with **this aggregated data being a true representation of real assessments in Supabase**.

### Requirement 4

**User Story:** As a psychologist, I want to compare assessment results across different patient groups, so that I can identify patterns and optimize intervention strategies.

#### Acceptance Criteria

1.  WHEN comparing patient groups THEN the system SHALL allow grouping by demographics, assessment dates, or **custom criteria (e.g., intervention type, specific conditions)**, **using real patient attributes from Supabase**.
2.  WHEN displaying group comparisons THEN the system SHALL show **statistical significance indicators (e.g., p-values, confidence intervals)** for score differences, **calculated from actual group data**.
3.  WHEN analyzing groups THEN the system SHALL provide **box plots, scatter plots, and distribution charts (histograms)** for each aptitude, **visualizing real data distributions from the selected groups in Supabase**.
4.  WHEN viewing comparisons THEN the system SHALL calculate and display **effect sizes and confidence intervals** for meaningful differences between groups, **based on real comparative data**.

### Requirement 5

**User Story:** As an administrator, I want to monitor system usage and performance metrics, so that I can ensure optimal platform operation and resource allocation.

#### Acceptance Criteria

1.  WHEN accessing system metrics THEN the system SHALL display assessment completion rates, average completion times, and **detailed user activity patterns (e.g., login frequency, feature usage)**, **derived from real system logs and Supabase interaction data**.
2.  WHEN viewing usage data THEN the system SHALL show **peak usage times and resource utilization statistics (e.g., CPU, memory usage for backend services)**, reflecting **actual system performance**.
3.  WHEN monitoring performance THEN the system SHALL **alert administrators to unusual patterns or potential issues (e.g., performance degradation below a predefined threshold, unusual spikes in error rates)**, based on **real-time system telemetry**.
4.  IF system performance degrades THEN the system SHALL provide **diagnostic information (e.g., error logs, service health checks) and suggested actions** to resolve the issue, **reflecting real diagnostic outputs**.

### Requirement 6

**User Story:** As a psychologist, I want to create custom dashboard views, so that I can focus on the metrics most relevant to my practice.

#### Acceptance Criteria

1.  WHEN customizing the dashboard THEN the system SHALL allow users to add, remove, and rearrange dashboard widgets.
2.  WHEN configuring widgets THEN the system SHALL provide options for different chart types, time periods, and data filters **specific to the available metrics and aptitudes**, ensuring **all displayed data is real and synchronized**.
3.  WHEN saving custom views THEN the system SHALL persist user preferences and allow **multiple named saved configurations**.
4.  WHEN sharing insights THEN the system SHALL allow users to export individual charts and **dashboard snapshots (e.g., as image files like PNG/JPG or PDF)**, **capturing the real-time displayed data**.

### Requirement 7

**User Story:** As an administrator, I want to set up automated reporting schedules, so that stakeholders receive regular updates without manual intervention.

#### Acceptance Criteria

1.  WHEN setting up automated reports THEN the system SHALL allow scheduling at daily, weekly, monthly, or **custom intervals (e.g., every X days, on the first Monday of the month)**.
2.  WHEN configuring scheduled reports THEN the system SHALL support multiple recipients and delivery methods (**email, in-system notifications**).
3.  WHEN generating scheduled reports THEN the system SHALL use the most current data available at the time of generation and include timestamp information, **guaranteeing the use of live, real data from Supabase**.
4.  IF a scheduled report fails THEN the system SHALL **notify administrators (via email/system notification) and provide specific error details** (e.g., reason for failure, affected report, timestamp).

---

## 4. Cross-Cutting Aspects

In addition to the detailed functional requirements, the development and deployment of the BAT-7 Dashboard must consider the following cross-cutting aspects to ensure a robust, secure, and usable solution:

* **4.1. Security and Data Privacy:**
    * **Authentication and Authorization:** Implementation of a robust role-based access control (RBAC) system (Administrator, Psychologist) to control access to information and functionalities.
    * **Encryption:** All communication between the frontend, backend, and database SHALL be encrypted (e.g., HTTPS/SSL).
    * **Anonymization/Pseudonymization:** Guarantee patient privacy by anonymizing sensitive data in aggregated reports and pseudonymizing detailed data when appropriate.
    * **Compliance with Data Protection Regulations:** Adherence to relevant data protection regulations (e.g., GDPR, HIPAA, Colombian Personal Data Protection Law, if applicable).
* **4.2. Data Integrity and Real-time Synchronization:**
    * **Single Source of Truth:** The **Supabase database SHALL be the definitive single source of truth** for all BAT-7 assessment data.
    * **Real Data Only:** All data displayed, processed, and reported by the dashboard SHALL be **real, actual assessment data obtained directly from or synchronized with the Supabase database, not fictitious or static data for operational modules**.
    * **Data Freshness:** The synchronization service SHALL ensure that the data presented on the dashboard is as current as possible, reflecting the latest assessments and updates in Supabase. Specific **data latency requirements** will be defined during the design phase (e.g., data updated every X minutes/hours).
    * **Data Validation:** Mechanisms SHALL be in place to validate the integrity and consistency of data synchronized from Supabase.
* **4.3. Performance:**
    * The platform SHALL ensure **fast loading times** for dashboards and **agile responses** to user interactions (e.g., filters, selections).
    * Complex reports SHALL be generated within **acceptable timeframes** (e.g., seconds for executive summaries, minutes for mass data exports).
    * The system SHALL support a **[Specify estimated number, e.g., "moderate/high"] number of concurrent users** without significant performance degradation.
* **4.4. Scalability:**
    * The microservices architecture and Docker containerization will facilitate the **horizontal scalability** of the backend to handle a growing volume of assessments and users.
    * The Supabase database will be leveraged for its inherent scalability to **store and process large volumes of historical and real-time data**.
* **4.5. Usability and User Experience (UX):**
    * The dashboard's user interface SHALL be **intuitive, clean, and easy to navigate**, minimizing the learning curve for administrators and psychologists.
    * Graphs SHALL be **clear, interactive, and visually appealing**, allowing for easy data interpretation.
    * **Responsive design** SHALL be prioritized to ensure a consistent experience across different devices (desktop, tablets).
* **4.6. Maintainability and Extensibility:**
    * The codebase SHALL be **clean, modular, and well-documented** to facilitate future enhancements, bug fixes, and the incorporation of new functionalities.
    * The architecture SHALL allow for the **easy integration** of new data sources or analytical modules in the future.
* **4.7. Error Handling and Notifications:**
    * The system SHALL handle errors **gracefully**, informing the user when necessary and providing detailed information to administrators for troubleshooting.
    * A **notification system** SHALL be implemented for critical events (e.g., synchronization failures, performance alerts).

---

This update explicitly weaves in the Supabase database as the source of real data throughout the document, particularly in the Deployment Strategy, module descriptions, and as a new, prominent "Data Integrity and Real-time Synchronization" cross-cutting aspect. This clarifies the critical dependency on live data for the dashboard's functionality.
```