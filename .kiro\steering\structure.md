# Project Structure & Architecture

## Root Directory Organization

```
├── src/                    # Main source code
├── public/                 # Static assets
├── docs/                   # Documentation and build output
├── .kiro/                  # Kiro IDE configuration
├── .github/                # GitHub workflows
├── node_modules/           # Dependencies
└── dist/                   # Production build output
```

## Source Code Architecture (`/src`)

### Core Application Files
- `main.jsx` - Application entry point with providers
- `App.jsx` - Main app component with routing and theme
- `index.css` - Global styles

### Directory Structure

#### `/components` - Reusable UI Components
- `admin/` - Administrative interface components
- `auth/` - Authentication components (currently unused)
- `common/` - Shared UI components (buttons, cards, etc.)
- `dashboard/` - Dashboard-specific components
- `forms/` - Form components and validation
- `layout/` - Layout components (sidebar, header)
- `patient/` - Patient management components
- `reports/` - Reporting and export components

#### `/pages` - Route-level Components
- `admin/` - Administrative pages
- `questionnaire/` - Test administration pages
- `reports/` - Results and analytics pages
- `student/` - Student/patient interface pages

#### `/services` - Business Logic & API
- `supabaseService.js` - Main database operations
- `authService.js` - Authentication logic
- `baremosService.js` - Score conversion utilities
- `adminService.js` - Administrative operations

#### `/context` & `/contexts` - State Management
- `NoAuthContext.jsx` - Development authentication context
- `PatientContext.jsx` - Patient-specific state
- `ToastContext.jsx` - Notification system

#### `/hooks` - Custom React Hooks
- `useAuth.js` - Authentication hook
- `useToast.js` - Toast notifications
- `useFormValidation.js` - Form validation utilities

#### `/utils` - Utility Functions
- `calculations.js` - Score calculations
- `validations.js` - Input validation
- `formatters.js` - Data formatting utilities

#### `/store` - Redux Configuration
- `index.js` - Store configuration
- `slices/` - Redux Toolkit slices

#### `/routes` - Routing Configuration
- `AppRoutes.jsx` - Main application routes
- `AdminRoutes.jsx` - Administrative routes

#### `/styles` - Styling
- `tailwind.css` - Tailwind imports
- `global.css` - Global styles
- `sidebar.css` - Sidebar-specific styles

## Naming Conventions

### Files & Directories
- **Components**: PascalCase (e.g., `PatientForm.jsx`)
- **Pages**: PascalCase (e.g., `Dashboard.jsx`)
- **Services**: camelCase (e.g., `supabaseService.js`)
- **Utilities**: camelCase (e.g., `dateUtils.js`)
- **Hooks**: camelCase starting with 'use' (e.g., `useAuth.js`)

### Code Conventions
- **React Components**: PascalCase function names
- **Variables/Functions**: camelCase
- **Constants**: UPPER_SNAKE_CASE
- **CSS Classes**: Tailwind utility classes preferred

## Import Patterns

### Path Aliases (configured in vite.config.js)
```javascript
import Component from '@/components/Component'
import { service } from '@services/service'
import { hook } from '@hooks/hook'
```

### Component Imports
- Default exports for components
- Named exports for utilities and services
- Absolute imports preferred over relative

## Architecture Patterns

### Component Structure
- Functional components with hooks
- Props validation with PropTypes
- Error boundaries for error handling
- Context providers for state management

### State Management
- Redux Toolkit for global state
- React Context for feature-specific state
- Local state with useState for component-specific data

### Data Flow
- Services handle all API communication
- Components consume data through hooks
- State updates flow through Redux or Context
- Side effects managed with useEffect

## Development Patterns

### Authentication States
- Currently using `NoAuthContext` for development
- Multiple auth contexts available for different scenarios
- Role-based access control implemented but disabled

### Testing Structure
- Unit tests in `__tests__` directories
- E2E tests with Cypress
- Mock services for testing
- Test utilities in `/src/test/`