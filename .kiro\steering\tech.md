# Technology Stack & Build System

## Core Technologies

### Frontend Framework
- **React 18.2.0** - Main UI framework with hooks and functional components
- **Vite 4.4.5** - Build tool and development server
- **JavaScript (ES2015+)** - Primary language with JSX

### Styling & UI
- **TailwindCSS 3.3.3** - Utility-first CSS framework
- **PostCSS** - CSS processing
- **React Icons** - Icon library
- **Custom CSS** - Additional styling in `/src/styles/`

### State Management & Data
- **Redux Toolkit** - Global state management
- **React Context** - Local state and auth context
- **Supabase** - Backend-as-a-Service (PostgreSQL database)
- **React Router DOM** - Client-side routing

### Development & Testing
- **Vitest** - Unit testing framework
- **Cypress** - E2E testing
- **Testing Library** - React component testing
- **ESLint** - Code linting

## Build System

### Development Commands
```bash
npm run dev          # Start development server (port 3010)
npm run build        # Production build
npm run preview      # Preview production build
```

### Testing Commands
```bash
npm run test         # Run unit tests
npm run test:ui      # Run tests with UI
npm run test:coverage # Run tests with coverage
npm run cypress:open # Open Cypress E2E tests
npm run e2e          # Run E2E tests
npm run test:all     # Run all tests
```

### Build Configuration
- **Base Path**: Conditional for GitHub Pages deployment (`/Bat-7/`)
- **Code Splitting**: Manual chunks for vendors, features, and routes
- **Minification**: Terser with console/debugger removal in production
- **Aliases**: Path aliases for cleaner imports (`@`, `@components`, etc.)

## Environment Setup

### Required Environment Variables
```bash
VITE_SUPABASE_URL=your_supabase_project_url
VITE_SUPABASE_ANON_KEY=your_supabase_anon_key
```

### Development Server
- Default port: 3010
- HMR enabled
- Host accessible for network testing

## Database Architecture

### Supabase Tables
- `instituciones` - Educational institutions
- `psicologos` - Psychology professionals  
- `pacientes` - Students/patients
- `aptitudes` - Cognitive aptitudes definitions
- `evaluaciones` - Assessment sessions
- `resultados` - Test results (PD/PC scores)
- `baremos` - Scoring conversion tables

### Key Services
- `supabaseService.js` - Main database operations
- `authService.js` - Authentication (currently disabled)
- `baremosService.js` - Score conversion utilities