# 📦 Instalación de Dependencias para Dashboard BAT-7

## 🚀 Dependencias Requeridas

Para que todas las funcionalidades avanzadas del Dashboard BAT-7 funcionen correctamente, necesitas instalar las siguientes dependencias:

### 📊 **Gráficos (Chart.js)**
```bash
npm install chart.js react-chartjs-2
```

### 📄 **Exportación PDF**
```bash
npm install jspdf html2canvas
```

### 🔐 **Criptografía para Firmas Digitales**
```bash
npm install crypto-js
```

### 📱 **Códigos QR (Opcional)**
```bash
npm install qrcode
```

## 🔧 **Instalación Completa**

Ejecuta este comando para instalar todas las dependencias de una vez:

```bash
npm install chart.js react-chartjs-2 jspdf html2canvas crypto-js qrcode
```

## ✅ **Verificación Post-Instalación**

Después de instalar las dependencias, necesitas **descomentar** las importaciones en los siguientes archivos:

### 1. **Gráficos**
- `src/components/dashboard/charts/AptitudeProfileChart.jsx`
- `src/components/dashboard/charts/RadarAptitudeChart.jsx`
- `src/components/dashboard/charts/ComparativeBarChart.jsx`
- `src/components/dashboard/charts/AttentionStyleQuadrant.jsx`

**Buscar y descomentar:**
```javascript
// import { Line } from 'react-chartjs-2'; // Comentado temporalmente
// import Chart.js imports comentados temporalmente
```

**Reemplazar por:**
```javascript
import { Line } from 'react-chartjs-2';
import {
  Chart as ChartJS,
  CategoryScale,
  LinearScale,
  PointElement,
  LineElement,
  Title,
  Tooltip,
  Legend,
  Filler
} from 'chart.js';

// Registrar componentes de Chart.js
ChartJS.register(
  CategoryScale,
  LinearScale,
  PointElement,
  LineElement,
  Title,
  Tooltip,
  Legend,
  Filler
);
```

### 2. **Exportación PDF**
- `src/services/PDFExportService.js`

**Buscar y descomentar:**
```javascript
// import jsPDF from 'jspdf'; // Comentado temporalmente
// import html2canvas from 'html2canvas'; // Comentado temporalmente
```

**Reemplazar por:**
```javascript
import jsPDF from 'jspdf';
import html2canvas from 'html2canvas';
```

### 3. **Firmas Digitales**
- `src/services/DigitalSignatureService.js`

**Buscar y descomentar:**
```javascript
// import CryptoJS from 'crypto-js'; // Comentado temporalmente hasta instalar dependencia
```

**Reemplazar por:**
```javascript
import CryptoJS from 'crypto-js';
```

## 🎯 **Funcionalidades que se Activarán**

Una vez instaladas las dependencias:

### ✅ **Gráficos Interactivos**
- Gráfico de líneas del perfil aptitudinal
- Gráfico radar/araña de aptitudes
- Gráfico de barras comparativo
- Cuadrante de estilo atencional

### ✅ **Exportación PDF Completa**
- Informes PDF profesionales
- Exportación de gráficos como imágenes
- Múltiples formatos de informe

### ✅ **Firmas Digitales Seguras**
- Hash criptográfico SHA256
- Validación de integridad
- Certificados digitales

### ✅ **Códigos QR de Validación**
- Verificación online de informes
- Trazabilidad completa

## 🔄 **Reiniciar el Servidor**

Después de instalar las dependencias y descomentar las importaciones:

```bash
npm run dev
```

## 🎉 **¡Listo!**

Tu Dashboard BAT-7 ahora tendrá todas las funcionalidades avanzadas completamente operativas:

- 📊 **9 tipos de visualización** diferentes
- 📄 **Exportación PDF** profesional
- 🔐 **Firmas digitales** con validación criptográfica
- 🎨 **Plantillas personalizables**
- 🔄 **Conexión automática** a base de datos
- 📱 **Códigos QR** de validación

---

## 🆘 **Solución de Problemas**

### Error: "Module not found"
- Verifica que todas las dependencias estén instaladas
- Reinicia el servidor de desarrollo

### Error: "Chart.js not registered"
- Asegúrate de descomentar las importaciones de Chart.js
- Verifica que los componentes estén registrados

### Error: "jsPDF is not defined"
- Descomenta las importaciones en PDFExportService.js
- Reinstala las dependencias si es necesario

---

**¡El Dashboard BAT-7 está listo para uso profesional!** 🚀
