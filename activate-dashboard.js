#!/usr/bin/env node

/**
 * Script para activar automáticamente todas las funcionalidades del Dashboard BAT-7
 * Instala dependencias y descomenta importaciones
 */

const fs = require('fs');
const path = require('path');
const { execSync } = require('child_process');

console.log('🚀 Activando Dashboard BAT-7...\n');

// 1. Instalar dependencias
console.log('📦 Instalando dependencias...');
try {
  execSync('npm install chart.js react-chartjs-2 jspdf html2canvas crypto-js qrcode', { stdio: 'inherit' });
  console.log('✅ Dependencias instaladas correctamente\n');
} catch (error) {
  console.error('❌ Error instalando dependencias:', error.message);
  process.exit(1);
}

// 2. Archivos a actualizar
const filesToUpdate = [
  {
    path: 'src/components/dashboard/charts/AptitudeProfileChart.jsx',
    imports: `import React, { memo, useMemo } from 'react';
import { Line } from 'react-chartjs-2';
import {
  Chart as ChartJS,
  CategoryScale,
  LinearScale,
  PointElement,
  LineElement,
  Title,
  Tooltip,
  Legend,
  Filler
} from 'chart.js';

// Registrar componentes de Chart.js
ChartJS.register(
  CategoryScale,
  LinearScale,
  PointElement,
  LineElement,
  Title,
  Tooltip,
  Legend,
  Filler
);`,
    component: 'Line'
  },
  {
    path: 'src/components/dashboard/charts/RadarAptitudeChart.jsx',
    imports: `import React, { memo, useMemo } from 'react';
import { Radar } from 'react-chartjs-2';
import {
  Chart as ChartJS,
  RadialLinearScale,
  PointElement,
  LineElement,
  Filler,
  Tooltip,
  Legend,
  Title
} from 'chart.js';

// Registrar componentes de Chart.js
ChartJS.register(RadialLinearScale, PointElement, LineElement, Filler, Tooltip, Legend, Title);`,
    component: 'Radar'
  },
  {
    path: 'src/components/dashboard/charts/ComparativeBarChart.jsx',
    imports: `import React, { memo, useMemo } from 'react';
import { Bar } from 'react-chartjs-2';
import {
  Chart as ChartJS,
  CategoryScale,
  LinearScale,
  BarElement,
  Title,
  Tooltip,
  Legend
} from 'chart.js';

// Registrar componentes de Chart.js
ChartJS.register(CategoryScale, LinearScale, BarElement, Title, Tooltip, Legend);`,
    component: 'Bar'
  },
  {
    path: 'src/components/dashboard/charts/AttentionStyleQuadrant.jsx',
    imports: `import React, { memo, useMemo } from 'react';
import { Scatter } from 'react-chartjs-2';
import {
  Chart as ChartJS,
  LinearScale,
  PointElement,
  LineElement,
  Tooltip,
  Legend,
  Title
} from 'chart.js';

// Registrar componentes de Chart.js
ChartJS.register(LinearScale, PointElement, LineElement, Tooltip, Legend, Title);`,
    component: 'Scatter'
  }
];

// 3. Actualizar archivos de gráficos
console.log('🎨 Activando gráficos...');
filesToUpdate.forEach(file => {
  try {
    if (fs.existsSync(file.path)) {
      let content = fs.readFileSync(file.path, 'utf8');
      
      // Reemplazar importaciones comentadas
      content = content.replace(
        /import React, { memo, useMemo } from 'react';\s*\/\/ import.*?\s*\/\/ Chart\.js imports comentados temporalmente/s,
        file.imports
      );
      
      // Reemplazar placeholder con componente real
      content = content.replace(
        /\/\* Gráfico principal - Placeholder temporal \*\/[\s\S]*?<\/div>/,
        `{/* Gráfico principal */}
      <div style={{ height: \`\${height}px\` }}>
        <${file.component} data={chartData} options={options} />
      </div>`
      );
      
      fs.writeFileSync(file.path, content);
      console.log(`✅ ${path.basename(file.path)} activado`);
    }
  } catch (error) {
    console.error(`❌ Error actualizando ${file.path}:`, error.message);
  }
});

// 4. Actualizar PDFExportService
console.log('\n📄 Activando exportación PDF...');
try {
  const pdfServicePath = 'src/services/PDFExportService.js';
  if (fs.existsSync(pdfServicePath)) {
    let content = fs.readFileSync(pdfServicePath, 'utf8');
    
    // Descomentar importaciones
    content = content.replace(
      /\/\/ import jsPDF from 'jspdf'; \/\/ Comentado temporalmente\s*\/\/ import html2canvas from 'html2canvas'; \/\/ Comentado temporalmente/,
      `import jsPDF from 'jspdf';
import html2canvas from 'html2canvas';`
    );
    
    fs.writeFileSync(pdfServicePath, content);
    console.log('✅ PDFExportService activado');
  }
} catch (error) {
  console.error('❌ Error actualizando PDFExportService:', error.message);
}

// 5. Actualizar DigitalSignatureService
console.log('🔐 Activando firmas digitales...');
try {
  const signatureServicePath = 'src/services/DigitalSignatureService.js';
  if (fs.existsSync(signatureServicePath)) {
    let content = fs.readFileSync(signatureServicePath, 'utf8');
    
    // Descomentar importación de CryptoJS
    content = content.replace(
      /\/\/ import CryptoJS from 'crypto-js'; \/\/ Comentado temporalmente hasta instalar dependencia/,
      `import CryptoJS from 'crypto-js';`
    );
    
    // Reemplazar función temporal con CryptoJS real
    content = content.replace(
      /\/\/ Implementación temporal de hash sin dependencias externas[\s\S]*?};/,
      '// CryptoJS está ahora disponible'
    );
    
    // Restaurar funciones CryptoJS
    content = content.replace(/createHash\(/g, 'CryptoJS.SHA256(');
    content = content.replace(/createHash\(/g, 'CryptoJS.SHA256(');
    
    fs.writeFileSync(signatureServicePath, content);
    console.log('✅ DigitalSignatureService activado');
  }
} catch (error) {
  console.error('❌ Error actualizando DigitalSignatureService:', error.message);
}

console.log('\n🎉 ¡Dashboard BAT-7 completamente activado!');
console.log('\n📋 Próximos pasos:');
console.log('1. Reinicia el servidor: npm run dev');
console.log('2. Navega a /admin/dashboard');
console.log('3. ¡Disfruta de los gráficos reales!');
