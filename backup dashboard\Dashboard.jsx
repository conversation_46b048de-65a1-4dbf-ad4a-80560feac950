import React, { useState } from 'react';
import PageHeader from '../../components/ui/PageHeader';
import FilterPanel from '../../components/dashboard/FilterPanel';
import DashboardToolbar from '../../components/dashboard/DashboardToolbar';
import DashboardViewSelector from '../../components/dashboard/DashboardViewSelector';
// Hook personalizado para datos del dashboard
import { useDashboardData } from '../../hooks/useDashboardData';
import { useDashboardExport } from '../../hooks/useDashboardExport';
// Vistas del dashboard
import {
  ExecutiveView,
  KpisView,
  GeneralView,
  TrendsView,
  ComparativeView,
  StatisticalView,
  ExportView
} from '../../components/dashboard/views';
import SyncTestView from '../../components/dashboard/views/enhanced/SyncTestView';
import {
  FaChartPie,
  FaChartLine,
  FaTachometerAlt,
  FaProjectDiagram,
  FaBalanceScale,
  FaDownload,
  FaShare,
  FaRedo,
  FaFilter,
  FaClock
} from 'react-icons/fa';
import { toast } from 'react-toastify';

/**
 * Dashboard Ejecutivo Refactorizado
 * Componente orquestador que maneja navegación y datos
 * Las vistas individuales están separadas en componentes específicos
 */
const Dashboard = () => {
  // Estados para UI
  const [selectedView, setSelectedView] = useState('executive');
  const [showFilters, setShowFilters] = useState(false);
  const [filters, setFilters] = useState({});

  // Hook personalizado para manejar todos los datos
  const { data, loading, error, lastUpdated, refetch, applyFilters } = useDashboardData(filters);
  const { exportAsPDF, exportAsExcel } = useDashboardExport(data);

  // Configuración de vistas del dashboard
  const dashboardViews = [
    {
      key: 'sync-test',
      label: '🔧 Pruebas Sync',
      description: 'Verificar sincronización de datos',
      icon: FaProjectDiagram
    },
    {
      key: 'executive',
      label: 'Resumen Ejecutivo',
      description: 'Vista estratégica con hallazgos clave',
      icon: FaChartPie
    },
    {
      key: 'kpis',
      label: 'KPIs Críticos',
      description: 'Indicadores clave de rendimiento',
      icon: FaTachometerAlt
    },
    {
      key: 'general',
      label: 'Visión General',
      description: 'Estadísticas principales y gráficos',
      icon: FaChartLine
    },
    {
      key: 'trends',
      label: 'Análisis de Tendencias',
      description: 'Evolución temporal de métricas',
      icon: FaChartLine
    },
    {
      key: 'comparative',
      label: 'Análisis Comparativo',
      description: 'Benchmarking y comparaciones',
      icon: FaBalanceScale
    },
    {
      key: 'statistical',
      label: 'Análisis Estadístico',
      description: 'Medidas de tendencia y dispersión',
      icon: FaProjectDiagram
    },
    {
      key: 'export',
      label: 'Exportación',
      description: 'Reportes y presentaciones',
      icon: FaDownload
    }
  ];

  // Manejadores de eventos
  const handleFiltersChange = async (newFilters) => {
    setFilters(newFilters);
    await applyFilters(newFilters);
    setShowFilters(false);
  };

  const handleShareDashboard = () => {
    const url = window.location.href;
    navigator.clipboard.writeText(url).then(() => {
      toast.success('URL copiada al portapapeles');
    }).catch(() => {
      toast.error('Error al copiar URL');
    });
  };

  const handleExport = (format) => {
    if (format === 'pdf') {
      exportAsPDF();
    } else if (format === 'excel') {
      exportAsExcel();
    } else {
      toast.warn(`Formato de exportación '${format}' no soportado.`);
    }
  };

  const handleExportComplete = (result) => {
    if (result.success) {
      toast.success('Exportación completada exitosamente');
    } else {
      toast.error('Error en la exportación');
    }
  };

  // Renderizar vista actual
  const renderCurrentView = () => {
    const {
      estadisticasGenerales,
      alertsData,
      kpiData,
      trendData,
      datosDistribucionNivel,
      datosPerfilInstitucional,
    } = data || {};

    switch (selectedView) {
      case 'sync-test':
        return <SyncTestView />;
      case 'executive':
        return (
          <ExecutiveView
            loading={loading}
            estadisticasGenerales={estadisticasGenerales}
            alertsData={alertsData}
            kpiData={kpiData}
            trendData={trendData}
          />
        );
      case 'kpis':
        return <KpisView loading={loading} kpiData={kpiData} alertsData={alertsData} />;
      case 'general':
        return (
          <GeneralView
            loading={loading}
            estadisticasGenerales={estadisticasGenerales}
            datosDistribucionNivel={datosDistribucionNivel}
            datosPerfilInstitucional={datosPerfilInstitucional}
          />
        );
      case 'trends':
        return <TrendsView loading={loading} trendData={trendData} />;
      case 'comparative':
        return <ComparativeView loading={loading} data={data} />;
      case 'statistical':
        return <StatisticalView loading={loading} data={data} filters={filters} />;
      case 'export':
        return (
          <ExportView
            loading={loading}
            estadisticasGenerales={estadisticasGenerales}
            onExport={handleExport}
            onExportComplete={handleExportComplete}
          />
        );
      default:
        return (
          <GeneralView
            loading={loading}
            estadisticasGenerales={estadisticasGenerales}
            datosDistribucionNivel={datosDistribucionNivel}
            datosPerfilInstitucional={datosPerfilInstitucional}
          />
        );
    }
  };

  return (
    <div className="min-h-screen bg-gray-50">
      <PageHeader
        title="📊 Dashboard Ejecutivo BAT-7"
        subtitle="Análisis integral de datos psicométricos"
      />

      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <DashboardToolbar
          lastUpdated={lastUpdated}
          loading={loading}
          onRefresh={refetch}
          onShare={handleShareDashboard}
          onShowFilters={() => setShowFilters(true)}
        />

        <DashboardViewSelector
          views={dashboardViews}
          selectedView={selectedView}
          onSelectView={setSelectedView}
        />

        {/* Contenido de la vista actual */}
        <div className="mb-8">
          {error && (
            <div className="mb-6 bg-yellow-50 border-l-4 border-yellow-400 p-4 rounded">
              <div className="flex">
                <div className="flex-shrink-0">
                  <i className="fas fa-exclamation-triangle text-yellow-400"></i>
                </div>
                <div className="ml-3">
                  <p className="text-sm text-yellow-700">
                    <strong>Advertencia:</strong> {error}
                  </p>
                </div>
              </div>
            </div>
          )}

          {renderCurrentView()}
        </div>

        {/* Panel de filtros */}
        <FilterPanel
          isOpen={showFilters}
          onClose={() => setShowFilters(false)}
          onFiltersChange={handleFiltersChange}
        />
      </div>
    </div>
  );
};

export default Dashboard;
