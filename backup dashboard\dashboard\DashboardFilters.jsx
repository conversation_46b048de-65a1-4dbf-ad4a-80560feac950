import React, { useState } from 'react';
import { Card, CardBody } from '../../../components/ui/Card';
import { FaFilter, FaTimes, FaCalendarAlt, FaUniversity, FaGraduationCap, FaVenusMars, FaBrain } from 'react-icons/fa';

/**
 * Componente de filtros para el Dashboard
 * Permite filtrar datos por fecha, institución, nivel educativo, etc.
 */
const DashboardFilters = ({ 
  filters, 
  availableOptions, 
  onUpdateFilter, 
  onUpdateDateRange, 
  onClearFilters, 
  onApplyPreset,
  loading 
}) => {
  const [isExpanded, setIsExpanded] = useState(false);

  const levelNames = {
    'E': 'Elemental',
    'M': 'Medio',
    'S': 'Superior'
  };

  const genderNames = {
    'M': 'Masculino',
    'F': 'Femenino'
  };

  const presets = [
    { id: 'last-7-days', name: 'Últimos 7 días', icon: 'fas fa-calendar-week' },
    { id: 'last-30-days', name: 'Últimos 30 días', icon: 'fas fa-calendar-alt' },
    { id: 'this-month', name: 'Este mes', icon: 'fas fa-calendar' },
    { id: 'elementary-only', name: 'Solo Elemental', icon: 'fas fa-child' },
    { id: 'middle-only', name: 'Solo Medio', icon: 'fas fa-user-graduate' },
    { id: 'high-only', name: 'Solo Superior', icon: 'fas fa-graduation-cap' }
  ];

  const hasActiveFilters = () => {
    return (
      (filters.dateRange.start && filters.dateRange.end) ||
      filters.institution ||
      filters.educationalLevel ||
      filters.gender ||
      filters.aptitude
    );
  };

  const getActiveFiltersCount = () => {
    let count = 0;
    if (filters.dateRange.start && filters.dateRange.end) count++;
    if (filters.institution) count++;
    if (filters.educationalLevel) count++;
    if (filters.gender) count++;
    if (filters.aptitude) count++;
    return count;
  };

  return (
    <Card className="mb-6">
      <CardBody className="p-4">
        {/* Header del filtro */}
        <div className="flex items-center justify-between mb-4">
          <div className="flex items-center">
            <FaFilter className="text-blue-500 mr-2" />
            <h3 className="text-lg font-semibold text-gray-800">Filtros</h3>
            {hasActiveFilters() && (
              <span className="ml-2 bg-blue-500 text-white text-xs px-2 py-1 rounded-full">
                {getActiveFiltersCount()}
              </span>
            )}
          </div>
          
          <div className="flex items-center space-x-2">
            {hasActiveFilters() && (
              <button
                onClick={onClearFilters}
                className="text-red-500 hover:text-red-700 text-sm font-medium transition-colors duration-200"
                title="Limpiar filtros"
              >
                <FaTimes className="mr-1" />
                Limpiar
              </button>
            )}
            
            <button
              onClick={() => setIsExpanded(!isExpanded)}
              className="text-blue-500 hover:text-blue-700 text-sm font-medium transition-colors duration-200"
            >
              {isExpanded ? 'Ocultar' : 'Mostrar'} filtros
            </button>
          </div>
        </div>

        {/* Presets rápidos */}
        <div className="mb-4">
          <h4 className="text-sm font-medium text-gray-700 mb-2">Filtros rápidos:</h4>
          <div className="flex flex-wrap gap-2">
            {presets.map(preset => (
              <button
                key={preset.id}
                onClick={() => onApplyPreset(preset.id)}
                className="flex items-center px-3 py-1 bg-gray-100 hover:bg-gray-200 text-gray-700 text-sm rounded-lg transition-colors duration-200"
                title={preset.name}
              >
                <i className={`${preset.icon} mr-1`}></i>
                {preset.name}
              </button>
            ))}
          </div>
        </div>

        {/* Filtros expandidos */}
        {isExpanded && (
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 pt-4 border-t border-gray-200 animate-slideInFromLeft">
            {/* Filtro de fecha */}
            <div>
              <label className="flex items-center text-sm font-medium text-gray-700 mb-2">
                <FaCalendarAlt className="mr-2 text-blue-500" />
                Rango de fechas
              </label>
              <div className="space-y-2">
                <input
                  type="date"
                  value={filters.dateRange.start || ''}
                  onChange={(e) => onUpdateDateRange(e.target.value, filters.dateRange.end)}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 text-sm"
                  placeholder="Fecha inicio"
                />
                <input
                  type="date"
                  value={filters.dateRange.end || ''}
                  onChange={(e) => onUpdateDateRange(filters.dateRange.start, e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 text-sm"
                  placeholder="Fecha fin"
                />
              </div>
            </div>

            {/* Filtro de institución */}
            <div>
              <label className="flex items-center text-sm font-medium text-gray-700 mb-2">
                <FaUniversity className="mr-2 text-green-500" />
                Institución
              </label>
              <select
                value={filters.institution || ''}
                onChange={(e) => onUpdateFilter('institution', e.target.value || null)}
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 text-sm"
                disabled={loading}
              >
                <option value="">Todas las instituciones</option>
                {availableOptions.institutions.map(institution => (
                  <option key={institution} value={institution}>
                    Institución {institution}
                  </option>
                ))}
              </select>
            </div>

            {/* Filtro de nivel educativo */}
            <div>
              <label className="flex items-center text-sm font-medium text-gray-700 mb-2">
                <FaGraduationCap className="mr-2 text-purple-500" />
                Nivel educativo
              </label>
              <select
                value={filters.educationalLevel || ''}
                onChange={(e) => onUpdateFilter('educationalLevel', e.target.value || null)}
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 text-sm"
              >
                <option value="">Todos los niveles</option>
                {availableOptions.educationalLevels.map(level => (
                  <option key={level} value={level}>
                    {levelNames[level] || level}
                  </option>
                ))}
              </select>
            </div>

            {/* Filtro de género */}
            <div>
              <label className="flex items-center text-sm font-medium text-gray-700 mb-2">
                <FaVenusMars className="mr-2 text-pink-500" />
                Género
              </label>
              <select
                value={filters.gender || ''}
                onChange={(e) => onUpdateFilter('gender', e.target.value || null)}
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 text-sm"
              >
                <option value="">Todos los géneros</option>
                {availableOptions.genders.map(gender => (
                  <option key={gender} value={gender}>
                    {genderNames[gender] || gender}
                  </option>
                ))}
              </select>
            </div>

            {/* Filtro de aptitud */}
            <div>
              <label className="flex items-center text-sm font-medium text-gray-700 mb-2">
                <FaBrain className="mr-2 text-orange-500" />
                Aptitud específica
              </label>
              <select
                value={filters.aptitude || ''}
                onChange={(e) => onUpdateFilter('aptitude', e.target.value || null)}
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 text-sm"
                disabled={loading}
              >
                <option value="">Todas las aptitudes</option>
                {availableOptions.aptitudes.map(aptitude => (
                  <option key={aptitude.id} value={aptitude.id}>
                    {aptitude.codigo} - {aptitude.nombre}
                  </option>
                ))}
              </select>
            </div>
          </div>
        )}

        {/* Resumen de filtros activos */}
        {hasActiveFilters() && (
          <div className="mt-4 pt-4 border-t border-gray-200">
            <h4 className="text-sm font-medium text-gray-700 mb-2">Filtros activos:</h4>
            <div className="flex flex-wrap gap-2">
              {filters.dateRange.start && filters.dateRange.end && (
                <span className="inline-flex items-center px-3 py-1 bg-blue-100 text-blue-800 text-sm rounded-full">
                  <FaCalendarAlt className="mr-1" />
                  {filters.dateRange.start} - {filters.dateRange.end}
                  <button
                    onClick={() => onUpdateDateRange(null, null)}
                    className="ml-2 text-blue-600 hover:text-blue-800"
                  >
                    <FaTimes />
                  </button>
                </span>
              )}
              
              {filters.educationalLevel && (
                <span className="inline-flex items-center px-3 py-1 bg-purple-100 text-purple-800 text-sm rounded-full">
                  <FaGraduationCap className="mr-1" />
                  {levelNames[filters.educationalLevel]}
                  <button
                    onClick={() => onUpdateFilter('educationalLevel', null)}
                    className="ml-2 text-purple-600 hover:text-purple-800"
                  >
                    <FaTimes />
                  </button>
                </span>
              )}
              
              {filters.gender && (
                <span className="inline-flex items-center px-3 py-1 bg-pink-100 text-pink-800 text-sm rounded-full">
                  <FaVenusMars className="mr-1" />
                  {genderNames[filters.gender]}
                  <button
                    onClick={() => onUpdateFilter('gender', null)}
                    className="ml-2 text-pink-600 hover:text-pink-800"
                  >
                    <FaTimes />
                  </button>
                </span>
              )}
            </div>
          </div>
        )}
      </CardBody>
    </Card>
  );
};

export default DashboardFilters;
