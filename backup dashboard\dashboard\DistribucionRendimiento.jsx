import React, { useState, useEffect } from 'react';
import { Card, CardHeader, CardBody } from '../../../components/ui/Card';
import supabase from '../../../api/supabaseClient';

const DistribucionRendimiento = () => {
  const [data, setData] = useState([]);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    const fetchData = async () => {
      try {
        const { data: result, error } = await supabase
          .from('dashboard_distribucion_rendimiento')
          .select('*');
        setData(result || []);
      } catch (error) {
        console.error('Error:', error);
      } finally {
        setLoading(false);
      }
    };
    fetchData();
  }, []);

  const niveles = ['E', 'M', 'S'];
  const categorias = ['Bajo', 'Promedio', 'Alto'];
  
  const getColorForCategoria = (categoria) => {
    const colors = {
      'Bajo': 'bg-red-500',
      'Promedio': 'bg-yellow-500', 
      'Alto': 'bg-green-500'
    };
    return colors[categoria] || 'bg-gray-500';
  };

  if (loading) {
    return (
      <Card>
        <CardHeader className="bg-gradient-to-r from-orange-500 to-orange-600 text-white">
          <h3 className="text-lg font-semibold">
            <i className="fas fa-chart-bar mr-2"></i>
            Distribución de Rendimiento
          </h3>
        </CardHeader>
        <CardBody>
          <div className="animate-pulse h-64 bg-gray-200 rounded"></div>
        </CardBody>
      </Card>
    );
  }

  return (
    <Card>
      <CardHeader className="bg-gradient-to-r from-orange-500 to-orange-600 text-white">
        <h3 className="text-lg font-semibold">
          <i className="fas fa-chart-bar mr-2"></i>
          Distribución de Rendimiento por Nivel
        </h3>
      </CardHeader>
      <CardBody>
        {data.length > 0 ? (
          <>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-6">
              {niveles.map(nivel => {
                const nivelData = data.filter(item => item.nivel === nivel);
                const nivelNombre = nivel === 'E' ? 'Elemental' : nivel === 'M' ? 'Medio' : 'Superior';
                
                return (
                  <div key={nivel} className="border rounded-lg p-4">
                    <h4 className="font-medium text-gray-800 mb-3 text-center">
                      Nivel {nivelNombre}
                    </h4>
                    <div className="space-y-2">
                      {categorias.map(categoria => {
                        const categoriaData = nivelData.filter(item => item.categoria_rendimiento === categoria);
                        const totalEstudiantes = categoriaData.reduce((sum, item) => sum + item.cantidad_estudiantes, 0);
                        const promedioPorcentaje = categoriaData.length > 0 
                          ? categoriaData.reduce((sum, item) => sum + item.porcentaje, 0) / categoriaData.length 
                          : 0;
                        
                        return (
                          <div key={categoria} className="flex items-center justify-between">
                            <div className="flex items-center">
                              <div className={`w-4 h-4 rounded mr-2 ${getColorForCategoria(categoria)}`}></div>
                              <span className="text-sm">{categoria}</span>
                            </div>
                            <div className="text-right">
                              <div className="text-sm font-medium">{totalEstudiantes}</div>
                              <div className="text-xs text-gray-500">{promedioPorcentaje.toFixed(1)}%</div>
                            </div>
                          </div>
                        );
                      })}
                    </div>
                  </div>
                );
              })}
            </div>
            
            <div className="p-4 bg-orange-50 rounded-lg">
              <h4 className="font-medium text-orange-800 mb-2">
                <i className="fas fa-info-circle mr-2"></i>
                Interpretación
              </h4>
              <p className="text-sm text-orange-700">
                Muestra la distribución de estudiantes en categorías de rendimiento. 
                Identifica niveles con alta concentración en "Bajo" que requieren intervención.
              </p>
            </div>
          </>
        ) : (
          <div className="text-center py-8 text-gray-500">
            <i className="fas fa-chart-bar text-4xl mb-4"></i>
            <p>No hay datos de distribución disponibles</p>
          </div>
        )}
      </CardBody>
    </Card>
  );
};

export default DistribucionRendimiento;
