// Script específico para debuggear los datos de Ana Sofia Rueda Acevedo
import supabase from './src/api/supabaseClient.js';

async function debugAnaSofia() {
  console.log('🔍 Investigando específicamente a Ana Sofia Rueda Acevedo...\n');

  try {
    // 1. Buscar a Ana Sofia en la tabla pacientes
    console.log('👤 1. Buscando a Ana Sofia en tabla "pacientes"...');
    const { data: anaSofiaSearch, error: searchError } = await supabase
      .from('pacientes')
      .select('*')
      .or('nombre.ilike.%ana%,apellido.ilike.%rueda%,apellido.ilike.%acevedo%');

    if (searchError) {
      console.error('❌ Error en búsqueda:', searchError);
      return;
    }

    console.log(`✅ Pacientes encontrados con "ana", "rueda" o "acevedo": ${anaSofiaSearch?.length || 0}`);
    anaSofiaSearch?.forEach((p, index) => {
      console.log(`   ${index + 1}. ${p.nombre} ${p.apellido} (ID: ${p.id})`);
      console.log(`      - Documento: ${p.documento || 'N/A'}`);
      console.log(`      - Email: ${p.email || 'N/A'}`);
      console.log(`      - Género: ${p.genero || 'N/A'}`);
      console.log(`      - Creado: ${new Date(p.created_at).toLocaleString('es-ES')}`);
    });

    // 2. Buscar específicamente "Ana Sofia Rueda Acevedo"
    console.log('\n🎯 2. Búsqueda específica de "Ana Sofia Rueda Acevedo"...');
    const anaSofia = anaSofiaSearch?.find(p => 
      p.nombre.toLowerCase().includes('ana') && 
      p.nombre.toLowerCase().includes('sofia') &&
      p.apellido.toLowerCase().includes('rueda') &&
      p.apellido.toLowerCase().includes('acevedo')
    );

    if (!anaSofia) {
      console.log('❌ Ana Sofia Rueda Acevedo NO encontrada en la tabla pacientes');
      
      // Buscar variaciones del nombre
      console.log('\n🔍 Buscando variaciones del nombre...');
      const variations = [
        'ana sofia',
        'ana',
        'sofia',
        'rueda',
        'acevedo'
      ];
      
      for (const variation of variations) {
        const { data: varResults } = await supabase
          .from('pacientes')
          .select('*')
          .or(`nombre.ilike.%${variation}%,apellido.ilike.%${variation}%`);
        
        if (varResults?.length > 0) {
          console.log(`   Encontrados con "${variation}":`);
          varResults.forEach(p => {
            console.log(`     - ${p.nombre} ${p.apellido} (ID: ${p.id})`);
          });
        }
      }
      return;
    }

    console.log(`✅ Ana Sofia encontrada: ${anaSofia.nombre} ${anaSofia.apellido}`);
    console.log(`   ID: ${anaSofia.id}`);
    console.log(`   Documento: ${anaSofia.documento || 'N/A'}`);
    console.log(`   Creado: ${new Date(anaSofia.created_at).toLocaleString('es-ES')}`);

    // 3. Buscar resultados de Ana Sofia
    console.log('\n📊 3. Buscando resultados de Ana Sofia...');
    const { data: resultados, error: resultadosError } = await supabase
      .from('resultados')
      .select(`
        id,
        puntaje_directo,
        percentil,
        errores,
        tiempo_segundos,
        concentracion,
        created_at,
        updated_at,
        aptitudes:aptitud_id (
          codigo,
          nombre,
          descripcion
        )
      `)
      .eq('paciente_id', anaSofia.id)
      .order('created_at', { ascending: false });

    if (resultadosError) {
      console.error('❌ Error al obtener resultados:', resultadosError);
      return;
    }

    console.log(`✅ Resultados encontrados: ${resultados?.length || 0}`);
    
    if (resultados?.length > 0) {
      console.log('\n📋 Detalles de resultados:');
      resultados.forEach((r, index) => {
        const fecha = new Date(r.created_at).toLocaleString('es-ES');
        console.log(`   ${index + 1}. ${r.aptitudes?.codigo || 'N/A'} - ${r.aptitudes?.nombre || 'Test Desconocido'}`);
        console.log(`      - PD: ${r.puntaje_directo || 'N/A'}`);
        console.log(`      - PC: ${r.percentil || 'N/A'}`);
        console.log(`      - Errores: ${r.errores || 0}`);
        console.log(`      - Tiempo: ${r.tiempo_segundos ? Math.round(r.tiempo_segundos / 60) + ' min' : 'N/A'}`);
        console.log(`      - Fecha: ${fecha}`);
        console.log(`      - ID resultado: ${r.id}`);
      });
    } else {
      console.log('❌ No se encontraron resultados para Ana Sofia');
    }

    // 4. Verificar si Ana Sofia aparece en la consulta de Results
    console.log('\n🔍 4. Simulando consulta de página Results...');
    
    // Esta es la misma consulta que usa la página Results
    const { data: todosLosPacientes, error: todosError } = await supabase
      .from('pacientes')
      .select(`
        id,
        nombre,
        apellido,
        documento,
        genero,
        created_at
      `)
      .order('nombre', { ascending: true });

    if (todosError) {
      console.error('❌ Error en consulta de Results:', todosError);
      return;
    }

    console.log(`✅ Total pacientes en consulta Results: ${todosLosPacientes?.length || 0}`);
    
    const anaSofiaEnResults = todosLosPacientes?.find(p => p.id === anaSofia.id);
    if (anaSofiaEnResults) {
      console.log('✅ Ana Sofia SÍ aparece en la consulta de Results');
      console.log(`   Posición en lista: ${todosLosPacientes.findIndex(p => p.id === anaSofia.id) + 1}`);
    } else {
      console.log('❌ Ana Sofia NO aparece en la consulta de Results');
    }

    // 5. Verificar todos los resultados
    console.log('\n📊 5. Verificando todos los resultados...');
    const { data: todosLosResultados, error: todosResultadosError } = await supabase
      .from('resultados')
      .select(`
        id,
        paciente_id,
        puntaje_directo,
        percentil,
        created_at,
        aptitudes:aptitud_id (
          codigo,
          nombre
        )
      `)
      .order('created_at', { ascending: false });

    if (todosResultadosError) {
      console.error('❌ Error al obtener todos los resultados:', todosResultadosError);
      return;
    }

    console.log(`✅ Total resultados en base de datos: ${todosLosResultados?.length || 0}`);
    
    const resultadosAnaSofia = todosLosResultados?.filter(r => r.paciente_id === anaSofia.id) || [];
    console.log(`✅ Resultados de Ana Sofia: ${resultadosAnaSofia.length}`);
    
    if (resultadosAnaSofia.length > 0) {
      console.log('   Tests completados por Ana Sofia:');
      resultadosAnaSofia.forEach(r => {
        const fecha = new Date(r.created_at).toLocaleDateString('es-ES');
        console.log(`     - ${r.aptitudes?.codigo || 'N/A'}: PD=${r.puntaje_directo}, PC=${r.percentil || 'N/A'} (${fecha})`);
      });
    }

    // 6. Resumen final
    console.log('\n📋 RESUMEN:');
    console.log(`✅ Ana Sofia existe en tabla pacientes: ${anaSofia ? 'SÍ' : 'NO'}`);
    console.log(`✅ Ana Sofia tiene resultados: ${resultados?.length > 0 ? 'SÍ' : 'NO'} (${resultados?.length || 0} resultados)`);
    console.log(`✅ Ana Sofia aparece en consulta Results: ${anaSofiaEnResults ? 'SÍ' : 'NO'}`);
    console.log(`✅ Total pacientes en sistema: ${todosLosPacientes?.length || 0}`);
    console.log(`✅ Total resultados en sistema: ${todosLosResultados?.length || 0}`);

    if (anaSofia && resultados?.length > 0 && anaSofiaEnResults) {
      console.log('\n🎉 CONCLUSIÓN: Ana Sofia debería aparecer en la página Results');
      console.log('   El problema podría estar en:');
      console.log('   1. Filtrado en el frontend');
      console.log('   2. Paginación');
      console.log('   3. Caché del navegador');
      console.log('   4. Estado de React no actualizado');
    } else {
      console.log('\n❌ PROBLEMA IDENTIFICADO:');
      if (!anaSofia) console.log('   - Ana Sofia no existe en tabla pacientes');
      if (!resultados?.length) console.log('   - Ana Sofia no tiene resultados');
      if (!anaSofiaEnResults) console.log('   - Ana Sofia no aparece en consulta Results');
    }

  } catch (error) {
    console.error('💥 Error general:', error);
  }
}

// Ejecutar el debugging
debugAnaSofia();
