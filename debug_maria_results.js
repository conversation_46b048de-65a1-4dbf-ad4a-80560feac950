import { createClient } from '@supabase/supabase-js';

const supabaseUrl = 'https://ydglduxhgwajqdseqzpy.supabase.co';
const supabaseKey = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InlkZ2xkdXhoZ3dhanFkc2VxenB5Iiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDYzMTI4NDEsImV4cCI6MjA2MTg4ODg0MX0.HEFdJm5qnXU1PQFbF-HkZ-bLez9LuPi3LepirU0nz4c';

const supabase = createClient(supabaseUrl, supabaseKey);

async function debugMariaResults() {
  console.log('🔍 Debugging <PERSON> results...');

  try {
    // 1. Verificar pacientes
    console.log('\n📊 1. Verificando pacientes...');
    const { data: pacientes, error: pacientesError } = await supabase
      .from('pacientes')
      .select('id, nombre, apellido')
      .order('nombre');

    if (pacientesError) {
      console.error('❌ Error al obtener pacientes:', pacientesError);
      return;
    }

    console.log(`✅ Total pacientes: ${pacientes.length}`);
    pacientes.forEach(p => {
      console.log(`   - ${p.nombre} ${p.apellido} (ID: ${p.id})`);
    });

    // 2. Buscar María González
    const maria = pacientes.find(p =>
      p.nombre.toLowerCase().includes('maría') || p.nombre.toLowerCase().includes('maria')
    );

    if (!maria) {
      console.log('❌ María González no encontrada');
      return;
    }

    console.log(`\n🎯 María encontrada: ${maria.nombre} ${maria.apellido} (ID: ${maria.id})`);

    // 3. Verificar resultados de María
    console.log('\n📊 3. Verificando resultados de María...');
    const { data: resultados, error: resultadosError } = await supabase
      .from('resultados')
      .select(`
        id,
        paciente_id,
        puntaje_directo,
        percentil,
        errores,
        tiempo_segundos,
        concentracion,
        created_at,
        aptitudes:aptitud_id (
          codigo,
          nombre,
          descripcion
        )
      `)
      .eq('paciente_id', maria.id)
      .order('created_at', { ascending: false });

    if (resultadosError) {
      console.error('❌ Error al obtener resultados:', resultadosError);
      return;
    }

    console.log(`✅ Total resultados de María: ${resultados.length}`);
    resultados.forEach(r => {
      console.log(`   - ${r.aptitudes.codigo} (${r.aptitudes.nombre}): PD=${r.puntaje_directo}, PC=${r.percentil}, Errores=${r.errores}`);
    });

    // 4. Simular la consulta del componente Results
    console.log('\n📊 4. Simulando consulta del componente Results...');
    const { data: todosResultados, error: todosError } = await supabase
      .from('resultados')
      .select(`
        id,
        paciente_id,
        puntaje_directo,
        percentil,
        errores,
        tiempo_segundos,
        concentracion,
        created_at,
        aptitudes:aptitud_id (
          codigo,
          nombre,
          descripcion
        )
      `)
      .order('created_at', { ascending: false });

    if (todosError) {
      console.error('❌ Error al obtener todos los resultados:', todosError);
      return;
    }

    const resultadosMaria = todosResultados.filter(r => r.paciente_id === maria.id);
    console.log(`✅ Resultados de María en consulta general: ${resultadosMaria.length}`);

    // 5. Verificar agrupación por paciente
    console.log('\n📊 5. Verificando agrupación por paciente...');
    const groupedByPatient = pacientes.map(paciente => {
      const pacienteResultados = todosResultados.filter(resultado => resultado.paciente_id === paciente.id);
      return {
        paciente: paciente,
        resultados: pacienteResultados,
        tieneResultados: pacienteResultados.length > 0
      };
    });

    const mariaGroup = groupedByPatient.find(g => g.paciente.id === maria.id);
    console.log(`✅ María en agrupación: ${mariaGroup ? 'SÍ' : 'NO'}`);
    if (mariaGroup) {
      console.log(`   - Tiene resultados: ${mariaGroup.tieneResultados}`);
      console.log(`   - Cantidad de resultados: ${mariaGroup.resultados.length}`);
    }

    // 6. Verificar pacientes con resultados
    const pacientesConResultados = groupedByPatient.filter(g => g.tieneResultados);
    console.log(`\n✅ Total pacientes con resultados: ${pacientesConResultados.length}`);
    pacientesConResultados.forEach(g => {
      console.log(`   - ${g.paciente.nombre} ${g.paciente.apellido}: ${g.resultados.length} resultados`);
    });

  } catch (error) {
    console.error('💥 Error general:', error);
  }
}

// Ejecutar el debug
debugMariaResults();