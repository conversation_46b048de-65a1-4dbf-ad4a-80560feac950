// Script para debuggear el problema de los pacientes en Results
import supabase from './src/api/supabaseClient.js';

async function debugPatients() {
  console.log('🔍 Iniciando debugging de pacientes...\n');

  try {
    // 1. Obtener todos los pacientes
    console.log('📋 1. Obteniendo todos los pacientes...');
    const { data: pacientes, error: pacientesError } = await supabase
      .from('pacientes')
      .select(`
        id,
        nombre,
        apellido,
        documento,
        genero,
        created_at
      `)
      .order('nombre', { ascending: true });

    if (pacientesError) {
      console.error('❌ Error al obtener pacientes:', pacientesError);
      return;
    }

    console.log(`✅ Pacientes encontrados: ${pacientes?.length || 0}`);
    pacientes?.forEach((p, index) => {
      console.log(`   ${index + 1}. ${p.nombre} ${p.apellido} (ID: ${p.id})`);
    });

    // 2. Obtener todos los resultados
    console.log('\n📊 2. Obteniendo todos los resultados...');
    const { data: resultados, error: resultadosError } = await supabase
      .from('resultados')
      .select(`
        id,
        paciente_id,
        puntaje_directo,
        percentil,
        errores,
        tiempo_segundos,
        concentracion,
        created_at,
        aptitudes:aptitud_id (
          codigo,
          nombre,
          descripcion
        )
      `)
      .order('created_at', { ascending: false });

    if (resultadosError) {
      console.error('❌ Error al obtener resultados:', resultadosError);
      return;
    }

    console.log(`✅ Resultados encontrados: ${resultados?.length || 0}`);

    // 3. Agrupar resultados por paciente
    console.log('\n🔗 3. Agrupando resultados por paciente...');
    const resultadosPorPaciente = {};
    resultados?.forEach(resultado => {
      const pacienteId = resultado.paciente_id;
      if (!resultadosPorPaciente[pacienteId]) {
        resultadosPorPaciente[pacienteId] = [];
      }
      resultadosPorPaciente[pacienteId].push(resultado);
    });

    console.log('📈 Resultados por paciente:');
    Object.entries(resultadosPorPaciente).forEach(([pacienteId, resultados]) => {
      const paciente = pacientes?.find(p => p.id === pacienteId);
      const nombrePaciente = paciente ? `${paciente.nombre} ${paciente.apellido}` : 'Paciente no encontrado';
      console.log(`   ${nombrePaciente} (${pacienteId}): ${resultados.length} resultado(s)`);
      
      resultados.forEach(r => {
        const fecha = new Date(r.created_at).toLocaleDateString('es-ES');
        console.log(`     - ${r.aptitudes?.codigo || 'N/A'}: PD=${r.puntaje_directo}, PC=${r.percentil || 'N/A'} (${fecha})`);
      });
    });

    // 4. Verificar pacientes sin resultados
    console.log('\n❓ 4. Pacientes sin resultados:');
    const pacientesSinResultados = pacientes?.filter(p => !resultadosPorPaciente[p.id]) || [];
    if (pacientesSinResultados.length > 0) {
      pacientesSinResultados.forEach(p => {
        console.log(`   - ${p.nombre} ${p.apellido} (ID: ${p.id})`);
      });
    } else {
      console.log('   ✅ Todos los pacientes tienen resultados');
    }

    // 5. Buscar específicamente a Ana Sofia Rueda Acevedo
    console.log('\n🔍 5. Buscando a Ana Sofia Rueda Acevedo...');
    const anaSofia = pacientes?.find(p => 
      p.nombre.toLowerCase().includes('ana') && 
      p.apellido.toLowerCase().includes('rueda')
    );

    if (anaSofia) {
      console.log(`✅ Ana Sofia encontrada: ${anaSofia.nombre} ${anaSofia.apellido} (ID: ${anaSofia.id})`);
      const resultadosAnaSofia = resultadosPorPaciente[anaSofia.id] || [];
      console.log(`   📊 Resultados: ${resultadosAnaSofia.length}`);
      
      if (resultadosAnaSofia.length > 0) {
        console.log('   📋 Detalles de resultados:');
        resultadosAnaSofia.forEach(r => {
          const fecha = new Date(r.created_at).toLocaleDateString('es-ES');
          const hora = new Date(r.created_at).toLocaleTimeString('es-ES');
          console.log(`     - ${r.aptitudes?.codigo || 'N/A'}: PD=${r.puntaje_directo}, PC=${r.percentil || 'N/A'} (${fecha} ${hora})`);
        });
      } else {
        console.log('   ❌ No tiene resultados registrados');
      }
    } else {
      console.log('❌ Ana Sofia Rueda Acevedo no encontrada en la base de datos');
    }

    // 6. Verificar resultados de hoy
    console.log('\n📅 6. Resultados de hoy:');
    const hoy = new Date().toISOString().split('T')[0];
    const resultadosHoy = resultados?.filter(r => r.created_at.startsWith(hoy)) || [];
    
    console.log(`✅ Resultados de hoy (${hoy}): ${resultadosHoy.length}`);
    resultadosHoy.forEach(r => {
      const paciente = pacientes?.find(p => p.id === r.paciente_id);
      const nombrePaciente = paciente ? `${paciente.nombre} ${paciente.apellido}` : 'Paciente no encontrado';
      const hora = new Date(r.created_at).toLocaleTimeString('es-ES');
      console.log(`   - ${nombrePaciente}: ${r.aptitudes?.codigo || 'N/A'} a las ${hora}`);
    });

  } catch (error) {
    console.error('💥 Error general:', error);
  }
}

// Ejecutar el debugging
debugPatients();
