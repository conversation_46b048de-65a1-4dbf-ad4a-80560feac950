import{j as e,H as s,I as a,F as r}from"./auth-3ab59eff.js";import{r as t,a as l}from"./react-vendor-99be060c.js";import"./ui-vendor-9705a4a1.js";import"./utils-vendor-4d1206d7.js";const i=()=>{const[i,n]=t.useState("<EMAIL>"),[d,c]=t.useState("123456"),[o,m]=t.useState("administrador"),[x,u]=t.useState(!1),h=l(),g=e=>{m(e),u(!0),setTimeout(()=>{switch(localStorage.setItem("isLoggedIn","true"),localStorage.setItem("userRole",e),localStorage.setItem("userEmail","<EMAIL>"),u(!1),e){case"administrador":h("/admin/administration");break;case"psicologo":h("/admin/candidates");break;default:h("/home")}},500)};return e.jsxs("div",{className:"min-h-screen flex",children:[e.jsxs("div",{className:"hidden lg:flex lg:w-1/2 bg-gradient-to-br from-blue-600 to-blue-800 relative overflow-hidden",children:[e.jsxs("div",{className:"absolute inset-0 opacity-10",children:[e.jsx("div",{className:"absolute top-20 left-20 w-32 h-32 border border-white rounded-full"}),e.jsx("div",{className:"absolute bottom-20 right-20 w-24 h-24 border border-white rounded-full"}),e.jsx("div",{className:"absolute top-1/2 left-10 w-16 h-16 border border-white rounded-full"})]}),e.jsxs("div",{className:"relative z-10 flex flex-col justify-center px-12 text-white",children:[e.jsx("div",{className:"mb-12",children:e.jsxs("div",{className:"flex items-center mb-4",children:[e.jsx("div",{className:"w-12 h-12 bg-orange-500 rounded-full flex items-center justify-center mr-4",children:e.jsx("span",{className:"text-white font-bold text-xl",children:"B"})}),e.jsxs("div",{children:[e.jsx("h1",{className:"text-3xl font-bold",children:"BAT-7"}),e.jsx("p",{className:"text-blue-200",children:"Sistema de Evaluación"})]})]})}),e.jsxs("div",{className:"mb-8",children:[e.jsxs("h2",{className:"text-4xl font-bold mb-4",children:["Bienvenido al",e.jsx("br",{}),e.jsx("span",{className:"text-orange-400",children:"Futuro de la Evaluación"}),e.jsx("br",{}),e.jsx("span",{className:"text-blue-200",children:"Psicométrica"})]}),e.jsx("p",{className:"text-blue-200 text-lg leading-relaxed",children:"Plataforma de nueva generación para evaluaciones psicológicas inteligentes y análisis avanzado"})]}),e.jsxs("div",{className:"space-y-4",children:[e.jsxs("div",{className:"flex items-center",children:[e.jsx("div",{className:"w-8 h-8 bg-orange-500 rounded-full flex items-center justify-center mr-4",children:e.jsx("span",{className:"text-white text-sm",children:"✓"})}),e.jsxs("div",{children:[e.jsx("h3",{className:"font-semibold",children:"Evaluaciones Inteligentes"}),e.jsx("p",{className:"text-blue-200 text-sm",children:"Adaptativas y avanzadas"})]})]}),e.jsxs("div",{className:"flex items-center",children:[e.jsx("div",{className:"w-8 h-8 bg-orange-500 rounded-full flex items-center justify-center mr-4",children:e.jsx("span",{className:"text-white text-sm",children:"✓"})}),e.jsxs("div",{children:[e.jsx("h3",{className:"font-semibold",children:"Dashboard Avanzado"}),e.jsx("p",{className:"text-blue-200 text-sm",children:"Análisis en tiempo real"})]})]}),e.jsxs("div",{className:"flex items-center",children:[e.jsx("div",{className:"w-8 h-8 bg-orange-500 rounded-full flex items-center justify-center mr-4",children:e.jsx("span",{className:"text-white text-sm",children:"✓"})}),e.jsxs("div",{children:[e.jsx("h3",{className:"font-semibold",children:"Seguridad Total"}),e.jsx("p",{className:"text-blue-200 text-sm",children:"Protección de datos garantizada"})]})]})]})]})]}),e.jsx("div",{className:"w-full lg:w-1/2 flex items-center justify-center p-8 bg-gray-50",children:e.jsxs("div",{className:"w-full max-w-md",children:[e.jsxs("div",{className:"lg:hidden text-center mb-8",children:[e.jsx("h1",{className:"text-3xl font-bold text-gray-800 mb-2",children:"BAT-7"}),e.jsx("p",{className:"text-gray-600",children:"Sistema de Evaluación"})]}),e.jsxs("div",{className:"bg-white rounded-lg shadow-lg p-8",children:[e.jsxs("form",{onSubmit:e=>{return s=void 0,a=null,r=function*(){e.preventDefault(),u(!0),setTimeout(()=>{switch(localStorage.setItem("isLoggedIn","true"),localStorage.setItem("userRole",o),localStorage.setItem("userEmail",i),u(!1),o){case"administrador":h("/admin/administration");break;case"psicologo":h("/admin/candidates");break;default:h("/home")}},1e3)},new Promise((t,l)=>{var i=s=>{try{d(r.next(s))}catch(e){l(e)}},n=s=>{try{d(r.throw(s))}catch(e){l(e)}},d=e=>e.done?t(e.value):Promise.resolve(e.value).then(i,n);d((r=r.apply(s,a)).next())});var s,a,r},className:"space-y-6",children:[e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-3",children:"TIPO DE USUARIO"}),e.jsxs("div",{className:"space-y-3",children:[e.jsxs("label",{className:"flex items-center p-3 border border-gray-200 rounded-lg cursor-pointer hover:bg-orange-50 transition-colors",children:[e.jsx("input",{type:"radio",name:"userType",value:"candidato",checked:"candidato"===o,onChange:e=>m(e.target.value),className:"sr-only"}),e.jsx("div",{className:"w-5 h-5 rounded-full border-2 mr-3 flex items-center justify-center "+("candidato"===o?"border-orange-500 bg-orange-500":"border-gray-300"),children:"candidato"===o&&e.jsx("div",{className:"w-2 h-2 bg-white rounded-full"})}),e.jsxs("div",{className:"flex items-center",children:[e.jsx("div",{className:"w-8 h-8 bg-orange-500 rounded-full flex items-center justify-center mr-3",children:e.jsx(s,{className:"text-white text-sm"})}),e.jsxs("div",{children:[e.jsx("div",{className:"font-medium text-gray-800",children:"Candidato"}),e.jsx("div",{className:"text-sm text-gray-500",children:"Acceso para realizar evaluaciones psicométricas"})]})]})]}),e.jsxs("label",{className:"flex items-center p-3 border border-gray-200 rounded-lg cursor-pointer hover:bg-gray-50 transition-colors",children:[e.jsx("input",{type:"radio",name:"userType",value:"psicologo",checked:"psicologo"===o,onChange:e=>m(e.target.value),className:"sr-only"}),e.jsx("div",{className:"w-5 h-5 rounded-full border-2 mr-3 flex items-center justify-center "+("psicologo"===o?"border-gray-500 bg-gray-500":"border-gray-300"),children:"psicologo"===o&&e.jsx("div",{className:"w-2 h-2 bg-white rounded-full"})}),e.jsxs("div",{className:"flex items-center",children:[e.jsx("div",{className:"w-8 h-8 bg-gray-500 rounded-full flex items-center justify-center mr-3",children:e.jsx(s,{className:"text-white text-sm"})}),e.jsxs("div",{children:[e.jsx("div",{className:"font-medium text-gray-800",children:"Psicólogo"}),e.jsx("div",{className:"text-sm text-gray-500",children:"Acceso para gestionar candidatos y resultados"})]})]})]}),e.jsxs("label",{className:"flex items-center p-3 border border-gray-200 rounded-lg cursor-pointer hover:bg-gray-50 transition-colors",children:[e.jsx("input",{type:"radio",name:"userType",value:"administrador",checked:"administrador"===o,onChange:e=>m(e.target.value),className:"sr-only"}),e.jsx("div",{className:"w-5 h-5 rounded-full border-2 mr-3 flex items-center justify-center "+("administrador"===o?"border-gray-500 bg-gray-500":"border-gray-300"),children:"administrador"===o&&e.jsx("div",{className:"w-2 h-2 bg-white rounded-full"})}),e.jsxs("div",{className:"flex items-center",children:[e.jsx("div",{className:"w-8 h-8 bg-gray-500 rounded-full flex items-center justify-center mr-3",children:e.jsx(s,{className:"text-white text-sm"})}),e.jsxs("div",{children:[e.jsx("div",{className:"font-medium text-gray-800",children:"Administrador"}),e.jsx("div",{className:"text-sm text-gray-500",children:"Acceso completo al sistema"})]})]})]})]})]}),e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"EMAIL O DOCUMENTO"}),e.jsxs("div",{className:"relative",children:[e.jsx(s,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400"}),e.jsx("input",{type:"text",value:i,onChange:e=>n(e.target.value),className:"w-full pl-10 pr-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-orange-500 focus:border-transparent",placeholder:"Número de documento",required:!0})]})]}),e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"CONTRASEÑA"}),e.jsxs("div",{className:"relative",children:[e.jsx(a,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400"}),e.jsx("input",{type:"password",value:d,onChange:e=>c(e.target.value),className:"w-full pl-10 pr-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-orange-500 focus:border-transparent",placeholder:"Ingresa tu contraseña",required:!0}),e.jsx("button",{type:"button",className:"absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-gray-600",children:e.jsx("i",{className:"fas fa-eye"})})]})]}),e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsxs("label",{className:"flex items-center",children:[e.jsx("input",{type:"checkbox",className:"rounded border-gray-300 text-orange-500 focus:ring-orange-500"}),e.jsx("span",{className:"ml-2 text-sm text-gray-600",children:"Recordarme"})]}),e.jsx("a",{href:"#",className:"text-sm text-orange-500 hover:text-orange-600",children:"¿Olvidaste tu contraseña?"})]}),e.jsx("button",{type:"submit",disabled:x,className:"w-full bg-orange-500 hover:bg-orange-600 text-white font-medium py-3 px-4 rounded-lg transition-colors duration-200 flex items-center justify-center",children:x?e.jsxs(e.Fragment,{children:[e.jsx(r,{className:"animate-spin mr-2"}),"Iniciando sesión..."]}):e.jsxs(e.Fragment,{children:[e.jsx(s,{className:"mr-2"}),"Iniciar Sesión"]})})]}),e.jsxs("div",{className:"mt-6 pt-6 border-t border-gray-200",children:[e.jsx("p",{className:"text-xs text-gray-500 text-center mb-3",children:"Acceso rápido para testing:"}),e.jsxs("div",{className:"grid grid-cols-3 gap-2",children:[e.jsx("button",{onClick:()=>g("administrador"),disabled:x,className:"px-2 py-1 text-xs bg-gray-100 text-gray-700 rounded hover:bg-gray-200 transition-colors",children:"Admin"}),e.jsx("button",{onClick:()=>g("psicologo"),disabled:x,className:"px-2 py-1 text-xs bg-gray-100 text-gray-700 rounded hover:bg-gray-200 transition-colors",children:"Psicólogo"}),e.jsx("button",{onClick:()=>g("candidato"),disabled:x,className:"px-2 py-1 text-xs bg-orange-100 text-orange-700 rounded hover:bg-orange-200 transition-colors",children:"Candidato"})]})]})]})]})})]})};export{i as default};
