var e=(e,a,s)=>new Promise((l,i)=>{var t=e=>{try{n(s.next(e))}catch(a){i(a)}},r=e=>{try{n(s.throw(e))}catch(a){i(a)}},n=e=>e.done?l(e.value):Promise.resolve(e.value).then(t,r);n((s=s.apply(e,a)).next())});import{j as a,s}from"./auth-3ab59eff.js";import{b as l,a as i,r as t}from"./react-vendor-99be060c.js";import{C as r,a as n,B as d,b as c}from"./admin-168d579d.js";import{Q as o}from"./ui-vendor-9705a4a1.js";import{B as m}from"./index-23a57a03.js";import{o as x}from"./interpretacionesAptitudes-bd504cf8.js";import"./utils-vendor-4d1206d7.js";const p=()=>{const{patientId:p}=l(),u=i(),[h,g]=t.useState(null),[b,j]=t.useState([]),[f,v]=t.useState(!0),[N,y]=t.useState(!1);t.useEffect(()=>{p&&e(void 0,null,function*(){try{v(!0);const{data:e,error:a}=yield s.from("pacientes").select("*").eq("id",p).single();if(a)throw a;const{data:l,error:i}=yield s.from("resultados").select("\n            id,\n            puntaje_directo,\n            percentil,\n            errores,\n            tiempo_segundos,\n            concentracion,\n            created_at,\n            aptitudes:aptitud_id (\n              codigo,\n              nombre,\n              descripcion\n            )\n          ").eq("paciente_id",p).order("created_at",{ascending:!1});if(i)throw i;g(e),j(l||[])}catch(e){o.error("Error al cargar los datos del paciente"),u("/admin/reports")}finally{v(!1)}})},[p,u]);const w=e=>{if(!e)return"N/A";const a=new Date,s=new Date(e);let l=a.getFullYear()-s.getFullYear();const i=a.getMonth()-s.getMonth();return(i<0||0===i&&a.getDate()<s.getDate())&&l--,l};if(f)return a.jsx("div",{className:"container mx-auto py-6",children:a.jsxs("div",{className:"py-16 text-center",children:[a.jsx("div",{className:"animate-spin rounded-full h-12 w-12 border-b-2 border-blue-500 mx-auto mb-4"}),a.jsx("p",{className:"text-gray-500",children:"Cargando informe completo..."})]})});if(!h)return a.jsx("div",{className:"container mx-auto py-6",children:a.jsx(r,{children:a.jsx(n,{children:a.jsxs("div",{className:"py-8 text-center",children:[a.jsx("i",{className:"fas fa-exclamation-triangle text-4xl text-yellow-500 mb-4"}),a.jsx("p",{className:"text-gray-500",children:"No se pudo cargar la información del paciente."}),a.jsx(d,{onClick:()=>u("/admin/reports"),className:"mt-4",children:"Volver a Resultados"})]})})})});const _=b.filter(e=>e.percentil).length>0?Math.round(b.filter(e=>e.percentil).reduce((e,a)=>e+a.percentil,0)/b.filter(e=>e.percentil).length):null,D=b.filter(e=>e.percentil),C=e=>D.find(a=>{var s;return(null==(s=a.aptitudes)?void 0:s.codigo)===e}),A=C("R"),E=C("N"),k=[null==A?void 0:A.percentil,null==E?void 0:E.percentil].filter(e=>void 0!==e),z=k.length>0?Math.round(k.reduce((e,a)=>e+a,0)/k.length):null,P=C("V"),S=C("O"),G=[null==P?void 0:P.percentil,null==S?void 0:S.percentil].filter(e=>void 0!==e),I=G.length>0?Math.round(G.reduce((e,a)=>e+a,0)/G.length):null,$=_,q=(e,a)=>{if(!a)return{nivel:"No evaluado",descripcion:"No hay datos suficientes para evaluar este índice."};let s,l,i;switch(s=a>=75?"Alto":a>=25?"Promedio":"Bajo",e){case"g":"Alto"===s?(l="Capacidad general elevada para comprender situaciones complejas, razonar y resolver problemas de manera efectiva.",i=["Habilidad para resolver eficientemente problemas complejos y novedosos","Buena capacidad para formular y contrastar hipótesis","Facilidad para abstraer información e integrarla con conocimiento previo","Elevado potencial para adquirir nuevos conocimientos"]):"Promedio"===s?(l="Capacidad general dentro del rango esperado para resolver problemas y comprender situaciones.",i=["Capacidad adecuada para resolver problemas de complejidad moderada","Habilidades de razonamiento en desarrollo","Potencial de aprendizaje dentro del rango promedio"]):(l="Dificultades en la capacidad general para resolver problemas complejos y comprender relaciones abstractas.",i=["Dificultades para aplicar el razonamiento a problemas complejos","Limitaciones para formar juicios que requieran abstracción","Posible necesidad de enseñanza más directiva y supervisada"]);break;case"Gf":"Alto"===s?(l="Excelente capacidad para el razonamiento inductivo y deductivo con problemas novedosos.",i=["Habilidad sobresaliente para aplicar razonamiento a problemas novedosos","Facilidad para identificar reglas y formular hipótesis","Nivel alto de razonamiento analítico","Buena integración de información visual y verbal"]):"Promedio"===s?(l="Capacidad adecuada para el razonamiento con contenidos abstractos y formales.",i=["Habilidades de razonamiento en desarrollo","Capacidad moderada para resolver problemas novedosos","Estrategias de resolución en proceso de consolidación"]):(l="Dificultades en el razonamiento inductivo y deductivo con problemas abstractos.",i=["Uso de estrategias poco eficaces para problemas novedosos","Falta de flexibilidad en soluciones alternativas","Dificultades para identificar reglas subyacentes","Integración defectuosa de información visual y verbal"]);break;case"Gc":"Alto"===s?(l="Excelente dominio de conocimientos adquiridos culturalmente y habilidades verbales.",i=["Habilidad para captar relaciones entre conceptos verbales","Buena capacidad de comprensión y expresión del lenguaje","Buen nivel de conocimiento léxico y ortográfico","Posiblemente buen nivel de cultura general"]):"Promedio"===s?(l="Conocimientos verbales y culturales dentro del rango esperado.",i=["Comprensión verbal adecuada para la edad","Conocimientos léxicos en desarrollo","Habilidades de expresión en proceso de consolidación"]):(l="Limitaciones en conocimientos verbales y habilidades de lenguaje adquiridas culturalmente.",i=["Procesamiento parcial de relaciones entre conceptos verbales","Dificultades en comprensión y expresión del lenguaje","Limitaciones en conocimiento léxico y ortográfico","Posible nivel bajo de cultura general"]);break;default:l="Interpretación no disponible para este índice.",i=[]}return{nivel:s,descripcion:l,caracteristicas:i}};return a.jsxs("div",{className:"container mx-auto py-6 max-w-6xl",children:[a.jsxs("div",{className:"mb-6 text-center",children:[a.jsxs("div",{className:"flex items-center justify-center mb-4",children:[a.jsx("div",{className:`w-16 h-16 bg-${"masculino"===(null==h?void 0:h.genero)?"blue":"pink"}-100 rounded-full flex items-center justify-center mr-4`,children:a.jsx("i",{className:`fas ${"masculino"===(null==h?void 0:h.genero)?"fa-mars text-blue-600":"fa-venus text-pink-600"} text-2xl`})}),a.jsxs("div",{children:[a.jsx("h1",{className:"text-3xl font-bold text-blue-800",children:"Informe General BAT-7"}),a.jsxs("p",{className:"text-gray-600",children:[null==h?void 0:h.nombre," ",null==h?void 0:h.apellido]})]})]}),a.jsxs("div",{className:"flex justify-center space-x-4 print-hide",children:[a.jsxs(d,{onClick:()=>u("/admin/reports"),variant:"outline",children:[a.jsx("i",{className:"fas fa-arrow-left mr-2"}),"Volver"]}),a.jsxs(d,{onClick:()=>e(void 0,null,function*(){var e;try{y(!0);const a={resultado_id:null==(e=b[0])?void 0:e.id,paciente_id:h.id,titulo:`Informe Completo Admin - ${null==h?void 0:h.nombre} ${null==h?void 0:h.apellido}`,contenido:{paciente:h,resultados:b.map(e=>{var a,s,l;return{id:e.id,test:{codigo:null==(a=e.aptitudes)?void 0:a.codigo,nombre:null==(s=e.aptitudes)?void 0:s.nombre,descripcion:null==(l=e.aptitudes)?void 0:l.descripcion},puntajes:{puntaje_directo:e.puntaje_directo,percentil:e.percentil,errores:e.errores,tiempo_segundos:e.tiempo_segundos,concentracion:e.concentracion},interpretacion:e.percentil?m.obtenerInterpretacionPC(e.percentil):{nivel:"Pendiente",color:"text-gray-600",bg:"bg-gray-100"},fecha_evaluacion:e.created_at}}),resumen:{total_tests:b.length,promedio_percentil:b.filter(e=>e.percentil).length>0?Math.round(b.filter(e=>e.percentil).reduce((e,a)=>e+a.percentil,0)/b.filter(e=>e.percentil).length):null,fecha_primera_evaluacion:b.length>0?b[b.length-1].created_at:null,fecha_ultima_evaluacion:b.length>0?b[0].created_at:null},fecha_generacion:(new Date).toISOString()},generado_por:"Administrador",tipo_informe:"evaluacion_completa",estado:"generado"},{data:l,error:i}=yield s.from("informes").insert([a]).select().single();if(i)return void o.error("Error al guardar el informe completo");o.success("Informe completo guardado exitosamente")}catch(a){o.error("Error al guardar el informe completo")}finally{y(!1)}}),disabled:N,variant:"primary",children:[a.jsx("i",{className:"fas fa-save mr-2"}),N?"Guardando...":"Guardar Informe"]}),a.jsxs(d,{onClick:()=>{const e=document.querySelectorAll(".print-hide");e.forEach(e=>e.style.display="none");const a=document.querySelector(".container"),s=document.body,l=document.documentElement,i=null==a?void 0:a.className,t=null==s?void 0:s.className,r=null==l?void 0:l.className;a&&(a.className+=" print-optimize"),s&&(s.className+=" print-optimize"),l&&(l.className+=" print-optimize");const n=document.createElement("style");n.textContent="\n      @media print {\n        * {\n          -webkit-print-color-adjust: exact !important;\n          color-adjust: exact !important;\n        }\n        .space-y-6 > * + * {\n          margin-top: 0.5rem !important;\n        }\n        .mb-6 {\n          margin-bottom: 0.5rem !important;\n        }\n        .py-6 {\n          padding-top: 0.5rem !important;\n          padding-bottom: 0.5rem !important;\n        }\n      }\n    ",document.head.appendChild(n);const d=document.title;document.title=`Informe_${null==h?void 0:h.nombre}_${null==h?void 0:h.apellido}_${(new Date).toLocaleDateString("es-ES").replace(/\//g,"-")}`,window.print(),setTimeout(()=>{e.forEach(e=>e.style.display=""),a&&i&&(a.className=i),s&&t&&(s.className=t),l&&r&&(l.className=r),document.head.removeChild(n),document.title=d},1e3)},variant:"secondary",children:[a.jsx("i",{className:"fas fa-file-pdf mr-2"}),"Generar PDF"]}),a.jsxs(d,{onClick:()=>window.print(),variant:"outline",children:[a.jsx("i",{className:"fas fa-print mr-2"}),"Imprimir"]})]})]}),a.jsxs(r,{className:"mb-6 print-keep-together shadow-lg border-l-4 border-blue-500",children:[a.jsx(c,{className:"bg-gradient-to-r from-blue-50 to-green-50 border-b-2 border-blue-200",children:a.jsxs("div",{className:"flex items-center",children:[a.jsx("div",{className:`w-12 h-12 bg-${"masculino"===(null==h?void 0:h.genero)?"blue":"pink"}-100 rounded-full flex items-center justify-center mr-4`,children:a.jsx("i",{className:`fas ${"masculino"===(null==h?void 0:h.genero)?"fa-mars text-blue-600":"fa-venus text-pink-600"} text-xl`})}),a.jsxs("h2",{className:"text-xl font-semibold text-blue-800",children:[a.jsx("i",{className:"fas fa-user mr-2"}),"Información del Paciente"]})]})}),a.jsxs(n,{className:"bg-white",children:[a.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-6 print:hidden",children:[a.jsxs("div",{className:"space-y-4",children:[a.jsxs("div",{className:"bg-blue-50 p-4 rounded-lg border-l-4 border-blue-400",children:[a.jsx("p",{className:"text-xs font-medium text-blue-600 uppercase tracking-wide mb-1",children:"Nombre Completo"}),a.jsxs("p",{className:"text-lg font-bold text-gray-900",children:[null==h?void 0:h.nombre," ",null==h?void 0:h.apellido]})]}),(null==h?void 0:h.documento)&&a.jsxs("div",{className:"bg-gray-50 p-4 rounded-lg border-l-4 border-gray-400",children:[a.jsx("p",{className:"text-xs font-medium text-gray-600 uppercase tracking-wide mb-1",children:"Documento"}),a.jsx("p",{className:"text-base font-semibold text-gray-900",children:h.documento})]})]}),a.jsxs("div",{className:"space-y-4",children:[(null==h?void 0:h.fecha_nacimiento)&&a.jsxs("div",{className:"bg-green-50 p-4 rounded-lg border-l-4 border-green-400",children:[a.jsx("p",{className:"text-xs font-medium text-green-600 uppercase tracking-wide mb-1",children:"Fecha de Nacimiento"}),a.jsx("p",{className:"text-base font-semibold text-gray-900",children:new Date(h.fecha_nacimiento).toLocaleDateString("es-ES")})]}),a.jsxs("div",{className:"bg-purple-50 p-4 rounded-lg border-l-4 border-purple-400",children:[a.jsx("p",{className:"text-xs font-medium text-purple-600 uppercase tracking-wide mb-1",children:"Edad"}),a.jsxs("p",{className:"text-base font-semibold text-gray-900",children:[w(null==h?void 0:h.fecha_nacimiento)," años"]})]})]}),a.jsxs("div",{className:"space-y-4",children:[a.jsxs("div",{className:`bg-${"masculino"===(null==h?void 0:h.genero)?"blue":"pink"}-50 p-4 rounded-lg border-l-4 border-${"masculino"===(null==h?void 0:h.genero)?"blue":"pink"}-400`,children:[a.jsx("p",{className:`text-xs font-medium text-${"masculino"===(null==h?void 0:h.genero)?"blue":"pink"}-600 uppercase tracking-wide mb-1`,children:"Género"}),a.jsxs("p",{className:"text-base font-semibold text-gray-900 capitalize flex items-center",children:[a.jsx("i",{className:`fas ${"masculino"===(null==h?void 0:h.genero)?"fa-mars text-blue-600":"fa-venus text-pink-600"} mr-2`}),null==h?void 0:h.genero]})]}),(null==h?void 0:h.email)&&a.jsxs("div",{className:"bg-orange-50 p-4 rounded-lg border-l-4 border-orange-400",children:[a.jsx("p",{className:"text-xs font-medium text-orange-600 uppercase tracking-wide mb-1",children:"Email"}),a.jsx("p",{className:"text-sm font-semibold text-gray-900 break-all",children:h.email})]})]})]}),a.jsx("div",{className:"hidden print:block",children:a.jsxs("div",{className:"grid grid-cols-2 gap-4 text-sm",children:[a.jsxs("div",{children:[a.jsxs("div",{className:"mb-2",children:[a.jsx("span",{className:"font-medium text-blue-600",children:"Nombre:"}),a.jsxs("span",{className:"ml-2 font-bold",children:[null==h?void 0:h.nombre," ",null==h?void 0:h.apellido]})]}),(null==h?void 0:h.documento)&&a.jsxs("div",{className:"mb-2",children:[a.jsx("span",{className:"font-medium text-gray-600",children:"Documento:"}),a.jsx("span",{className:"ml-2",children:h.documento})]}),(null==h?void 0:h.email)&&a.jsxs("div",{className:"mb-2",children:[a.jsx("span",{className:"font-medium text-orange-600",children:"Email:"}),a.jsx("span",{className:"ml-2 text-xs",children:h.email})]})]}),a.jsxs("div",{children:[(null==h?void 0:h.fecha_nacimiento)&&a.jsxs("div",{className:"mb-2",children:[a.jsx("span",{className:"font-medium text-green-600",children:"Fecha de Nacimiento:"}),a.jsx("span",{className:"ml-2",children:new Date(h.fecha_nacimiento).toLocaleDateString("es-ES")})]}),a.jsxs("div",{className:"mb-2",children:[a.jsx("span",{className:"font-medium text-purple-600",children:"Edad:"}),a.jsxs("span",{className:"ml-2",children:[w(null==h?void 0:h.fecha_nacimiento)," años"]})]}),a.jsxs("div",{className:"mb-2",children:[a.jsx("span",{className:`font-medium text-${"masculino"===(null==h?void 0:h.genero)?"blue":"pink"}-600`,children:"Género:"}),a.jsxs("span",{className:"ml-2 capitalize",children:[a.jsx("i",{className:`fas ${"masculino"===(null==h?void 0:h.genero)?"fa-mars text-blue-600":"fa-venus text-pink-600"} mr-1`}),null==h?void 0:h.genero]})]})]})]})})]})]}),a.jsxs(r,{className:"mb-6",children:[a.jsx(c,{className:"bg-green-50 border-b",children:a.jsxs("h2",{className:"text-xl font-semibold text-blue-800",children:[a.jsx("i",{className:"fas fa-chart-pie mr-2"}),"Resumen General"]})}),a.jsxs(n,{className:"print-compact",children:[a.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-4 print-grid-horizontal gap-6 mb-6",children:[a.jsxs("div",{className:"text-center p-4 bg-blue-50 rounded-lg",children:[a.jsx("div",{className:"text-3xl font-bold text-blue-600 mb-2",children:b.length}),a.jsx("div",{className:"text-sm font-medium text-blue-700",children:"Tests Completados"})]}),a.jsxs("div",{className:"text-center p-4 bg-green-50 rounded-lg",children:[a.jsx("div",{className:"text-3xl font-bold text-green-600 mb-2",children:_||"N/A"}),a.jsx("div",{className:"text-sm font-medium text-green-700",children:"Percentil Promedio"})]}),a.jsxs("div",{className:"text-center p-4 bg-purple-50 rounded-lg",children:[a.jsx("div",{className:"text-3xl font-bold text-purple-600 mb-2",children:b.filter(e=>e.percentil&&e.percentil>=75).length}),a.jsx("div",{className:"text-sm font-medium text-purple-700",children:"Aptitudes Altas (≥75)"})]}),a.jsxs("div",{className:"text-center p-4 bg-orange-50 rounded-lg",children:[a.jsx("div",{className:"text-3xl font-bold text-orange-600 mb-2",children:b.filter(e=>e.percentil&&e.percentil<=25).length}),a.jsx("div",{className:"text-sm font-medium text-orange-700",children:"Aptitudes a Reforzar (≤25)"})]})]}),a.jsxs("div",{className:"border-t pt-6",children:[a.jsx("h3",{className:"text-lg font-semibold text-gray-800 mb-4 text-center",children:"Índices Especializados BAT-7"}),a.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-4 print-grid-horizontal gap-6",children:[a.jsxs("div",{className:"text-center p-4 bg-indigo-50 rounded-lg border-2 border-indigo-200",children:[a.jsx("div",{className:"text-2xl font-bold text-indigo-600 mb-2",children:_||"N/A"}),a.jsx("div",{className:"text-sm font-medium text-indigo-700",children:"Total BAT"}),a.jsx("div",{className:"text-xs text-indigo-600 mt-1",children:"Capacidad General"})]}),a.jsxs("div",{className:"text-center p-4 bg-cyan-50 rounded-lg border-2 border-cyan-200",children:[a.jsx("div",{className:"text-2xl font-bold text-cyan-600 mb-2",children:$||"N/A"}),a.jsx("div",{className:"text-sm font-medium text-cyan-700",children:"Índice g"}),a.jsx("div",{className:"text-xs text-cyan-600 mt-1",children:"Capacidad General"})]}),a.jsxs("div",{className:"text-center p-4 bg-teal-50 rounded-lg border-2 border-teal-200",children:[a.jsx("div",{className:"text-2xl font-bold text-teal-600 mb-2",children:z||"N/A"}),a.jsx("div",{className:"text-sm font-medium text-teal-700",children:"Índice Gf"}),a.jsx("div",{className:"text-xs text-teal-600 mt-1",children:"Inteligencia Fluida"})]}),a.jsxs("div",{className:"text-center p-4 bg-emerald-50 rounded-lg border-2 border-emerald-200",children:[a.jsx("div",{className:"text-2xl font-bold text-emerald-600 mb-2",children:I||"N/A"}),a.jsx("div",{className:"text-sm font-medium text-emerald-700",children:"Índice Gc"}),a.jsx("div",{className:"text-xs text-emerald-600 mt-1",children:"Inteligencia Cristalizada"})]})]})]}),b.length>0&&a.jsxs("div",{className:"mt-6 grid grid-cols-1 md:grid-cols-2 gap-4 text-sm",children:[a.jsxs("div",{children:[a.jsx("span",{className:"text-gray-500",children:"Primera evaluación:"}),a.jsx("span",{className:"ml-2 font-medium",children:new Date(b[b.length-1].created_at).toLocaleDateString("es-ES")})]}),a.jsxs("div",{children:[a.jsx("span",{className:"text-gray-500",children:"Última evaluación:"}),a.jsx("span",{className:"ml-2 font-medium",children:new Date(b[0].created_at).toLocaleDateString("es-ES")})]})]})]})]}),a.jsxs(r,{className:"mb-6 print-keep-together",children:[a.jsx(c,{className:"bg-gray-50 border-b",children:a.jsxs("h2",{className:"text-xl font-semibold text-blue-800",children:[a.jsx("i",{className:"fas fa-list-alt mr-2"}),"Resultados Detallados por Aptitud"]})}),a.jsx(n,{className:"p-0",children:0===b.length?a.jsxs("div",{className:"py-8 text-center",children:[a.jsx("i",{className:"fas fa-clipboard-list text-4xl text-gray-300 mb-4"}),a.jsx("p",{className:"text-gray-500",children:"No hay resultados de tests disponibles para este paciente."})]}):a.jsx("div",{className:"overflow-x-auto",children:a.jsxs("table",{className:"w-full",children:[a.jsx("thead",{children:a.jsxs("tr",{className:"bg-slate-800 text-white",children:[a.jsx("th",{className:"px-4 py-3 text-left font-semibold",children:"S"}),a.jsx("th",{className:"px-4 py-3 text-left font-semibold",children:"APTITUDES EVALUADAS"}),a.jsx("th",{className:"px-4 py-3 text-center font-semibold",children:"PD"}),a.jsx("th",{className:"px-4 py-3 text-center font-semibold",children:"PC"}),a.jsx("th",{className:"px-4 py-3 text-left font-semibold",children:"PERFIL DE LAS APTITUDES"})]})}),a.jsx("tbody",{children:b.map((e,s)=>{var l,i,t;null==(l=e.aptitudes)||l.codigo;const r=e.percentil||0;let n="bg-blue-500";return n=r>=80?"bg-orange-500":r>=60?"bg-blue-500":r>=40?"bg-blue-400":"bg-blue-300",a.jsxs("tr",{className:s%2==0?"bg-white":"bg-gray-50",children:[a.jsx("td",{className:"px-4 py-3",children:a.jsx("div",{className:"w-8 h-8 bg-teal-600 rounded-full flex items-center justify-center text-white font-bold text-sm",children:null==(i=e.aptitudes)?void 0:i.codigo})}),a.jsx("td",{className:"px-4 py-3",children:a.jsx("div",{className:"font-medium text-gray-900",children:null==(t=e.aptitudes)?void 0:t.nombre})}),a.jsx("td",{className:"px-4 py-3 text-center font-bold text-gray-900",children:e.puntaje_directo}),a.jsx("td",{className:"px-4 py-3 text-center font-bold text-gray-900",children:e.percentil||"N/A"}),a.jsx("td",{className:"px-4 py-3",children:a.jsx("div",{className:"flex items-center",children:a.jsx("div",{className:"flex-1 bg-gray-200 rounded-full h-6 mr-3",children:a.jsx("div",{className:`${n} h-6 rounded-full flex items-center justify-end pr-2`,style:{width:`${Math.max(r,5)}%`},children:a.jsx("span",{className:"text-white text-xs font-bold",children:r>0?r:""})})})})})]},e.id)})})]})})})]}),b.length>0&&a.jsxs(r,{className:"mb-6 print-keep-together",children:[a.jsx(c,{className:"bg-purple-50 border-b",children:a.jsxs("h2",{className:"text-xl font-semibold text-blue-800",children:[a.jsx("i",{className:"fas fa-brain mr-2"}),"Interpretación Cualitativa de Aptitudes"]})}),a.jsx(n,{children:a.jsx("div",{className:"space-y-6",children:b.map((e,s)=>{var l,i,t;const r=x(null==(l=e.aptitudes)?void 0:l.codigo,e.percentil||0);return r?a.jsxs("div",{className:"border-l-4 border-blue-500 pl-6 py-4 bg-gray-50 rounded-r-lg",children:[a.jsxs("div",{className:"flex items-center mb-3",children:[a.jsx("div",{className:"w-10 h-10 bg-teal-600 rounded-full flex items-center justify-center text-white font-bold mr-3",children:null==(i=e.aptitudes)?void 0:i.codigo}),a.jsxs("div",{children:[a.jsxs("h3",{className:"text-lg font-semibold text-gray-900",children:[null==(t=e.aptitudes)?void 0:t.nombre," - Nivel ",r.nivel]}),a.jsxs("p",{className:"text-sm text-gray-600",children:["Percentil: ",e.percentil||"N/A"]})]})]}),a.jsxs("div",{className:"mb-4",children:[a.jsx("h4",{className:"font-medium text-gray-800 mb-2",children:"Descripción:"}),a.jsx("p",{className:"text-gray-700 text-sm leading-relaxed",children:r.descripcion})]}),a.jsxs("div",{children:[a.jsxs("h4",{className:"font-medium text-gray-800 mb-2",children:["Características ","Alto"===r.nivel?"Fortalezas":"Áreas de Mejora",":"]}),a.jsx("ul",{className:"list-disc list-inside space-y-1 text-sm text-gray-700",children:r.caracteristicas.map((e,s)=>a.jsx("li",{className:"leading-relaxed",children:e},s))})]})]},e.id):null})})})]}),a.jsxs(r,{className:"mb-6 print-keep-together",children:[a.jsx(c,{className:"bg-indigo-50 border-b",children:a.jsxs("h2",{className:"text-xl font-semibold text-blue-800",children:[a.jsx("i",{className:"fas fa-chart-line mr-2"}),"Interpretación Cualitativa de Índices Especializados"]})}),a.jsx(n,{children:a.jsxs("div",{className:"space-y-6",children:[_&&a.jsxs("div",{className:"border-l-4 border-indigo-500 pl-6 py-4 bg-indigo-50 rounded-r-lg",children:[a.jsxs("div",{className:"flex items-center mb-3",children:[a.jsx("div",{className:"w-12 h-12 bg-indigo-600 rounded-full flex items-center justify-center text-white font-bold mr-4",children:"g"}),a.jsxs("div",{children:[a.jsxs("h3",{className:"text-lg font-semibold text-gray-900",children:["Índice g - Capacidad General: ",q("g",$).nivel]}),a.jsxs("p",{className:"text-sm text-gray-600",children:["Percentil: ",$]})]})]}),a.jsxs("div",{className:"mb-4",children:[a.jsx("h4",{className:"font-medium text-gray-800 mb-2",children:"Descripción:"}),a.jsx("p",{className:"text-gray-700 text-sm leading-relaxed",children:q("g",$).descripcion})]}),a.jsxs("div",{children:[a.jsx("h4",{className:"font-medium text-gray-800 mb-2",children:"Características:"}),a.jsx("ul",{className:"list-disc list-inside space-y-1 text-sm text-gray-700",children:q("g",$).caracteristicas.map((e,s)=>a.jsx("li",{className:"leading-relaxed",children:e},s))})]})]}),z&&a.jsxs("div",{className:"border-l-4 border-teal-500 pl-6 py-4 bg-teal-50 rounded-r-lg",children:[a.jsxs("div",{className:"flex items-center mb-3",children:[a.jsx("div",{className:"w-12 h-12 bg-teal-600 rounded-full flex items-center justify-center text-white font-bold mr-4",children:"Gf"}),a.jsxs("div",{children:[a.jsxs("h3",{className:"text-lg font-semibold text-gray-900",children:["Índice Gf - Inteligencia Fluida: ",q("Gf",z).nivel]}),a.jsxs("p",{className:"text-sm text-gray-600",children:["Percentil: ",z," (basado en R + N)"]})]})]}),a.jsxs("div",{className:"mb-4",children:[a.jsx("h4",{className:"font-medium text-gray-800 mb-2",children:"Descripción:"}),a.jsx("p",{className:"text-gray-700 text-sm leading-relaxed",children:q("Gf",z).descripcion})]}),a.jsxs("div",{children:[a.jsx("h4",{className:"font-medium text-gray-800 mb-2",children:"Características:"}),a.jsx("ul",{className:"list-disc list-inside space-y-1 text-sm text-gray-700",children:q("Gf",z).caracteristicas.map((e,s)=>a.jsx("li",{className:"leading-relaxed",children:e},s))})]})]}),I&&a.jsxs("div",{className:"border-l-4 border-emerald-500 pl-6 py-4 bg-emerald-50 rounded-r-lg",children:[a.jsxs("div",{className:"flex items-center mb-3",children:[a.jsx("div",{className:"w-12 h-12 bg-emerald-600 rounded-full flex items-center justify-center text-white font-bold mr-4",children:"Gc"}),a.jsxs("div",{children:[a.jsxs("h3",{className:"text-lg font-semibold text-gray-900",children:["Índice Gc - Inteligencia Cristalizada: ",q("Gc",I).nivel]}),a.jsxs("p",{className:"text-sm text-gray-600",children:["Percentil: ",I," (basado en V + O)"]})]})]}),a.jsxs("div",{className:"mb-4",children:[a.jsx("h4",{className:"font-medium text-gray-800 mb-2",children:"Descripción:"}),a.jsx("p",{className:"text-gray-700 text-sm leading-relaxed",children:q("Gc",I).descripcion})]}),a.jsxs("div",{children:[a.jsx("h4",{className:"font-medium text-gray-800 mb-2",children:"Características:"}),a.jsx("ul",{className:"list-disc list-inside space-y-1 text-sm text-gray-700",children:q("Gc",I).caracteristicas.map((e,s)=>a.jsx("li",{className:"leading-relaxed",children:e},s))})]})]}),z&&I&&Math.abs(z-I)>15&&a.jsxs("div",{className:"border-l-4 border-yellow-500 pl-6 py-4 bg-yellow-50 rounded-r-lg",children:[a.jsxs("div",{className:"flex items-center mb-3",children:[a.jsx("div",{className:"w-12 h-12 bg-yellow-600 rounded-full flex items-center justify-center text-white font-bold mr-4",children:a.jsx("i",{className:"fas fa-balance-scale"})}),a.jsx("div",{children:a.jsx("h3",{className:"text-lg font-semibold text-gray-900",children:"Análisis de Disparidad entre Índices"})})]}),a.jsx("div",{children:a.jsxs("p",{className:"text-gray-700 text-sm leading-relaxed",children:["Se observa una diferencia significativa entre la Inteligencia Fluida (Gf: ",z,") y la Inteligencia Cristalizada (Gc: ",I,"). Esta disparidad sugiere un perfil cognitivo heterogéneo que requiere consideración especial en las recomendaciones de intervención.",z>I?" El evaluado muestra mayor fortaleza en razonamiento abstracto que en conocimientos adquiridos.":" El evaluado muestra mayor fortaleza en conocimientos adquiridos que en razonamiento abstracto."]})})]})]})})]}),a.jsxs("div",{className:"text-center text-sm text-gray-500 border-t pt-4",children:[a.jsxs("p",{children:["Informe completo generado el ",(new Date).toLocaleDateString("es-ES")," a las ",(new Date).toLocaleTimeString("es-ES")]}),a.jsx("p",{className:"mt-1",children:"Sistema de Evaluación Psicológica - BAT-7 - Panel de Administración"})]})]})};export{p as default};
