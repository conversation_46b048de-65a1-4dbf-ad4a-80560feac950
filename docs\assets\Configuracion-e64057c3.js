var e=Object.defineProperty,s=Object.defineProperties,a=Object.getOwnPropertyDescriptors,t=Object.getOwnPropertySymbols,r=Object.prototype.hasOwnProperty,i=Object.prototype.propertyIsEnumerable,l=(s,a,t)=>a in s?e(s,a,{enumerable:!0,configurable:!0,writable:!0,value:t}):s[a]=t,n=(e,s)=>{for(var a in s||(s={}))r.call(s,a)&&l(e,a,s[a]);if(t)for(var a of t(s))i.call(s,a)&&l(e,a,s[a]);return e},o=(e,t)=>s(e,a(t)),d=(e,s,a)=>new Promise((t,r)=>{var i=e=>{try{n(a.next(e))}catch(s){r(s)}},l=e=>{try{n(a.throw(e))}catch(s){r(s)}},n=e=>e.done?t(e.value):Promise.resolve(e.value).then(i,l);n((a=a.apply(e,s)).next())});import{i as c,E as m,T as x,U as u,j as p,I as h,V as g,W as b,t as f,c as j,d as v,L as y,X as N,h as w,H as C,Y as S,Z as k,a as A,_,$ as D,a0 as L,a1 as U,l as P,C as z,a2 as E,a3 as T,q as O,p as M,a4 as I,B as R,D as $,g as B}from"./auth-3ab59eff.js";import{r as G}from"./react-vendor-99be060c.js";import{P as q}from"./admin-168d579d.js";import{Q as F}from"./ui-vendor-9705a4a1.js";import"./utils-vendor-4d1206d7.js";const H=({title:e,value:s,icon:a,color:t,percentage:r,trend:i,subtitle:l})=>p.jsxs("div",{className:"bg-white rounded-lg shadow-sm p-6 border border-gray-200 hover:shadow-md transition-shadow",children:[p.jsxs("div",{className:"flex items-center justify-between",children:[p.jsxs("div",{className:"flex-1",children:[p.jsx("p",{className:"text-sm font-medium text-gray-600 mb-1",children:e}),p.jsx("p",{className:"text-3xl font-bold text-gray-900",children:s}),l&&p.jsx("p",{className:"text-xs text-gray-500 mt-1",children:l})]}),p.jsx("div",{className:"flex-shrink-0",children:p.jsx("div",{className:"w-12 h-12 rounded-full flex items-center justify-center text-white",style:{backgroundColor:t},children:p.jsx(a,{className:"w-6 h-6"})})})]}),r&&p.jsxs("div",{className:"mt-4 flex items-center",children:[p.jsxs("span",{className:"text-sm font-medium px-2 py-1 rounded "+("up"===i?"text-green-700 bg-green-100":"text-red-700 bg-red-100"),children:["up"===i?"+":"",r,"%"]}),p.jsx("span",{className:"text-sm text-gray-500 ml-2",children:l||"vs sem. anterior"})]})]}),J=()=>{const[e,s]=G.useState({totalUsers:20,todayActions:6,newUsers:0,completedTests:30}),[a,t]=G.useState(!1),r=[{title:"Usuarios Totales",value:e.totalUsers,icon:c,color:"#3B82F6",percentage:12,trend:"up",subtitle:"vs ayer"},{title:"Acciones Hoy",value:e.todayActions,icon:m,color:"#10B981",percentage:12,trend:"up",subtitle:"vs ayer"},{title:"Nuevos (7 días)",value:e.newUsers,icon:x,color:"#3B82F6",percentage:-33,trend:"down",subtitle:"vs sem. anterior"},{title:"Tests Completados",value:e.completedTests,icon:u,color:"#F59E0B",percentage:5,trend:"up",subtitle:"este mes"}],i=[{id:1,type:"Nuevo usuario registrado",user:"<EMAIL>",time:"Hace 15m",icon:x,color:"#10B981"},{id:2,type:"Test MACI-II completado",user:"<EMAIL>",time:"Hace 1h",icon:u,color:"#F59E0B"},{id:3,type:"Rol de usuario actualizado",user:"<EMAIL>",time:"Hace 2h",icon:h,color:"#6366F1"}];return a?p.jsxs("div",{className:"flex items-center justify-center h-64",children:[p.jsx("div",{className:"animate-spin rounded-full h-12 w-12 border-b-2 border-blue-500"}),p.jsx("span",{className:"ml-3 text-gray-600",children:"Cargando..."})]}):p.jsxs("div",{className:"space-y-6",children:[p.jsxs("div",{className:"text-center",children:[p.jsx("h2",{className:"text-2xl font-bold text-gray-900 mb-2",children:"Dashboard de Administración"}),p.jsx("p",{className:"text-gray-600",children:"Resumen general del sistema BAT-7"})]}),p.jsx("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6",children:r.map((e,s)=>p.jsx(H,{title:e.title,value:e.value,icon:e.icon,color:e.color,percentage:e.percentage,trend:e.trend,subtitle:e.subtitle},s))}),p.jsxs("div",{className:"grid grid-cols-1 lg:grid-cols-2 gap-6",children:[p.jsxs("div",{className:"bg-white rounded-lg shadow-sm p-6 border border-gray-200",children:[p.jsx("h3",{className:"text-lg font-semibold text-gray-900 mb-4",children:"Actividad de Usuarios (Últimos 7 días)"}),p.jsx("div",{className:"h-48 flex items-end justify-between space-x-2",children:[12,8,15,10,6,14,9].map((e,s)=>p.jsxs("div",{className:"flex-1 flex flex-col items-center",children:[p.jsx("div",{className:"w-full bg-gradient-to-t from-blue-500 to-blue-300 rounded-t-lg",style:{height:e/15*100+"%",minHeight:"20px"}}),p.jsx("span",{className:"text-xs text-gray-500 mt-2",children:["Lun","Mar","Mié","Jue","Vie","Sáb","Dom"][s]})]},s))})]}),p.jsxs("div",{className:"bg-white rounded-lg shadow-sm p-6 border border-gray-200",children:[p.jsx("h3",{className:"text-lg font-semibold text-gray-900 mb-4",children:"Actividad Reciente"}),p.jsx("div",{className:"space-y-4",children:i.map(e=>{const s=e.icon;return p.jsxs("div",{className:"flex items-center space-x-3",children:[p.jsx("div",{className:"w-8 h-8 rounded-full flex items-center justify-center text-white flex-shrink-0",style:{backgroundColor:e.color},children:p.jsx(s,{className:"w-4 h-4"})}),p.jsxs("div",{className:"flex-1 min-w-0",children:[p.jsx("p",{className:"text-sm font-medium text-gray-900",children:e.type}),p.jsx("p",{className:"text-sm text-gray-500 truncate",children:e.user})]}),p.jsx("div",{className:"text-sm text-gray-500",children:e.time})]},e.id)})})]})]})]})},V=()=>{const[e,s]=G.useState([{id:1,nombre:"Juan",apellido:"Pérez",email:"<EMAIL>",documento:"12345678",tipo_usuario:"candidato",activo:!0,fecha_creacion:"2025-07-15"},{id:2,nombre:"María",apellido:"García",email:"<EMAIL>",documento:"87654321",tipo_usuario:"psicologo",activo:!0,fecha_creacion:"2025-07-14"},{id:3,nombre:"Admin",apellido:"Sistema",email:"<EMAIL>",documento:"11111111",tipo_usuario:"administrador",activo:!0,fecha_creacion:"2025-07-10"}]),[a,t]=G.useState(""),[r,i]=G.useState("all"),[l,d]=G.useState(!1),[m,u]=G.useState(null),[h,y]=G.useState(!1),N=e.filter(e=>{var s,t,i,l;const n=(null==(s=e.nombre)?void 0:s.toLowerCase().includes(a.toLowerCase()))||(null==(t=e.apellido)?void 0:t.toLowerCase().includes(a.toLowerCase()))||(null==(i=e.email)?void 0:i.toLowerCase().includes(a.toLowerCase()))||(null==(l=e.documento)?void 0:l.includes(a)),o="all"===r||e.tipo_usuario===r;return n&&o}),w={total:e.length,active:e.filter(e=>!0===e.activo).length,inactive:e.filter(e=>!1===e.activo).length},C=e=>{switch(null==e?void 0:e.toLowerCase()){case"administrador":return"bg-red-100 text-red-800";case"psicologo":return"bg-blue-100 text-blue-800";case"candidato":return"bg-green-100 text-green-800";default:return"bg-gray-100 text-gray-800"}};return p.jsxs("div",{className:"space-y-6",children:[p.jsxs("div",{className:"text-center mb-6",children:[p.jsx("h2",{className:"text-2xl font-bold text-gray-900",children:"Gestión de Usuarios"}),p.jsx("p",{className:"text-gray-600 mt-2",children:"Administra usuarios del sistema"})]}),p.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-6 mb-6",children:[p.jsx("div",{className:"bg-white p-6 rounded-lg shadow-sm border border-gray-200",children:p.jsxs("div",{className:"flex items-center",children:[p.jsx("div",{className:"p-3 rounded-full bg-blue-100 mr-4",children:p.jsx(c,{className:"w-6 h-6 text-blue-600"})}),p.jsxs("div",{children:[p.jsx("p",{className:"text-2xl font-bold text-gray-900",children:w.total}),p.jsx("p",{className:"text-gray-600",children:"Total Usuarios"})]})]})}),p.jsx("div",{className:"bg-white p-6 rounded-lg shadow-sm border border-gray-200",children:p.jsxs("div",{className:"flex items-center",children:[p.jsx("div",{className:"p-3 rounded-full bg-green-100 mr-4",children:p.jsx(g,{className:"w-6 h-6 text-green-600"})}),p.jsxs("div",{children:[p.jsx("p",{className:"text-2xl font-bold text-gray-900",children:w.active}),p.jsx("p",{className:"text-gray-600",children:"Usuarios Activos"})]})]})}),p.jsx("div",{className:"bg-white p-6 rounded-lg shadow-sm border border-gray-200",children:p.jsxs("div",{className:"flex items-center",children:[p.jsx("div",{className:"p-3 rounded-full bg-red-100 mr-4",children:p.jsx(b,{className:"w-6 h-6 text-red-600"})}),p.jsxs("div",{children:[p.jsx("p",{className:"text-2xl font-bold text-gray-900",children:w.inactive}),p.jsx("p",{className:"text-gray-600",children:"Usuarios Inactivos"})]})]})})]}),p.jsxs("div",{className:"bg-white p-6 rounded-lg shadow-sm border border-gray-200",children:[p.jsxs("div",{className:"flex flex-col md:flex-row gap-4 mb-6",children:[p.jsx("div",{className:"flex-1",children:p.jsxs("div",{className:"relative",children:[p.jsx(f,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400"}),p.jsx("input",{type:"text",placeholder:"Buscar usuarios...",value:a,onChange:e=>t(e.target.value),className:"w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"})]})}),p.jsxs("select",{value:r,onChange:e=>i(e.target.value),className:"px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent",children:[p.jsx("option",{value:"all",children:"Todos los roles"}),p.jsx("option",{value:"administrador",children:"Administrador"}),p.jsx("option",{value:"psicologo",children:"Psicólogo"}),p.jsx("option",{value:"candidato",children:"Candidato"})]}),p.jsxs("button",{className:"px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 flex items-center space-x-2",onClick:()=>d(!0),children:[p.jsx(x,{}),p.jsx("span",{children:"Nuevo Usuario"})]})]}),p.jsx("div",{className:"overflow-x-auto",children:p.jsxs("table",{className:"min-w-full",children:[p.jsx("thead",{children:p.jsxs("tr",{className:"border-b border-gray-200",children:[p.jsx("th",{className:"text-left py-3 px-4 font-medium text-gray-700",children:"Usuario"}),p.jsx("th",{className:"text-left py-3 px-4 font-medium text-gray-700",children:"Email"}),p.jsx("th",{className:"text-left py-3 px-4 font-medium text-gray-700",children:"Documento"}),p.jsx("th",{className:"text-left py-3 px-4 font-medium text-gray-700",children:"Rol"}),p.jsx("th",{className:"text-left py-3 px-4 font-medium text-gray-700",children:"Estado"}),p.jsx("th",{className:"text-left py-3 px-4 font-medium text-gray-700",children:"Acciones"})]})}),p.jsx("tbody",{children:N.map(a=>{return p.jsxs("tr",{className:"border-b border-gray-100 hover:bg-gray-50",children:[p.jsx("td",{className:"py-3 px-4",children:p.jsxs("div",{children:[p.jsxs("div",{className:"font-medium text-gray-900",children:[a.nombre," ",a.apellido]}),p.jsxs("div",{className:"text-sm text-gray-500",children:["ID: ",a.id]})]})}),p.jsx("td",{className:"py-3 px-4 text-gray-600",children:a.email}),p.jsx("td",{className:"py-3 px-4 text-gray-600",children:a.documento}),p.jsx("td",{className:"py-3 px-4",children:p.jsx("span",{className:`px-2 py-1 text-xs font-medium rounded-full ${C(a.tipo_usuario)}`,children:a.tipo_usuario})}),p.jsx("td",{className:"py-3 px-4",children:p.jsx("span",{className:"px-2 py-1 text-xs font-medium rounded-full "+(t=a.activo,!0===t||"active"===t?"bg-green-100 text-green-800":"bg-red-100 text-red-800"),children:a.activo?"Activo":"Inactivo"})}),p.jsx("td",{className:"py-3 px-4",children:p.jsxs("div",{className:"flex space-x-2",children:[p.jsx("button",{className:"p-2 text-blue-600 hover:bg-blue-100 rounded",onClick:()=>(e=>{u(e),y(!0)})(a),title:"Editar usuario",children:p.jsx(j,{className:"w-4 h-4"})}),p.jsx("button",{className:"p-2 text-red-600 hover:bg-red-100 rounded",onClick:()=>{return t=a.id,void(window.confirm("¿Está seguro de que desea eliminar este usuario?")&&s(e.filter(e=>e.id!==t)));var t},title:"Eliminar usuario",children:p.jsx(v,{className:"w-4 h-4"})})]})})]},a.id);var t})})]})}),0===N.length&&p.jsx("div",{className:"text-center py-8 text-gray-500",children:"No se encontraron usuarios que coincidan con los criterios de búsqueda."})]}),l&&p.jsx(Z,{isOpen:l,onClose:()=>d(!1),onSave:a=>{const t=o(n({id:Math.max(...e.map(e=>e.id))+1},a),{fecha_creacion:(new Date).toISOString().split("T")[0],activo:!0});s([...e,t]),d(!1)},title:"Crear Nuevo Usuario"}),h&&p.jsx(Z,{isOpen:h,onClose:()=>y(!1),onSave:a=>{s(e.map(e=>e.id===m.id?n(n({},e),a):e)),y(!1),u(null)},title:"Editar Usuario",initialData:m})]})},Z=({isOpen:e,onClose:s,onSave:a,title:t,initialData:r=null})=>{const[i,l]=G.useState({nombre:(null==r?void 0:r.nombre)||"",apellido:(null==r?void 0:r.apellido)||"",email:(null==r?void 0:r.email)||"",documento:(null==r?void 0:r.documento)||"",tipo_usuario:(null==r?void 0:r.tipo_usuario)||"candidato"});return e?p.jsx("div",{className:"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50",children:p.jsxs("div",{className:"bg-white rounded-lg p-6 w-full max-w-md",children:[p.jsx("h3",{className:"text-lg font-semibold mb-4",children:t}),p.jsxs("form",{onSubmit:e=>{e.preventDefault(),i.nombre&&i.apellido&&i.email&&i.documento&&(a(i),l({nombre:"",apellido:"",email:"",documento:"",tipo_usuario:"candidato"}))},className:"space-y-4",children:[p.jsxs("div",{children:[p.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Nombre"}),p.jsx("input",{type:"text",value:i.nombre,onChange:e=>l(o(n({},i),{nombre:e.target.value})),className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500",required:!0})]}),p.jsxs("div",{children:[p.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Apellido"}),p.jsx("input",{type:"text",value:i.apellido,onChange:e=>l(o(n({},i),{apellido:e.target.value})),className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500",required:!0})]}),p.jsxs("div",{children:[p.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Email"}),p.jsx("input",{type:"email",value:i.email,onChange:e=>l(o(n({},i),{email:e.target.value})),className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500",required:!0})]}),p.jsxs("div",{children:[p.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Documento"}),p.jsx("input",{type:"text",value:i.documento,onChange:e=>l(o(n({},i),{documento:e.target.value})),className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500",required:!0})]}),p.jsxs("div",{children:[p.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Tipo de Usuario"}),p.jsxs("select",{value:i.tipo_usuario,onChange:e=>l(o(n({},i),{tipo_usuario:e.target.value})),className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500",children:[p.jsx("option",{value:"candidato",children:"Candidato"}),p.jsx("option",{value:"psicologo",children:"Psicólogo"}),p.jsx("option",{value:"administrador",children:"Administrador"})]})]}),p.jsxs("div",{className:"flex justify-end space-x-3 pt-4",children:[p.jsx("button",{type:"button",onClick:s,className:"px-4 py-2 text-gray-600 border border-gray-300 rounded-md hover:bg-gray-50",children:"Cancelar"}),p.jsx("button",{type:"submit",className:"px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700",children:r?"Actualizar":"Crear"})]})]})]})}):null},W=()=>{const[e,s]=G.useState(""),[a,t]=G.useState(!1),[r,i]=G.useState([{id:1,name:"Inicio",path:"/home",description:"Página principal del sistema",status:"Activa",permissions:{administrador:!0,psicologo:!0,candidato:!0},specificUsers:["U1","U2"]},{id:2,name:"Cuestionario",path:"/cuestionario",description:"Evaluaciones psicológicas BAT-7",status:"Activa",permissions:{administrador:!0,psicologo:!0,candidato:!0},specificUsers:[]},{id:3,name:"Resultados",path:"/resultados",description:"Visualización de resultados de evaluaciones",status:"Activa",permissions:{administrador:!0,psicologo:!0,candidato:!1},specificUsers:[]},{id:4,name:"Informes",path:"/informes",description:"Generación y gestión de informes",status:"Activa",permissions:{administrador:!0,psicologo:!0,candidato:!1},specificUsers:[]},{id:5,name:"Administración",path:"/admin/administration",description:"Panel de administración del sistema",status:"Activa",permissions:{administrador:!0,psicologo:!1,candidato:!1},specificUsers:[]},{id:6,name:"Configuración",path:"/configuracion",description:"Configuración del sistema",status:"Activa",permissions:{administrador:!0,psicologo:!1,candidato:!1},specificUsers:[]},{id:7,name:"Pacientes",path:"/pacientes",description:"Gestión de pacientes",status:"Activa",permissions:{administrador:!0,psicologo:!0,candidato:!1},specificUsers:[]},{id:8,name:"Soporte",path:"/soporte",description:"Centro de ayuda y soporte técnico",status:"Activa",permissions:{administrador:!0,psicologo:!0,candidato:!0},specificUsers:[]},{id:9,name:"Ayuda",path:"/ayuda",description:"Documentación y guías de usuario",status:"Activa",permissions:{administrador:!0,psicologo:!0,candidato:!0},specificUsers:[]}]),l=r.filter(s=>s.name.toLowerCase().includes(e.toLowerCase())||s.path.toLowerCase().includes(e.toLowerCase())),d=(e,s)=>{if(!s)return"bg-gray-300";switch(e){case"administrador":return"bg-blue-500";case"psicologo":return"bg-green-500";case"candidato":return"bg-orange-500";default:return"bg-gray-500"}};return p.jsxs("div",{className:"space-y-6",children:[p.jsxs("div",{className:"flex items-center space-x-3",children:[p.jsx("div",{className:"p-2 bg-blue-100 rounded-lg",children:p.jsx(c,{className:"w-5 h-5 text-blue-600"})}),p.jsxs("div",{children:[p.jsx("h2",{className:"text-xl font-semibold text-gray-900",children:"Control de Acceso a Páginas"}),p.jsx("p",{className:"text-gray-600",children:"Gestiona qué roles pueden acceder a cada página del sistema. Los cambios se aplican inmediatamente."})]})]}),p.jsx("div",{className:"bg-white p-4 rounded-lg shadow-sm border border-gray-200",children:p.jsxs("div",{className:"flex flex-col sm:flex-row gap-4",children:[p.jsxs("div",{className:"flex-1 relative",children:[p.jsx(f,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4"}),p.jsx("input",{type:"text",placeholder:"Buscar rutas por nombre o path...",value:e,onChange:e=>s(e.target.value),className:"w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"})]}),p.jsxs("button",{onClick:()=>t(!a),className:"px-4 py-2 border border-gray-300 rounded-lg hover:bg-gray-50 flex items-center space-x-2",children:[p.jsx(y,{className:"w-4 h-4"}),p.jsx("span",{children:"Filtros Avanzados"})]})]})}),p.jsxs("div",{className:"bg-white rounded-lg shadow-sm border border-gray-200 overflow-hidden",children:[p.jsx("div",{className:"bg-blue-600 text-white",children:p.jsxs("div",{className:"grid grid-cols-12 gap-4 px-6 py-4 font-medium",children:[p.jsx("div",{className:"col-span-2",children:"Página"}),p.jsx("div",{className:"col-span-2",children:"Estado"}),p.jsx("div",{className:"col-span-2 text-center",children:"👑 Administrador"}),p.jsx("div",{className:"col-span-2 text-center",children:"👨‍⚕️ Psicólogo"}),p.jsx("div",{className:"col-span-2 text-center",children:"🎓 Candidato"}),p.jsx("div",{className:"col-span-1 text-center",children:"👥 Usuarios Específicos"}),p.jsx("div",{className:"col-span-1 text-center",children:"Info"})]})}),p.jsx("div",{className:"divide-y divide-gray-200",children:l.map(e=>p.jsxs("div",{className:"grid grid-cols-12 gap-4 px-6 py-4 hover:bg-gray-50",children:[p.jsxs("div",{className:"col-span-2",children:[p.jsx("div",{className:"font-medium text-gray-900",children:e.name}),p.jsx("div",{className:"text-sm text-gray-500",children:e.path}),p.jsx("div",{className:"text-xs text-gray-400",children:e.description})]}),p.jsx("div",{className:"col-span-2 flex items-center",children:p.jsx("span",{className:"inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium "+("Activa"===e.status?"bg-green-100 text-green-800":"bg-red-100 text-red-800"),children:e.status})}),["administrador","psicologo","candidato"].map(s=>p.jsx("div",{className:"col-span-2 flex justify-center",children:p.jsx("button",{onClick:()=>((e,s)=>{i(r.map(a=>a.id===e?o(n({},a),{permissions:o(n({},a.permissions),{[s]:!a.permissions[s]})}):a))})(e.id,s),className:`w-12 h-6 rounded-full transition-colors duration-200 ease-in-out focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 ${e.permissions[s]?d(s,!0):"bg-gray-300"}`,children:p.jsx("div",{className:"w-5 h-5 bg-white rounded-full shadow transform transition-transform duration-200 ease-in-out "+(e.permissions[s]?"translate-x-6":"translate-x-0.5")})})},s)),p.jsx("div",{className:"col-span-1 flex justify-center",children:p.jsxs("button",{className:"px-3 py-1 text-xs bg-blue-100 text-blue-600 rounded-full hover:bg-blue-200",children:["👥 ",e.specificUsers.length," usuario(s)"]})}),p.jsx("div",{className:"col-span-1 flex justify-center",children:p.jsx("button",{className:"p-1 text-gray-400 hover:text-gray-600",children:p.jsx(N,{className:"w-4 h-4"})})})]},e.id))})]}),p.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-4 gap-4",children:[p.jsxs("div",{className:"bg-white p-4 rounded-lg shadow-sm border border-gray-200",children:[p.jsx("div",{className:"text-2xl font-bold text-blue-600",children:r.length}),p.jsx("div",{className:"text-sm text-gray-600",children:"Total de Páginas"})]}),p.jsxs("div",{className:"bg-white p-4 rounded-lg shadow-sm border border-gray-200",children:[p.jsx("div",{className:"text-2xl font-bold text-green-600",children:r.filter(e=>"Activa"===e.status).length}),p.jsx("div",{className:"text-sm text-gray-600",children:"Páginas Activas"})]}),p.jsxs("div",{className:"bg-white p-4 rounded-lg shadow-sm border border-gray-200",children:[p.jsx("div",{className:"text-2xl font-bold text-orange-600",children:r.filter(e=>e.permissions.candidato).length}),p.jsx("div",{className:"text-sm text-gray-600",children:"Accesibles a Candidatos"})]}),p.jsxs("div",{className:"bg-white p-4 rounded-lg shadow-sm border border-gray-200",children:[p.jsx("div",{className:"text-2xl font-bold text-purple-600",children:r.filter(e=>e.specificUsers.length>0).length}),p.jsx("div",{className:"text-sm text-gray-600",children:"Con Usuarios Específicos"})]})]})]})},X=()=>{const[e,s]=G.useState([]),[a,t]=G.useState([]),[r,i]=G.useState([]),[l,c]=G.useState(!0),[m,x]=G.useState(""),[u,h]=G.useState("");G.useState([]);const[g,b]=G.useState(!1),[j,v]=G.useState([]),[y,N]=G.useState("");G.useEffect(()=>{L()},[]);const L=()=>d(void 0,null,function*(){try{c(!0),U(),P(),z()}catch(e){F.error("Error al cargar datos")}finally{c(!1)}}),U=()=>{s([{id:"1",nombre:"Dr. Rodriguez",apellido:"Martínez",email:"<EMAIL>",tipo_usuario:"psicologo",especialidad:"Psicología Clínica"},{id:"2",nombre:"Dra. Martínez",apellido:"López",email:"<EMAIL>",tipo_usuario:"psicologo",especialidad:"Psicología Educativa"},{id:"3",nombre:"Dr. García",apellido:"Fernández",email:"<EMAIL>",tipo_usuario:"psicologo",especialidad:"Psicología Organizacional"}])},P=()=>{t([{id:"3",nombre:"Juan",apellido:"Pérez",email:"<EMAIL>",tipo_usuario:"candidato",documento:"12345678"},{id:"4",nombre:"María",apellido:"García",email:"<EMAIL>",tipo_usuario:"candidato",documento:"87654321"},{id:"5",nombre:"Carlos",apellido:"López",email:"<EMAIL>",tipo_usuario:"candidato",documento:"11223344"},{id:"6",nombre:"Ana",apellido:"Martínez",email:"<EMAIL>",tipo_usuario:"candidato",documento:"44332211"}])},z=()=>{i([{id:1,psicologo_id:"1",paciente_id:"3",assigned_at:"2025-07-15T10:30:00Z",is_active:!0,psychologist:{id:"1",nombre:"Dr. Rodriguez",apellido:"Martínez",email:"<EMAIL>"},patient:{id:"3",nombre:"Juan",apellido:"Pérez",email:"<EMAIL>"}},{id:2,psicologo_id:"2",paciente_id:"4",assigned_at:"2025-07-14T14:20:00Z",is_active:!0,psychologist:{id:"2",nombre:"Dra. Martínez",apellido:"López",email:"<EMAIL>"},patient:{id:"4",nombre:"María",apellido:"García",email:"<EMAIL>"}}])},E=()=>{const e=r.filter(e=>!0===e.is_active).map(e=>e.paciente_id);return a.filter(s=>!e.includes(s.id))},T=e.filter(e=>{var s,a,t;return(null==(s=e.nombre)?void 0:s.toLowerCase().includes(m.toLowerCase()))||(null==(a=e.apellido)?void 0:a.toLowerCase().includes(m.toLowerCase()))||(null==(t=e.email)?void 0:t.toLowerCase().includes(m.toLowerCase()))});return l?p.jsx("div",{className:"flex justify-center items-center h-64",children:p.jsx("div",{className:"animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600"})}):p.jsxs("div",{className:"space-y-6",children:[p.jsxs("div",{className:"text-center mb-6",children:[p.jsx("h2",{className:"text-2xl font-bold text-gray-900",children:"Asignación de Pacientes"}),p.jsx("p",{className:"text-gray-600 mt-2",children:"Gestiona la asignación de pacientes a psicólogos"})]}),p.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-4 gap-6 mb-6",children:[p.jsx("div",{className:"bg-white rounded-lg shadow-md p-6 border-l-4 border-blue-500",children:p.jsxs("div",{className:"flex items-center justify-between",children:[p.jsxs("div",{children:[p.jsx("p",{className:"text-sm font-medium text-gray-600",children:"Psicólogos"}),p.jsx("p",{className:"text-3xl font-bold text-gray-900",children:e.length})]}),p.jsx(w,{className:"w-8 h-8 text-blue-500"})]})}),p.jsx("div",{className:"bg-white rounded-lg shadow-md p-6 border-l-4 border-green-500",children:p.jsxs("div",{className:"flex items-center justify-between",children:[p.jsxs("div",{children:[p.jsx("p",{className:"text-sm font-medium text-gray-600",children:"Pacientes"}),p.jsx("p",{className:"text-3xl font-bold text-gray-900",children:a.length})]}),p.jsx(C,{className:"w-8 h-8 text-green-500"})]})}),p.jsx("div",{className:"bg-white rounded-lg shadow-md p-6 border-l-4 border-orange-500",children:p.jsxs("div",{className:"flex items-center justify-between",children:[p.jsxs("div",{children:[p.jsx("p",{className:"text-sm font-medium text-gray-600",children:"Asignaciones"}),p.jsx("p",{className:"text-3xl font-bold text-gray-900",children:r.filter(e=>"active"===e.status).length})]}),p.jsx(S,{className:"w-8 h-8 text-orange-500"})]})}),p.jsx("div",{className:"bg-white rounded-lg shadow-md p-6 border-l-4 border-red-500",children:p.jsxs("div",{className:"flex items-center justify-between",children:[p.jsxs("div",{children:[p.jsx("p",{className:"text-sm font-medium text-gray-600",children:"Sin Asignar"}),p.jsx("p",{className:"text-3xl font-bold text-gray-900",children:E().length})]}),p.jsx(k,{className:"w-8 h-8 text-red-500"})]})})]}),p.jsxs("div",{className:"bg-white rounded-lg shadow-md p-6",children:[p.jsxs("div",{className:"flex flex-col md:flex-row md:items-center md:justify-between space-y-4 md:space-y-0 mb-6",children:[p.jsxs("div",{className:"relative",children:[p.jsx(f,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400"}),p.jsx("input",{type:"text",placeholder:"Buscar psicólogos...",value:m,onChange:e=>x(e.target.value),className:"pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"})]}),p.jsxs("button",{onClick:()=>b(!0),className:"bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg flex items-center space-x-2 transition-colors",children:[p.jsx(A,{}),p.jsx("span",{children:"Nueva Asignación"})]})]}),p.jsx("div",{className:"space-y-6",children:T.map(e=>{const s=(t=e.id,r.filter(e=>e.psicologo_id===t&&!0===e.is_active).map(e=>{const s=a.find(s=>s.id===e.paciente_id);return o(n({},e),{patient:s})}).filter(e=>e.patient));var t;return p.jsxs("div",{className:"border border-gray-200 rounded-lg p-6",children:[p.jsxs("div",{className:"flex items-center justify-between mb-4",children:[p.jsxs("div",{className:"flex items-center space-x-3",children:[p.jsx("div",{className:"w-10 h-10 bg-blue-100 rounded-full flex items-center justify-center",children:p.jsx(w,{className:"w-5 h-5 text-blue-600"})}),p.jsxs("div",{children:[p.jsxs("h3",{className:"text-lg font-semibold text-gray-900",children:[e.nombre," ",e.apellido]}),p.jsx("p",{className:"text-sm text-gray-600",children:e.email})]})]}),p.jsxs("div",{className:"text-right",children:[p.jsx("p",{className:"text-sm text-gray-600",children:"Pacientes asignados"}),p.jsx("p",{className:"text-2xl font-bold text-blue-600",children:s.length})]})]}),s.length>0?p.jsx("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4",children:s.map(e=>p.jsxs("div",{className:"bg-gray-50 rounded-lg p-4 flex items-center justify-between",children:[p.jsxs("div",{className:"flex items-center space-x-3",children:[p.jsx("div",{className:"w-8 h-8 bg-green-100 rounded-full flex items-center justify-center",children:p.jsx(C,{className:"w-4 h-4 text-green-600"})}),p.jsxs("div",{children:[p.jsxs("p",{className:"text-sm font-medium text-gray-900",children:[e.patient.nombre," ",e.patient.apellido]}),p.jsx("p",{className:"text-xs text-gray-600",children:e.patient.email})]})]}),p.jsx("button",{onClick:()=>{return s=e.id,d(void 0,null,function*(){if(window.confirm("¿Estás seguro de que quieres desasignar este paciente?"))try{i(e=>e.filter(e=>e.id!==s));const{error:e}=yield supabase.from("patient_assignments").delete().eq("id",s);if(e&&"42P01"!==e.code)throw e;F.success("Paciente desasignado exitosamente")}catch(e){F.error("Error al desasignar paciente")}});var s},className:"text-red-600 hover:text-red-900 p-1",title:"Desasignar paciente",children:p.jsx(_,{})})]},e.id))}):p.jsxs("div",{className:"text-center py-8 text-gray-500",children:[p.jsx(C,{className:"w-12 h-12 mx-auto mb-2 opacity-50"}),p.jsx("p",{children:"No hay pacientes asignados"})]})]},e.id)})}),0===T.length&&p.jsx("div",{className:"text-center py-8",children:p.jsx("p",{className:"text-gray-500",children:"No se encontraron psicólogos"})})]}),g&&p.jsx("div",{className:"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50",children:p.jsxs("div",{className:"bg-white rounded-lg p-6 w-full max-w-lg",children:[p.jsx("h3",{className:"text-lg font-semibold text-gray-900 mb-4",children:"Nueva Asignación"}),p.jsxs("div",{className:"space-y-4",children:[p.jsxs("div",{children:[p.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Psicólogo"}),p.jsxs("select",{value:u,onChange:e=>h(e.target.value),className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent",children:[p.jsx("option",{value:"",children:"Seleccionar psicólogo"}),e.map(e=>p.jsxs("option",{value:e.id,children:[e.nombre," ",e.apellido]},e.id))]})]}),p.jsxs("div",{children:[p.jsxs("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:["Pacientes sin asignar (",E().filter(e=>e.nombre.toLowerCase().includes(y.toLowerCase())||e.apellido.toLowerCase().includes(y.toLowerCase())||e.email.toLowerCase().includes(y.toLowerCase())).length,")"]}),p.jsxs("div",{className:"relative mb-3",children:[p.jsx(f,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4"}),p.jsx("input",{type:"text",placeholder:"Buscar pacientes...",value:y,onChange:e=>N(e.target.value),className:"w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"})]}),p.jsxs("div",{className:"max-h-48 overflow-y-auto border border-gray-300 rounded-lg",children:[E().filter(e=>e.nombre.toLowerCase().includes(y.toLowerCase())||e.apellido.toLowerCase().includes(y.toLowerCase())||e.email.toLowerCase().includes(y.toLowerCase())).map(e=>p.jsxs("div",{className:"p-3 hover:bg-gray-50 cursor-pointer border-b border-gray-200 last:border-b-0 flex items-center justify-between "+(j.includes(e.id)?"bg-blue-50 border-blue-200":""),onClick:()=>{return s=e.id,void v(e=>e.includes(s)?e.filter(e=>e!==s):[...e,s]);var s},children:[p.jsxs("div",{className:"flex-1",children:[p.jsxs("p",{className:"text-sm font-medium text-gray-900",children:[e.nombre," ",e.apellido]}),p.jsx("p",{className:"text-xs text-gray-600",children:e.email})]}),j.includes(e.id)&&p.jsx(D,{className:"w-4 h-4 text-blue-600"})]},e.id)),0===E().filter(e=>e.nombre.toLowerCase().includes(y.toLowerCase())||e.apellido.toLowerCase().includes(y.toLowerCase())||e.email.toLowerCase().includes(y.toLowerCase())).length&&p.jsx("div",{className:"p-4 text-center text-gray-500",children:y?"No se encontraron pacientes":"No hay pacientes sin asignar"})]}),j.length>0&&p.jsxs("div",{className:"mt-2 text-sm text-blue-600",children:[j.length," paciente(s) seleccionado(s)"]})]})]}),p.jsxs("div",{className:"flex space-x-2 mt-6",children:[p.jsx("button",{onClick:()=>{b(!1),v([]),h(""),N("")},className:"flex-1 bg-gray-600 hover:bg-gray-700 text-white px-4 py-2 rounded-lg transition-colors",children:"Cancelar"}),p.jsxs("button",{onClick:()=>d(void 0,null,function*(){if(u&&0!==j.length)try{const e=j.map(e=>({id:`${u}-${e}-${Date.now()}`,psicologo_id:u,paciente_id:e,assigned_at:(new Date).toISOString(),is_active:!0}));i(s=>[...s,...e]),v([]),h(""),N(""),b(!1),F.success(`${j.length} paciente(s) asignado(s) exitosamente`)}catch(e){F.error("Error al guardar asignaciones")}else F.error("Debe seleccionar un psicólogo y al menos un paciente")}),disabled:!u||0===j.length,className:"flex-1 bg-blue-600 hover:bg-blue-700 disabled:bg-gray-400 disabled:cursor-not-allowed text-white px-4 py-2 rounded-lg transition-colors flex items-center justify-center space-x-2",children:[p.jsx(D,{className:"w-4 h-4"}),p.jsxs("span",{children:["Guardar (",j.length,")"]})]})]})]})})]})},Y=()=>{const[e,s]=G.useState({totalSessions:1234,averageSessionTime:25,testsCompleted:89,activeUsers:18}),[a,t]=G.useState("7days"),[r,i]=G.useState(!0),[l,o]=G.useState([]),[m,x]=G.useState([]);G.useEffect(()=>{h()},[a]);const h=()=>d(void 0,null,function*(){try{i(!0),g(),b(),f()}catch(e){F.error("Error al cargar datos de uso")}finally{i(!1)}}),g=()=>{s({totalSessions:1234,averageSessionTime:25,testsCompleted:89,activeUsers:18})},b=()=>{const e=[{id:1,user:"<EMAIL>",action:"login",timestamp:new Date(Date.now()-18e5).toISOString(),duration:null,details:"Inicio de sesión exitoso"},{id:2,user:"<EMAIL>",action:"test_completed",timestamp:new Date(Date.now()-72e5).toISOString(),duration:45,details:"Test MACI-II completado"},{id:3,user:"<EMAIL>",action:"session_end",timestamp:new Date(Date.now()-144e5).toISOString(),duration:32,details:"Sesión finalizada"},{id:4,user:"<EMAIL>",action:"login",timestamp:new Date(Date.now()-216e5).toISOString(),duration:null,details:"Acceso a panel de administración"},{id:5,user:"<EMAIL>",action:"test_started",timestamp:new Date(Date.now()-288e5).toISOString(),duration:null,details:"Evaluación iniciada"}];o(e)},f=()=>{const e="24hours"===a?1:"7days"===a?7:"30days"===a?30:90,s=[];for(let a=e-1;a>=0;a--){const t=new Date;t.setDate(t.getDate()-a),s.push({date:t.toLocaleDateString("es-ES",n({day:"2-digit",month:"short"},e>30&&{year:"2-digit"})),sessions:Math.floor(15*Math.random())+5,tests:Math.floor(8*Math.random())+2,users:Math.floor(12*Math.random())+3})}x(s)},j=e=>{switch(e){case"login":return p.jsx(T,{className:"w-4 h-4 text-blue-600"});case"test_completed":return p.jsx(u,{className:"w-4 h-4 text-green-600"});case"test_started":return p.jsx(E,{className:"w-4 h-4 text-orange-600"});case"session_end":return p.jsx(z,{className:"w-4 h-4 text-gray-600"});default:return p.jsx(c,{className:"w-4 h-4 text-gray-600"})}},v=e=>{switch(e){case"login":return"bg-blue-100 text-blue-800";case"test_completed":return"bg-green-100 text-green-800";case"test_started":return"bg-orange-100 text-orange-800";default:return"bg-gray-100 text-gray-800"}},y=e=>{const s=new Date,a=new Date(e),t=Math.floor((s-a)/6e4);return t<60?`Hace ${t} min`:t<1440?`Hace ${Math.floor(t/60)} h`:`Hace ${Math.floor(t/1440)} días`};return r?p.jsx("div",{className:"flex justify-center items-center h-64",children:p.jsx("div",{className:"animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600"})}):p.jsxs("div",{className:"space-y-6",children:[p.jsxs("div",{className:"text-center mb-6",children:[p.jsx("h2",{className:"text-2xl font-bold text-gray-900",children:"Control de Usos"}),p.jsx("p",{className:"text-gray-600 mt-2",children:"Monitorea el uso del sistema y estadísticas"})]}),p.jsxs("div",{className:"bg-white rounded-lg shadow-md p-6",children:[p.jsxs("div",{className:"flex flex-col md:flex-row md:items-center md:justify-between space-y-4 md:space-y-0 mb-6",children:[p.jsxs("div",{className:"flex items-center space-x-4",children:[p.jsx(L,{className:"text-gray-400"}),p.jsx("select",{value:a,onChange:e=>t(e.target.value),className:"px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent",children:[{value:"24hours",label:"Últimas 24 horas"},{value:"7days",label:"Últimos 7 días"},{value:"30days",label:"Últimos 30 días"},{value:"90days",label:"Últimos 90 días"}].map(e=>p.jsx("option",{value:e.value,children:e.label},e.value))})]}),p.jsxs("button",{onClick:()=>{const e=[["Usuario","Acción","Fecha","Duración","Detalles"],...l.map(e=>[e.user,e.action,new Date(e.timestamp).toLocaleString("es-ES"),e.duration?`${e.duration} min`:"N/A",e.details])].map(e=>e.join(",")).join("\n"),s=new Blob([e],{type:"text/csv"}),t=window.URL.createObjectURL(s),r=document.createElement("a");r.href=t,r.download=`reporte_uso_${a}_${(new Date).toISOString().split("T")[0]}.csv`,r.click(),window.URL.revokeObjectURL(t),F.success("Reporte exportado exitosamente")},className:"bg-green-600 hover:bg-green-700 text-white px-4 py-2 rounded-lg flex items-center space-x-2 transition-colors",children:[p.jsx(U,{}),p.jsx("span",{children:"Exportar Reporte"})]})]}),p.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-4 gap-6 mb-8",children:[p.jsx("div",{className:"bg-gradient-to-r from-blue-500 to-blue-600 rounded-lg shadow-md p-6 text-white",children:p.jsxs("div",{className:"flex items-center justify-between",children:[p.jsxs("div",{children:[p.jsx("p",{className:"text-blue-100 text-sm font-medium",children:"Total Sesiones"}),p.jsx("p",{className:"text-3xl font-bold",children:e.totalSessions})]}),p.jsx(P,{className:"w-8 h-8 text-blue-200"})]})}),p.jsx("div",{className:"bg-gradient-to-r from-green-500 to-green-600 rounded-lg shadow-md p-6 text-white",children:p.jsxs("div",{className:"flex items-center justify-between",children:[p.jsxs("div",{children:[p.jsx("p",{className:"text-green-100 text-sm font-medium",children:"Tiempo Promedio"}),p.jsxs("p",{className:"text-3xl font-bold",children:[e.averageSessionTime,p.jsx("span",{className:"text-lg",children:"min"})]})]}),p.jsx(z,{className:"w-8 h-8 text-green-200"})]})}),p.jsx("div",{className:"bg-gradient-to-r from-orange-500 to-orange-600 rounded-lg shadow-md p-6 text-white",children:p.jsxs("div",{className:"flex items-center justify-between",children:[p.jsxs("div",{children:[p.jsx("p",{className:"text-orange-100 text-sm font-medium",children:"Tests Completados"}),p.jsx("p",{className:"text-3xl font-bold",children:e.testsCompleted})]}),p.jsx(u,{className:"w-8 h-8 text-orange-200"})]})}),p.jsx("div",{className:"bg-gradient-to-r from-purple-500 to-purple-600 rounded-lg shadow-md p-6 text-white",children:p.jsxs("div",{className:"flex items-center justify-between",children:[p.jsxs("div",{children:[p.jsx("p",{className:"text-purple-100 text-sm font-medium",children:"Usuarios Activos"}),p.jsx("p",{className:"text-3xl font-bold",children:e.activeUsers})]}),p.jsx(c,{className:"w-8 h-8 text-purple-200"})]})})]}),p.jsxs("div",{className:"mb-8",children:[p.jsx("h3",{className:"text-lg font-semibold text-gray-900 mb-4",children:"Actividad del Sistema"}),p.jsx("div",{className:"h-64 flex items-end justify-between space-x-2 bg-gray-50 rounded-lg p-4",children:m.map((e,s)=>p.jsxs("div",{className:"flex-1 flex flex-col items-center",children:[p.jsx("div",{className:"w-full bg-gray-200 rounded-t-lg relative",style:{height:"200px"},children:p.jsx("div",{className:"absolute bottom-0 w-full bg-blue-500 rounded-t-lg opacity-80",style:{height:e.sessions/Math.max(...m.map(e=>Math.max(e.sessions,e.tests,e.users)))*200+"px"},title:`Sesiones: ${e.sessions}`})}),p.jsx("p",{className:"text-xs text-gray-600 mt-2",children:e.date}),p.jsxs("div",{className:"text-xs text-center mt-1",children:[p.jsx("div",{className:"text-blue-600 font-medium",children:e.sessions}),p.jsx("div",{className:"text-gray-500",children:"sesiones"})]})]},s))}),p.jsxs("div",{className:"mt-4 flex justify-center space-x-6",children:[p.jsxs("div",{className:"flex items-center",children:[p.jsx("div",{className:"w-3 h-3 bg-blue-500 rounded mr-2"}),p.jsx("span",{className:"text-sm text-gray-600",children:"Sesiones"})]}),p.jsxs("div",{className:"flex items-center",children:[p.jsx("div",{className:"w-3 h-3 bg-green-500 rounded mr-2"}),p.jsx("span",{className:"text-sm text-gray-600",children:"Tests"})]}),p.jsxs("div",{className:"flex items-center",children:[p.jsx("div",{className:"w-3 h-3 bg-orange-500 rounded mr-2"}),p.jsx("span",{className:"text-sm text-gray-600",children:"Usuarios"})]})]})]}),p.jsxs("div",{children:[p.jsx("h3",{className:"text-lg font-semibold text-gray-900 mb-4",children:"Actividad Reciente"}),p.jsx("div",{className:"overflow-x-auto",children:p.jsxs("table",{className:"min-w-full divide-y divide-gray-200",children:[p.jsx("thead",{className:"bg-gray-50",children:p.jsxs("tr",{children:[p.jsx("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Usuario"}),p.jsx("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Acción"}),p.jsx("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Tiempo"}),p.jsx("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Duración"}),p.jsx("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Detalles"})]})}),p.jsx("tbody",{className:"bg-white divide-y divide-gray-200",children:l.map(e=>p.jsxs("tr",{className:"hover:bg-gray-50",children:[p.jsx("td",{className:"px-6 py-4 whitespace-nowrap",children:p.jsx("div",{className:"text-sm font-medium text-gray-900",children:e.user})}),p.jsx("td",{className:"px-6 py-4 whitespace-nowrap",children:p.jsxs("div",{className:"flex items-center space-x-2",children:[j(e.action),p.jsx("span",{className:`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${v(e.action)}`,children:e.action.replace("_"," ")})]})}),p.jsx("td",{className:"px-6 py-4 whitespace-nowrap text-sm text-gray-500",children:y(e.timestamp)}),p.jsx("td",{className:"px-6 py-4 whitespace-nowrap text-sm text-gray-900",children:e.duration?`${e.duration} min`:"-"}),p.jsx("td",{className:"px-6 py-4 text-sm text-gray-900",children:e.details})]},e.id))})]})})]})]})]})},K=()=>{const[e,s]=G.useState("profile"),[a,t]=G.useState({nombre:"Usuario Demo",apellido:"Sistema",email:"<EMAIL>",documento:"12345678"}),[r,i]=G.useState({emailNotifications:!0,pushNotifications:!1,weeklyReports:!0}),[l,d]=G.useState({twoFactorAuth:!1,sessionTimeout:"30",passwordExpiry:"90"}),c=[{id:"profile",name:"Perfil",icon:C,description:"Información personal"},{id:"notifications",name:"Notificaciones",icon:M,description:"Preferencias de notificaciones"},{id:"security",name:"Seguridad",icon:I,description:"Configuración de seguridad"}],m=(e,s)=>{t(a=>o(n({},a),{[e]:s}))},x=(e,s)=>{i(a=>o(n({},a),{[e]:s}))},u=(e,s)=>{d(a=>o(n({},a),{[e]:s}))},h=()=>p.jsxs("div",{className:"space-y-6",children:[p.jsx("h3",{className:"text-lg font-semibold text-gray-900",children:"Información Personal"}),p.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[p.jsxs("div",{children:[p.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Nombre"}),p.jsxs("div",{className:"relative",children:[p.jsx(C,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400"}),p.jsx("input",{type:"text",value:a.nombre,onChange:e=>m("nombre",e.target.value),className:"w-full pl-10 pr-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"})]})]}),p.jsxs("div",{children:[p.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Apellido"}),p.jsxs("div",{className:"relative",children:[p.jsx(C,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400"}),p.jsx("input",{type:"text",value:a.apellido,onChange:e=>m("apellido",e.target.value),className:"w-full pl-10 pr-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"})]})]}),p.jsxs("div",{children:[p.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Email"}),p.jsxs("div",{className:"relative",children:[p.jsx(R,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400"}),p.jsx("input",{type:"email",value:a.email,onChange:e=>m("email",e.target.value),className:"w-full pl-10 pr-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"})]})]}),p.jsxs("div",{children:[p.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Documento"}),p.jsx("input",{type:"text",value:a.documento,onChange:e=>m("documento",e.target.value),className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"})]})]})]});return p.jsxs("div",{className:"space-y-6",children:[p.jsxs("div",{className:"text-center mb-6",children:[p.jsx("h2",{className:"text-2xl font-bold text-gray-900",children:"Configuración Personal"}),p.jsx("p",{className:"text-gray-600 mt-2",children:"Administra tu perfil y preferencias"})]}),p.jsx("div",{className:"flex flex-wrap gap-4 mb-8 justify-center",children:c.map(a=>p.jsxs("button",{onClick:()=>s(a.id),className:"flex items-center px-6 py-3 rounded-lg font-medium transition-colors "+(e===a.id?"bg-blue-600 text-white shadow-lg":"bg-white text-gray-700 hover:bg-gray-50 border border-gray-200"),children:[p.jsx(a.icon,{className:"w-5 h-5 mr-2"}),a.name]},a.id))}),p.jsxs("div",{className:"bg-white rounded-lg shadow-sm border border-gray-200 p-6",children:[(()=>{switch(e){case"profile":default:return h();case"notifications":return p.jsxs("div",{className:"space-y-6",children:[p.jsx("h3",{className:"text-lg font-semibold text-gray-900",children:"Preferencias de Notificaciones"}),p.jsxs("div",{className:"space-y-4",children:[p.jsxs("div",{className:"flex items-center justify-between",children:[p.jsxs("div",{children:[p.jsx("h4",{className:"text-sm font-medium text-gray-900",children:"Notificaciones por Email"}),p.jsx("p",{className:"text-sm text-gray-500",children:"Recibir notificaciones importantes por correo"})]}),p.jsxs("label",{className:"relative inline-flex items-center cursor-pointer",children:[p.jsx("input",{type:"checkbox",checked:r.emailNotifications,onChange:e=>x("emailNotifications",e.target.checked),className:"sr-only peer"}),p.jsx("div",{className:"w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-blue-600"})]})]}),p.jsxs("div",{className:"flex items-center justify-between",children:[p.jsxs("div",{children:[p.jsx("h4",{className:"text-sm font-medium text-gray-900",children:"Notificaciones Push"}),p.jsx("p",{className:"text-sm text-gray-500",children:"Notificaciones en tiempo real en el navegador"})]}),p.jsxs("label",{className:"relative inline-flex items-center cursor-pointer",children:[p.jsx("input",{type:"checkbox",checked:r.pushNotifications,onChange:e=>x("pushNotifications",e.target.checked),className:"sr-only peer"}),p.jsx("div",{className:"w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-blue-600"})]})]}),p.jsxs("div",{className:"flex items-center justify-between",children:[p.jsxs("div",{children:[p.jsx("h4",{className:"text-sm font-medium text-gray-900",children:"Reportes Semanales"}),p.jsx("p",{className:"text-sm text-gray-500",children:"Resumen semanal de actividad"})]}),p.jsxs("label",{className:"relative inline-flex items-center cursor-pointer",children:[p.jsx("input",{type:"checkbox",checked:r.weeklyReports,onChange:e=>x("weeklyReports",e.target.checked),className:"sr-only peer"}),p.jsx("div",{className:"w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-blue-600"})]})]})]})]});case"security":return p.jsxs("div",{className:"space-y-6",children:[p.jsx("h3",{className:"text-lg font-semibold text-gray-900",children:"Configuración de Seguridad"}),p.jsxs("div",{className:"space-y-4",children:[p.jsxs("div",{className:"flex items-center justify-between",children:[p.jsxs("div",{children:[p.jsx("h4",{className:"text-sm font-medium text-gray-900",children:"Autenticación de Dos Factores"}),p.jsx("p",{className:"text-sm text-gray-500",children:"Agregar una capa extra de seguridad"})]}),p.jsxs("label",{className:"relative inline-flex items-center cursor-pointer",children:[p.jsx("input",{type:"checkbox",checked:l.twoFactorAuth,onChange:e=>u("twoFactorAuth",e.target.checked),className:"sr-only peer"}),p.jsx("div",{className:"w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-blue-600"})]})]}),p.jsxs("div",{children:[p.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Tiempo de Sesión (minutos)"}),p.jsxs("select",{value:l.sessionTimeout,onChange:e=>u("sessionTimeout",e.target.value),className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent",children:[p.jsx("option",{value:"15",children:"15 minutos"}),p.jsx("option",{value:"30",children:"30 minutos"}),p.jsx("option",{value:"60",children:"1 hora"}),p.jsx("option",{value:"120",children:"2 horas"})]})]}),p.jsxs("div",{children:[p.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Expiración de Contraseña (días)"}),p.jsxs("select",{value:l.passwordExpiry,onChange:e=>u("passwordExpiry",e.target.value),className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent",children:[p.jsx("option",{value:"30",children:"30 días"}),p.jsx("option",{value:"60",children:"60 días"}),p.jsx("option",{value:"90",children:"90 días"}),p.jsx("option",{value:"180",children:"180 días"})]})]})]})]})}})(),p.jsx("div",{className:"mt-8 pt-6 border-t border-gray-200",children:p.jsxs("button",{onClick:()=>{alert("Configuración guardada correctamente")},className:"w-full md:w-auto px-6 py-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700 flex items-center justify-center space-x-2 transition-colors",children:[p.jsx(O,{className:"w-5 h-5"}),p.jsx("span",{children:"Guardar Cambios"})]})})]})]})},Q=()=>{var e;const{user:s,userRole:a}=$(),[t,r]=G.useState("dashboard"),i="administrador"===a||"administrador"===(null==s?void 0:s.tipo_usuario),l=[{id:"dashboard",name:"Dashboard",icon:m,component:J,adminOnly:!0,description:"Resumen general del sistema"},{id:"users",name:"Gestión de Usuarios",icon:c,component:V,adminOnly:!0,description:"Administra usuarios del sistema"},{id:"access",name:"Control de Acceso",icon:I,component:W,adminOnly:!0,description:"Gestiona permisos de acceso a páginas"},{id:"assignments",name:"Asignación de Pacientes",icon:w,component:X,adminOnly:!0,description:"Asigna pacientes a psicólogos"},{id:"usage",name:"Control de Usos",icon:P,component:Y,adminOnly:!0,description:"Monitorea el uso del sistema"},{id:"settings",name:"Configuración Personal",icon:B,component:K,adminOnly:!1,description:"Configuración personal y preferencias"}],n=G.useMemo(()=>l.filter(e=>!e.adminOnly||i),[i]);G.useEffect(()=>{n.length>0&&!n.some(e=>e.id===t)&&r(n[0].id)},[n,t]);return i||0!==n.length?p.jsxs("div",{className:"min-h-screen bg-gray-50",children:[p.jsx(q,{title:"Configuración del Sistema",subtitle:"Gestiona usuarios, configuraciones y estadísticas del sistema",icon:B}),p.jsxs("div",{className:"container mx-auto px-4 py-8",children:[p.jsxs("div",{className:"bg-white rounded-lg shadow-md mb-6",children:[p.jsx("div",{className:"border-b border-gray-200",children:p.jsx("nav",{className:"flex space-x-8 px-6","aria-label":"Tabs",children:n.map(e=>{const s=e.icon,a=t===e.id;return p.jsxs("button",{onClick:()=>r(e.id),className:`\n                      flex items-center space-x-2 py-4 px-1 border-b-2 font-medium text-sm transition-colors duration-200\n                      ${a?"border-blue-500 text-blue-600":"border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300"}\n                    `,"aria-current":a?"page":void 0,children:[p.jsx(s,{className:"w-5 h-5 "+(a?"text-blue-600":"text-gray-400")}),p.jsx("span",{children:e.name})]},e.id)})})}),p.jsx("div",{className:"px-6 py-4 bg-gray-50 border-b border-gray-200",children:p.jsx("p",{className:"text-sm text-gray-600",children:null==(e=n.find(e=>e.id===t))?void 0:e.description})}),p.jsx("div",{className:"p-6",children:(()=>{const e=n.find(e=>e.id===t);if(!e)return null;const s=e.component;return p.jsx(s,{})})()})]}),p.jsx("div",{className:"text-center text-sm text-gray-500",children:p.jsxs("p",{children:["Sistema de Administración BAT-7 • Usuario: ",null==s?void 0:s.nombre," ",null==s?void 0:s.apellido," • Rol: ",a||(null==s?void 0:s.tipo_usuario)]})})]})]}):p.jsx("div",{className:"container mx-auto px-4 py-8",children:p.jsx("div",{className:"bg-yellow-50 border border-yellow-200 rounded-md p-4",children:p.jsxs("div",{className:"text-yellow-800",children:[p.jsx("h3",{className:"text-lg font-medium",children:"Acceso Restringido"}),p.jsx("p",{className:"mt-2",children:"No tienes permisos para acceder a esta sección."})]})})})};export{Q as default};
