import{j as e,F as s,i as t,h as a,b as l,l as i}from"./auth-3ab59eff.js";import{r}from"./react-vendor-99be060c.js";import{e as n}from"./enhancedSupabaseService-a93ecdc2.js";import"./ui-vendor-9705a4a1.js";import"./utils-vendor-4d1206d7.js";const d=()=>{const[d,c]=r.useState({totalPatients:0,totalPsychologists:0,totalInstitutions:0}),[o,x]=r.useState(!0);return r.useEffect(()=>{var e,s,t;e=void 0,s=null,t=function*(){x(!0);try{const[e,s,t]=yield Promise.all([n.getPatients(),n.getPsychologists(),n.getInstitutions()]);c({totalPatients:Array.isArray(e.data)?e.data.length:0,totalPsychologists:Array.isArray(s.data)?s.data.length:0,totalInstitutions:Array.isArray(t.data)?t.data.length:0})}catch(e){}finally{x(!1)}},new Promise((a,l)=>{var i=e=>{try{n(t.next(e))}catch(s){l(s)}},r=e=>{try{n(t.throw(e))}catch(s){l(s)}},n=e=>e.done?a(e.value):Promise.resolve(e.value).then(i,r);n((t=t.apply(e,s)).next())})},[]),e.jsxs("div",{className:"space-y-6",children:[e.jsxs("header",{children:[e.jsx("h1",{className:"text-2xl font-bold text-gray-900",children:"Dashboard"}),e.jsx("p",{className:"mt-1 text-sm text-gray-600",children:"Bienvenido al sistema de gestión psicológica"})]}),o?e.jsx("div",{className:"flex justify-center items-center p-12",children:e.jsx(s,{className:"animate-spin text-blue-600 text-3xl"})}):e.jsxs(e.Fragment,{children:[e.jsxs("div",{className:"grid grid-cols-1 gap-5 sm:grid-cols-2 lg:grid-cols-3",children:[e.jsxs("div",{className:"bg-white overflow-hidden shadow rounded-lg",children:[e.jsx("div",{className:"p-5",children:e.jsxs("div",{className:"flex items-center",children:[e.jsx("div",{className:"flex-shrink-0 bg-blue-500 rounded-md p-3",children:e.jsx(t,{className:"h-6 w-6 text-white"})}),e.jsx("div",{className:"ml-5 w-0 flex-1",children:e.jsxs("dl",{children:[e.jsx("dt",{className:"text-sm font-medium text-gray-500 truncate",children:"Total Pacientes"}),e.jsx("dd",{children:e.jsx("div",{className:"text-lg font-medium text-gray-900",children:d.totalPatients})})]})})]})}),e.jsx("div",{className:"bg-gray-50 px-5 py-3",children:e.jsx("div",{className:"text-sm",children:e.jsx("a",{href:"/patients",className:"font-medium text-blue-600 hover:text-blue-500",children:"Ver todos los pacientes"})})})]}),e.jsxs("div",{className:"bg-white overflow-hidden shadow rounded-lg",children:[e.jsx("div",{className:"p-5",children:e.jsxs("div",{className:"flex items-center",children:[e.jsx("div",{className:"flex-shrink-0 bg-green-500 rounded-md p-3",children:e.jsx(a,{className:"h-6 w-6 text-white"})}),e.jsx("div",{className:"ml-5 w-0 flex-1",children:e.jsxs("dl",{children:[e.jsx("dt",{className:"text-sm font-medium text-gray-500 truncate",children:"Total Psicólogos"}),e.jsx("dd",{children:e.jsx("div",{className:"text-lg font-medium text-gray-900",children:d.totalPsychologists})})]})})]})}),e.jsx("div",{className:"bg-gray-50 px-5 py-3",children:e.jsx("div",{className:"text-sm",children:e.jsx("a",{href:"/psychologists",className:"font-medium text-blue-600 hover:text-blue-500",children:"Ver todos los psicólogos"})})})]}),e.jsxs("div",{className:"bg-white overflow-hidden shadow rounded-lg",children:[e.jsx("div",{className:"p-5",children:e.jsxs("div",{className:"flex items-center",children:[e.jsx("div",{className:"flex-shrink-0 bg-purple-500 rounded-md p-3",children:e.jsx(l,{className:"h-6 w-6 text-white"})}),e.jsx("div",{className:"ml-5 w-0 flex-1",children:e.jsxs("dl",{children:[e.jsx("dt",{className:"text-sm font-medium text-gray-500 truncate",children:"Total Instituciones"}),e.jsx("dd",{children:e.jsx("div",{className:"text-lg font-medium text-gray-900",children:d.totalInstitutions})})]})})]})}),e.jsx("div",{className:"bg-gray-50 px-5 py-3",children:e.jsx("div",{className:"text-sm",children:e.jsx("a",{href:"/institutions",className:"font-medium text-blue-600 hover:text-blue-500",children:"Ver todas las instituciones"})})})]})]}),e.jsxs("section",{className:"bg-white shadow rounded-lg p-6",children:[e.jsx("h2",{className:"text-xl font-medium text-gray-900 mb-4",children:"Acciones rápidas"}),e.jsxs("div",{className:"grid grid-cols-1 gap-4 sm:grid-cols-2 lg:grid-cols-4",children:[e.jsxs("a",{href:"/patients/new",className:"bg-blue-50 hover:bg-blue-100 p-4 rounded-lg flex flex-col items-center justify-center transition duration-150 ease-in-out",children:[e.jsx(t,{className:"h-8 w-8 text-blue-600 mb-2"}),e.jsx("span",{className:"text-sm font-medium text-blue-700",children:"Nuevo Paciente"})]}),e.jsxs("a",{href:"/psychologists/new",className:"bg-green-50 hover:bg-green-100 p-4 rounded-lg flex flex-col items-center justify-center transition duration-150 ease-in-out",children:[e.jsx(a,{className:"h-8 w-8 text-green-600 mb-2"}),e.jsx("span",{className:"text-sm font-medium text-green-700",children:"Nuevo Psicólogo"})]}),e.jsxs("a",{href:"/institutions/new",className:"bg-purple-50 hover:bg-purple-100 p-4 rounded-lg flex flex-col items-center justify-center transition duration-150 ease-in-out",children:[e.jsx(l,{className:"h-8 w-8 text-purple-600 mb-2"}),e.jsx("span",{className:"text-sm font-medium text-purple-700",children:"Nueva Institución"})]}),e.jsxs("a",{href:"/reports",className:"bg-yellow-50 hover:bg-yellow-100 p-4 rounded-lg flex flex-col items-center justify-center transition duration-150 ease-in-out",children:[e.jsx(i,{className:"h-8 w-8 text-yellow-600 mb-2"}),e.jsx("span",{className:"text-sm font-medium text-yellow-700",children:"Reportes"})]})]})]})]})]})};export{d as default};
