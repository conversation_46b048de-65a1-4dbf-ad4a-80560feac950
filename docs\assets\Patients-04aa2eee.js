import{j as e,f as s,e as a,H as t,s as r,t as l,L as i,M as n,N as o,O as c,P as d,F as x,Q as m,R as p,S as u}from"./auth-3ab59eff.js";import{r as h}from"./react-vendor-99be060c.js";import{C as g,b,a as j,c as f}from"./admin-168d579d.js";import{P as N}from"./index-23a57a03.js";import{l as v}from"./utils-vendor-4d1206d7.js";import"./ui-vendor-9705a4a1.js";const y=({patient:r})=>{var l,i,n;const o=r.edad||(e=>{if(!e)return null;const s=new Date,a=new Date(e);if(isNaN(a.getTime()))return null;let t=s.getFullYear()-a.getFullYear();const r=s.getMonth()-a.get<PERSON>onth();return(r<0||0===r&&s.getDate()<a.getDate())&&t--,t>=0?t:null})(r.fecha_nacimiento);return e.jsx("div",{className:"bg-white rounded-lg shadow-sm border border-gray-200 overflow-hidden patient-card",children:e.jsxs("div",{className:"p-4",children:[e.jsxs("div",{className:"flex items-center mb-4",children:[e.jsx("div",{className:"patient-avatar "+("masculino"===(null==(l=r.genero)?void 0:l.toLowerCase())?"patient-avatar-male":"femenino"===(null==(i=r.genero)?void 0:i.toLowerCase())?"patient-avatar-female":"patient-avatar-other"),children:((e,s)=>{if(!e)return"";const a=e.charAt(0).toUpperCase(),t=s?s.charAt(0).toUpperCase():"";return t?`${a}${t}`:a})(r.nombre,r.apellido||r.apellidos)}),e.jsxs("div",{className:"ml-3",children:[e.jsx("h3",{className:"patient-name",children:`${r.nombre||""} ${r.apellido||r.apellidos||""}`.trim()}),e.jsx("p",{className:"patient-education",children:r.nivel_educativo?r.nivel_educativo:r.grado||"4° Medio"})]})]}),e.jsxs("div",{className:"patient-info-grid",children:[e.jsxs("div",{children:[e.jsx("span",{className:"patient-info-label",children:"Edad:"})," ",o?`${o} años`:"No disponible"]}),e.jsxs("div",{className:"flex items-center",children:[e.jsx("span",{className:"patient-info-label",children:"Sexo:"}),e.jsxs("span",{className:"flex items-center",children:[(()=>{const l=r.genero?r.genero.toLowerCase():"";return"masculino"===l?e.jsx(s,{className:"text-blue-600"}):"femenino"===l?e.jsx(a,{className:"text-pink-600"}):e.jsx(t,{className:"text-gray-600"})})(),e.jsx("span",{className:"ml-1",children:r.genero||"No especificado"})]})]})]}),e.jsx("div",{className:"patient-psychologist",children:r.psicologo_id?e.jsxs("span",{children:["Psicólogo: ",(null==(n=r.psicologo)?void 0:n.nombre)||"Asignado"]}):e.jsx("span",{children:"Sin psicólogo asignado"})})]})})};y.propTypes={patient:N.shape({id:N.string,nombre:N.string.isRequired,apellido:N.string,apellidos:N.string,fecha_nacimiento:N.string,genero:N.string,edad:N.number,nivel_educativo:N.string,grado:N.string,psicologo_id:N.string,psicologo:N.object}).isRequired};const w=({patients:s,loading:a,onSelectPatient:t})=>a?null:e.jsx("div",{className:"overflow-x-auto",children:e.jsxs("table",{className:"min-w-full divide-y divide-gray-200",children:[e.jsx("thead",{className:"bg-gray-50",children:e.jsxs("tr",{children:[e.jsx("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Nombre"}),e.jsx("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Documento"}),e.jsx("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Género"}),e.jsx("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Fecha Nacimiento"}),e.jsx("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Psicólogo"}),e.jsx("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Acciones"})]})}),e.jsx("tbody",{className:"bg-white divide-y divide-gray-200",children:s.map(s=>e.jsxs("tr",{className:"hover:bg-gray-50",children:[e.jsx("td",{className:"px-6 py-4 whitespace-nowrap",children:e.jsx("div",{className:"flex items-center",children:e.jsx("div",{className:"ml-4",children:e.jsxs("div",{className:"text-sm font-medium text-gray-900",children:[s.nombre," ",s.apellidos]})})})}),e.jsx("td",{className:"px-6 py-4 whitespace-nowrap",children:e.jsx("div",{className:"text-sm text-gray-900",children:s.documento_identidad})}),e.jsx("td",{className:"px-6 py-4 whitespace-nowrap",children:e.jsx("div",{className:"text-sm text-gray-900",children:s.genero})}),e.jsx("td",{className:"px-6 py-4 whitespace-nowrap",children:e.jsx("div",{className:"text-sm text-gray-900",children:new Date(s.fecha_nacimiento).toLocaleDateString()})}),e.jsx("td",{className:"px-6 py-4 whitespace-nowrap",children:e.jsx("div",{className:"text-sm text-gray-900",children:s.psicologo?`${s.psicologo.nombre} ${s.psicologo.apellido}`:"No asignado"})}),e.jsx("td",{className:"px-6 py-4 whitespace-nowrap text-sm font-medium",children:e.jsx("button",{onClick:()=>t(s),className:"text-blue-600 hover:text-blue-900",children:"Ver detalles"})})]},s.id))})]})}),k=()=>{const[s,a]=h.useState([]),[N,k]=h.useState([]),[C,S]=h.useState(!0),[_,P]=h.useState(0),[$,F]=h.useState(1),[L,D]=h.useState(12),[M,A]=h.useState(""),[q,O]=h.useState("asc"),[T,V]=h.useState("nombre"),[R,z]=h.useState(""),[E,U]=h.useState("grid"),Y=h.useCallback(()=>{return e=void 0,s=null,t=function*(){try{S(!0);let e=r.from("pacientes").select("\n          *,\n          psicologo:psicologo_id (\n            id, nombre, apellido\n          )\n        ",{count:"exact"});M&&(e=e.or(`nombre.ilike.%${M}%,apellidos.ilike.%${M}%,documento_identidad.ilike.%${M}%`)),R&&(e=e.eq("genero",R)),e=e.order(T,{ascending:"asc"===q});const s=($-1)*L,t=s+L-1,{data:l,error:i,count:n}=yield e.range(s,t);if(i)throw i;a(l||[]),k(l||[]),P(n||0)}catch(e){}finally{S(!1)}},new Promise((a,r)=>{var l=e=>{try{n(t.next(e))}catch(s){r(s)}},i=e=>{try{n(t.throw(e))}catch(s){r(s)}},n=e=>e.done?a(e.value):Promise.resolve(e.value).then(l,i);n((t=t.apply(e,s)).next())});var e,s,t},[M,R,q,T,$,L]);h.useEffect(()=>{Y()},[Y]);const B=h.useCallback(v.debounce(e=>{A(e),F(1)},500),[]),G=()=>{A(""),z(""),V("nombre"),O("asc"),F(1);const e=document.querySelector('input[type="text"]');e&&(e.value="")},H=()=>{window.scrollTo({top:0,behavior:"smooth"})},Q=e=>{F(e),H()},I=Math.ceil(_/L),J=e=>{};return e.jsxs("div",{className:"container mx-auto py-6",children:[e.jsx("div",{className:"mb-8",children:e.jsxs("div",{className:"text-center",children:[e.jsxs("div",{className:"inline-flex items-center justify-center mb-3",children:[e.jsx("div",{className:"bg-yellow-400 p-3 rounded-full mr-3 shadow-md",children:e.jsx(t,{className:"text-white text-2xl"})}),e.jsx("h1",{className:"text-3xl font-bold bg-gradient-to-r from-blue-800 to-indigo-950 bg-clip-text text-transparent",children:"Pacientes"})]}),e.jsx("p",{className:"text-gray-600 max-w-2xl mx-auto",children:"Lista de pacientes registrados en el sistema para evaluaciones psicométricas"})]})}),e.jsx("div",{className:"mb-8 bg-white p-4 rounded-lg shadow-sm",children:e.jsxs("div",{className:"flex flex-col md:flex-row gap-4",children:[e.jsxs("div",{className:"relative flex-grow",children:[e.jsx("div",{className:"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none",children:e.jsx(l,{className:"text-blue-400"})}),e.jsx("input",{type:"text",className:"block w-full pl-10 pr-3 py-2 border border-gray-200 rounded-lg leading-5 bg-white placeholder-gray-500 focus:outline-none focus:ring-blue-500 focus:border-blue-500 shadow-sm",placeholder:"Buscar paciente por nombre, apellido o documento...",onChange:e=>{const s=e.target.value;e.target.value=s,B(s)}})]}),e.jsxs("div",{className:"flex flex-wrap gap-2",children:[e.jsxs("div",{className:"relative inline-block",children:[e.jsxs("select",{className:"appearance-none pl-8 pr-4 py-2 border border-gray-200 rounded-lg bg-white focus:outline-none focus:ring-blue-500 focus:border-blue-500 shadow-sm",value:R,onChange:e=>{z(e.target.value),F(1)},children:[e.jsx("option",{value:"",children:"Todos los géneros"}),e.jsx("option",{value:"Masculino",children:"Masculino"}),e.jsx("option",{value:"Femenino",children:"Femenino"}),e.jsx("option",{value:"Otro",children:"Otro"})]}),e.jsx("div",{className:"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none",children:e.jsx(i,{className:"text-blue-400"})})]}),e.jsxs("div",{className:"relative inline-block",children:[e.jsxs("select",{className:"appearance-none pl-8 pr-4 py-2 border border-gray-200 rounded-lg bg-white focus:outline-none focus:ring-blue-500 focus:border-blue-500 shadow-sm",value:T,onChange:e=>{V(e.target.value),F(1)},children:[e.jsx("option",{value:"nombre",children:"Nombre"}),e.jsx("option",{value:"apellidos",children:"Apellidos"}),e.jsx("option",{value:"fecha_nacimiento",children:"Fecha de nacimiento"}),e.jsx("option",{value:"documento_identidad",children:"Documento"})]}),e.jsx("div",{className:"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none",children:e.jsx(i,{className:"text-blue-400"})})]}),e.jsxs("button",{className:"flex items-center px-4 py-2 border border-gray-200 rounded-lg bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-blue-500 shadow-sm",onClick:()=>{O(e=>"asc"===e?"desc":"asc"),F(1)},children:["asc"===q?e.jsx(n,{className:"mr-2 text-blue-500"}):e.jsx(o,{className:"mr-2 text-blue-500"}),e.jsx("span",{children:"Ordenar"})]}),e.jsx("button",{className:"flex items-center px-4 py-2 border border-gray-200 rounded-lg bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-blue-500 shadow-sm",onClick:()=>U("grid"===E?"table":"grid"),children:"grid"===E?e.jsxs(e.Fragment,{children:[e.jsx(c,{className:"mr-2 text-blue-500"}),e.jsx("span",{children:"Vista tabla"})]}):e.jsxs(e.Fragment,{children:[e.jsx(d,{className:"mr-2 text-blue-500"}),e.jsx("span",{children:"Vista tarjetas"})]})}),(M||R||"nombre"!==T||"asc"!==q)&&e.jsx("button",{className:"px-4 py-2 border border-gray-200 rounded-lg bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-blue-500 shadow-sm",onClick:G,children:"Limpiar filtros"})]})]})}),e.jsxs(g,{className:"shadow-md border-0 rounded-xl overflow-hidden",children:[e.jsx(b,{className:"bg-gradient-to-r from-blue-900 to-indigo-950 text-white border-0",children:e.jsxs("div",{className:"flex items-center justify-center",children:[e.jsx("h2",{className:"text-xl font-semibold",children:"Lista de Pacientes"}),e.jsx("span",{className:"ml-3 bg-white text-blue-600 rounded-full px-3 py-1 text-sm font-medium",children:_})]})}),e.jsxs(j,{className:"p-6",children:[C?e.jsxs("div",{className:"text-center py-12",children:[e.jsx(x,{className:"animate-spin text-blue-500 text-4xl mx-auto mb-4"}),e.jsx("p",{className:"text-gray-600",children:"Cargando pacientes..."})]}):0===N.length?e.jsx("div",{className:"text-center py-12",children:M||R?e.jsxs("div",{children:[e.jsx("p",{className:"text-gray-500 mb-2",children:"No se encontraron pacientes que coincidan con los filtros aplicados"}),e.jsx("button",{className:"text-blue-500 hover:text-blue-700 font-medium",onClick:G,children:"Limpiar filtros"})]}):e.jsx("p",{className:"text-gray-500",children:"No hay pacientes registrados"})}):"grid"===E?e.jsx("div",{className:"grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6 p-2",children:N.map(s=>e.jsx(y,{patient:s,onClick:()=>{}},s.id))}):e.jsx(w,{patients:N,loading:C,onSelectPatient:J}),I>1&&e.jsx("div",{className:"flex items-center justify-center mt-6",children:e.jsxs("nav",{className:"relative z-0 inline-flex rounded-md shadow-sm -space-x-px","aria-label":"Pagination",children:[e.jsxs("button",{onClick:()=>Q($-1),disabled:1===$,className:"relative inline-flex items-center px-2 py-2 rounded-l-md border border-gray-300 bg-white text-sm font-medium "+(1===$?"text-gray-300 cursor-not-allowed":"text-gray-500 hover:bg-gray-50"),children:[e.jsx("span",{className:"sr-only",children:"Anterior"}),e.jsx(m,{className:"h-5 w-5","aria-hidden":"true"})]}),[...Array(I)].map((s,a)=>{const t=a+1;return 1===t||t===I||t>=$-1&&t<=$+1?e.jsx("button",{onClick:()=>Q(t),className:"relative inline-flex items-center px-4 py-2 border text-sm font-medium "+($===t?"z-10 bg-blue-50 border-blue-500 text-blue-600":"bg-white border-gray-300 text-gray-500 hover:bg-gray-50"),children:t},t):2===t&&$>3||t===I-1&&$<I-2?e.jsx("span",{className:"relative inline-flex items-center px-4 py-2 border border-gray-300 bg-white text-sm font-medium text-gray-700",children:"..."},t):null}),e.jsxs("button",{onClick:()=>Q($+1),disabled:$===I,className:"relative inline-flex items-center px-2 py-2 rounded-r-md border border-gray-300 bg-white text-sm font-medium "+($===I?"text-gray-300 cursor-not-allowed":"text-gray-500 hover:bg-gray-50"),children:[e.jsx("span",{className:"sr-only",children:"Siguiente"}),e.jsx(p,{className:"h-5 w-5","aria-hidden":"true"})]})]})})]}),e.jsxs(f,{className:"bg-gray-50 border-t border-gray-100 p-4 flex justify-between items-center",children:[e.jsxs("p",{className:"text-sm text-gray-600",children:["Mostrando ",N.length," de ",s.length," pacientes"]}),e.jsxs("button",{onClick:H,className:"flex items-center text-blue-600 hover:text-blue-800 transition-colors",children:[e.jsx(u,{className:"mr-1"})," Volver arriba"]})]})]})]})};export{k as default};
