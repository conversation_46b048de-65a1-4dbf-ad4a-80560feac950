var e=(e,t,s)=>new Promise((a,r)=>{var n=e=>{try{l(s.next(e))}catch(t){r(t)}},i=e=>{try{l(s.throw(e))}catch(t){r(t)}},l=e=>e.done?a(e.value):Promise.resolve(e.value).then(n,i);l((s=s.apply(e,t)).next())});import{D as t,j as s,F as a,i as r,b as n,h as i,w as l,k as c,t as o,a as d,c as x,d as m,s as p}from"./auth-3ab59eff.js";import{r as h}from"./react-vendor-99be060c.js";import{Q as u}from"./ui-vendor-9705a4a1.js";import{P as g}from"./admin-168d579d.js";import"./utils-vendor-4d1206d7.js";const f=()=>{const{user:f,isAdmin:y,isPsicologo:j,loading:b}=t(),[N,v]=h.useState([]),[w,C]=h.useState(!1),[k,P]=h.useState("");if(b)return s.jsx("div",{className:"min-h-screen flex items-center justify-center bg-gray-50",children:s.jsxs("div",{className:"flex items-center",children:[s.jsx(a,{className:"animate-spin text-blue-500 text-2xl mr-3"}),s.jsx("span",{className:"text-gray-600",children:"Cargando..."})]})});if(!y&&!j)return s.jsx("div",{className:"min-h-screen flex items-center justify-center bg-gray-50",children:s.jsxs("div",{className:"text-center p-8 bg-white rounded-lg shadow-lg max-w-md",children:[s.jsx("div",{className:"w-16 h-16 bg-red-100 rounded-full flex items-center justify-center mx-auto mb-4",children:s.jsx(r,{className:"text-red-600 text-2xl"})}),s.jsx("h2",{className:"text-2xl font-bold text-red-600 mb-4",children:"Acceso Denegado"}),s.jsx("p",{className:"text-gray-600 mb-6",children:"Solo los administradores y psicólogos pueden gestionar pacientes."}),s.jsx("button",{onClick:()=>window.history.back(),className:"px-4 py-2 bg-gray-500 text-white rounded-lg hover:bg-gray-600 transition-colors",children:"Volver"})]})});const E=()=>e(void 0,null,function*(){C(!0);try{const{data:e,error:t}=yield p.from("pacientes").select("*").order("created_at",{ascending:!1});if(t)throw t;v(e||[])}catch(e){u.error("Error al cargar los pacientes")}finally{C(!1)}});h.useEffect(()=>{E();e(void 0,null,function*(){try{const{count:e}=yield p.from("pacientes").select("*",{count:"exact",head:!0}),{count:t}=yield p.from("resultados").select("*",{count:"exact",head:!0})}catch(e){}})},[]);const S=N.filter(e=>{var t,s,a,r;if(!k)return!0;const n=k.toLowerCase();return(null==(t=e.nombre)?void 0:t.toLowerCase().includes(n))||(null==(s=e.apellido)?void 0:s.toLowerCase().includes(n))||(null==(a=e.documento)?void 0:a.toLowerCase().includes(n))||(null==(r=e.email)?void 0:r.toLowerCase().includes(n))});return s.jsxs("div",{className:"min-h-screen bg-gray-50",children:[s.jsx(g,{title:"Gestión de Pacientes",subtitle:"Administra la información y el historial de tus pacientes registrados en la plataforma",icon:r}),s.jsx("div",{className:"bg-white shadow-sm border-b",children:s.jsx("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8",children:s.jsxs("div",{className:"flex space-x-8 overflow-x-auto",children:[s.jsxs("button",{className:"flex items-center px-4 py-4 text-sm font-medium text-gray-500 hover:text-gray-700 border-b-2 border-transparent hover:border-gray-300",children:[s.jsx(n,{className:"mr-2 text-orange-500"}),"Instituciones"]}),s.jsxs("button",{className:"flex items-center px-4 py-4 text-sm font-medium text-gray-500 hover:text-gray-700 border-b-2 border-transparent hover:border-gray-300",children:[s.jsx(i,{className:"mr-2 text-gray-500"}),"Psicólogos"]}),s.jsxs("button",{className:"flex items-center px-4 py-4 text-sm font-medium text-blue-600 border-b-2 border-blue-600",children:[s.jsx(r,{className:"mr-2 text-blue-600"}),"Pacientes"]}),s.jsxs("button",{className:"flex items-center px-4 py-4 text-sm font-medium text-gray-500 hover:text-gray-700 border-b-2 border-transparent hover:border-gray-300",children:[s.jsx(l,{className:"mr-2 text-gray-500"}),"Conversión PD→PC"]}),s.jsxs("button",{className:"flex items-center px-4 py-4 text-sm font-medium text-gray-500 hover:text-gray-700 border-b-2 border-transparent hover:border-gray-300",children:[s.jsx(c,{className:"mr-2 text-gray-500"}),"Supabase"]})]})})}),s.jsxs("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6",children:[s.jsx("div",{className:"mb-6",children:s.jsxs("div",{className:"flex items-center justify-between",children:[s.jsxs("div",{children:[s.jsx("h2",{className:"text-2xl font-bold text-gray-900",children:"Gestión de Pacientes"}),s.jsxs("p",{className:"text-gray-600 mt-1",children:["Administre los pacientes registrados en el sistema (",S.length," pacientes)"]})]}),s.jsxs("div",{className:"flex items-center space-x-3",children:[s.jsxs("div",{className:"relative",children:[s.jsx(o,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400"}),s.jsx("input",{type:"text",placeholder:"Buscar paciente...",value:k,onChange:e=>P(e.target.value),className:"pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"})]}),y&&s.jsxs("button",{onClick:()=>{u.info("Funcionalidad de crear paciente en desarrollo")},className:"flex items-center px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors",children:[s.jsx(d,{className:"mr-2"}),"Nuevo Paciente"]})]})]})}),s.jsx("div",{className:"bg-white shadow-sm rounded-lg overflow-hidden",children:s.jsx("div",{className:"overflow-x-auto",children:s.jsxs("table",{className:"min-w-full divide-y divide-gray-200",children:[s.jsx("thead",{className:"bg-blue-600 text-white",children:s.jsxs("tr",{children:[s.jsx("th",{className:"px-6 py-3 text-left text-xs font-medium uppercase tracking-wider",children:"Nombre Completo"}),s.jsx("th",{className:"px-6 py-3 text-left text-xs font-medium uppercase tracking-wider",children:"Email"}),s.jsx("th",{className:"px-6 py-3 text-left text-xs font-medium uppercase tracking-wider",children:"Documento"}),s.jsx("th",{className:"px-6 py-3 text-left text-xs font-medium uppercase tracking-wider",children:"Género"}),s.jsx("th",{className:"px-6 py-3 text-left text-xs font-medium uppercase tracking-wider",children:"Fecha de Nacimiento"}),s.jsx("th",{className:"px-6 py-3 text-left text-xs font-medium uppercase tracking-wider",children:"Nivel Educativo"}),y&&s.jsx("th",{className:"px-6 py-3 text-left text-xs font-medium uppercase tracking-wider",children:"Acciones"})]})}),s.jsx("tbody",{className:"bg-white divide-y divide-gray-200",children:w?s.jsx("tr",{children:s.jsx("td",{colSpan:y?7:6,className:"px-6 py-12 text-center",children:s.jsxs("div",{className:"flex items-center justify-center",children:[s.jsx(a,{className:"animate-spin text-blue-500 text-2xl mr-3"}),s.jsx("span",{className:"text-gray-600",children:"Cargando pacientes..."})]})})}):0===S.length?s.jsx("tr",{children:s.jsx("td",{colSpan:y?7:6,className:"px-6 py-12 text-center text-gray-500",children:0===N.length?"No hay pacientes registrados":"No se encontraron pacientes que coincidan con la búsqueda"})}):S.map(t=>{var a,r;return s.jsxs("tr",{className:"hover:bg-gray-50",children:[s.jsx("td",{className:"px-6 py-4 whitespace-nowrap",children:s.jsxs("div",{className:"flex items-center",children:[s.jsx("div",{className:"w-8 h-8 bg-blue-500 rounded-full flex items-center justify-center mr-3",children:s.jsx("span",{className:"text-white text-sm font-medium",children:null==(r=null==(a=t.nombre)?void 0:a.charAt(0))?void 0:r.toUpperCase()})}),s.jsxs("div",{children:[s.jsxs("div",{className:"text-sm font-medium text-gray-900",children:[t.nombre," ",t.apellido]}),s.jsx("div",{className:"text-sm text-gray-500",children:t.telefono&&`Tel: ${t.telefono}`})]})]})}),s.jsx("td",{className:"px-6 py-4 whitespace-nowrap text-sm text-gray-900",children:t.email||"-"}),s.jsx("td",{className:"px-6 py-4 whitespace-nowrap text-sm text-gray-900",children:s.jsx("span",{className:"px-2 py-1 text-xs font-medium bg-blue-100 text-blue-800 rounded-full",children:t.documento||"-"})}),s.jsx("td",{className:"px-6 py-4 whitespace-nowrap text-sm text-gray-900",children:s.jsx("span",{className:"px-2 py-1 text-xs font-medium rounded-full "+("masculino"===t.genero?"bg-blue-100 text-blue-800":"femenino"===t.genero?"bg-pink-100 text-pink-800":"bg-gray-100 text-gray-800"),children:t.genero?t.genero.charAt(0).toUpperCase()+t.genero.slice(1):"-"})}),s.jsx("td",{className:"px-6 py-4 whitespace-nowrap text-sm text-gray-900",children:t.fecha_nacimiento?new Date(t.fecha_nacimiento).toLocaleDateString("es-ES"):"-"}),s.jsx("td",{className:"px-6 py-4 text-sm text-gray-900 max-w-xs truncate",children:t.nivel_educativo||"-"}),y&&s.jsx("td",{className:"px-6 py-4 whitespace-nowrap text-sm font-medium",children:s.jsxs("div",{className:"flex space-x-2",children:[s.jsx("button",{onClick:()=>(e=>{u.info(`Editar paciente: ${e.nombre} ${e.apellido}`)})(t),className:"text-blue-600 hover:text-blue-900 transition-colors",title:"Editar paciente",children:s.jsx(x,{})}),s.jsx("button",{onClick:()=>(t=>e(void 0,null,function*(){if(window.confirm(`¿Está seguro de eliminar al paciente ${t.nombre} ${t.apellido}?`))try{C(!0);const{error:e}=yield p.from("pacientes").delete().eq("id",t.id);if(e)throw e;u.success("Paciente eliminado correctamente"),E()}catch(e){u.error("Error al eliminar el paciente")}finally{C(!1)}}))(t),className:"text-red-600 hover:text-red-900 transition-colors",title:"Eliminar paciente",children:s.jsx(m,{})})]})})]},t.id)})})]})})}),s.jsx("div",{className:"mt-8 text-center text-sm text-gray-500",children:"© 2025 Sistema de Gestión Psicológica - Panel de Administración"})]})]})};export{f as default};
