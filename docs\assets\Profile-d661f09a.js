var e=Object.defineProperty,s=Object.defineProperties,r=Object.getOwnPropertyDescriptors,a=Object.getOwnPropertySymbols,t=Object.prototype.hasOwnProperty,o=Object.prototype.propertyIsEnumerable,n=(s,r,a)=>r in s?e(s,r,{enumerable:!0,configurable:!0,writable:!0,value:a}):s[r]=a,l=(e,s)=>{for(var r in s||(s={}))t.call(s,r)&&n(e,r,s[r]);if(a)for(var r of a(s))o.call(s,r)&&n(e,r,s[r]);return e},i=(e,a)=>s(e,r(a)),d=(e,s,r)=>new Promise((a,t)=>{var o=e=>{try{l(r.next(e))}catch(s){t(s)}},n=e=>{try{l(r.throw(e))}catch(s){t(s)}},l=e=>e.done?a(e.value):Promise.resolve(e.value).then(o,n);l((r=r.apply(e,s)).next())});import{s as c,j as m,F as u,H as x,B as h,q as f,J as b,K as p}from"./auth-3ab59eff.js";import{r as w}from"./react-vendor-99be060c.js";import{Q as g}from"./ui-vendor-9705a4a1.js";import"./utils-vendor-4d1206d7.js";class y{static getCurrentUser(){return d(this,null,function*(){try{const{data:{session:e}}=yield c.auth.getSession();if(!(null==e?void 0:e.user))return null;const{data:s,error:r}=yield c.from("usuarios").select("*").eq("id",e.user.id).single();return r?e.user:l(l({},e.user),s)}catch(e){return null}})}static login(e){return d(this,arguments,function*({identifier:e,password:s}){try{let r;if(e.includes("@")){const{data:a,error:t}=yield c.auth.signInWithPassword({email:e,password:s});if(t)throw t;r=a}else{const{data:a,error:t}=yield c.from("usuarios").select("id").eq("documento",e).single();if(t||!a)throw new Error("Usuario no encontrado con ese documento");const{data:o,error:n}=yield c.auth.admin.getUserById(a.id);if(n||!o.user)throw new Error("Error al obtener datos de autenticación");const{data:l,error:i}=yield c.auth.signInWithPassword({email:o.user.email,password:s});if(i)throw i;r=l}const a=yield this.getCurrentUser();return a&&(yield c.from("usuarios").update({ultimo_acceso:(new Date).toISOString()}).eq("id",a.id)),g.success("Inicio de sesión exitoso"),{success:!0,user:a,session:r.session}}catch(r){return g.error(r.message||"Error al iniciar sesión"),{success:!1,message:r.message}}})}static register(e){return d(this,null,function*(){try{const{email:s,password:r,nombre:a,apellido:t,documento:o,rol:n="estudiante"}=e;if(o){const{data:e}=yield c.from("usuarios").select("id").eq("documento",o).single();if(e)throw new Error("Ya existe un usuario con ese documento")}const{data:l,error:i}=yield c.auth.signUp({email:s,password:r,options:{data:{nombre:a,apellido:t}}});if(i)throw i;const{error:d}=yield c.from("usuarios").insert([{id:l.user.id,documento:o,nombre:a,apellido:t,rol:n,activo:!0,fecha_creacion:(new Date).toISOString()}]);if(d)throw d;return g.success("Registro exitoso"),{success:!0,user:l.user,message:"Registro exitoso. Revisa tu email para confirmar tu cuenta."}}catch(s){return g.error(s.message||"Error al registrar usuario"),{success:!1,message:s.message}}})}static logout(){return d(this,null,function*(){try{const{error:e}=yield c.auth.signOut();if(e)throw e;return g.info("Sesión cerrada correctamente"),{success:!0}}catch(e){return g.error(e.message||"Error al cerrar sesión"),{success:!1,message:e.message}}})}static resetPassword(e){return d(this,null,function*(){try{const{error:s}=yield c.auth.resetPasswordForEmail(e,{redirectTo:`${window.location.origin}/reset-password`});if(s)throw s;return g.success("Se ha enviado un enlace para restablecer la contraseña"),{success:!0}}catch(s){return g.error(s.message||"Error al restablecer contraseña"),{success:!1,message:s.message}}})}static updatePassword(e){return d(this,null,function*(){try{const{error:s}=yield c.auth.updateUser({password:e});if(s)throw s;return g.success("Contraseña actualizada correctamente"),{success:!0}}catch(s){return g.error(s.message||"Error al actualizar contraseña"),{success:!1,message:s.message}}})}static hasRole(e,s){var r;if(!e)return!1;return((null==(r=e.rol)?void 0:r.toLowerCase())||"")===s.toLowerCase()}static isAdmin(e){return this.hasRole(e,"administrador")}static isPsychologist(e){return this.hasRole(e,"psicologo")}static isStudent(e){return this.hasRole(e,"estudiante")}}const j=()=>{var e;const[s,r]=w.useState(null),[a,t]=w.useState({nombre:"",apellidos:"",telefono:""}),[o,n]=w.useState(!0),[j,v]=w.useState(!1),[N,P]=w.useState(!1),[C,E]=w.useState({currentPassword:"",newPassword:"",confirmPassword:""}),[S,k]=w.useState({});w.useEffect(()=>{d(void 0,null,function*(){n(!0);try{const e=yield y.getCurrentUser();if(r(e),e){const{data:s,error:r}=yield c.from("perfiles").select("*").eq("user_id",e.id).single();s&&!r&&t({nombre:s.nombre||"",apellidos:s.apellidos||"",telefono:s.telefono||""})}}catch(e){g.error("No se pudieron cargar los datos del perfil")}finally{n(!1)}})},[]);const O=e=>{const{name:s,value:r}=e.target;t(i(l({},a),{[s]:r}))},F=e=>{const{name:s,value:r}=e.target;E(i(l({},C),{[s]:r}))};return o?m.jsxs("div",{className:"flex justify-center items-center p-12",children:[m.jsx(u,{className:"animate-spin text-blue-600 text-3xl"}),m.jsx("span",{className:"ml-2",children:"Cargando perfil..."})]}):m.jsxs("div",{className:"max-w-3xl mx-auto",children:[m.jsxs("header",{className:"mb-6",children:[m.jsx("h1",{className:"text-2xl font-bold text-gray-900",children:"Mi Perfil"}),m.jsx("p",{className:"mt-1 text-sm text-gray-600",children:"Administra tu información personal y de acceso"})]}),m.jsxs("div",{className:"bg-white shadow rounded-lg overflow-hidden",children:[m.jsxs("div",{className:"p-6 border-b border-gray-200",children:[m.jsxs("div",{className:"flex items-center mb-6",children:[m.jsx("div",{className:"h-16 w-16 rounded-full bg-blue-600 flex items-center justify-center text-white text-xl font-semibold",children:(null==(e=null==s?void 0:s.email)?void 0:e.charAt(0).toUpperCase())||m.jsx(x,{className:"h-8 w-8"})}),m.jsxs("div",{className:"ml-4",children:[m.jsx("h2",{className:"text-lg font-medium text-gray-900",children:a.nombre?`${a.nombre} ${a.apellidos}`:"Usuario"}),m.jsx("p",{className:"text-sm text-gray-500",children:null==s?void 0:s.email})]})]}),m.jsxs("form",{onSubmit:e=>d(void 0,null,function*(){if(e.preventDefault(),(()=>{const e={};return a.nombre&&a.nombre.length<2&&(e.nombre="El nombre debe tener al menos 2 caracteres"),a.apellidos&&a.apellidos.length<2&&(e.apellidos="Los apellidos deben tener al menos 2 caracteres"),k(e),0===Object.keys(e).length})()){v(!0);try{if(!s)throw new Error("No hay usuario autenticado");const{error:e}=yield c.from("perfiles").upsert({user_id:s.id,nombre:a.nombre,apellidos:a.apellidos,telefono:a.telefono,updated_at:new Date});if(e)throw e;g.success("Perfil actualizado correctamente")}catch(r){g.error("Error al guardar los cambios del perfil")}finally{v(!1)}}}),children:[m.jsxs("div",{className:"grid grid-cols-1 gap-y-6 gap-x-4 sm:grid-cols-6",children:[m.jsxs("div",{className:"sm:col-span-3",children:[m.jsx("label",{htmlFor:"nombre",className:"block text-sm font-medium text-gray-700",children:"Nombre"}),m.jsxs("div",{className:"mt-1 relative",children:[m.jsx("input",{type:"text",name:"nombre",id:"nombre",value:a.nombre,onChange:O,className:"block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 sm:text-sm "+(S.nombre?"border-red-500":"")}),S.nombre&&m.jsx("p",{className:"mt-1 text-sm text-red-600",children:S.nombre})]})]}),m.jsxs("div",{className:"sm:col-span-3",children:[m.jsx("label",{htmlFor:"apellidos",className:"block text-sm font-medium text-gray-700",children:"Apellidos"}),m.jsxs("div",{className:"mt-1",children:[m.jsx("input",{type:"text",name:"apellidos",id:"apellidos",value:a.apellidos,onChange:O,className:"block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 sm:text-sm "+(S.apellidos?"border-red-500":"")}),S.apellidos&&m.jsx("p",{className:"mt-1 text-sm text-red-600",children:S.apellidos})]})]}),m.jsxs("div",{className:"sm:col-span-6",children:[m.jsx("label",{htmlFor:"email",className:"block text-sm font-medium text-gray-700",children:"Email"}),m.jsxs("div",{className:"mt-1 relative rounded-md shadow-sm",children:[m.jsx("div",{className:"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none",children:m.jsx(h,{className:"h-5 w-5 text-gray-400"})}),m.jsx("input",{type:"text",name:"email",id:"email",disabled:!0,value:(null==s?void 0:s.email)||"",className:"block w-full pl-10 pr-3 py-2 border border-gray-300 bg-gray-50 rounded-md leading-5 text-gray-500 sm:text-sm"})]}),m.jsx("p",{className:"mt-1 text-xs text-gray-500",children:"El email no se puede modificar"})]}),m.jsxs("div",{className:"sm:col-span-4",children:[m.jsx("label",{htmlFor:"telefono",className:"block text-sm font-medium text-gray-700",children:"Teléfono"}),m.jsxs("div",{className:"mt-1",children:[m.jsx("input",{type:"text",name:"telefono",id:"telefono",value:a.telefono,onChange:O,className:"block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 sm:text-sm "+(S.telefono?"border-red-500":"")}),S.telefono&&m.jsx("p",{className:"mt-1 text-sm text-red-600",children:S.telefono})]})]})]}),m.jsx("div",{className:"mt-6",children:m.jsx("button",{type:"submit",disabled:j,className:`inline-flex justify-center py-2 px-4 border border-transparent shadow-sm text-sm font-medium rounded-md text-white ${j?"bg-blue-400":"bg-blue-600 hover:bg-blue-700"} focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500`,children:j?m.jsxs(m.Fragment,{children:[m.jsx(u,{className:"animate-spin mr-2 h-4 w-4"}),"Guardando..."]}):m.jsxs(m.Fragment,{children:[m.jsx(f,{className:"mr-2 h-4 w-4"}),"Guardar Cambios"]})})})]})]}),m.jsxs("div",{className:"p-6",children:[m.jsxs("div",{className:"flex items-center justify-between mb-4",children:[m.jsx("h3",{className:"text-lg font-medium text-gray-900",children:"Seguridad"}),m.jsx("button",{type:"button",onClick:()=>P(!N),className:"text-sm font-medium text-blue-600 hover:text-blue-500",children:N?"Cancelar":"Cambiar contraseña"})]}),N?m.jsx("form",{onSubmit:e=>d(void 0,null,function*(){if(e.preventDefault(),(()=>{const e={};return C.currentPassword||(e.currentPassword="La contraseña actual es requerida"),C.newPassword?C.newPassword.length<6&&(e.newPassword="La nueva contraseña debe tener al menos 6 caracteres"):e.newPassword="La nueva contraseña es requerida",C.newPassword!==C.confirmPassword&&(e.confirmPassword="Las contraseñas no coinciden"),k(e),0===Object.keys(e).length})()){v(!0);try{const{error:e}=yield c.auth.updateUser({password:C.newPassword});if(e)throw e;E({currentPassword:"",newPassword:"",confirmPassword:""}),P(!1),g.success("Contraseña actualizada correctamente")}catch(s){g.error("Error al cambiar la contraseña")}finally{v(!1)}}}),children:m.jsxs("div",{className:"space-y-4",children:[m.jsxs("div",{children:[m.jsx("label",{htmlFor:"currentPassword",className:"block text-sm font-medium text-gray-700",children:"Contraseña actual"}),m.jsxs("div",{className:"mt-1 relative rounded-md shadow-sm",children:[m.jsx("div",{className:"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none",children:m.jsx(b,{className:"h-5 w-5 text-gray-400"})}),m.jsx("input",{type:"password",name:"currentPassword",id:"currentPassword",value:C.currentPassword,onChange:F,className:`block w-full pl-10 pr-3 py-2 border ${S.currentPassword?"border-red-500":"border-gray-300"} rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm`}),S.currentPassword&&m.jsx("p",{className:"mt-1 text-sm text-red-600",children:S.currentPassword})]})]}),m.jsxs("div",{children:[m.jsx("label",{htmlFor:"newPassword",className:"block text-sm font-medium text-gray-700",children:"Nueva contraseña"}),m.jsxs("div",{className:"mt-1 relative rounded-md shadow-sm",children:[m.jsx("div",{className:"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none",children:m.jsx(b,{className:"h-5 w-5 text-gray-400"})}),m.jsx("input",{type:"password",name:"newPassword",id:"newPassword",value:C.newPassword,onChange:F,className:`block w-full pl-10 pr-3 py-2 border ${S.newPassword?"border-red-500":"border-gray-300"} rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm`}),S.newPassword&&m.jsx("p",{className:"mt-1 text-sm text-red-600",children:S.newPassword})]})]}),m.jsxs("div",{children:[m.jsx("label",{htmlFor:"confirmPassword",className:"block text-sm font-medium text-gray-700",children:"Confirmar nueva contraseña"}),m.jsxs("div",{className:"mt-1 relative rounded-md shadow-sm",children:[m.jsx("div",{className:"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none",children:m.jsx(b,{className:"h-5 w-5 text-gray-400"})}),m.jsx("input",{type:"password",name:"confirmPassword",id:"confirmPassword",value:C.confirmPassword,onChange:F,className:`block w-full pl-10 pr-3 py-2 border ${S.confirmPassword?"border-red-500":"border-gray-300"} rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm`}),S.confirmPassword&&m.jsx("p",{className:"mt-1 text-sm text-red-600",children:S.confirmPassword})]})]}),m.jsx("div",{className:"rounded-md bg-yellow-50 p-4",children:m.jsxs("div",{className:"flex",children:[m.jsx("div",{className:"flex-shrink-0",children:m.jsx(p,{className:"h-5 w-5 text-yellow-400"})}),m.jsxs("div",{className:"ml-3",children:[m.jsx("h3",{className:"text-sm font-medium text-yellow-800",children:"Información importante"}),m.jsx("div",{className:"mt-2 text-sm text-yellow-700",children:m.jsx("p",{children:"Por seguridad, cierre todas las sesiones activas después de cambiar su contraseña."})})]})]})}),m.jsx("div",{className:"mt-4",children:m.jsx("button",{type:"submit",disabled:j,className:`inline-flex justify-center py-2 px-4 border border-transparent shadow-sm text-sm font-medium rounded-md text-white ${j?"bg-blue-400":"bg-blue-600 hover:bg-blue-700"} focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500`,children:j?m.jsxs(m.Fragment,{children:[m.jsx(u,{className:"animate-spin mr-2 h-4 w-4"}),"Actualizando..."]}):"Actualizar contraseña"})})]})}):m.jsx("p",{className:"text-sm text-gray-600",children:"Es recomendable cambiar su contraseña regularmente para mantener la seguridad de su cuenta."})]})]})]})};export{j as default};
