import{j as s}from"./auth-3ab59eff.js";import"./react-vendor-99be060c.js";import{C as e,b as t,a}from"./admin-168d579d.js";import"./ui-vendor-9705a4a1.js";import"./utils-vendor-4d1206d7.js";const r=()=>s.jsxs("div",{className:"container mx-auto py-6",children:[s.jsx("h1",{className:"text-2xl font-bold text-gray-800 mb-6",children:"Reportes de Estudiantes"}),s.jsxs(e,{children:[s.jsx(t,{children:s.jsx("h2",{className:"text-lg font-medium",children:"Panel de Reportes"})}),s.jsx(a,{children:s.jsx("p",{className:"text-gray-600",children:"Esta sección permitirá visualizar reportes y estadísticas de los estudiantes asignados (componente en desarrollo)."})})]})]});export{r as default};
