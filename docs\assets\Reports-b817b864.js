var e=(e,t,s)=>new Promise((a,r)=>{var l=e=>{try{i(s.next(e))}catch(t){r(t)}},n=e=>{try{i(s.throw(e))}catch(t){r(t)}},i=e=>e.done?a(e.value):Promise.resolve(e.value).then(l,n);i((s=s.apply(e,t)).next())});import{s as t,j as s,l as a}from"./auth-3ab59eff.js";import{a as r,r as l}from"./react-vendor-99be060c.js";import{P as n,C as i,b as c,a as o,B as d}from"./admin-168d579d.js";import{Q as x}from"./ui-vendor-9705a4a1.js";import{B as m}from"./index-23a57a03.js";import"./utils-vendor-4d1206d7.js";const u=()=>{const u=r(),[p,h]=l.useState([]),[j,f]=l.useState(!0),[g,b]=l.useState(""),[N,v]=l.useState(""),[y,w]=l.useState([]),[C,P]=l.useState(new Set);l.useEffect(()=>{D(),k()},[]);const k=()=>e(void 0,null,function*(){try{const{data:e,error:s}=yield t.from("aptitudes").select("*").order("codigo");if(s)throw s;w(e||[])}catch(e){x.error("Error al cargar las aptitudes")}}),D=()=>e(void 0,null,function*(){try{f(!0);const{data:e,error:s}=yield t.from("resultados").select("\n          id,\n          puntaje_directo,\n          percentil,\n          errores,\n          tiempo_segundos,\n          concentracion,\n          created_at,\n          pacientes:paciente_id (\n            id,\n            nombre,\n            apellido,\n            documento,\n            genero\n          ),\n          aptitudes:aptitud_id (\n            codigo,\n            nombre,\n            descripcion\n          )\n        ").order("created_at",{ascending:!1});if(s)return void x.error("Error al cargar los resultados");const a=e.reduce((e,t)=>{var s,a,r;const l=null==(s=t.pacientes)?void 0:s.id;if(!l)return e;e[l]||(e[l]={paciente:t.pacientes,resultados:[],fechaUltimaEvaluacion:t.created_at});const n=t.percentil?m.obtenerInterpretacionPC(t.percentil):{nivel:"Pendiente",color:"text-gray-600",bg:"bg-gray-100"};return e[l].resultados.push({id:t.id,test:(null==(a=t.aptitudes)?void 0:a.codigo)||"N/A",testName:(null==(r=t.aptitudes)?void 0:r.nombre)||"Test Desconocido",puntajePD:t.puntaje_directo||0,puntajePC:t.percentil||"N/A",errores:t.errores||0,tiempo:t.tiempo_segundos?`${Math.round(t.tiempo_segundos/60)}:${String(t.tiempo_segundos%60).padStart(2,"0")}`:"N/A",concentracion:t.concentracion?`${t.concentracion.toFixed(1)}%`:"N/A",fecha:new Date(t.created_at).toLocaleDateString("es-ES"),interpretacion:n.nivel,interpretacionColor:n.color,interpretacionBg:n.bg}),new Date(t.created_at)>new Date(e[l].fechaUltimaEvaluacion)&&(e[l].fechaUltimaEvaluacion=t.created_at),e},{}),r=Object.values(a).sort((e,t)=>new Date(t.fechaUltimaEvaluacion)-new Date(e.fechaUltimaEvaluacion));h(r),f(!1)}catch(e){x.error("Error al cargar los resultados"),f(!1)}}),E=p.filter(e=>{const t=e.paciente;return!!(!g||(null==t?void 0:t.nombre)&&t.nombre.toLowerCase().includes(g.toLowerCase())||(null==t?void 0:t.apellido)&&t.apellido.toLowerCase().includes(g.toLowerCase())||(null==t?void 0:t.documento)&&t.documento.toLowerCase().includes(g.toLowerCase()))&&(!N||e.resultados.some(e=>e.test===N))}),S=e=>({V:"fas fa-comments",E:"fas fa-cube",A:"fas fa-eye",R:"fas fa-puzzle-piece",N:"fas fa-calculator",M:"fas fa-cogs",O:"fas fa-spell-check"}[e]||"fas fa-clipboard-list");return s.jsxs("div",{children:[s.jsx(n,{title:"Resultados de Tests",subtitle:"Visualiza todos los resultados de tests aplicados a los pacientes",icon:a}),s.jsxs("div",{className:"container mx-auto px-4 py-8",children:[s.jsxs(i,{className:"mb-6",children:[s.jsx(c,{children:s.jsxs("h2",{className:"text-lg font-semibold text-gray-800",children:[s.jsx("i",{className:"fas fa-filter mr-2 text-blue-600"}),"Filtros de Búsqueda"]})}),s.jsx(o,{children:s.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[s.jsxs("div",{children:[s.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Buscar Paciente"}),s.jsxs("div",{className:"relative",children:[s.jsx("input",{type:"text",placeholder:"Nombre, apellido o documento...",value:g,onChange:e=>b(e.target.value),className:"w-full px-4 py-2 pl-10 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"}),s.jsx("i",{className:"fas fa-search absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400"})]})]}),s.jsxs("div",{children:[s.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Filtrar por Test"}),s.jsxs("select",{value:N,onChange:e=>v(e.target.value),className:"w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent",children:[s.jsx("option",{value:"",children:"Todos los tests"}),y.map(e=>s.jsxs("option",{value:e.codigo,children:[e.codigo," - ",e.nombre]},e.id))]})]})]})})]}),s.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-4 gap-4 mb-6",children:[s.jsx(i,{children:s.jsxs(o,{className:"text-center",children:[s.jsx("div",{className:"text-2xl font-bold text-blue-600",children:p.reduce((e,t)=>e+t.resultados.length,0)}),s.jsx("div",{className:"text-sm text-gray-600",children:"Total Resultados"})]})}),s.jsx(i,{children:s.jsxs(o,{className:"text-center",children:[s.jsx("div",{className:"text-2xl font-bold text-green-600",children:p.length}),s.jsx("div",{className:"text-sm text-gray-600",children:"Pacientes Evaluados"})]})}),s.jsx(i,{children:s.jsxs(o,{className:"text-center",children:[s.jsx("div",{className:"text-2xl font-bold text-purple-600",children:y.length}),s.jsx("div",{className:"text-sm text-gray-600",children:"Tests Disponibles"})]})}),s.jsx(i,{children:s.jsxs(o,{className:"text-center",children:[s.jsx("div",{className:"text-2xl font-bold text-orange-600",children:p.length>0?Math.round(p.reduce((e,t)=>e+t.resultados.reduce((e,t)=>e+(t.puntajePD||0),0),0)/p.reduce((e,t)=>e+t.resultados.length,0)):0}),s.jsx("div",{className:"text-sm text-gray-600",children:"Promedio PD"})]})})]}),s.jsxs("div",{className:"flex justify-between items-center mb-6",children:[s.jsxs("div",{children:[s.jsxs("h2",{className:"text-2xl font-bold text-blue-800",children:[s.jsx("i",{className:"fas fa-chart-line mr-3 text-blue-600"}),"Resultados Detallados"]}),s.jsxs("p",{className:"text-gray-600 mt-1",children:[E.length," paciente",1!==E.length?"s":""," con resultados"]})]}),E.length>0&&s.jsxs("div",{className:"flex items-center space-x-3",children:[s.jsxs("div",{className:"text-sm text-gray-500 mr-2",children:[C.size," de ",E.length," expandidos"]}),s.jsxs(d,{onClick:()=>{const e=new Set(E.map(e=>e.paciente.id));P(e)},variant:"outline",size:"sm",className:"text-green-600 border-green-300 hover:bg-green-50",children:[s.jsx("i",{className:"fas fa-expand-arrows-alt mr-2"}),"Expandir Todo"]}),s.jsxs(d,{onClick:()=>{P(new Set)},variant:"outline",size:"sm",className:"text-orange-600 border-orange-300 hover:bg-orange-50",children:[s.jsx("i",{className:"fas fa-compress-arrows-alt mr-2"}),"Contraer Todo"]})]})]}),j?s.jsxs("div",{className:"py-16 text-center",children:[s.jsx("div",{className:"animate-spin rounded-full h-12 w-12 border-b-2 border-blue-500 mx-auto mb-4"}),s.jsx("p",{className:"text-gray-500",children:"Cargando resultados..."})]}):s.jsx(s.Fragment,{children:0===E.length?s.jsx(i,{children:s.jsx(o,{children:s.jsxs("div",{className:"py-8 text-center",children:[s.jsx("i",{className:"fas fa-clipboard-list text-4xl text-gray-300 mb-4"}),s.jsx("p",{className:"text-gray-500",children:"No hay resultados de tests disponibles."}),s.jsx("p",{className:"text-sm text-gray-400 mt-2",children:"Los resultados aparecerán aquí una vez que se completen los tests."})]})})}):s.jsx("div",{className:"space-y-4",children:E.map((e,t)=>{var a,r,l,n,d;const x=C.has(e.paciente.id),m="femenino"===(null==(a=e.paciente)?void 0:a.genero),p=m?"bg-gradient-to-r from-pink-500 to-pink-600 border-b border-pink-300":"bg-gradient-to-r from-blue-500 to-blue-600 border-b border-blue-300",h=m?"border-pink-200":"border-blue-200";return s.jsxs(i,{className:`accordion-card overflow-hidden shadow-lg border ${h}`,children:[s.jsx(c,{className:`accordion-header ${p} cursor-pointer`,onClick:()=>(e=>{const t=new Set(C);t.has(e)?t.delete(e):t.add(e),P(t)})(e.paciente.id),children:s.jsxs("div",{className:"flex items-center justify-between",children:[s.jsxs("div",{className:"flex items-center",children:[s.jsx("button",{className:"mr-3 text-white transition-colors "+(m?"hover:text-pink-100":"hover:text-blue-100"),children:s.jsx("i",{className:`fas chevron-icon ${x?"fa-chevron-down chevron-expanded":"fa-chevron-right"} text-lg`})}),s.jsx("div",{className:"w-12 h-12 rounded-full flex items-center justify-center text-white text-lg font-bold mr-4 shadow-lg "+(null==(r=e.paciente)||r.genero,"bg-white bg-opacity-20 border-2 border-white border-opacity-30"),children:s.jsx("i",{className:"fas "+("masculino"===(null==(l=e.paciente)?void 0:l.genero)?"fa-mars text-blue-200":"fa-venus text-pink-100")})}),s.jsxs("div",{children:[s.jsxs("h3",{className:"text-lg font-bold text-white",children:[null==(n=e.paciente)?void 0:n.nombre," ",null==(d=e.paciente)?void 0:d.apellido,x&&s.jsx("i",{className:"fas fa-eye ml-2 text-sm "+(m?"text-pink-200":"text-blue-200")})]}),s.jsxs("p",{className:"text-sm "+(m?"text-pink-100":"text-blue-100"),children:[s.jsx("i",{className:"fas fa-clipboard-check mr-1"}),e.resultados.length," test",1!==e.resultados.length?"s":""," completado",1!==e.resultados.length?"s":"",!x&&s.jsx("span",{className:"ml-2 text-xs "+(m?"text-pink-200":"text-blue-200"),children:"• Haz clic para expandir"})]})]})]}),s.jsxs("div",{className:"flex items-center space-x-4",children:[s.jsxs("div",{className:"text-right",children:[s.jsx("p",{className:"text-xs "+(m?"text-pink-100":"text-blue-100"),children:"Última evaluación"}),s.jsx("p",{className:"text-white font-semibold text-sm",children:new Date(e.fechaUltimaEvaluacion).toLocaleDateString("es-ES")})]}),s.jsxs("button",{onClick:t=>{t.preventDefault(),t.stopPropagation(),u(`/admin/informe-completo/${e.paciente.id}`)},className:"bg-white text-blue-600 hover:bg-blue-50 border border-white shadow-lg px-3 py-1.5 text-xs rounded-md inline-flex items-center justify-center font-medium focus:outline-none focus:ring-2 focus:ring-offset-2 transition-colors",children:[s.jsx("i",{className:"fas fa-file-alt mr-2"}),"Ver Informe Completo"]})]})]})}),!x&&s.jsx("div",{className:"px-6 py-3 bg-blue-25 border-b border-blue-100",children:s.jsxs("div",{className:"flex items-center justify-between text-sm",children:[s.jsxs("div",{className:"flex items-center space-x-4",children:[s.jsxs("span",{className:"text-gray-600",children:[s.jsx("i",{className:"fas fa-chart-bar mr-1 text-blue-500"}),"Promedio PC: ",s.jsx("span",{className:"font-semibold text-blue-600",children:Math.round(e.resultados.reduce((e,t)=>e+("N/A"!==t.puntajePC?t.puntajePC:0),0)/e.resultados.filter(e=>"N/A"!==e.puntajePC).length)||0})]}),s.jsxs("span",{className:"text-gray-600",children:[s.jsx("i",{className:"fas fa-tasks mr-1 text-green-500"}),"Tests: ",s.jsx("span",{className:"font-semibold text-green-600",children:e.resultados.map(e=>e.test).join(", ")})]})]}),s.jsx("span",{className:"text-xs text-gray-500 italic",children:"Haz clic para ver detalles"})]})}),s.jsx("div",{className:"accordion-content overflow-hidden "+(x?"max-h-screen opacity-100 slide-down":"max-h-0 opacity-0"),children:s.jsx(o,{className:"p-0",children:s.jsx("div",{className:"overflow-x-auto",children:s.jsxs("table",{className:"w-full",children:[s.jsx("thead",{className:"bg-blue-50 border-b border-blue-200",children:s.jsxs("tr",{children:[s.jsx("th",{className:"px-4 py-3 text-center text-xs font-medium text-blue-700 uppercase tracking-wider",children:"Test"}),s.jsx("th",{className:"px-4 py-3 text-center text-xs font-medium text-blue-700 uppercase tracking-wider",children:"Puntaje PD"}),s.jsx("th",{className:"px-4 py-3 text-center text-xs font-medium text-blue-700 uppercase tracking-wider",children:"Puntaje PC"}),s.jsx("th",{className:"px-4 py-3 text-center text-xs font-medium text-blue-700 uppercase tracking-wider",children:"Errores"}),s.jsx("th",{className:"px-4 py-3 text-center text-xs font-medium text-blue-700 uppercase tracking-wider",children:"Tiempo"}),s.jsx("th",{className:"px-4 py-3 text-center text-xs font-medium text-blue-700 uppercase tracking-wider",children:"Fecha Test"})]})}),s.jsx("tbody",{className:"bg-white divide-y divide-gray-200",children:(j=e.resultados,N?j.filter(e=>e.test===N):j).map((e,t)=>{return s.jsxs("tr",{className:t%2==0?"bg-white":"bg-gray-50",children:[s.jsx("td",{className:"px-4 py-4 text-center",children:s.jsxs("div",{className:"flex items-center justify-center",children:[s.jsx("div",{className:`w-8 h-8 rounded-full bg-gray-100 flex items-center justify-center mr-2 ${a=e.test,{V:"text-blue-600",E:"text-indigo-600",A:"text-red-600",R:"text-amber-600",N:"text-teal-600",M:"text-slate-600",O:"text-green-600"}[a]||"text-gray-600"}`,children:s.jsx("i",{className:S(e.test)})}),s.jsxs("div",{className:"text-left",children:[s.jsx("div",{className:"text-sm font-medium text-gray-900",children:e.test}),s.jsx("div",{className:"text-xs text-gray-500",children:e.testName})]})]})}),s.jsx("td",{className:"px-4 py-4 text-center",children:s.jsx("span",{className:"text-lg font-bold text-orange-600 bg-orange-100 px-3 py-1 rounded-full",children:e.puntajePD})}),s.jsx("td",{className:"px-4 py-4 text-center",children:"N/A"!==e.puntajePC?s.jsxs("div",{className:"flex flex-col items-center",children:[s.jsx("span",{className:"text-lg font-bold text-blue-600 bg-blue-100 px-3 py-1 rounded-full mb-1",children:e.puntajePC}),s.jsx("span",{className:`text-xs px-2 py-1 rounded-full ${e.interpretacionBg} ${e.interpretacionColor}`,children:e.interpretacion})]}):s.jsx("span",{className:"text-gray-400 text-sm",children:"Pendiente"})}),s.jsx("td",{className:"px-4 py-4 text-center",children:s.jsx("span",{className:"text-sm font-medium text-gray-700",children:e.errores})}),s.jsx("td",{className:"px-4 py-4 text-center",children:s.jsx("span",{className:"text-sm font-medium text-gray-700",children:e.tiempo})}),s.jsx("td",{className:"px-4 py-4 text-center",children:s.jsx("span",{className:"text-sm text-gray-500",children:e.fecha})})]},e.id);var a})})]})})})})]},e.paciente.id);var j})})})]})]})};export{u as default};
