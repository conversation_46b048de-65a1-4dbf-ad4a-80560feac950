import{j as e,E as t,s}from"./auth-3ab59eff.js";import{r as a,L as r}from"./react-vendor-99be060c.js";import{P as n,C as l,a as c,b as i,B as d}from"./admin-168d579d.js";import{u as o}from"./useToast-f53e3f80.js";import{B as x}from"./index-23a57a03.js";import"./ui-vendor-9705a4a1.js";import"./utils-vendor-4d1206d7.js";const m=()=>{const[m,p]=a.useState([]),[u,h]=a.useState(!0),{showToast:f}=o();a.useEffect(()=>{var e,t,a;e=void 0,t=null,a=function*(){try{h(!0);const{data:e,error:t}=yield s.from("resultados").select("\n            id,\n            puntaje_directo,\n            percentil,\n            errores,\n            tiempo_segundos,\n            concentracion,\n\n            created_at,\n            pacientes:paciente_id (\n              id,\n              nombre,\n              apellido,\n              documento,\n              genero\n            ),\n            aptitudes:aptitud_id (\n              codigo,\n              nombre,\n              descripcion\n            )\n          ").order("created_at",{ascending:!1});if(t)return void f("Error al cargar los resultados","error");const a=e.reduce((e,t)=>{var s,a,r;const n=null==(s=t.pacientes)?void 0:s.id;if(!n)return e;e[n]||(e[n]={paciente:t.pacientes,resultados:[],fechaUltimaEvaluacion:t.created_at});const l=t.percentil?x.obtenerInterpretacionPC(t.percentil):{nivel:"Pendiente",color:"text-gray-600",bg:"bg-gray-100"};return e[n].resultados.push({id:t.id,test:(null==(a=t.aptitudes)?void 0:a.codigo)||"N/A",testName:(null==(r=t.aptitudes)?void 0:r.nombre)||"Test Desconocido",puntajePD:t.puntaje_directo||0,puntajePC:t.percentil||"N/A",errores:t.errores||0,tiempo:t.tiempo_segundos?`${Math.round(t.tiempo_segundos/60)}:${String(t.tiempo_segundos%60).padStart(2,"0")}`:"N/A",concentracion:t.concentracion?`${t.concentracion.toFixed(1)}%`:"N/A",fecha:new Date(t.created_at).toLocaleDateString("es-ES"),interpretacion:l.nivel,interpretacionColor:l.color,interpretacionBg:l.bg}),new Date(t.created_at)>new Date(e[n].fechaUltimaEvaluacion)&&(e[n].fechaUltimaEvaluacion=t.created_at),e},{}),r=Object.values(a).sort((e,t)=>new Date(t.fechaUltimaEvaluacion)-new Date(e.fechaUltimaEvaluacion));p(r),h(!1)}catch(e){f("Error al cargar los resultados","error"),h(!1)}},new Promise((s,r)=>{var n=e=>{try{c(a.next(e))}catch(t){r(t)}},l=e=>{try{c(a.throw(e))}catch(t){r(t)}},c=e=>e.done?s(e.value):Promise.resolve(e.value).then(n,l);c((a=a.apply(e,t)).next())})},[f]);const j=e=>({V:"fas fa-comments",E:"fas fa-cube",A:"fas fa-eye",R:"fas fa-puzzle-piece",N:"fas fa-calculator",M:"fas fa-cogs",O:"fas fa-spell-check"}[e]||"fas fa-clipboard-list");return e.jsxs("div",{children:[e.jsx(n,{title:"Resultados de Tests",subtitle:`${m.length} paciente${1!==m.length?"s":""} con resultados disponibles`,icon:t}),e.jsx("div",{className:"container mx-auto py-6",children:u?e.jsxs("div",{className:"py-16 text-center",children:[e.jsx("div",{className:"animate-spin rounded-full h-12 w-12 border-b-2 border-blue-500 mx-auto mb-4"}),e.jsx("p",{className:"text-gray-500",children:"Cargando resultados..."})]}):e.jsx(e.Fragment,{children:0===m.length?e.jsx(l,{children:e.jsx(c,{children:e.jsxs("div",{className:"py-8 text-center",children:[e.jsx("i",{className:"fas fa-clipboard-list text-4xl text-gray-300 mb-4"}),e.jsx("p",{className:"text-gray-500",children:"No hay resultados de tests disponibles."}),e.jsx("p",{className:"text-sm text-gray-400 mt-2",children:"Los resultados aparecerán aquí una vez que se completen los tests."})]})})}):e.jsx("div",{className:"space-y-6",children:m.map((t,s)=>{var a,n,o,x;return e.jsxs(l,{className:"overflow-hidden shadow-lg border border-blue-200",children:[e.jsx(i,{className:"bg-gradient-to-r from-blue-500 to-blue-600 border-b border-blue-300",children:e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsxs("div",{className:"flex items-center",children:[e.jsx("div",{className:"w-14 h-14 rounded-full flex items-center justify-center text-white text-xl font-bold mr-4 shadow-lg "+(null==(a=t.paciente)||a.genero,"bg-white bg-opacity-20 border-2 border-white border-opacity-30"),children:e.jsx("i",{className:"fas "+("masculino"===(null==(n=t.paciente)?void 0:n.genero)?"fa-mars text-blue-100":"fa-venus text-pink-200")})}),e.jsxs("div",{children:[e.jsxs("h3",{className:"text-xl font-bold text-white",children:[null==(o=t.paciente)?void 0:o.nombre," ",null==(x=t.paciente)?void 0:x.apellido]}),e.jsxs("p",{className:"text-blue-100 text-sm",children:[e.jsx("i",{className:"fas fa-clipboard-check mr-1"}),t.resultados.length," test",1!==t.resultados.length?"s":""," completado",1!==t.resultados.length?"s":""]})]})]}),e.jsxs("div",{className:"flex items-center space-x-4",children:[e.jsxs("div",{className:"text-right",children:[e.jsx("p",{className:"text-blue-100 text-sm",children:"Última evaluación"}),e.jsx("p",{className:"text-white font-semibold",children:new Date(t.fechaUltimaEvaluacion).toLocaleDateString("es-ES")})]}),e.jsxs(d,{as:r,to:`/student/informe-completo/${t.paciente.id}`,className:"bg-white text-blue-600 hover:bg-blue-50 border-white shadow-lg",size:"sm",children:[e.jsx("i",{className:"fas fa-file-alt mr-2"}),"Ver Informe Completo"]})]})]})}),e.jsx(c,{className:"p-0",children:e.jsx("div",{className:"overflow-x-auto",children:e.jsxs("table",{className:"w-full",children:[e.jsx("thead",{className:"bg-blue-50 border-b border-blue-200",children:e.jsxs("tr",{children:[e.jsx("th",{className:"px-4 py-3 text-center text-xs font-medium text-blue-700 uppercase tracking-wider",children:"Test"}),e.jsx("th",{className:"px-4 py-3 text-center text-xs font-medium text-blue-700 uppercase tracking-wider",children:"Puntaje PD"}),e.jsx("th",{className:"px-4 py-3 text-center text-xs font-medium text-blue-700 uppercase tracking-wider",children:"Puntaje PC"}),e.jsx("th",{className:"px-4 py-3 text-center text-xs font-medium text-blue-700 uppercase tracking-wider",children:"Errores"}),e.jsx("th",{className:"px-4 py-3 text-center text-xs font-medium text-blue-700 uppercase tracking-wider",children:"Tiempo"}),e.jsx("th",{className:"px-4 py-3 text-center text-xs font-medium text-blue-700 uppercase tracking-wider",children:"Fecha Test"})]})}),e.jsx("tbody",{className:"bg-white divide-y divide-gray-200",children:t.resultados.map((t,s)=>{return e.jsxs("tr",{className:s%2==0?"bg-white":"bg-gray-50",children:[e.jsx("td",{className:"px-4 py-4 text-center",children:e.jsxs("div",{className:"flex items-center justify-center",children:[e.jsx("div",{className:`w-8 h-8 rounded-full bg-gray-100 flex items-center justify-center mr-2 ${a=t.test,{V:"text-blue-600",E:"text-indigo-600",A:"text-red-600",R:"text-amber-600",N:"text-teal-600",M:"text-slate-600",O:"text-green-600"}[a]||"text-gray-600"}`,children:e.jsx("i",{className:j(t.test)})}),e.jsxs("div",{className:"text-left",children:[e.jsx("div",{className:"text-sm font-medium text-gray-900",children:t.test}),e.jsx("div",{className:"text-xs text-gray-500",children:t.testName})]})]})}),e.jsx("td",{className:"px-4 py-4 text-center",children:e.jsx("span",{className:"text-lg font-bold text-orange-600 bg-orange-100 px-3 py-1 rounded-full",children:t.puntajePD})}),e.jsx("td",{className:"px-4 py-4 text-center",children:"N/A"!==t.puntajePC?e.jsxs("div",{className:"flex flex-col items-center",children:[e.jsx("span",{className:"text-lg font-bold text-blue-600 bg-blue-100 px-3 py-1 rounded-full mb-1",children:t.puntajePC}),e.jsx("span",{className:`text-xs px-2 py-1 rounded-full ${t.interpretacionBg} ${t.interpretacionColor}`,children:t.interpretacion})]}):e.jsx("span",{className:"text-gray-400 text-sm",children:"Pendiente"})}),e.jsx("td",{className:"px-4 py-4 text-center",children:e.jsx("span",{className:"text-sm font-medium text-gray-700",children:t.errores})}),e.jsx("td",{className:"px-4 py-4 text-center",children:e.jsx("span",{className:"text-sm font-medium text-gray-700",children:t.tiempo})}),e.jsx("td",{className:"px-4 py-4 text-center",children:e.jsx("span",{className:"text-sm text-gray-500",children:t.fecha})})]},t.id);var a})})]})})})]},t.paciente.id)})})})})]})};export{m as default};
