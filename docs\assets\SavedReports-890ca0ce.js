var e=(e,s,a)=>new Promise((i,r)=>{var t=e=>{try{l(a.next(e))}catch(s){r(s)}},n=e=>{try{l(a.throw(e))}catch(s){r(s)}},l=e=>e.done?i(e.value):Promise.resolve(e.value).then(t,n);l((a=a.apply(e,s)).next())});import{j as s,s as a}from"./auth-3ab59eff.js";import{r as i,L as r}from"./react-vendor-99be060c.js";import{B as t,C as n,a as l,b as o}from"./admin-168d579d.js";import{Q as d}from"./ui-vendor-9705a4a1.js";import"./utils-vendor-4d1206d7.js";const c=()=>{const[c,m]=i.useState([]),[x,u]=i.useState(!0),[f,h]=i.useState("all");i.useEffect(()=>{e(void 0,null,function*(){try{u(!0);const{data:e,error:s}=yield a.from("informes").select("\n            id,\n            titulo,\n            tipo_informe,\n            estado,\n            fecha_generacion,\n            generado_por,\n            observaciones,\n            pacientes:paciente_id (\n              id,\n              nombre,\n              apellido,\n              documento,\n              genero,\n              email\n            ),\n            resultados:resultado_id (\n              id,\n              aptitudes:aptitud_id (\n                codigo,\n                nombre\n              )\n            )\n          ").order("fecha_generacion",{ascending:!1});if(s)return void d.error("Error al cargar los informes guardados");m(e||[])}catch(e){d.error("Error al cargar los informes guardados")}finally{u(!1)}})},[]);const p=e=>"evaluacion_completa"===e?"fas fa-file-medical":"fas fa-file-alt",g=c.filter(e=>"all"===f||("individual"===f?"evaluacion_individual"===e.tipo_informe:"complete"!==f||"evaluacion_completa"===e.tipo_informe));return s.jsxs("div",{className:"container mx-auto py-6",children:[s.jsxs("div",{className:"flex justify-between items-center mb-6",children:[s.jsxs("div",{children:[s.jsxs("h1",{className:"text-2xl font-bold text-blue-800",children:[s.jsx("i",{className:"fas fa-archive mr-3 text-blue-600"}),"Informes Guardados - Administración"]}),s.jsxs("p",{className:"text-gray-600 mt-1",children:[g.length," informe",1!==g.length?"s":""," guardado",1!==g.length?"s":""]})]}),s.jsxs("div",{className:"flex space-x-2",children:[s.jsx(t,{onClick:()=>h("all"),variant:"all"===f?"primary":"outline",size:"sm",children:"Todos"}),s.jsx(t,{onClick:()=>h("individual"),variant:"individual"===f?"primary":"outline",size:"sm",children:"Individuales"}),s.jsx(t,{onClick:()=>h("complete"),variant:"complete"===f?"primary":"outline",size:"sm",children:"Completos"})]})]}),x?s.jsxs("div",{className:"py-16 text-center",children:[s.jsx("div",{className:"animate-spin rounded-full h-12 w-12 border-b-2 border-blue-500 mx-auto mb-4"}),s.jsx("p",{className:"text-gray-500",children:"Cargando informes guardados..."})]}):s.jsx(s.Fragment,{children:0===g.length?s.jsx(n,{children:s.jsx(l,{children:s.jsxs("div",{className:"py-8 text-center",children:[s.jsx("i",{className:"fas fa-folder-open text-4xl text-gray-300 mb-4"}),s.jsx("p",{className:"text-gray-500",children:"No hay informes guardados disponibles."}),s.jsx("p",{className:"text-sm text-gray-400 mt-2",children:"Los informes aparecerán aquí una vez que se generen y guarden."}),s.jsxs(t,{as:r,to:"/admin/reports",className:"mt-4",children:[s.jsx("i",{className:"fas fa-plus mr-2"}),"Ver Resultados"]})]})})}):s.jsx("div",{className:"grid grid-cols-1 lg:grid-cols-2 xl:grid-cols-3 gap-6",children:g.map(i=>{var x,u,f,h,g,v;const j="evaluacion_completa"===i.tipo_informe?"blue":"green";var b;return s.jsxs(n,{className:`overflow-hidden shadow-lg border border-${j}-200 hover:shadow-xl transition-shadow duration-300`,children:[s.jsx(o,{className:`bg-gradient-to-r from-${j}-500 to-${j}-600 border-b border-${j}-300`,children:s.jsxs("div",{className:"flex items-center justify-between",children:[s.jsxs("div",{className:"flex items-center",children:[s.jsx("div",{className:"w-12 h-12 bg-white bg-opacity-20 rounded-full flex items-center justify-center mr-3 border-2 border-white border-opacity-30",children:s.jsx("i",{className:`${p(i.tipo_informe)} text-white text-lg`})}),s.jsxs("div",{children:[s.jsx("h3",{className:"text-white font-semibold text-sm",children:"evaluacion_completa"===i.tipo_informe?"Informe Completo":"Informe Individual"}),s.jsx("p",{className:"text-white text-opacity-80 text-xs",children:new Date(i.fecha_generacion).toLocaleDateString("es-ES")})]})]}),s.jsx("span",{className:`px-2 py-1 rounded-full text-xs font-medium ${b=i.estado,{generado:"bg-blue-100 text-blue-800",revisado:"bg-yellow-100 text-yellow-800",finalizado:"bg-green-100 text-green-800"}[b]||"bg-gray-100 text-gray-800"}`,children:i.estado})]})}),s.jsx(l,{className:"p-4",children:s.jsxs("div",{className:"space-y-3",children:[s.jsxs("div",{className:"flex items-center",children:[s.jsx("div",{className:"w-10 h-10 rounded-full flex items-center justify-center text-white text-sm mr-3 "+("masculino"===(null==(x=i.pacientes)?void 0:x.genero)?"bg-blue-500":"bg-pink-500"),children:s.jsx("i",{className:"fas "+("masculino"===(null==(u=i.pacientes)?void 0:u.genero)?"fa-mars":"fa-venus")})}),s.jsxs("div",{children:[s.jsxs("p",{className:"font-semibold text-gray-900 text-sm",children:[null==(f=i.pacientes)?void 0:f.nombre," ",null==(h=i.pacientes)?void 0:h.apellido]}),(null==(g=i.pacientes)?void 0:g.documento)&&s.jsxs("p",{className:"text-gray-500 text-xs",children:["Doc: ",i.pacientes.documento]})]})]}),"evaluacion_individual"===i.tipo_informe&&(null==(v=i.resultados)?void 0:v.aptitudes)&&s.jsxs("div",{className:"bg-gray-50 p-2 rounded-lg",children:[s.jsx("p",{className:"text-xs text-gray-500",children:"Test Evaluado:"}),s.jsxs("p",{className:"text-sm font-medium text-gray-700",children:[i.resultados.aptitudes.codigo," - ",i.resultados.aptitudes.nombre]})]}),s.jsxs("div",{children:[s.jsx("p",{className:"text-xs text-gray-500",children:"Título:"}),s.jsx("p",{className:"text-sm text-gray-700 line-clamp-2",children:i.titulo})]}),i.observaciones&&s.jsxs("div",{children:[s.jsx("p",{className:"text-xs text-gray-500",children:"Observaciones:"}),s.jsx("p",{className:"text-sm text-gray-700 line-clamp-2",children:i.observaciones})]}),s.jsxs("div",{className:"text-xs text-gray-500 space-y-1",children:[s.jsxs("p",{children:["Generado por: ",i.generado_por||"Sistema"]}),s.jsxs("p",{children:["Fecha: ",new Date(i.fecha_generacion).toLocaleString("es-ES")]})]})]})}),s.jsx("div",{className:"bg-gray-50 px-4 py-3 border-t",children:s.jsxs("div",{className:"flex justify-between items-center",children:[s.jsxs(t,{as:r,to:`/admin/informe-guardado/${i.id}`,variant:"primary",size:"sm",children:[s.jsx("i",{className:"fas fa-eye mr-1"}),"Ver Informe"]}),s.jsxs("div",{className:"flex space-x-2",children:[s.jsx(t,{onClick:()=>window.open(`/admin/informe-guardado/${i.id}`,"_blank"),variant:"outline",size:"sm",children:s.jsx("i",{className:"fas fa-external-link-alt"})}),s.jsx(t,{onClick:()=>{return s=i.id,e(void 0,null,function*(){if(window.confirm("¿Estás seguro de que deseas eliminar este informe?"))try{const{error:e}=yield a.from("informes").delete().eq("id",s);if(e)throw e;m(c.filter(e=>e.id!==s)),d.success("Informe eliminado correctamente")}catch(e){d.error("Error al eliminar el informe")}});var s},variant:"outline",size:"sm",className:"text-red-600 hover:text-red-700 hover:bg-red-50",children:s.jsx("i",{className:"fas fa-trash"})})]})]})})]},i.id)})})})]})};export{c as default};
