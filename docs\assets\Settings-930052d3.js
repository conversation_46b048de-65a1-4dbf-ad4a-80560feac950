var e=Object.defineProperty,s=Object.defineProperties,a=Object.getOwnPropertyDescriptors,t=Object.getOwnPropertySymbols,r=Object.prototype.hasOwnProperty,i=Object.prototype.propertyIsEnumerable,l=(s,a,t)=>a in s?e(s,a,{enumerable:!0,configurable:!0,writable:!0,value:t}):s[a]=t,n=(e,s)=>{for(var a in s||(s={}))r.call(s,a)&&l(e,a,s[a]);if(t)for(var a of t(s))i.call(s,a)&&l(e,a,s[a]);return e},c=(e,t)=>s(e,a(t)),o=(e,s,a)=>new Promise((t,r)=>{var i=e=>{try{n(a.next(e))}catch(s){r(s)}},l=e=>{try{n(a.throw(e))}catch(s){r(s)}},n=e=>e.done?t(e.value):Promise.resolve(e.value).then(i,l);n((a=a.apply(e,s)).next())});import{u as d,j as m,F as x,g as h,m as b,n as u,o as g,p as f,q as p,s as y}from"./auth-3ab59eff.js";import{r as j}from"./react-vendor-99be060c.js";import{Q as N}from"./ui-vendor-9705a4a1.js";import"./utils-vendor-4d1206d7.js";const v=()=>{const{user:e,loading:s}=d(),[a,t]=j.useState(!0),[r,i]=j.useState(!1),[l,v]=j.useState({theme:"light",language:"es",notifications:{email:!0,browser:!0,mobile:!1},display:{fontSize:"medium",compactView:!1,highContrast:!1}});j.useEffect(()=>{o(void 0,null,function*(){if(!s){t(!0);try{if(e){const{data:s,error:a}=yield y.from("user_settings").select("*").eq("user_id",e.id).single();if(s&&!a)v(e=>n(n({},e),s.settings||{}));else{const e=window.matchMedia("(prefers-color-scheme: dark)").matches;v(s=>c(n({},s),{theme:e?"dark":"light"}))}}}catch(a){N.error("No se pudieron cargar las configuraciones")}finally{t(!1)}}})},[e,s]);const w=(e,s,a)=>{v(t=>c(n({},t),e?{[e]:c(n({},t[e]),{[s]:a})}:{[s]:a}))},k=e=>{const s=document.body;if("dark"===e.theme)s.classList.add("dark-mode");else if("light"===e.theme)s.classList.remove("dark-mode");else if("system"===e.theme){window.matchMedia("(prefers-color-scheme: dark)").matches?s.classList.add("dark-mode"):s.classList.remove("dark-mode")}s.classList.remove("font-small","font-medium","font-large"),s.classList.add(`font-${e.display.fontSize}`),e.display.highContrast?s.classList.add("high-contrast"):s.classList.remove("high-contrast"),e.display.compactView?s.classList.add("compact-view"):s.classList.remove("compact-view"),localStorage.setItem("userTheme",e.theme),localStorage.setItem("userLanguage",e.language)};return j.useEffect(()=>{a||k(l)},[l,a]),a||s?m.jsxs("div",{className:"flex justify-center items-center p-12",children:[m.jsx(x,{className:"animate-spin text-blue-600 text-3xl"}),m.jsx("span",{className:"ml-2",children:"Cargando configuraciones..."})]}):m.jsxs("div",{className:"max-w-4xl mx-auto",children:[m.jsxs("header",{className:"mb-6",children:[m.jsx("h1",{className:"text-2xl font-bold text-gray-900",children:"Configuración"}),m.jsx("p",{className:"mt-1 text-sm text-gray-600",children:"Personaliza tu experiencia en la aplicación"})]}),m.jsxs("div",{className:"bg-white shadow rounded-lg overflow-hidden",children:[m.jsxs("div",{className:"p-6 border-b border-gray-200",children:[m.jsxs("h2",{className:"text-lg font-medium text-gray-900 mb-4 flex items-center",children:[m.jsx(h,{className:"mr-2 text-gray-500"}),"Apariencia"]}),m.jsxs("div",{className:"space-y-6",children:[m.jsxs("div",{children:[m.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Tema"}),m.jsxs("div",{className:"flex items-center space-x-4",children:[m.jsxs("button",{type:"button",onClick:()=>w(null,"theme","light"),className:`flex flex-col items-center p-3 rounded-lg ${"light"===l.theme?"bg-blue-100 border-2 border-blue-500":"bg-gray-100 border-2 border-transparent hover:bg-gray-200"} transition-colors`,children:[m.jsx(b,{className:"h-6 w-6 text-yellow-500 mb-2"}),m.jsx("span",{className:"text-sm font-medium",children:"Claro"})]}),m.jsxs("button",{type:"button",onClick:()=>w(null,"theme","dark"),className:`flex flex-col items-center p-3 rounded-lg ${"dark"===l.theme?"bg-blue-100 border-2 border-blue-500":"bg-gray-100 border-2 border-transparent hover:bg-gray-200"} transition-colors`,children:[m.jsx(u,{className:"h-6 w-6 text-gray-700 mb-2"}),m.jsx("span",{className:"text-sm font-medium",children:"Oscuro"})]}),m.jsxs("button",{type:"button",onClick:()=>w(null,"theme","system"),className:`flex flex-col items-center p-3 rounded-lg ${"system"===l.theme?"bg-blue-100 border-2 border-blue-500":"bg-gray-100 border-2 border-transparent hover:bg-gray-200"} transition-colors`,children:[m.jsxs("div",{className:"h-6 w-6 flex items-center justify-center mb-2",children:[m.jsx(b,{className:"h-4 w-4 text-yellow-500"}),m.jsx(u,{className:"h-4 w-4 text-gray-700 ml-1"})]}),m.jsx("span",{className:"text-sm font-medium",children:"Sistema"})]})]})]}),m.jsxs("div",{children:[m.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Tamaño de fuente"}),m.jsxs("div",{className:"flex items-center space-x-4",children:[m.jsx("button",{type:"button",onClick:()=>w("display","fontSize","small"),className:`px-4 py-2 rounded-md ${"small"===l.display.fontSize?"bg-blue-100 border border-blue-500":"bg-gray-100 border border-transparent hover:bg-gray-200"} transition-colors`,children:m.jsx("span",{className:"text-xs",children:"Pequeño"})}),m.jsx("button",{type:"button",onClick:()=>w("display","fontSize","medium"),className:`px-4 py-2 rounded-md ${"medium"===l.display.fontSize?"bg-blue-100 border border-blue-500":"bg-gray-100 border border-transparent hover:bg-gray-200"} transition-colors`,children:m.jsx("span",{className:"text-sm",children:"Mediano"})}),m.jsx("button",{type:"button",onClick:()=>w("display","fontSize","large"),className:`px-4 py-2 rounded-md ${"large"===l.display.fontSize?"bg-blue-100 border border-blue-500":"bg-gray-100 border border-transparent hover:bg-gray-200"} transition-colors`,children:m.jsx("span",{className:"text-base",children:"Grande"})})]})]}),m.jsxs("div",{children:[m.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Opciones de visualización"}),m.jsxs("div",{className:"space-y-3",children:[m.jsxs("div",{className:"flex items-center",children:[m.jsx("input",{id:"compactView",name:"compactView",type:"checkbox",checked:l.display.compactView,onChange:e=>w("display","compactView",e.target.checked),className:"h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"}),m.jsx("label",{htmlFor:"compactView",className:"ml-2 block text-sm text-gray-700",children:"Vista compacta (reduce espaciado)"})]}),m.jsxs("div",{className:"flex items-center",children:[m.jsx("input",{id:"highContrast",name:"highContrast",type:"checkbox",checked:l.display.highContrast,onChange:e=>w("display","highContrast",e.target.checked),className:"h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"}),m.jsx("label",{htmlFor:"highContrast",className:"ml-2 block text-sm text-gray-700",children:"Alto contraste (mejora accesibilidad)"})]})]})]})]})]}),m.jsxs("div",{className:"p-6 border-b border-gray-200",children:[m.jsxs("h2",{className:"text-lg font-medium text-gray-900 mb-4 flex items-center",children:[m.jsx(g,{className:"mr-2 text-gray-500"}),"Idioma"]}),m.jsx("div",{className:"space-y-4",children:m.jsxs("div",{children:[m.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Seleccionar idioma"}),m.jsxs("select",{value:l.language,onChange:e=>w(null,"language",e.target.value),className:"mt-1 block w-full pl-3 pr-10 py-2 text-base border-gray-300 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm rounded-md",children:[m.jsx("option",{value:"es",children:"Español"}),m.jsx("option",{value:"en",children:"English"})]})]})})]}),m.jsxs("div",{className:"p-6 border-b border-gray-200",children:[m.jsxs("h2",{className:"text-lg font-medium text-gray-900 mb-4 flex items-center",children:[m.jsx(f,{className:"mr-2 text-gray-500"}),"Notificaciones"]}),m.jsxs("div",{className:"space-y-4",children:[m.jsxs("div",{className:"flex items-center justify-between",children:[m.jsxs("div",{children:[m.jsx("h3",{className:"text-sm font-medium text-gray-700",children:"Notificaciones por email"}),m.jsx("p",{className:"text-xs text-gray-500",children:"Recibir alertas y recordatorios en su correo"})]}),m.jsx("div",{className:"ml-4",children:m.jsxs("label",{className:"switch",children:[m.jsx("input",{type:"checkbox",checked:l.notifications.email,onChange:e=>w("notifications","email",e.target.checked),className:"sr-only"}),m.jsx("div",{className:`slider ${l.notifications.email?"bg-blue-600":"bg-gray-200"} relative inline-flex items-center h-6 rounded-full w-11 transition-colors`,children:m.jsx("span",{className:`toggle ${l.notifications.email?"translate-x-6":"translate-x-1"} inline-block w-4 h-4 transform bg-white rounded-full transition-transform`})})]})})]}),m.jsxs("div",{className:"flex items-center justify-between",children:[m.jsxs("div",{children:[m.jsx("h3",{className:"text-sm font-medium text-gray-700",children:"Notificaciones en el navegador"}),m.jsx("p",{className:"text-xs text-gray-500",children:"Recibir alertas mientras usa la aplicación"})]}),m.jsx("div",{className:"ml-4",children:m.jsxs("label",{className:"switch",children:[m.jsx("input",{type:"checkbox",checked:l.notifications.browser,onChange:e=>w("notifications","browser",e.target.checked),className:"sr-only"}),m.jsx("div",{className:`slider ${l.notifications.browser?"bg-blue-600":"bg-gray-200"} relative inline-flex items-center h-6 rounded-full w-11 transition-colors`,children:m.jsx("span",{className:`toggle ${l.notifications.browser?"translate-x-6":"translate-x-1"} inline-block w-4 h-4 transform bg-white rounded-full transition-transform`})})]})})]}),m.jsxs("div",{className:"flex items-center justify-between",children:[m.jsxs("div",{children:[m.jsx("h3",{className:"text-sm font-medium text-gray-700",children:"Notificaciones móviles"}),m.jsx("p",{className:"text-xs text-gray-500",children:"Recibir alertas en su dispositivo móvil"})]}),m.jsx("div",{className:"ml-4",children:m.jsxs("label",{className:"switch",children:[m.jsx("input",{type:"checkbox",checked:l.notifications.mobile,onChange:e=>w("notifications","mobile",e.target.checked),className:"sr-only"}),m.jsx("div",{className:`slider ${l.notifications.mobile?"bg-blue-600":"bg-gray-200"} relative inline-flex items-center h-6 rounded-full w-11 transition-colors`,children:m.jsx("span",{className:`toggle ${l.notifications.mobile?"translate-x-6":"translate-x-1"} inline-block w-4 h-4 transform bg-white rounded-full transition-transform`})})]})})]})]})]}),m.jsx("div",{className:"p-6 bg-gray-50",children:m.jsx("div",{className:"flex justify-end",children:m.jsx("button",{type:"button",onClick:()=>o(void 0,null,function*(){if(e){i(!0);try{const{error:s}=yield y.from("user_settings").upsert({user_id:e.id,settings:l,updated_at:new Date});if(s)throw s;k(l),N.success("Configuraciones guardadas correctamente")}catch(s){N.error("Error al guardar configuraciones")}finally{i(!1)}}else N.error("Debe iniciar sesión para guardar configuraciones")}),disabled:r,className:`inline-flex justify-center py-2 px-4 border border-transparent shadow-sm text-sm font-medium rounded-md text-white ${r?"bg-blue-400":"bg-blue-600 hover:bg-blue-700"} focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500`,children:r?m.jsxs(m.Fragment,{children:[m.jsx(x,{className:"animate-spin mr-2 h-4 w-4"}),"Guardando..."]}):m.jsxs(m.Fragment,{children:[m.jsx(p,{className:"mr-2 h-4 w-4"}),"Guardar configuración"]})})})})]})]})};export{v as default};
