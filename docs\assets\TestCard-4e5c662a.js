import{j as e}from"./auth-3ab59eff.js";import{L as s}from"./react-vendor-99be060c.js";import{P as a}from"./index-23a57a03.js";const i=({test:a,iconClass:i,bgClass:r,textClass:t,buttonColor:l,abbreviation:n,showButton:d=!0,disabled:c=!1,patientId:o=null,level:b="E"})=>e.jsx("div",{className:"test-card-container",children:e.jsxs("div",{className:"test-card",children:[n&&e.jsx("div",{className:`abbreviation-circle ${{blue:"bg-blue-600",green:"bg-green-600",red:"bg-red-600",amber:"bg-amber-600",indigo:"bg-indigo-600",gray:"bg-gray-700",slate:"bg-slate-600",teal:"bg-teal-600",purple:"bg-purple-600",pink:"bg-pink-600"}[l]}`,children:n}),e.jsx("div",{className:"test-card-level-badge",children:e.jsxs("span",{className:"level-badge "+("E"===b?"level-badge-green":"M"===b?"level-badge-blue":"S"===b?"level-badge-purple":"level-badge-gray"),children:["📗 Nivel ",b]})}),e.jsxs("div",{className:"test-card-header",children:[e.jsx("div",{className:`test-card-icon ${r}`,children:e.jsx("i",{className:`${i} ${t}`})}),e.jsx("h3",{className:"test-card-title",children:a.title})]}),e.jsx("div",{className:"test-card-description",children:e.jsx("p",{children:a.description})}),e.jsxs("div",{className:"test-card-info-container",children:[e.jsxs("div",{className:"test-card-info",children:[e.jsx("span",{className:"info-label",children:"Tiempo"}),e.jsx("span",{className:"info-value",children:a.time}),e.jsx("span",{className:"info-unit",children:"minutos"})]}),e.jsxs("div",{className:"test-card-info",children:[e.jsx("span",{className:"info-label",children:"Preguntas"}),e.jsx("span",{className:"info-value",children:a.questions})]})]}),e.jsx("div",{className:"test-card-button-container",children:c?e.jsxs("button",{disabled:!0,className:"test-card-button bg-gray-400 cursor-not-allowed opacity-50",children:[e.jsx("i",{className:"fas fa-lock mr-2"}),"Selecciona Paciente"]}):e.jsxs(s,{to:a.path||`/test/instructions/${a.id}`,state:{patientId:o},className:`test-card-button ${{blue:"bg-blue-600 hover:bg-blue-700",green:"bg-green-600 hover:bg-green-700",red:"bg-red-600 hover:bg-red-700",amber:"bg-amber-600 hover:bg-amber-700",indigo:"bg-indigo-600 hover:bg-indigo-700",gray:"bg-gray-700 hover:bg-gray-800",slate:"bg-slate-600 hover:bg-slate-700",teal:"bg-teal-600 hover:bg-teal-700",purple:"bg-purple-600 hover:bg-purple-700",pink:"bg-pink-600 hover:bg-pink-700"}[l]}`,children:[e.jsx("i",{className:"fas fa-play-circle mr-2"}),"Iniciar Test"]})})]})});i.propTypes={test:a.shape({id:a.string.isRequired,title:a.string.isRequired,description:a.string.isRequired,time:a.number.isRequired,questions:a.number.isRequired,path:a.string}).isRequired,iconClass:a.string.isRequired,bgClass:a.string.isRequired,textClass:a.string.isRequired,buttonColor:a.string.isRequired,abbreviation:a.string,showButton:a.bool,disabled:a.bool,patientId:a.string,level:a.string};export{i as T};
