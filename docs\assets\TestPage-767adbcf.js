var e=(e,a,t)=>new Promise((s,i)=>{var r=e=>{try{o(t.next(e))}catch(a){i(a)}},n=e=>{try{o(t.throw(e))}catch(a){i(a)}},o=e=>e.done?s(e.value):Promise.resolve(e.value).then(r,n);o((t=t.apply(e,a)).next())});import{s as a,j as t}from"./auth-3ab59eff.js";import{r as s}from"./react-vendor-99be060c.js";import{C as i,b as r,a as n,B as o}from"./admin-168d579d.js";import{e as l}from"./enhancedSupabaseService-a93ecdc2.js";import"./ui-vendor-9705a4a1.js";import"./utils-vendor-4d1206d7.js";const d=(e,a,t)=>{t.error||t.isOffline},c=()=>e(void 0,null,function*(){const e=yield l.getInstitutions();d(0,0,e);const a=yield l.createInstitution({nombre:`Test Institución ${Date.now()}`,direccion:"Dirección de prueba",telefono:"123456789"});if(d(0,0,a),a.data&&!a.error){const e=a.data.id,t=yield l.updateInstitution(e,{nombre:`${a.data.nombre} (Actualizada)`,direccion:"Dirección actualizada"});d(0,0,t);const s=yield l.deleteInstitution(e);d(0,0,s)}return"Pruebas de instituciones completadas"}),u=()=>e(void 0,null,function*(){const e=yield l.getInstitutions();if(!e.data||0===e.data.length)return"Error: No hay instituciones disponibles";const t=yield l.getPsychologists();d(0,0,t);const s=Date.now(),i=`test.psicologo.${s}@example.com`,{data:r,error:n}=yield a.auth.signUp({email:i,password:"Temporal123!",options:{data:{rol:"psicologo",nombre_completo:`Test Psicólogo ${s}`}}});if(n)return"Error al crear usuario para psicólogo";const o=yield l.createPsychologist({nombre:"Test",apellidos:`Psicólogo ${s}`,email:i,documento_identidad:`DOC-${s}`,telefono:"987654321",institucion_id:e.data[0].id,usuario_id:r.user.id});if(d(0,0,o),o.data&&!o.error){const e=o.data.id,a=yield l.updatePsychologist(e,{nombre:"Test (Actualizado)",apellidos:`Psicólogo ${s}`,documento_identidad:`DOC-${s}-UPD`,telefono:"987654322"});d(0,0,a);const t=yield l.deletePsychologist(e);d(0,0,t)}return"Pruebas de psicólogos completadas"}),m=()=>e(void 0,null,function*(){const e=yield l.getInstitutions();if(!e.data||0===e.data.length)return"Error: No hay instituciones disponibles";const a=yield l.getPsychologists(),t=a.data&&a.data.length>0?a.data[0].id:null,s=yield l.getPatients();d(0,0,s);const i=Date.now(),r=yield l.createPatient({nombre:`Test Paciente ${i}`,fecha_nacimiento:"2000-01-01",genero:"Masculino",institucion_id:e.data[0].id,psicologo_id:t,notas:"Paciente de prueba"});if(d(0,0,r),r.data&&!r.error){const e=r.data.id,a=yield l.updatePatient(e,{nombre:`Test Paciente ${i} (Actualizado)`,fecha_nacimiento:"2000-02-02",notas:"Paciente de prueba actualizado"});d(0,0,a);const t=yield l.deletePatient(e);d(0,0,t)}return"Pruebas de pacientes completadas"}),p=()=>e(void 0,null,function*(){const e=l.getSyncStatus();if(e.pendingCount>0){e.operations.forEach((e,a)=>{});const a=yield l.syncPendingOperations();a.errors.length>0&&a.errors.forEach((e,a)=>{})}return"Pruebas de sincronización completadas"}),h=()=>e(void 0,null,function*(){try{const{data:{user:e}}=yield a.auth.getUser();return e?(yield c(),yield u(),yield m(),yield p(),"Todas las pruebas completadas con éxito"):"Error: Usuario no autenticado"}catch(e){return`Error al ejecutar pruebas: ${e.message}`}}),b=c,g=u,x=m,y=p,j=()=>{const[a,l]=s.useState(!1),[d,c]=s.useState([]),[u,m]=s.useState("all"),p=[{value:"all",label:"Todas las pruebas"},{value:"instituciones",label:"Pruebas de Instituciones"},{value:"psicologos",label:"Pruebas de Psicólogos"},{value:"pacientes",label:"Pruebas de Pacientes"},{value:"sincronizacion",label:"Pruebas de Sincronización"}];return t.jsxs("div",{className:"container mx-auto py-6",children:[t.jsx("h1",{className:"text-2xl font-bold text-gray-800 mb-6",children:"Página de Pruebas"}),t.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-6",children:[t.jsxs(i,{className:"md:col-span-1",children:[t.jsx(r,{children:t.jsx("h2",{className:"text-lg font-medium",children:"Panel de Control"})}),t.jsx(n,{children:t.jsxs("div",{className:"space-y-4",children:[t.jsxs("div",{children:[t.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Seleccionar prueba"}),t.jsx("select",{className:"form-select w-full",value:u,onChange:e=>m(e.target.value),disabled:a,children:p.map(e=>t.jsx("option",{value:e.value,children:e.label},e.value))})]}),t.jsx(o,{variant:"primary",className:"w-full",onClick:()=>{return a=u,e(void 0,null,function*(){l(!0);try{let e;switch(a){case"all":e=yield h();break;case"instituciones":e=yield b();break;case"psicologos":e=yield g();break;case"pacientes":e=yield x();break;case"sincronizacion":e=yield y();break;default:e="Prueba no válida"}c(t=>[{id:Date.now(),test:a,result:e,timestamp:(new Date).toLocaleString()},...t])}catch(e){c(t=>[{id:Date.now(),test:a,result:`Error: ${e.message}`,timestamp:(new Date).toLocaleString(),error:!0},...t])}finally{l(!1)}});var a},disabled:a,children:a?"Ejecutando...":"Ejecutar Prueba"}),t.jsxs("div",{className:"text-sm text-gray-500",children:[t.jsx("p",{children:"Esta página permite ejecutar pruebas manuales para verificar el funcionamiento de las operaciones CRUD."}),t.jsx("p",{className:"mt-2",children:"Las pruebas se ejecutan contra la base de datos real, así que úsala con precaución."})]})]})})]}),t.jsxs(i,{className:"md:col-span-2",children:[t.jsx(r,{children:t.jsx("h2",{className:"text-lg font-medium",children:"Resultados"})}),t.jsx(n,{children:0===d.length?t.jsx("div",{className:"text-center py-8 text-gray-500",children:"No hay resultados. Ejecuta una prueba para ver los resultados aquí."}):t.jsx("div",{className:"space-y-4",children:d.map(e=>{var a;return t.jsxs("div",{className:"p-4 rounded-lg border "+(e.error?"border-red-200 bg-red-50":"border-green-200 bg-green-50"),children:[t.jsxs("div",{className:"flex justify-between items-start",children:[t.jsxs("div",{children:[t.jsx("h3",{className:"font-medium",children:(null==(a=p.find(a=>a.value===e.test))?void 0:a.label)||e.test}),t.jsx("p",{className:"text-sm text-gray-500",children:e.timestamp})]}),t.jsx("span",{className:"px-2 py-1 text-xs rounded-full "+(e.error?"bg-red-100 text-red-800":"bg-green-100 text-green-800"),children:e.error?"Error":"Éxito"})]}),t.jsx("div",{className:"mt-2",children:t.jsx("pre",{className:"text-sm whitespace-pre-wrap bg-white p-2 rounded border",children:e.result})})]},e.id)})})})]})]}),t.jsx("div",{className:"mt-6 text-sm text-gray-500",children:t.jsx("p",{children:"Nota: Los resultados detallados de las pruebas se muestran en la consola del navegador."})})]})};export{j as default};
