import{j as s}from"./auth-3ab59eff.js";import"./react-vendor-99be060c.js";import{C as e,b as a,a as r}from"./admin-168d579d.js";import"./ui-vendor-9705a4a1.js";import"./utils-vendor-4d1206d7.js";const i=()=>s.jsxs("div",{className:"container mx-auto py-6",children:[s.jsx("h1",{className:"text-2xl font-bold text-gray-800 mb-6",children:"Administración de Usuarios"}),s.jsxs(e,{children:[s.jsx(a,{children:s.jsx("h2",{className:"text-lg font-medium",children:"Lista de Usuarios"})}),s.jsx(r,{children:s.jsx("p",{className:"text-gray-600",children:"Esta sección permitirá gestionar los usuarios del sistema (componente en desarrollo)."})})]})]});export{i as default};
