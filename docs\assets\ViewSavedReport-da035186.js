import{j as e,s as a}from"./auth-3ab59eff.js";import{b as s,a as l,r as i}from"./react-vendor-99be060c.js";import{C as t,a as n,B as r,b as d}from"./admin-168d579d.js";import{Q as c}from"./ui-vendor-9705a4a1.js";import{o}from"./interpretacionesAptitudes-bd504cf8.js";import"./utils-vendor-4d1206d7.js";const m=()=>{var m,x,p,u;const{reportId:h}=s(),b=l(),[g,j]=i.useState(null),[f,v]=i.useState(!0);i.useEffect(()=>{h&&(()=>{return e=void 0,s=null,l=function*(){try{v(!0);const{data:e,error:s}=yield a.from("informes").select("\n            id,\n            titulo,\n            contenido,\n            tipo_informe,\n            estado,\n            fecha_generacion,\n            generado_por,\n            observaciones,\n            pacientes:paciente_id (\n              id,\n              nombre,\n              apellido,\n              documento,\n              genero,\n              fecha_nacimiento,\n              email,\n              telefono\n            )\n          ").eq("id",h).single();if(s)return c.error("Error al cargar el informe"),void b("/admin/informes-guardados");j(e)}catch(e){c.error("Error al cargar el informe"),b("/admin/informes-guardados")}finally{v(!1)}},new Promise((a,i)=>{var t=e=>{try{r(l.next(e))}catch(a){i(a)}},n=e=>{try{r(l.throw(e))}catch(a){i(a)}},r=e=>e.done?a(e.value):Promise.resolve(e.value).then(t,n);r((l=l.apply(e,s)).next())});var e,s,l})()},[h,b]);const N=e=>{if(!e)return"N/A";const a=new Date,s=new Date(e);let l=a.getFullYear()-s.getFullYear();const i=a.getMonth()-s.getMonth();return(i<0||0===i&&a.getDate()<s.getDate())&&l--,l};if(f)return e.jsx("div",{className:"container mx-auto py-6",children:e.jsxs("div",{className:"py-16 text-center",children:[e.jsx("div",{className:"animate-spin rounded-full h-12 w-12 border-b-2 border-blue-500 mx-auto mb-4"}),e.jsx("p",{className:"text-gray-500",children:"Cargando informe guardado..."})]})});if(!g)return e.jsx("div",{className:"container mx-auto py-6",children:e.jsx(t,{children:e.jsx(n,{children:e.jsxs("div",{className:"py-8 text-center",children:[e.jsx("i",{className:"fas fa-exclamation-triangle text-4xl text-yellow-500 mb-4"}),e.jsx("p",{className:"text-gray-500",children:"No se pudo cargar el informe."}),e.jsx(r,{onClick:()=>b("/admin/informes-guardados"),className:"mt-4",children:"Volver a Informes"})]})})})});const y=g.contenido,w=y.paciente||g.pacientes;g.tipo_informe;const D=y.resultados||[],E=D.length,C=D.filter(e=>{var a;return null==(a=e.puntajes)?void 0:a.percentil}),A=C.length>0?Math.round(C.reduce((e,a)=>e+a.puntajes.percentil,0)/C.length):0,k=C.filter(e=>e.puntajes.percentil>=75).length,z=C.filter(e=>e.puntajes.percentil<=25).length,P=e=>C.find(a=>{var s;return(null==(s=a.test)?void 0:s.codigo)===e}),G=P("R"),S=P("N"),I=[null==(m=null==G?void 0:G.puntajes)?void 0:m.percentil,null==(x=null==S?void 0:S.puntajes)?void 0:x.percentil].filter(e=>void 0!==e),_=I.length>0?Math.round(I.reduce((e,a)=>e+a,0)/I.length):null,q=P("V"),$=P("O"),F=[null==(p=null==q?void 0:q.puntajes)?void 0:p.percentil,null==(u=null==$?void 0:$.puntajes)?void 0:u.percentil].filter(e=>void 0!==e),L=F.length>0?Math.round(F.reduce((e,a)=>e+a,0)/F.length):null,M=A,T=(e,a)=>{if(!a)return{nivel:"No evaluado",descripcion:"No hay datos suficientes para evaluar este índice."};let s,l,i;switch(s=a>=75?"Alto":a>=25?"Promedio":"Bajo",e){case"g":"Alto"===s?(l="Capacidad general elevada para comprender situaciones complejas, razonar y resolver problemas de manera efectiva.",i=["Habilidad para resolver eficientemente problemas complejos y novedosos","Buena capacidad para formular y contrastar hipótesis","Facilidad para abstraer información e integrarla con conocimiento previo","Elevado potencial para adquirir nuevos conocimientos"]):"Promedio"===s?(l="Capacidad general dentro del rango esperado para resolver problemas y comprender situaciones.",i=["Capacidad adecuada para resolver problemas de complejidad moderada","Habilidades de razonamiento en desarrollo","Potencial de aprendizaje dentro del rango promedio"]):(l="Dificultades en la capacidad general para resolver problemas complejos y comprender relaciones abstractas.",i=["Dificultades para aplicar el razonamiento a problemas complejos","Limitaciones para formar juicios que requieran abstracción","Posible necesidad de enseñanza más directiva y supervisada"]);break;case"Gf":"Alto"===s?(l="Excelente capacidad para el razonamiento inductivo y deductivo con problemas novedosos.",i=["Habilidad sobresaliente para aplicar razonamiento a problemas novedosos","Facilidad para identificar reglas y formular hipótesis","Nivel alto de razonamiento analítico","Buena integración de información visual y verbal"]):"Promedio"===s?(l="Capacidad adecuada para el razonamiento con contenidos abstractos y formales.",i=["Habilidades de razonamiento en desarrollo","Capacidad moderada para resolver problemas novedosos","Estrategias de resolución en proceso de consolidación"]):(l="Dificultades en el razonamiento inductivo y deductivo con problemas abstractos.",i=["Uso de estrategias poco eficaces para problemas novedosos","Falta de flexibilidad en soluciones alternativas","Dificultades para identificar reglas subyacentes","Integración defectuosa de información visual y verbal"]);break;case"Gc":"Alto"===s?(l="Excelente dominio de conocimientos adquiridos culturalmente y habilidades verbales.",i=["Habilidad para captar relaciones entre conceptos verbales","Buena capacidad de comprensión y expresión del lenguaje","Buen nivel de conocimiento léxico y ortográfico","Posiblemente buen nivel de cultura general"]):"Promedio"===s?(l="Conocimientos verbales y culturales dentro del rango esperado.",i=["Comprensión verbal adecuada para la edad","Conocimientos léxicos en desarrollo","Habilidades de expresión en proceso de consolidación"]):(l="Limitaciones en conocimientos verbales y habilidades de lenguaje adquiridas culturalmente.",i=["Procesamiento parcial de relaciones entre conceptos verbales","Dificultades en comprensión y expresión del lenguaje","Limitaciones en conocimiento léxico y ortográfico","Posible nivel bajo de cultura general"]);break;default:l="Interpretación no disponible para este índice.",i=[]}return{nivel:s,descripcion:l,caracteristicas:i}},R=D.map(e=>new Date(e.fecha_evaluacion)).filter(e=>!isNaN(e)),B=R.length>0?new Date(Math.min(...R)):null,H=R.length>0?new Date(Math.max(...R)):null;return e.jsxs("div",{className:"container mx-auto py-6 max-w-6xl",children:[e.jsxs("div",{className:"mb-6 text-center",children:[e.jsxs("div",{className:"flex items-center justify-center mb-4",children:[e.jsx("div",{className:`w-16 h-16 bg-${"masculino"===(null==w?void 0:w.genero)?"blue":"pink"}-100 rounded-full flex items-center justify-center mr-4`,children:e.jsx("i",{className:`fas ${"masculino"===(null==w?void 0:w.genero)?"fa-mars text-blue-600":"fa-venus text-pink-600"} text-2xl`})}),e.jsxs("div",{children:[e.jsx("h1",{className:"text-3xl font-bold text-blue-800",children:"Informe Completo de Evaluación - Admin"}),e.jsxs("p",{className:"text-gray-600",children:[null==w?void 0:w.nombre," ",null==w?void 0:w.apellido]})]})]}),e.jsxs("div",{className:"flex justify-center space-x-4 print-hide",children:[e.jsxs(r,{onClick:()=>b("/admin/informes-guardados"),variant:"outline",children:[e.jsx("i",{className:"fas fa-arrow-left mr-2"}),"Volver"]}),e.jsxs(r,{onClick:()=>{const e=document.querySelectorAll(".print-hide");e.forEach(e=>e.style.display="none");const a=document.querySelector(".container"),s=document.body,l=document.documentElement,i=null==a?void 0:a.className,t=null==s?void 0:s.className,n=null==l?void 0:l.className;a&&(a.className+=" print-optimize"),s&&(s.className+=" print-optimize"),l&&(l.className+=" print-optimize");const r=document.createElement("style");r.textContent="\n      @media print {\n        * {\n          -webkit-print-color-adjust: exact !important;\n          color-adjust: exact !important;\n        }\n        .space-y-6 > * + * {\n          margin-top: 0.5rem !important;\n        }\n        .mb-6 {\n          margin-bottom: 0.5rem !important;\n        }\n        .py-6 {\n          padding-top: 0.5rem !important;\n          padding-bottom: 0.5rem !important;\n        }\n      }\n    ",document.head.appendChild(r);const d=document.title;document.title=`Informe_${null==w?void 0:w.nombre}_${null==w?void 0:w.apellido}_${(new Date).toLocaleDateString("es-ES").replace(/\//g,"-")}`,window.print(),setTimeout(()=>{e.forEach(e=>e.style.display=""),a&&i&&(a.className=i),s&&t&&(s.className=t),l&&n&&(l.className=n),document.head.removeChild(r),document.title=d},1e3)},variant:"primary",children:[e.jsx("i",{className:"fas fa-file-pdf mr-2"}),"Generar PDF"]}),e.jsxs(r,{onClick:()=>window.print(),variant:"outline",children:[e.jsx("i",{className:"fas fa-print mr-2"}),"Imprimir"]})]})]}),e.jsxs(t,{className:"mb-6 print-keep-together shadow-lg border-l-4 border-blue-500",children:[e.jsx(d,{className:"bg-gradient-to-r from-blue-50 to-green-50 border-b-2 border-blue-200",children:e.jsxs("div",{className:"flex items-center",children:[e.jsx("div",{className:`w-12 h-12 bg-${"masculino"===(null==w?void 0:w.genero)?"blue":"pink"}-100 rounded-full flex items-center justify-center mr-4`,children:e.jsx("i",{className:`fas ${"masculino"===(null==w?void 0:w.genero)?"fa-mars text-blue-600":"fa-venus text-pink-600"} text-xl`})}),e.jsxs("h2",{className:"text-xl font-semibold text-blue-800",children:[e.jsx("i",{className:"fas fa-user mr-2"}),"Información del Paciente"]})]})}),e.jsxs(n,{className:"bg-white",children:[e.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-6 print:hidden",children:[e.jsxs("div",{className:"space-y-4",children:[e.jsxs("div",{className:"bg-blue-50 p-4 rounded-lg border-l-4 border-blue-400",children:[e.jsx("p",{className:"text-xs font-medium text-blue-600 uppercase tracking-wide mb-1",children:"Nombre Completo"}),e.jsxs("p",{className:"text-lg font-bold text-gray-900",children:[null==w?void 0:w.nombre," ",null==w?void 0:w.apellido]})]}),(null==w?void 0:w.documento)&&e.jsxs("div",{className:"bg-gray-50 p-4 rounded-lg border-l-4 border-gray-400",children:[e.jsx("p",{className:"text-xs font-medium text-gray-600 uppercase tracking-wide mb-1",children:"Documento"}),e.jsx("p",{className:"text-base font-semibold text-gray-900",children:w.documento})]})]}),e.jsxs("div",{className:"space-y-4",children:[(null==w?void 0:w.fecha_nacimiento)&&e.jsxs("div",{className:"bg-green-50 p-4 rounded-lg border-l-4 border-green-400",children:[e.jsx("p",{className:"text-xs font-medium text-green-600 uppercase tracking-wide mb-1",children:"Fecha de Nacimiento"}),e.jsx("p",{className:"text-base font-semibold text-gray-900",children:new Date(w.fecha_nacimiento).toLocaleDateString("es-ES")})]}),e.jsxs("div",{className:"bg-purple-50 p-4 rounded-lg border-l-4 border-purple-400",children:[e.jsx("p",{className:"text-xs font-medium text-purple-600 uppercase tracking-wide mb-1",children:"Edad"}),e.jsxs("p",{className:"text-base font-semibold text-gray-900",children:[N(null==w?void 0:w.fecha_nacimiento)," años"]})]})]}),e.jsxs("div",{className:"space-y-4",children:[e.jsxs("div",{className:`bg-${"masculino"===(null==w?void 0:w.genero)?"blue":"pink"}-50 p-4 rounded-lg border-l-4 border-${"masculino"===(null==w?void 0:w.genero)?"blue":"pink"}-400`,children:[e.jsx("p",{className:`text-xs font-medium text-${"masculino"===(null==w?void 0:w.genero)?"blue":"pink"}-600 uppercase tracking-wide mb-1`,children:"Género"}),e.jsxs("p",{className:"text-base font-semibold text-gray-900 capitalize flex items-center",children:[e.jsx("i",{className:`fas ${"masculino"===(null==w?void 0:w.genero)?"fa-mars text-blue-600":"fa-venus text-pink-600"} mr-2`}),null==w?void 0:w.genero]})]}),(null==w?void 0:w.email)&&e.jsxs("div",{className:"bg-orange-50 p-4 rounded-lg border-l-4 border-orange-400",children:[e.jsx("p",{className:"text-xs font-medium text-orange-600 uppercase tracking-wide mb-1",children:"Email"}),e.jsx("p",{className:"text-sm font-semibold text-gray-900 break-all",children:w.email})]})]})]}),e.jsx("div",{className:"hidden print:block",children:e.jsxs("div",{className:"grid grid-cols-2 gap-4 text-sm",children:[e.jsxs("div",{children:[e.jsxs("div",{className:"mb-2",children:[e.jsx("span",{className:"font-medium text-blue-600",children:"Nombre:"}),e.jsxs("span",{className:"ml-2 font-bold",children:[null==w?void 0:w.nombre," ",null==w?void 0:w.apellido]})]}),(null==w?void 0:w.documento)&&e.jsxs("div",{className:"mb-2",children:[e.jsx("span",{className:"font-medium text-gray-600",children:"Documento:"}),e.jsx("span",{className:"ml-2",children:w.documento})]}),(null==w?void 0:w.email)&&e.jsxs("div",{className:"mb-2",children:[e.jsx("span",{className:"font-medium text-orange-600",children:"Email:"}),e.jsx("span",{className:"ml-2 text-xs",children:w.email})]})]}),e.jsxs("div",{children:[(null==w?void 0:w.fecha_nacimiento)&&e.jsxs("div",{className:"mb-2",children:[e.jsx("span",{className:"font-medium text-green-600",children:"Fecha de Nacimiento:"}),e.jsx("span",{className:"ml-2",children:new Date(w.fecha_nacimiento).toLocaleDateString("es-ES")})]}),e.jsxs("div",{className:"mb-2",children:[e.jsx("span",{className:"font-medium text-purple-600",children:"Edad:"}),e.jsxs("span",{className:"ml-2",children:[N(null==w?void 0:w.fecha_nacimiento)," años"]})]}),e.jsxs("div",{className:"mb-2",children:[e.jsx("span",{className:`font-medium text-${"masculino"===(null==w?void 0:w.genero)?"blue":"pink"}-600`,children:"Género:"}),e.jsxs("span",{className:"ml-2 capitalize",children:[e.jsx("i",{className:`fas ${"masculino"===(null==w?void 0:w.genero)?"fa-mars text-blue-600":"fa-venus text-pink-600"} mr-1`}),null==w?void 0:w.genero]})]})]})]})})]})]}),e.jsxs(t,{className:"mb-6 print-keep-together",children:[e.jsx(d,{className:"bg-purple-50 border-b",children:e.jsxs("h2",{className:"text-xl font-semibold text-blue-800",children:[e.jsx("i",{className:"fas fa-chart-pie mr-2"}),"Resumen General"]})}),e.jsxs(n,{className:"print-compact",children:[e.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-4 print-grid-horizontal gap-6 mb-6",children:[e.jsxs("div",{className:"text-center p-4 bg-blue-50 rounded-lg",children:[e.jsx("div",{className:"text-3xl font-bold text-blue-600 mb-2",children:E}),e.jsx("div",{className:"text-sm font-medium text-blue-700",children:"Tests Completados"})]}),e.jsxs("div",{className:"text-center p-4 bg-green-50 rounded-lg",children:[e.jsx("div",{className:"text-3xl font-bold text-green-600 mb-2",children:A}),e.jsx("div",{className:"text-sm font-medium text-green-700",children:"Percentil Promedio"})]}),e.jsxs("div",{className:"text-center p-4 bg-purple-50 rounded-lg",children:[e.jsx("div",{className:"text-3xl font-bold text-purple-600 mb-2",children:k}),e.jsx("div",{className:"text-sm font-medium text-purple-700",children:"Aptitudes Altas (≥75)"})]}),e.jsxs("div",{className:"text-center p-4 bg-orange-50 rounded-lg",children:[e.jsx("div",{className:"text-3xl font-bold text-orange-600 mb-2",children:z}),e.jsx("div",{className:"text-sm font-medium text-orange-700",children:"Aptitudes a Reforzar (≤25)"})]})]}),e.jsxs("div",{className:"border-t pt-6",children:[e.jsx("h3",{className:"text-lg font-semibold text-gray-800 mb-4 text-center",children:"Índices Especializados BAT-7"}),e.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-4 print-grid-horizontal gap-6",children:[e.jsxs("div",{className:"text-center p-4 bg-indigo-50 rounded-lg border-2 border-indigo-200",children:[e.jsx("div",{className:"text-2xl font-bold text-indigo-600 mb-2",children:A}),e.jsx("div",{className:"text-sm font-medium text-indigo-700",children:"Total BAT"}),e.jsx("div",{className:"text-xs text-indigo-600 mt-1",children:"Capacidad General"})]}),e.jsxs("div",{className:"text-center p-4 bg-cyan-50 rounded-lg border-2 border-cyan-200",children:[e.jsx("div",{className:"text-2xl font-bold text-cyan-600 mb-2",children:M}),e.jsx("div",{className:"text-sm font-medium text-cyan-700",children:"Índice g"}),e.jsx("div",{className:"text-xs text-cyan-600 mt-1",children:"Capacidad General"})]}),e.jsxs("div",{className:"text-center p-4 bg-teal-50 rounded-lg border-2 border-teal-200",children:[e.jsx("div",{className:"text-2xl font-bold text-teal-600 mb-2",children:_||"N/A"}),e.jsx("div",{className:"text-sm font-medium text-teal-700",children:"Índice Gf"}),e.jsx("div",{className:"text-xs text-teal-600 mt-1",children:"Inteligencia Fluida"})]}),e.jsxs("div",{className:"text-center p-4 bg-emerald-50 rounded-lg border-2 border-emerald-200",children:[e.jsx("div",{className:"text-2xl font-bold text-emerald-600 mb-2",children:L||"N/A"}),e.jsx("div",{className:"text-sm font-medium text-emerald-700",children:"Índice Gc"}),e.jsx("div",{className:"text-xs text-emerald-600 mt-1",children:"Inteligencia Cristalizada"})]})]})]}),e.jsxs("div",{className:"mt-6 flex justify-between text-sm text-gray-600",children:[e.jsxs("div",{children:[e.jsx("span",{className:"font-medium",children:"Primera evaluación:"})," ",B?B.toLocaleDateString("es-ES"):"N/A"]}),e.jsxs("div",{children:[e.jsx("span",{className:"font-medium",children:"Última evaluación:"})," ",H?H.toLocaleDateString("es-ES"):"N/A"]})]})]})]}),e.jsxs(t,{className:"mb-6 print-keep-together",children:[e.jsx(d,{className:"bg-gray-50 border-b",children:e.jsxs("h2",{className:"text-xl font-semibold text-blue-800",children:[e.jsx("i",{className:"fas fa-list-alt mr-2"}),"Resultados Detallados por Aptitud"]})}),e.jsx(n,{className:"p-0",children:0===D.length?e.jsxs("div",{className:"py-8 text-center",children:[e.jsx("i",{className:"fas fa-clipboard-list text-4xl text-gray-300 mb-4"}),e.jsx("p",{className:"text-gray-500",children:"No hay resultados de tests disponibles para este paciente."})]}):e.jsx("div",{className:"overflow-x-auto",children:e.jsxs("table",{className:"w-full",children:[e.jsx("thead",{children:e.jsxs("tr",{className:"bg-slate-800 text-white",children:[e.jsx("th",{className:"px-4 py-3 text-left font-semibold",children:"S"}),e.jsx("th",{className:"px-4 py-3 text-left font-semibold",children:"APTITUDES EVALUADAS"}),e.jsx("th",{className:"px-4 py-3 text-center font-semibold",children:"PD"}),e.jsx("th",{className:"px-4 py-3 text-center font-semibold",children:"PC"}),e.jsx("th",{className:"px-4 py-3 text-left font-semibold",children:"PERFIL DE LAS APTITUDES"})]})}),e.jsx("tbody",{children:D.map((a,s)=>{var l,i,t,n,r,d;null==(l=a.test)||l.codigo;const c=(null==(i=a.puntajes)?void 0:i.percentil)||0;let o="bg-blue-500";return o=c>=80?"bg-orange-500":c>=60?"bg-blue-500":c>=40?"bg-blue-400":"bg-blue-300",e.jsxs("tr",{className:s%2==0?"bg-white":"bg-gray-50",children:[e.jsx("td",{className:"px-4 py-3",children:e.jsx("div",{className:"w-8 h-8 bg-teal-600 rounded-full flex items-center justify-center text-white font-bold text-sm",children:null==(t=a.test)?void 0:t.codigo})}),e.jsx("td",{className:"px-4 py-3",children:e.jsx("div",{className:"font-medium text-gray-900",children:null==(n=a.test)?void 0:n.nombre})}),e.jsx("td",{className:"px-4 py-3 text-center font-bold text-gray-900",children:(null==(r=a.puntajes)?void 0:r.puntaje_directo)||"N/A"}),e.jsx("td",{className:"px-4 py-3 text-center font-bold text-gray-900",children:(null==(d=a.puntajes)?void 0:d.percentil)||"N/A"}),e.jsx("td",{className:"px-4 py-3",children:e.jsx("div",{className:"flex items-center",children:e.jsx("div",{className:"flex-1 bg-gray-200 rounded-full h-6 mr-3",children:e.jsx("div",{className:`${o} h-6 rounded-full flex items-center justify-end pr-2`,style:{width:`${Math.max(c,5)}%`},children:e.jsx("span",{className:"text-white text-xs font-bold",children:c>0?c:""})})})})})]},s)})})]})})})]}),D.length>0&&e.jsxs(t,{className:"mb-6 print-keep-together",children:[e.jsx(d,{className:"bg-purple-50 border-b",children:e.jsxs("h2",{className:"text-xl font-semibold text-blue-800",children:[e.jsx("i",{className:"fas fa-brain mr-2"}),"Interpretación Cualitativa de Aptitudes"]})}),e.jsx(n,{children:e.jsx("div",{className:"space-y-6",children:D.map((a,s)=>{var l,i,t,n,r;const d=o(null==(l=a.test)?void 0:l.codigo,(null==(i=a.puntajes)?void 0:i.percentil)||0);return d?e.jsxs("div",{className:"border-l-4 border-blue-500 pl-6 py-4 bg-gray-50 rounded-r-lg",children:[e.jsxs("div",{className:"flex items-center mb-3",children:[e.jsx("div",{className:"w-10 h-10 bg-teal-600 rounded-full flex items-center justify-center text-white font-bold mr-3",children:null==(t=a.test)?void 0:t.codigo}),e.jsxs("div",{children:[e.jsxs("h3",{className:"text-lg font-semibold text-gray-900",children:[null==(n=a.test)?void 0:n.nombre," - Nivel ",d.nivel]}),e.jsxs("p",{className:"text-sm text-gray-600",children:["Percentil: ",(null==(r=a.puntajes)?void 0:r.percentil)||"N/A"]})]})]}),e.jsxs("div",{className:"mb-4",children:[e.jsx("h4",{className:"font-medium text-gray-800 mb-2",children:"Descripción:"}),e.jsx("p",{className:"text-gray-700 text-sm leading-relaxed",children:d.descripcion})]}),e.jsxs("div",{children:[e.jsxs("h4",{className:"font-medium text-gray-800 mb-2",children:["Características ","Alto"===d.nivel?"Fortalezas":"Áreas de Mejora",":"]}),e.jsx("ul",{className:"list-disc list-inside space-y-1 text-sm text-gray-700",children:d.caracteristicas.map((a,s)=>e.jsx("li",{className:"leading-relaxed",children:a},s))})]})]},s):null})})})]}),e.jsxs(t,{className:"mb-6 print-keep-together",children:[e.jsx(d,{className:"bg-indigo-50 border-b",children:e.jsxs("h2",{className:"text-xl font-semibold text-blue-800",children:[e.jsx("i",{className:"fas fa-chart-line mr-2"}),"Interpretación Cualitativa de Índices Especializados"]})}),e.jsx(n,{children:e.jsxs("div",{className:"space-y-6",children:[A&&e.jsxs("div",{className:"border-l-4 border-indigo-500 pl-6 py-4 bg-indigo-50 rounded-r-lg",children:[e.jsxs("div",{className:"flex items-center mb-3",children:[e.jsx("div",{className:"w-12 h-12 bg-indigo-600 rounded-full flex items-center justify-center text-white font-bold mr-4",children:"g"}),e.jsxs("div",{children:[e.jsxs("h3",{className:"text-lg font-semibold text-gray-900",children:["Índice g - Capacidad General: ",T("g",M).nivel]}),e.jsxs("p",{className:"text-sm text-gray-600",children:["Percentil: ",M]})]})]}),e.jsxs("div",{className:"mb-4",children:[e.jsx("h4",{className:"font-medium text-gray-800 mb-2",children:"Descripción:"}),e.jsx("p",{className:"text-gray-700 text-sm leading-relaxed",children:T("g",M).descripcion})]}),e.jsxs("div",{children:[e.jsx("h4",{className:"font-medium text-gray-800 mb-2",children:"Características:"}),e.jsx("ul",{className:"list-disc list-inside space-y-1 text-sm text-gray-700",children:T("g",M).caracteristicas.map((a,s)=>e.jsx("li",{className:"leading-relaxed",children:a},s))})]})]}),_&&e.jsxs("div",{className:"border-l-4 border-teal-500 pl-6 py-4 bg-teal-50 rounded-r-lg",children:[e.jsxs("div",{className:"flex items-center mb-3",children:[e.jsx("div",{className:"w-12 h-12 bg-teal-600 rounded-full flex items-center justify-center text-white font-bold mr-4",children:"Gf"}),e.jsxs("div",{children:[e.jsxs("h3",{className:"text-lg font-semibold text-gray-900",children:["Índice Gf - Inteligencia Fluida: ",T("Gf",_).nivel]}),e.jsxs("p",{className:"text-sm text-gray-600",children:["Percentil: ",_," (basado en R + N)"]})]})]}),e.jsxs("div",{className:"mb-4",children:[e.jsx("h4",{className:"font-medium text-gray-800 mb-2",children:"Descripción:"}),e.jsx("p",{className:"text-gray-700 text-sm leading-relaxed",children:T("Gf",_).descripcion})]}),e.jsxs("div",{children:[e.jsx("h4",{className:"font-medium text-gray-800 mb-2",children:"Características:"}),e.jsx("ul",{className:"list-disc list-inside space-y-1 text-sm text-gray-700",children:T("Gf",_).caracteristicas.map((a,s)=>e.jsx("li",{className:"leading-relaxed",children:a},s))})]})]}),L&&e.jsxs("div",{className:"border-l-4 border-emerald-500 pl-6 py-4 bg-emerald-50 rounded-r-lg",children:[e.jsxs("div",{className:"flex items-center mb-3",children:[e.jsx("div",{className:"w-12 h-12 bg-emerald-600 rounded-full flex items-center justify-center text-white font-bold mr-4",children:"Gc"}),e.jsxs("div",{children:[e.jsxs("h3",{className:"text-lg font-semibold text-gray-900",children:["Índice Gc - Inteligencia Cristalizada: ",T("Gc",L).nivel]}),e.jsxs("p",{className:"text-sm text-gray-600",children:["Percentil: ",L," (basado en V + O)"]})]})]}),e.jsxs("div",{className:"mb-4",children:[e.jsx("h4",{className:"font-medium text-gray-800 mb-2",children:"Descripción:"}),e.jsx("p",{className:"text-gray-700 text-sm leading-relaxed",children:T("Gc",L).descripcion})]}),e.jsxs("div",{children:[e.jsx("h4",{className:"font-medium text-gray-800 mb-2",children:"Características:"}),e.jsx("ul",{className:"list-disc list-inside space-y-1 text-sm text-gray-700",children:T("Gc",L).caracteristicas.map((a,s)=>e.jsx("li",{className:"leading-relaxed",children:a},s))})]})]}),_&&L&&Math.abs(_-L)>15&&e.jsxs("div",{className:"border-l-4 border-yellow-500 pl-6 py-4 bg-yellow-50 rounded-r-lg",children:[e.jsxs("div",{className:"flex items-center mb-3",children:[e.jsx("div",{className:"w-12 h-12 bg-yellow-600 rounded-full flex items-center justify-center text-white font-bold mr-4",children:e.jsx("i",{className:"fas fa-balance-scale"})}),e.jsx("div",{children:e.jsx("h3",{className:"text-lg font-semibold text-gray-900",children:"Análisis de Disparidad entre Índices"})})]}),e.jsx("div",{children:e.jsxs("p",{className:"text-gray-700 text-sm leading-relaxed",children:["Se observa una diferencia significativa entre la Inteligencia Fluida (Gf: ",_,") y la Inteligencia Cristalizada (Gc: ",L,"). Esta disparidad sugiere un perfil cognitivo heterogéneo que requiere consideración especial en las recomendaciones de intervención.",_>L?" El evaluado muestra mayor fortaleza en razonamiento abstracto que en conocimientos adquiridos.":" El evaluado muestra mayor fortaleza en conocimientos adquiridos que en razonamiento abstracto."]})})]})]})})]}),e.jsxs(t,{className:"mb-6 print-keep-together",children:[e.jsx(d,{className:"bg-yellow-50 border-b",children:e.jsxs("h2",{className:"text-xl font-semibold text-blue-800",children:[e.jsx("i",{className:"fas fa-lightbulb mr-2"}),"Recomendaciones Generales"]})}),e.jsx(n,{children:e.jsxs("div",{className:"space-y-4",children:[e.jsxs("div",{children:[e.jsx("h4",{className:"font-semibold text-gray-800 mb-2",children:"Análisis General del Rendimiento"}),e.jsxs("p",{className:"text-gray-600 text-sm leading-relaxed",children:["El evaluado presenta un percentil promedio de ",A,", lo que indica un rendimiento"," ",A>=70?"por encima del promedio":A>=30?"en el rango promedio":"por debajo del promedio"," ","en las aptitudes evaluadas."]})]}),e.jsxs("div",{children:[e.jsx("h4",{className:"font-semibold text-gray-800 mb-2",children:"Recomendaciones Específicas"}),e.jsxs("ul",{className:"text-gray-600 text-sm space-y-1 list-disc list-inside",children:[e.jsx("li",{children:"Realizar seguimiento periódico del progreso en todas las aptitudes"}),e.jsx("li",{children:"Considerar la aplicación de tests complementarios según las necesidades identificadas"}),e.jsx("li",{children:"Mantener un registro detallado de las intervenciones y su efectividad"}),k>0&&e.jsx("li",{children:"Considerar actividades de enriquecimiento en las aptitudes con alto rendimiento"}),z>0&&e.jsx("li",{children:"Implementar estrategias de apoyo en las aptitudes con rendimiento bajo"})]})]})]})})]}),e.jsxs("div",{className:"text-center text-sm text-gray-500 border-t pt-4",children:[e.jsxs("p",{children:["Informe generado el ",new Date(g.fecha_generacion).toLocaleDateString("es-ES")," a las ",new Date(g.fecha_generacion).toLocaleTimeString("es-ES")]}),e.jsx("p",{className:"mt-1",children:"Sistema de Evaluación Psicológica - BAT-7 - Panel de Administración"})]})]})};export{m as default};
