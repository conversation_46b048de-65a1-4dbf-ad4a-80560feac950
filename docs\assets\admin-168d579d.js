var e=Object.defineProperty,t=Object.defineProperties,s=Object.getOwnPropertyDescriptors,i=Object.getOwnPropertySymbols,a=Object.prototype.hasOwnProperty,r=Object.prototype.propertyIsEnumerable,n=(t,s,i)=>s in t?e(t,s,{enumerable:!0,configurable:!0,writable:!0,value:i}):t[s]=i,l=(e,t)=>{for(var s in t||(t={}))a.call(t,s)&&n(e,s,t[s]);if(i)for(var s of i(t))r.call(t,s)&&n(e,s,t[s]);return e},o=(e,i)=>t(e,s(i)),c=(e,t)=>{var s={};for(var n in e)a.call(e,n)&&t.indexOf(n)<0&&(s[n]=e[n]);if(null!=e&&i)for(var n of i(e))t.indexOf(n)<0&&r.call(e,n)&&(s[n]=e[n]);return s},d=(e,t,s)=>new Promise((i,a)=>{var r=e=>{try{l(s.next(e))}catch(t){a(t)}},n=e=>{try{l(s.throw(e))}catch(t){a(t)}},l=e=>e.done?i(e.value):Promise.resolve(e.value).then(r,n);l((s=s.apply(e,t)).next())});import{j as m,s as u,F as x,a as p,b as h,c as g,d as b,e as f,f as y,g as j,h as v,i as N,k as w}from"./auth-3ab59eff.js";import{L as _,r as C,u as S}from"./react-vendor-99be060c.js";import{Q as k}from"./ui-vendor-9705a4a1.js";const P=e=>{var t=e,{children:s,className:i=""}=t,a=c(t,["children","className"]);return m.jsx("div",o(l({className:`bg-white rounded-lg shadow-sm border border-gray-200 ${i}`},a),{children:s}))},E=e=>{var t=e,{children:s,className:i=""}=t,a=c(t,["children","className"]);return m.jsx("div",o(l({className:`px-6 py-4 border-b border-gray-200 ${i}`},a),{children:s}))},D=e=>{var t=e,{children:s,className:i=""}=t,a=c(t,["children","className"]);return m.jsx("div",o(l({className:`px-6 py-4 ${i}`},a),{children:s}))},I=e=>{var t=e,{children:s,className:i=""}=t,a=c(t,["children","className"]);return m.jsx("div",o(l({className:`px-6 py-4 border-t border-gray-200 ${i}`},a),{children:s}))},A=e=>{var t=e,{children:s,variant:i="primary",size:a="md",className:r="",disabled:n=!1,as:d="button",to:u}=t,x=c(t,["children","variant","size","className","disabled","as","to"]);const p=`inline-flex items-center justify-center font-medium rounded-md focus:outline-none focus:ring-2 focus:ring-offset-2 transition-colors ${{primary:"bg-blue-600 text-white hover:bg-blue-700 focus:ring-blue-500 shadow-sm",secondary:"bg-gray-100 text-gray-800 hover:bg-gray-200 focus:ring-gray-400 shadow-sm",outline:"bg-white text-gray-700 border border-gray-300 hover:bg-gray-50 focus:ring-gray-400 shadow-sm",danger:"bg-red-600 text-white hover:bg-red-700 focus:ring-red-500 shadow-sm",success:"bg-green-600 text-white hover:bg-green-700 focus:ring-green-500 shadow-sm"}[i]} ${{sm:"text-xs px-3 py-1.5",md:"text-sm px-4 py-2",lg:"text-base px-6 py-3"}[a]} ${n?"opacity-50 cursor-not-allowed":"cursor-pointer"} ${r}`;if(d===_||"Link"===d&&u)return m.jsx(_,o(l({to:u,className:p},x),{children:s}));if("function"==typeof d){const e=d;return m.jsx(e,o(l({className:p},x),{children:s}))}return m.jsx("button",o(l({className:p,disabled:n},x),{children:s}))};class L{static recalcularTodosLosPercentiles(){return d(this,null,function*(){try{const{data:e,error:t}=yield u.rpc("recalcular_todos_los_percentiles");if(t)return k.error("Error al recalcular percentiles en Supabase"),{success:!1,error:t};const s=e||0;return s>0?k.success(`Se actualizaron ${s} resultados con sus percentiles`):k.info("No hay resultados pendientes de conversión"),{success:!0,count:s}}catch(e){return k.error("Error al ejecutar el recálculo de percentiles"),{success:!1,error:e}}})}static probarConversion(e,t,s){return d(this,null,function*(){try{const{data:i,error:a}=yield u.rpc("convertir_pd_a_pc",{p_puntaje_directo:e,p_aptitud_codigo:t,p_edad:s});return a?{success:!1,error:a}:{success:!0,percentil:i}}catch(i){return{success:!1,error:i}}})}static verificarFuncionesDisponibles(){return d(this,null,function*(){try{return!!(yield this.probarConversion(25,"V",13)).success}catch(e){return!1}})}static configurarConversionAutomatica(){return d(this,null,function*(){try{if(!(yield this.verificarFuncionesDisponibles()))return k.warning("Las funciones de conversión automática no están configuradas en Supabase"),!1;return(yield this.recalcularTodosLosPercentiles()).success?(k.success("Conversión automática configurada correctamente"),!0):(k.error("Error al configurar la conversión automática"),!1)}catch(e){return k.error("Error al configurar la conversión automática"),!1}})}static obtenerEstadisticasConversion(){return d(this,null,function*(){try{const{data:e,error:t}=yield u.from("resultados").select("id",{count:"exact"}).not("percentil","is",null),{data:s,error:i}=yield u.from("resultados").select("id",{count:"exact"}).is("percentil",null);if(t||i)return null;return{totalResultados:((null==e?void 0:e.length)||0)+((null==s?void 0:s.length)||0),conPercentil:(null==e?void 0:e.length)||0,sinPercentil:(null==s?void 0:s.length)||0,porcentajeConvertido:(((null==e?void 0:e.length)||0)/(((null==e?void 0:e.length)||0)+((null==s?void 0:s.length)||0))*100).toFixed(1)}}catch(e){return null}})}static forzarConversionResultado(e){return d(this,null,function*(){try{const{data:t,error:s}=yield u.from("resultados").select("\n          id,\n          puntaje_directo,\n          aptitudes:aptitud_id (codigo),\n          pacientes:paciente_id (fecha_nacimiento)\n        ").eq("id",e).single();if(s||!t)return{success:!1,error:s};const i=new Date(t.pacientes.fecha_nacimiento),a=new Date;let r=a.getFullYear()-i.getFullYear();const n=a.getMonth()-i.getMonth();(n<0||0===n&&a.getDate()<i.getDate())&&r--;const l=yield this.probarConversion(t.puntaje_directo,t.aptitudes.codigo,r);if(!l.success)return{success:!1,error:"Error en conversión"};const{data:o,error:c}=yield u.from("resultados").update({percentil:l.percentil,updated_at:(new Date).toISOString()}).eq("id",e).select().single();return c?{success:!1,error:c}:(k.success(`Conversión completada: PC ${l.percentil}`),{success:!0,resultado:o})}catch(t){return{success:!1,error:t}}})}static verificarBaremos(){return d(this,null,function*(){try{const{data:e,error:t}=yield u.from("baremos").select("factor, baremo_grupo, count(*)").group("factor, baremo_grupo");return t?{success:!1,error:t}:{success:!0,baremos:e}}catch(e){return{success:!1,error:e}}})}}const O=({title:e,subtitle:t,icon:s,className:i="",showTransitions:a=!0})=>m.jsxs("div",{className:`animated-gradient text-white relative overflow-hidden page-header-effect ${i}`,children:[m.jsxs("div",{className:"absolute inset-0 opacity-10",children:[m.jsx("div",{className:"absolute inset-0 bg-gradient-to-r from-blue-500/20 via-purple-500/30 to-cyan-500/20 animate-pulse"}),m.jsx("div",{className:"absolute inset-0 bg-gradient-to-l from-indigo-500/20 via-pink-500/20 to-blue-500/30 banner-glow"})]}),m.jsx("div",{className:"absolute inset-0 overflow-hidden opacity-20",children:m.jsx("div",{className:"absolute top-0 left-0 w-full h-full bg-gradient-to-r from-transparent via-white/5 to-transparent transform -skew-x-12 banner-shimmer"})}),m.jsxs("div",{className:"absolute inset-0 overflow-hidden opacity-30",children:[m.jsx("div",{className:"absolute top-4 left-10 w-1 h-1 bg-yellow-300 rounded-full floating-particle animate-ping"}),m.jsx("div",{className:"absolute top-8 right-20 w-2 h-2 bg-cyan-300 rounded-full floating-particle animate-pulse"}),m.jsx("div",{className:"absolute bottom-6 left-1/4 w-1 h-1 bg-pink-300 rounded-full floating-particle animate-bounce"}),m.jsx("div",{className:"absolute bottom-4 right-1/3 w-2 h-2 bg-green-300 rounded-full floating-particle animate-pulse delay-500"})]}),m.jsx("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6 relative z-10",children:m.jsxs("div",{className:"text-center",children:[m.jsxs("div",{className:"flex items-center justify-center mb-3 "+(a?"transition-all duration-300 ease-in-out hover:scale-105":""),children:[s&&m.jsx("div",{className:"w-12 h-12 bg-[#f59e0b] rounded-full flex items-center justify-center mr-4 shadow-lg yellow-icon-glow "+(a?"transition-all duration-300 ease-in-out hover:bg-yellow-400 hover:shadow-xl hover:shadow-yellow-500/25 hover:scale-110 button-glow":""),children:m.jsx(s,{className:"text-white text-xl"})}),m.jsx("h1",{className:"text-3xl font-bold text-glow "+(a?"transition-all duration-300 ease-in-out hover:text-yellow-200 hover:scale-105":""),children:e})]}),t&&m.jsx("p",{className:"text-blue-100 text-lg "+(a?"transition-all duration-300 ease-in-out hover:text-white":""),children:t})]})}),m.jsx("div",{className:"absolute bottom-0 left-0 right-0 h-1 bg-gradient-to-r from-transparent via-yellow-400 to-transparent opacity-50"})]}),q=Object.freeze(Object.defineProperty({__proto__:null,default:()=>m.jsxs("div",{className:"container mx-auto py-6",children:[m.jsx("h1",{className:"text-2xl font-bold text-gray-800 mb-6",children:"Administración de Instituciones"}),m.jsxs(P,{children:[m.jsx(E,{children:m.jsx("h2",{className:"text-lg font-medium",children:"Lista de Instituciones"})}),m.jsx(D,{children:m.jsx("p",{className:"text-gray-600",children:"Esta sección permitirá gestionar las instituciones registradas en el sistema (componente en desarrollo)."})})]})]})},Symbol.toStringTag,{value:"Module"})),z=(e,t,s,i)=>d(void 0,null,function*(){try{yield u.from("logs").insert({action:e,table_name:t,record_id:s,data:i,created_at:(new Date).toISOString()})}catch(a){}}),$={checkConnection(){return d(this,null,function*(){try{const{data:e,error:t}=yield u.rpc("get_tables");if(t)throw t;return{success:!0,data:e}}catch(e){return{success:!1,error:e}}})},getInstitutions(){return d(this,null,function*(){return yield u.from("instituciones").select("*").order("nombre",{ascending:!0})})},createInstitution(e){return d(this,null,function*(){const t={nombre:e.nombre,direccion:e.direccion||"",telefono:e.telefono||"",email:e.email||"",sitio_web:e.sitio_web||"",logo_url:e.logo_url||"",created_at:(new Date).toISOString(),updated_at:(new Date).toISOString()},s=yield u.from("instituciones").insert([t]).select();return s.data&&s.data.length>0&&(yield z("create","instituciones",s.data[0].id,t)),s})},updateInstitution(e,t){return d(this,null,function*(){const s={nombre:t.nombre,direccion:t.direccion||"",telefono:t.telefono||"",email:t.email||"",sitio_web:t.sitio_web||"",logo_url:t.logo_url||"",updated_at:(new Date).toISOString()},i=yield u.from("instituciones").update(s).eq("id",e).select();return i.data&&i.data.length>0&&(yield z("update","instituciones",e,s)),i})},deleteInstitution(e){return d(this,null,function*(){const{data:t}=yield u.from("instituciones").select("*").eq("id",e).single(),s=yield u.from("instituciones").delete().eq("id",e);return!s.error&&t&&(yield z("delete","instituciones",e,t)),s})},getPsychologists(){return d(this,null,function*(){return yield u.from("psicologos").select("*, instituciones(id, nombre)").order("nombre",{ascending:!0})})},createPsychologist(e){return d(this,null,function*(){const t={usuario_id:e.usuario_id||null,institucion_id:e.institucion_id,nombre:e.nombre,apellido:e.apellido||"",documento:e.documento||"",email:e.email||"",telefono:e.telefono||"",genero:e.genero||"",fecha_nacimiento:e.fecha_nacimiento||null,direccion:e.direccion||"",especialidad:e.especialidad||"",licencia:e.licencia||"",notas:e.notas||"",created_at:(new Date).toISOString(),updated_at:(new Date).toISOString()},s=yield u.from("psicologos").insert([t]).select();return s.data&&s.data.length>0&&(yield z("create","psicologos",s.data[0].id,t)),s})},updatePsychologist(e,t){return d(this,null,function*(){const s={institucion_id:t.institucion_id,nombre:t.nombre,apellido:t.apellido||"",documento:t.documento||"",email:t.email||"",telefono:t.telefono||"",genero:t.genero||"",fecha_nacimiento:t.fecha_nacimiento||null,direccion:t.direccion||"",especialidad:t.especialidad||"",licencia:t.licencia||"",notas:t.notas||"",updated_at:(new Date).toISOString()},i=yield u.from("psicologos").update(s).eq("id",e).select();return i.data&&i.data.length>0&&(yield z("update","psicologos",e,s)),i})},deletePsychologist(e){return d(this,null,function*(){const{data:t}=yield u.from("psicologos").select("*").eq("id",e).single(),s=yield u.from("psicologos").delete().eq("id",e);return!s.error&&t&&(yield z("delete","psicologos",e,t)),s})},getPatients(){return d(this,null,function*(){return yield u.from("pacientes").select("*, instituciones(id, nombre), psicologos(id, nombre, apellido)").order("nombre",{ascending:!0})})},createPatient(e){return d(this,null,function*(){const t={usuario_id:e.usuario_id||null,psicologo_id:e.psicologo_id||null,institucion_id:e.institucion_id,nombre:e.nombre,apellido:e.apellido||"",documento:e.documento||"",email:e.email||"",telefono:e.telefono||"",genero:e.genero||"",fecha_nacimiento:e.fecha_nacimiento||null,direccion:e.direccion||"",nivel_educativo:e.nivel_educativo||"",ocupacion:e.ocupacion||"",estado_civil:e.estado_civil||"",notas:e.notas||"",created_at:(new Date).toISOString(),updated_at:(new Date).toISOString()},s=yield u.from("pacientes").insert([t]).select();return s.data&&s.data.length>0&&(yield z("create","pacientes",s.data[0].id,t)),s})},updatePatient(e,t){return d(this,null,function*(){const s={psicologo_id:t.psicologo_id||null,institucion_id:t.institucion_id,nombre:t.nombre,apellido:t.apellido||"",documento:t.documento||"",email:t.email||"",telefono:t.telefono||"",genero:t.genero||"",fecha_nacimiento:t.fecha_nacimiento||null,direccion:t.direccion||"",nivel_educativo:t.nivel_educativo||"",ocupacion:t.ocupacion||"",estado_civil:t.estado_civil||"",notas:t.notas||"",updated_at:(new Date).toISOString()},i=yield u.from("pacientes").update(s).eq("id",e).select();return i.data&&i.data.length>0&&(yield z("update","pacientes",e,s)),i})},deletePatient(e){return d(this,null,function*(){const{data:t}=yield u.from("pacientes").select("*").eq("id",e).single(),s=yield u.from("pacientes").delete().eq("id",e);return!s.error&&t&&(yield z("delete","pacientes",e,t)),s})},getPatientsByPsychologist(e){return d(this,null,function*(){return yield u.from("pacientes").select("*").eq("psicologo_id",e).order("nombre",{ascending:!0})})},getPatientsByInstitution(e){return d(this,null,function*(){return yield u.from("pacientes").select("*").eq("institucion_id",e).order("nombre",{ascending:!0})})},getPsychologistsByInstitution(e){return d(this,null,function*(){return yield u.from("psicologos").select("*").eq("institucion_id",e).order("nombre",{ascending:!0})})}},F=({initialData:e,onSubmit:t,onCancel:s})=>{const[i,a]=C.useState({nombre:"",direccion:"",telefono:"",email:"",sitio_web:"",logo_url:""}),[r,n]=C.useState(!1);C.useEffect(()=>{e&&a({nombre:e.nombre||"",direccion:e.direccion||"",telefono:e.telefono||"",email:e.email||"",sitio_web:e.sitio_web||"",logo_url:e.logo_url||""})},[e]);const c=e=>{const{name:t,value:s,type:r,checked:n}=e.target;a(o(l({},i),{[t]:"checkbox"===r?n:s}))};return m.jsxs("form",{onSubmit:e=>d(void 0,null,function*(){if(e.preventDefault(),i.nombre.trim()||(alert("El nombre de la institución es obligatorio"),0)){n(!0);try{yield t(i)}catch(s){}finally{n(!1)}}}),className:"space-y-4",children:[m.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[m.jsxs("div",{className:"md:col-span-2",children:[m.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Nombre de la Institución *"}),m.jsx("input",{type:"text",name:"nombre",value:i.nombre,onChange:c,className:"w-full p-2 border border-gray-300 rounded-md",required:!0})]}),m.jsxs("div",{children:[m.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Email"}),m.jsx("input",{type:"email",name:"email",value:i.email,onChange:c,className:"w-full p-2 border border-gray-300 rounded-md"})]}),m.jsxs("div",{children:[m.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Dirección"}),m.jsx("input",{type:"text",name:"direccion",value:i.direccion,onChange:c,className:"w-full p-2 border border-gray-300 rounded-md"})]}),m.jsxs("div",{children:[m.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Teléfono"}),m.jsx("input",{type:"text",name:"telefono",value:i.telefono,onChange:c,className:"w-full p-2 border border-gray-300 rounded-md"})]}),m.jsxs("div",{children:[m.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Sitio Web"}),m.jsx("input",{type:"url",name:"sitio_web",value:i.sitio_web,onChange:c,className:"w-full p-2 border border-gray-300 rounded-md",placeholder:"https://ejemplo.com"})]}),m.jsxs("div",{children:[m.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"URL del Logo"}),m.jsx("input",{type:"url",name:"logo_url",value:i.logo_url,onChange:c,className:"w-full p-2 border border-gray-300 rounded-md",placeholder:"https://ejemplo.com/logo.png"})]})]}),m.jsxs("div",{className:"flex justify-end space-x-3 mt-6",children:[m.jsx("button",{type:"button",onClick:s,className:"px-4 py-2 bg-gray-200 text-gray-800 rounded-md hover:bg-gray-300",children:"Cancelar"}),m.jsx("button",{type:"submit",className:"px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700",disabled:r,children:r?m.jsxs(m.Fragment,{children:[m.jsx(x,{className:"animate-spin inline mr-2"}),"Guardando..."]}):e?"Actualizar":"Crear"})]})]})},B=()=>{const[e,t]=C.useState([]),[s,i]=C.useState(!0),[a,r]=C.useState(!1),[n,l]=C.useState(null),[o,c]=C.useState("");C.useEffect(()=>{u()},[]);const u=()=>d(void 0,null,function*(){i(!0);try{const{data:e,error:s}=yield $.getInstitutions();if(s)throw s;t(e||[])}catch(e){k.error("Error al cargar las instituciones")}finally{i(!1)}}),f=(e=null)=>{l(e),r(!0)},y=()=>{r(!1),l(null)},j=e.filter(e=>{var t,s,i,a;return(null==(t=e.nombre)?void 0:t.toLowerCase().includes(o.toLowerCase()))||(null==(s=e.email)?void 0:s.toLowerCase().includes(o.toLowerCase()))||(null==(i=e.direccion)?void 0:i.toLowerCase().includes(o.toLowerCase()))||(null==(a=e.telefono)?void 0:a.toLowerCase().includes(o.toLowerCase()))});return m.jsxs("div",{className:"space-y-6",children:[m.jsxs("div",{className:"flex flex-col md:flex-row md:items-center md:justify-between gap-4",children:[m.jsxs("div",{children:[m.jsx("h2",{className:"text-xl font-bold text-gray-900",children:"Gestión de Instituciones"}),m.jsx("p",{className:"text-gray-600",children:"Administre las instituciones registradas en el sistema"})]}),m.jsxs("div",{className:"flex flex-col sm:flex-row gap-2",children:[m.jsxs("div",{className:"relative",children:[m.jsx("input",{type:"text",placeholder:"Buscar institución...",className:"pl-10 pr-4 py-2 border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500",value:o,onChange:e=>c(e.target.value)}),m.jsx("div",{className:"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none",children:m.jsx("svg",{className:"h-5 w-5 text-gray-400",fill:"currentColor",viewBox:"0 0 20 20",children:m.jsx("path",{fillRule:"evenodd",d:"M8 4a4 4 0 100 8 4 4 0 000-8zM2 8a6 6 0 1110.89 3.476l4.817 4.817a1 1 0 01-1.414 1.414l-4.816-4.816A6 6 0 012 8z",clipRule:"evenodd"})})})]}),m.jsxs("button",{onClick:()=>f(),className:"flex items-center justify-center px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors",disabled:s,children:[m.jsx(p,{className:"mr-2"}),"Nueva Institución"]})]})]}),m.jsx("div",{className:"bg-white shadow overflow-hidden rounded-lg",children:s?m.jsxs("div",{className:"flex justify-center items-center p-8",children:[m.jsx(x,{className:"animate-spin text-blue-600 text-2xl mr-2"}),m.jsx("span",{children:"Cargando instituciones..."})]}):0===j.length?m.jsx("div",{className:"text-center p-8 text-gray-500",children:o?"No se encontraron instituciones que coincidan con la búsqueda.":"No hay instituciones registradas."}):m.jsx("div",{className:"overflow-x-auto",children:m.jsxs("table",{className:"min-w-full divide-y divide-gray-200",children:[m.jsx("thead",{className:"bg-sky-800",children:m.jsxs("tr",{children:[m.jsx("th",{className:"px-6 py-3 text-left text-xs font-medium text-white uppercase tracking-wider",children:"Nombre"}),m.jsx("th",{className:"px-6 py-3 text-left text-xs font-medium text-white uppercase tracking-wider",children:"Email"}),m.jsx("th",{className:"px-6 py-3 text-left text-xs font-medium text-white uppercase tracking-wider",children:"Dirección"}),m.jsx("th",{className:"px-6 py-3 text-left text-xs font-medium text-white uppercase tracking-wider",children:"Teléfono"}),m.jsx("th",{className:"px-6 py-3 text-left text-xs font-medium text-white uppercase tracking-wider",children:"Sitio Web"}),m.jsx("th",{className:"px-6 py-3 text-right text-xs font-medium text-white uppercase tracking-wider",children:"Acciones"})]})}),m.jsx("tbody",{className:"bg-white divide-y divide-gray-200",children:j.map(e=>m.jsxs("tr",{className:"hover:bg-gray-50",children:[m.jsx("td",{className:"px-6 py-4 whitespace-nowrap",children:m.jsxs("div",{className:"flex items-center",children:[m.jsx(h,{className:"text-gray-500 mr-2"}),m.jsx("div",{className:"text-sm font-medium text-gray-900",children:e.nombre})]})}),m.jsx("td",{className:"px-6 py-4 whitespace-nowrap text-sm text-gray-500",children:e.email}),m.jsx("td",{className:"px-6 py-4 whitespace-nowrap text-sm text-gray-500",children:e.direccion}),m.jsx("td",{className:"px-6 py-4 whitespace-nowrap text-sm text-gray-500",children:e.telefono}),m.jsx("td",{className:"px-6 py-4 whitespace-nowrap text-sm text-gray-500",children:e.sitio_web?m.jsx("a",{href:e.sitio_web,target:"_blank",rel:"noopener noreferrer",className:"text-blue-600 hover:underline",children:e.sitio_web.replace(/^https?:\/\//,"").split("/")[0]}):"-"}),m.jsxs("td",{className:"px-6 py-4 whitespace-nowrap text-right text-sm font-medium",children:[m.jsx("button",{onClick:()=>f(e),className:"text-blue-600 hover:text-blue-900 mr-3",children:m.jsx(g,{})}),m.jsx("button",{onClick:()=>{return t=e.id,d(void 0,null,function*(){if(window.confirm("¿Está seguro de que desea eliminar esta institución?"))try{i(!0);const{error:e}=yield $.deleteInstitution(t);if(e)throw e;k.success("Institución eliminada correctamente"),u()}catch(e){k.error("Error al eliminar la institución")}finally{i(!1)}});var t},className:"text-red-600 hover:text-red-900",children:m.jsx(b,{})})]})]},e.id))})]})})}),a&&m.jsx("div",{className:"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50",children:m.jsxs("div",{className:"bg-white rounded-lg shadow-xl p-6 w-full max-w-3xl mx-4",children:[m.jsx("h2",{className:"text-xl font-bold mb-4",children:n?"Editar Institución":"Nueva Institución"}),m.jsx(F,{initialData:n,onSubmit:e=>d(void 0,null,function*(){try{let t;if(i(!0),n){if(t=yield $.updateInstitution(n.id,e),t.error)throw t.error;k.success("Institución actualizada correctamente")}else{if(t=yield $.createInstitution(e),t.error)throw t.error;k.success("Institución creada correctamente")}u(),y()}catch(t){k.error(n?"Error al actualizar la institución":"Error al crear la institución")}finally{i(!1)}}),onCancel:y})]})})]})},M=()=>{const[e,t]=C.useState([]),[s,i]=C.useState([]),[a,r]=C.useState(!0),[n,c]=C.useState(!1),[u,h]=C.useState(null),[j,v]=C.useState(""),[N,w]=C.useState({nombre:"",apellido:"",genero:"masculino",email:"",telefono:"",especialidad:"",institucion_id:"",activo:!0});C.useEffect(()=>{_(),S()},[]);const _=()=>d(void 0,null,function*(){r(!0);try{const{data:e,error:s}=yield $.getPsychologists();if(s)throw s;t(e||[])}catch(e){k.error("Error al cargar los psicólogos")}finally{r(!1)}}),S=()=>d(void 0,null,function*(){try{const{data:e,error:t}=yield $.getInstitutions();if(t)throw t;i(e||[])}catch(e){k.error("Error al cargar las instituciones")}}),P=(e=null)=>{h(e),w(e?{nombre:e.nombre||"",apellido:e.apellido||"",genero:e.genero||"masculino",email:e.email||"",telefono:e.telefono||"",especialidad:e.especialidad||"",institucion_id:e.institucion_id||"",activo:!1!==e.activo}:{nombre:"",apellido:"",genero:"masculino",email:"",telefono:"",especialidad:"",institucion_id:"",activo:!0}),c(!0)},E=()=>{c(!1),h(null)},D=e=>{const{name:t,value:s,type:i,checked:a}=e.target;w(o(l({},N),{[t]:"checkbox"===i?a:s}))},I=e.filter(e=>{var t,s,i,a;return(null==(t=e.nombre)?void 0:t.toLowerCase().includes(j.toLowerCase()))||(null==(s=e.apellido)?void 0:s.toLowerCase().includes(j.toLowerCase()))||(null==(i=e.email)?void 0:i.toLowerCase().includes(j.toLowerCase()))||(null==(a=e.especialidad)?void 0:a.toLowerCase().includes(j.toLowerCase()))}),A=e=>{const t=s.find(t=>t.id===e);return t?t.nombre:"No asignada"};return m.jsxs("div",{className:"space-y-6",children:[m.jsxs("div",{className:"flex flex-col md:flex-row md:items-center md:justify-between gap-4",children:[m.jsxs("div",{children:[m.jsx("h2",{className:"text-xl font-bold text-gray-900",children:"Gestión de Psicólogos"}),m.jsx("p",{className:"text-gray-600",children:"Administre los psicólogos registrados en el sistema"})]}),m.jsxs("div",{className:"flex flex-col sm:flex-row gap-2",children:[m.jsxs("div",{className:"relative",children:[m.jsx("input",{type:"text",placeholder:"Buscar psicólogo...",className:"pl-10 pr-4 py-2 border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500",value:j,onChange:e=>v(e.target.value)}),m.jsx("div",{className:"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none",children:m.jsx("svg",{className:"h-5 w-5 text-gray-400",fill:"currentColor",viewBox:"0 0 20 20",children:m.jsx("path",{fillRule:"evenodd",d:"M8 4a4 4 0 100 8 4 4 0 000-8zM2 8a6 6 0 1110.89 3.476l4.817 4.817a1 1 0 01-1.414 1.414l-4.816-4.816A6 6 0 012 8z",clipRule:"evenodd"})})})]}),m.jsxs("button",{onClick:()=>P(),className:"flex items-center justify-center px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors",disabled:a,children:[m.jsx(p,{className:"mr-2"}),"Nuevo Psicólogo"]})]})]}),m.jsx("div",{className:"bg-white shadow overflow-hidden rounded-lg",children:a&&0===e.length?m.jsxs("div",{className:"flex justify-center items-center p-8",children:[m.jsx(x,{className:"animate-spin text-blue-600 text-2xl mr-2"}),m.jsx("span",{children:"Cargando psicólogos..."})]}):0===I.length?m.jsx("div",{className:"text-center p-8 text-gray-500",children:j?"No se encontraron psicólogos que coincidan con la búsqueda.":"No hay psicólogos registrados."}):m.jsx("div",{className:"overflow-x-auto",children:m.jsxs("table",{className:"min-w-full divide-y divide-gray-200",children:[m.jsx("thead",{className:"bg-sky-800",children:m.jsxs("tr",{children:[m.jsx("th",{className:"px-6 py-3 text-left text-xs font-medium text-white uppercase tracking-wider",children:"Nombre"}),m.jsx("th",{className:"px-6 py-3 text-left text-xs font-medium text-white uppercase tracking-wider",children:"Email"}),m.jsx("th",{className:"px-6 py-3 text-left text-xs font-medium text-white uppercase tracking-wider",children:"Teléfono"}),m.jsx("th",{className:"px-6 py-3 text-left text-xs font-medium text-white uppercase tracking-wider",children:"Especialidad"}),m.jsx("th",{className:"px-6 py-3 text-left text-xs font-medium text-white uppercase tracking-wider",children:"Institución"}),m.jsx("th",{className:"px-6 py-3 text-left text-xs font-medium text-white uppercase tracking-wider",children:"Estado"}),m.jsx("th",{className:"px-6 py-3 text-right text-xs font-medium text-white uppercase tracking-wider",children:"Acciones"})]})}),m.jsx("tbody",{className:"bg-white divide-y divide-gray-200",children:I.map(e=>m.jsxs("tr",{className:"hover:bg-gray-50",children:[m.jsx("td",{className:"px-6 py-4 whitespace-nowrap",children:m.jsxs("div",{className:"flex items-center",children:["femenino"===e.genero?m.jsx(f,{className:"text-pink-500 mr-2"}):m.jsx(y,{className:"text-blue-500 mr-2"}),m.jsxs("div",{className:"text-sm font-medium text-gray-900",children:[e.nombre," ",e.apellido]})]})}),m.jsx("td",{className:"px-6 py-4 whitespace-nowrap text-sm text-gray-500",children:e.email||"-"}),m.jsx("td",{className:"px-6 py-4 whitespace-nowrap text-sm text-gray-500",children:e.telefono||"-"}),m.jsx("td",{className:"px-6 py-4 whitespace-nowrap text-sm text-gray-500",children:e.especialidad||"-"}),m.jsx("td",{className:"px-6 py-4 whitespace-nowrap text-sm text-gray-500",children:A(e.institucion_id)}),m.jsx("td",{className:"px-6 py-4 whitespace-nowrap",children:m.jsx("span",{className:"px-2 inline-flex text-xs leading-5 font-semibold rounded-full "+(e.activo?"bg-green-100 text-green-800":"bg-red-100 text-red-800"),children:e.activo?"Activo":"Inactivo"})}),m.jsxs("td",{className:"px-6 py-4 whitespace-nowrap text-right text-sm font-medium",children:[m.jsx("button",{onClick:()=>P(e),className:"text-blue-600 hover:text-blue-900 mr-3",children:m.jsx(g,{})}),m.jsx("button",{onClick:()=>{return t=e.id,d(void 0,null,function*(){if(window.confirm("¿Está seguro de que desea eliminar este psicólogo?"))try{r(!0);const{error:e}=yield $.deletePsychologist(t);if(e)throw e;k.success("Psicólogo eliminado correctamente"),_()}catch(e){k.error("Error al eliminar el psicólogo")}finally{r(!1)}});var t},className:"text-red-600 hover:text-red-900",children:m.jsx(b,{})})]})]},e.id))})]})})}),n&&m.jsx("div",{className:"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50",children:m.jsxs("div",{className:"bg-white rounded-lg shadow-xl p-6 w-full max-w-3xl mx-4",children:[m.jsx("h2",{className:"text-xl font-bold mb-4",children:u?"Editar Psicólogo":"Nuevo Psicólogo"}),m.jsxs("form",{onSubmit:e=>d(void 0,null,function*(){if(e.preventDefault(),N.nombre.trim()?N.apellido.trim()?N.institucion_id?!N.email||/\S+@\S+\.\S+/.test(N.email)||(k.error("El email no es válido"),0):(k.error("Debe seleccionar una institución"),0):(k.error("El apellido es obligatorio"),0):(k.error("El nombre es obligatorio"),0))try{let e;if(r(!0),u){if(e=yield $.updatePsychologist(u.id,N),e.error)throw e.error;k.success("Psicólogo actualizado correctamente")}else{if(e=yield $.createPsychologist(N),e.error)throw e.error;k.success("Psicólogo creado correctamente")}_(),E()}catch(t){k.error(u?"Error al actualizar el psicólogo":"Error al crear el psicólogo")}finally{r(!1)}}),className:"space-y-4",children:[m.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[m.jsxs("div",{children:[m.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Nombre *"}),m.jsx("input",{type:"text",name:"nombre",value:N.nombre,onChange:D,className:"w-full p-2 border border-gray-300 rounded-md",required:!0})]}),m.jsxs("div",{children:[m.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Apellido *"}),m.jsx("input",{type:"text",name:"apellido",value:N.apellido,onChange:D,className:"w-full p-2 border border-gray-300 rounded-md",required:!0})]}),m.jsxs("div",{children:[m.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Género"}),m.jsxs("select",{name:"genero",value:N.genero,onChange:D,className:"w-full p-2 border border-gray-300 rounded-md",children:[m.jsx("option",{value:"masculino",children:"Masculino"}),m.jsx("option",{value:"femenino",children:"Femenino"})]})]}),m.jsxs("div",{children:[m.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Email"}),m.jsx("input",{type:"email",name:"email",value:N.email,onChange:D,className:"w-full p-2 border border-gray-300 rounded-md"})]}),m.jsxs("div",{children:[m.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Teléfono"}),m.jsx("input",{type:"text",name:"telefono",value:N.telefono,onChange:D,className:"w-full p-2 border border-gray-300 rounded-md"})]}),m.jsxs("div",{children:[m.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Especialidad"}),m.jsx("input",{type:"text",name:"especialidad",value:N.especialidad,onChange:D,className:"w-full p-2 border border-gray-300 rounded-md"})]}),m.jsxs("div",{children:[m.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Institución *"}),m.jsxs("select",{name:"institucion_id",value:N.institucion_id,onChange:D,className:"w-full p-2 border border-gray-300 rounded-md",required:!0,children:[m.jsx("option",{value:"",children:"Seleccione una institución"}),s.map(e=>m.jsx("option",{value:e.id,children:e.nombre},e.id))]})]}),m.jsxs("div",{className:"flex items-center",children:[m.jsx("input",{type:"checkbox",name:"activo",checked:N.activo,onChange:D,className:"h-4 w-4 text-blue-600 border-gray-300 rounded"}),m.jsx("label",{className:"ml-2 block text-sm text-gray-700",children:"Psicólogo Activo"})]})]}),m.jsxs("div",{className:"flex justify-end space-x-3 mt-6",children:[m.jsx("button",{type:"button",onClick:E,className:"px-4 py-2 bg-gray-200 text-gray-800 rounded-md hover:bg-gray-300",children:"Cancelar"}),m.jsx("button",{type:"submit",className:"px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700",disabled:a,children:a?m.jsxs(m.Fragment,{children:[m.jsx(x,{className:"animate-spin inline mr-2"}),"Guardando..."]}):u?"Actualizar":"Crear"})]})]})]})})]})},R=()=>{const[e,t]=C.useState([]),[s,i]=C.useState([]),[a,r]=C.useState([]),[n,c]=C.useState(!0),[u,h]=C.useState(!1),[j,v]=C.useState(null),[N,w]=C.useState("");C.useState("nombre"),C.useState("asc");const[_,S]=C.useState({institucion_id:"",genero:"",psicologo_id:"",edad_min:"",edad_max:""}),[P,E]=C.useState({nombre:"",apellido:"",genero:"masculino",fecha_nacimiento:"",documento:"",telefono:"",email:"",institucion_id:"",psicologo_id:"",activo:!0});C.useEffect(()=>{D(),I(),A()},[]);const D=()=>d(void 0,null,function*(){c(!0);try{const{data:e,error:s}=yield $.getPatients();if(s)throw s;const i=(Array.isArray(e)?e:[]).map(e=>o(l({},e),{edad:z(e.fecha_nacimiento)}));t(i)}catch(e){k.error("Error: "+(e.message||"Error al cargar los pacientes"))}finally{c(!1)}}),I=()=>d(void 0,null,function*(){try{const{data:e,error:t}=yield $.getInstitutions();if(t)throw t;i(e||[])}catch(e){k.error("Error: "+(e.message||"Error al cargar las instituciones"))}}),A=()=>d(void 0,null,function*(){try{const{data:e,error:t}=yield $.getPsychologists();if(t)throw t;r(e||[])}catch(e){k.error("Error: "+(e.message||"Error al cargar los psicólogos"))}}),L=(e=null)=>{v(e),E(e?{nombre:e.nombre||"",apellido:e.apellido||"",genero:e.genero||"masculino",fecha_nacimiento:e.fecha_nacimiento||"",documento:e.documento||"",telefono:e.telefono||"",email:e.email||"",institucion_id:e.institucion_id||"",psicologo_id:e.psicologo_id||"",activo:!1!==e.activo}:{nombre:"",apellido:"",genero:"masculino",fecha_nacimiento:"",documento:"",telefono:"",email:"",institucion_id:"",psicologo_id:"",activo:!0}),h(!0)},O=()=>{h(!1),v(null)},q=e=>{const{name:t,value:s,type:i,checked:a}=e.target;E(o(l({},P),{[t]:"checkbox"===i?a:s}))},z=e=>{if(!e)return"-";const t=new Date,s=new Date(e);let i=t.getFullYear()-s.getFullYear();const a=t.getMonth()-s.getMonth();return(a<0||0===a&&t.getDate()<s.getDate())&&i--,i},F=e=>{const t=s.find(t=>t.id===e);return t?t.nombre:"No asignada"},B=e=>{const t=a.find(t=>t.id===e);return t?`${t.nombre} ${t.apellido}`:"No asignado"},M=e.filter(e=>{var t,i,a,r,n,l,o;const c=N.toLowerCase(),d=!N||(null==(t=e.nombre)?void 0:t.toLowerCase().includes(c))||(null==(i=e.apellidos)?void 0:i.toLowerCase().includes(c))||(null==(a=e.documento_identidad)?void 0:a.toLowerCase().includes(c))||(null==(r=e.email)?void 0:r.toLowerCase().includes(c))||(null==(n=e.telefono)?void 0:n.toLowerCase().includes(c))||((null==(l=s.find(t=>t.id===e.institucion_id))?void 0:l.nombre)||"").toLowerCase().includes(c)||(null==(o=e.notas)?void 0:o.toLowerCase().includes(c)),m=!_.institucion_id||e.institucion_id===_.institucion_id,u=!_.genero||e.genero===_.genero,x=!_.psicologo_id||("null"===_.psicologo_id?!e.psicologo_id:e.psicologo_id===_.psicologo_id),p="number"==typeof e.edad?e.edad:-1,h=parseInt(_.edad_min),g=isNaN(h)||-1===p||p>=h,b=parseInt(_.edad_max),f=isNaN(b)||-1===p||p<=b;return d&&m&&u&&x&&g&&f});return s.map(e=>({value:e.id,label:e.nombre})),a.map(e=>({value:e.id,label:`${e.nombre} ${e.apellidos}`})),s.map(e=>({value:e.id,label:e.nombre})),a.map(e=>({value:e.id,label:`${e.nombre} ${e.apellidos}`})),m.jsxs("div",{className:"space-y-6",children:[m.jsxs("div",{className:"flex flex-col md:flex-row md:items-center md:justify-between gap-4",children:[m.jsxs("div",{children:[m.jsx("h2",{className:"text-xl font-bold text-gray-900",children:"Gestión de Pacientes"}),m.jsx("p",{className:"text-gray-600",children:"Administre los pacientes registrados en el sistema"})]}),m.jsxs("div",{className:"flex flex-col sm:flex-row gap-2",children:[m.jsxs("div",{className:"relative",children:[m.jsx("input",{type:"text",placeholder:"Buscar paciente...",className:"pl-10 pr-4 py-2 border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500",value:N,onChange:e=>w(e.target.value)}),m.jsx("div",{className:"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none",children:m.jsx("svg",{className:"h-5 w-5 text-gray-400",fill:"currentColor",viewBox:"0 0 20 20",children:m.jsx("path",{fillRule:"evenodd",d:"M8 4a4 4 0 100 8 4 4 0 000-8zM2 8a6 6 0 1110.89 3.476l4.817 4.817a1 1 0 01-1.414 1.414l-4.816-4.816A6 6 0 012 8z",clipRule:"evenodd"})})})]}),m.jsxs("button",{onClick:()=>L(),className:"flex items-center justify-center px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors",disabled:n,children:[m.jsx(p,{className:"mr-2"}),"Nuevo Paciente"]})]})]}),m.jsx("div",{className:"bg-white shadow overflow-hidden rounded-lg",children:n&&0===e.length?m.jsxs("div",{className:"flex justify-center items-center p-8",children:[m.jsx(x,{className:"animate-spin text-blue-600 text-2xl mr-2"}),m.jsx("span",{children:"Cargando pacientes..."})]}):0===M.length?m.jsx("div",{className:"text-center p-8 text-gray-500",children:N?"No se encontraron pacientes que coincidan con la búsqueda.":"No hay pacientes registrados."}):m.jsx("div",{className:"overflow-x-auto",children:m.jsxs("table",{className:"min-w-full divide-y divide-gray-200",children:[m.jsx("thead",{className:"bg-sky-800",children:m.jsxs("tr",{children:[m.jsx("th",{className:"px-6 py-3 text-left text-xs font-medium text-white uppercase tracking-wider",children:"Nombre"}),m.jsx("th",{className:"px-6 py-3 text-left text-xs font-medium text-white uppercase tracking-wider",children:"Documento"}),m.jsx("th",{className:"px-6 py-3 text-left text-xs font-medium text-white uppercase tracking-wider",children:"Edad"}),m.jsx("th",{className:"px-6 py-3 text-left text-xs font-medium text-white uppercase tracking-wider",children:"Contacto"}),m.jsx("th",{className:"px-6 py-3 text-left text-xs font-medium text-white uppercase tracking-wider",children:"Institución"}),m.jsx("th",{className:"px-6 py-3 text-left text-xs font-medium text-white uppercase tracking-wider",children:"Psicólogo"}),m.jsx("th",{className:"px-6 py-3 text-left text-xs font-medium text-white uppercase tracking-wider",children:"Estado"}),m.jsx("th",{className:"px-6 py-3 text-right text-xs font-medium text-white uppercase tracking-wider",children:"Acciones"})]})}),m.jsx("tbody",{className:"bg-white divide-y divide-gray-200",children:M.map(e=>m.jsxs("tr",{className:"hover:bg-gray-50",children:[m.jsx("td",{className:"px-6 py-4 whitespace-nowrap",children:m.jsxs("div",{className:"flex items-center",children:["femenino"===e.genero?m.jsx(f,{className:"text-pink-500 mr-2"}):m.jsx(y,{className:"text-blue-500 mr-2"}),m.jsxs("div",{className:"text-sm font-medium text-gray-900",children:[e.nombre," ",e.apellido]})]})}),m.jsx("td",{className:"px-6 py-4 whitespace-nowrap text-sm text-gray-500",children:e.documento||"-"}),m.jsx("td",{className:"px-6 py-4 whitespace-nowrap text-sm text-gray-500",children:z(e.fecha_nacimiento)}),m.jsx("td",{className:"px-6 py-4 whitespace-nowrap text-sm text-gray-500",children:e.email?m.jsxs("div",{children:[m.jsx("div",{children:e.email}),e.telefono&&m.jsx("div",{children:e.telefono})]}):e.telefono||"-"}),m.jsx("td",{className:"px-6 py-4 whitespace-nowrap text-sm text-gray-500",children:F(e.institucion_id)}),m.jsx("td",{className:"px-6 py-4 whitespace-nowrap text-sm text-gray-500",children:B(e.psicologo_id)}),m.jsx("td",{className:"px-6 py-4 whitespace-nowrap",children:m.jsx("span",{className:"px-2 inline-flex text-xs leading-5 font-semibold rounded-full "+(e.activo?"bg-green-100 text-green-800":"bg-red-100 text-red-800"),children:e.activo?"Activo":"Inactivo"})}),m.jsxs("td",{className:"px-6 py-4 whitespace-nowrap text-right text-sm font-medium",children:[m.jsx("button",{onClick:()=>L(e),className:"text-blue-600 hover:text-blue-900 mr-3",children:m.jsx(g,{})}),m.jsx("button",{onClick:()=>{return t=e.id,d(void 0,null,function*(){if(window.confirm("¿Está seguro de que desea eliminar este paciente?"))try{c(!0);const{error:e}=yield $.deletePatient(t);if(e)throw e;k.success("Paciente eliminado correctamente"),D()}catch(e){k.error("Error: "+(e.message||"Error al eliminar el paciente"))}finally{c(!1)}});var t},className:"text-red-600 hover:text-red-900",children:m.jsx(b,{})})]})]},e.id))})]})})}),u&&m.jsx("div",{className:"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50",children:m.jsxs("div",{className:"bg-white rounded-lg shadow-xl p-6 w-full max-w-3xl mx-4",children:[m.jsx("h2",{className:"text-xl font-bold mb-4",children:j?"Editar Paciente":"Nuevo Paciente"}),m.jsxs("form",{onSubmit:e=>d(void 0,null,function*(){if(e.preventDefault(),P.nombre.trim()?P.apellido.trim()?P.institucion_id?!P.email||/\S+@\S+\.\S+/.test(P.email)||(k.error("El email no es válido"),0):(k.error("Debe seleccionar una institución"),0):(k.error("El apellido es obligatorio"),0):(k.error("El nombre es obligatorio"),0))try{c(!0);const e={nombre:P.nombre,apellido:P.apellido,genero:P.genero,fecha_nacimiento:P.fecha_nacimiento||null,documento:P.documento||"",telefono:P.telefono||"",email:P.email||"",institucion_id:P.institucion_id,psicologo_id:P.psicologo_id||null,activo:P.activo,updated_at:(new Date).toISOString()};let t;if(j||(e.created_at=(new Date).toISOString()),j){if(t=yield $.updatePatient(j.id,e),t.error)throw t.error;k.success(`Paciente "${P.nombre}" actualizado correctamente`)}else{if(t=yield $.createPatient(e),t.error)throw t.error;k.success(`Paciente "${P.nombre}" creado correctamente`)}D(),O()}catch(t){k.error("Error: "+(t.message||(j?"Error al actualizar el paciente":"Error al crear el paciente")))}finally{c(!1)}}),className:"space-y-4",children:[m.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[m.jsxs("div",{children:[m.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Nombre *"}),m.jsx("input",{type:"text",name:"nombre",value:P.nombre,onChange:q,className:"w-full p-2 border border-gray-300 rounded-md",required:!0})]}),m.jsxs("div",{children:[m.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Apellido *"}),m.jsx("input",{type:"text",name:"apellido",value:P.apellido,onChange:q,className:"w-full p-2 border border-gray-300 rounded-md",required:!0})]}),m.jsxs("div",{children:[m.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Género"}),m.jsxs("select",{name:"genero",value:P.genero,onChange:q,className:"w-full p-2 border border-gray-300 rounded-md",children:[m.jsx("option",{value:"masculino",children:"Masculino"}),m.jsx("option",{value:"femenino",children:"Femenino"})]})]}),m.jsxs("div",{children:[m.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Fecha de Nacimiento"}),m.jsx("input",{type:"date",name:"fecha_nacimiento",value:P.fecha_nacimiento,onChange:q,className:"w-full p-2 border border-gray-300 rounded-md"})]}),m.jsxs("div",{children:[m.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Documento"}),m.jsx("input",{type:"text",name:"documento",value:P.documento,onChange:q,className:"w-full p-2 border border-gray-300 rounded-md"})]}),m.jsxs("div",{children:[m.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Teléfono"}),m.jsx("input",{type:"text",name:"telefono",value:P.telefono,onChange:q,className:"w-full p-2 border border-gray-300 rounded-md"})]}),m.jsxs("div",{children:[m.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Email"}),m.jsx("input",{type:"email",name:"email",value:P.email,onChange:q,className:"w-full p-2 border border-gray-300 rounded-md"})]}),m.jsxs("div",{children:[m.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Institución *"}),m.jsxs("select",{name:"institucion_id",value:P.institucion_id,onChange:q,className:"w-full p-2 border border-gray-300 rounded-md",required:!0,children:[m.jsx("option",{value:"",children:"Seleccione una institución"}),s.map(e=>m.jsx("option",{value:e.id,children:e.nombre},e.id))]})]}),m.jsxs("div",{children:[m.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Psicólogo"}),m.jsxs("select",{name:"psicologo_id",value:P.psicologo_id,onChange:q,className:"w-full p-2 border border-gray-300 rounded-md",children:[m.jsx("option",{value:"",children:"Seleccione un psicólogo"}),a.map(e=>m.jsxs("option",{value:e.id,children:[e.nombre," ",e.apellido]},e.id))]})]}),m.jsxs("div",{className:"flex items-center",children:[m.jsx("input",{type:"checkbox",name:"activo",checked:P.activo,onChange:q,className:"h-4 w-4 text-blue-600 border-gray-300 rounded"}),m.jsx("label",{className:"ml-2 block text-sm text-gray-700",children:"Paciente Activo"})]})]}),m.jsxs("div",{className:"flex justify-end space-x-3 mt-6",children:[m.jsx("button",{type:"button",onClick:O,className:"px-4 py-2 bg-gray-200 text-gray-800 rounded-md hover:bg-gray-300",children:"Cancelar"}),m.jsx("button",{type:"submit",className:"px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700",disabled:n,children:n?m.jsxs(m.Fragment,{children:[m.jsx(x,{className:"animate-spin inline mr-2"}),"Guardando..."]}):j?"Actualizar":"Crear"})]})]})]})})]})},T=({onConnectionChange:e})=>{const[t,s]=C.useState(!1),[i,a]=C.useState("No probado"),[r,n]=C.useState([]),[l,o]=C.useState(""),[c,x]=C.useState([]);return C.useEffect(()=>{var e;l&&(e=l,d(void 0,null,function*(){if(e){s(!0);try{const{data:t,error:s}=yield u.from(e).select("*").limit(10);if(s)throw s;x(t||[]),k.success(`Datos de la tabla ${e} cargados correctamente`)}catch(t){k.error(`Error al cargar datos: ${t.message||"Desconocido"}`),x([])}finally{s(!1)}}}))},[l]),m.jsxs("div",{className:"bg-white p-6 rounded-lg shadow-md",children:[m.jsx("h2",{className:"text-2xl font-bold mb-4 text-center",children:"Prueba de Conexión a Supabase"}),m.jsxs("div",{className:"mb-6",children:[m.jsxs("div",{className:"flex items-center justify-between mb-2",children:[m.jsx("span",{className:"font-semibold",children:"Estado de la conexión:"}),m.jsx("span",{className:"px-3 py-1 rounded-full text-sm "+("Conectado"===i?"bg-green-100 text-green-800":"No probado"===i?"bg-gray-100 text-gray-800":"bg-red-100 text-red-800"),children:i})]}),m.jsx("button",{onClick:()=>d(void 0,null,function*(){s(!0);try{const{data:t,error:s}=yield u.from("information_schema.columns").select("table_name").eq("table_schema","public").order("table_name");if(s)throw s;const i=[...new Set(t.map(e=>e.table_name))].map(e=>({name:e,schema:"public"}));a("Conectado"),n(i||[]),e&&e(!0),k.success("Conexión a Supabase establecida correctamente")}catch(t){try{const{data:t}=yield u.auth.getSession();a("Conectado (sin acceso a tablas)"),e&&e(!0),k.warning("Conexión establecida pero sin acceso a la información de tablas")}catch(i){a(`Error: ${t.message||"Desconocido"}`),e&&e(!1),k.error(`Error al conectar con Supabase: ${t.message||"Desconocido"}`)}}finally{s(!1)}}),disabled:t,className:"w-full bg-blue-600 text-white py-2 rounded-md hover:bg-blue-700 transition-colors disabled:bg-blue-300",children:t?"Probando...":"Probar Conexión"})]}),r.length>0&&m.jsxs("div",{className:"mb-6",children:[m.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Seleccionar tabla:"}),m.jsxs("select",{value:l,onChange:e=>o(e.target.value),className:"w-full border border-gray-300 rounded-md py-2 px-3",disabled:t,children:[m.jsx("option",{value:"",children:"Seleccione una tabla"}),r.map(e=>m.jsx("option",{value:e.name,children:e.name},e.name))]})]}),c.length>0&&m.jsxs("div",{children:[m.jsxs("h3",{className:"text-lg font-semibold mb-2",children:["Datos de ",l]}),m.jsx("div",{className:"overflow-x-auto",children:m.jsxs("table",{className:"min-w-full divide-y divide-gray-200",children:[m.jsx("thead",{className:"bg-gray-50",children:m.jsx("tr",{children:Object.keys(c[0]).map(e=>m.jsx("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:e},e))})}),m.jsx("tbody",{className:"bg-white divide-y divide-gray-200",children:c.map((e,t)=>m.jsx("tr",{children:Object.values(e).map((e,t)=>m.jsx("td",{className:"px-6 py-4 whitespace-nowrap text-sm text-gray-500",children:"object"==typeof e?JSON.stringify(e):String(e)},t))},t))})]})})]})]})},G=()=>{const[e,t]=C.useState(!1),[s,i]=C.useState(null),[a,r]=C.useState(!1),[n,c]=C.useState({puntajeDirecto:25,aptitudCodigo:"V",edad:13});C.useEffect(()=>{u(),x()},[]);const u=()=>d(void 0,null,function*(){const e=yield L.obtenerEstadisticasConversion();i(e)}),x=()=>d(void 0,null,function*(){const e=yield L.verificarFuncionesDisponibles();r(e)});return m.jsxs("div",{className:"space-y-6",children:[m.jsxs(P,{children:[m.jsx(E,{children:m.jsxs("h3",{className:"text-lg font-semibold text-gray-800",children:[m.jsx("i",{className:"fas fa-cogs mr-2 text-blue-600"}),"Estado del Sistema de Conversión"]})}),m.jsx(D,{children:m.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[m.jsxs("div",{className:"flex items-center",children:[m.jsx("div",{className:"w-3 h-3 rounded-full mr-3 "+(a?"bg-green-500":"bg-red-500")}),m.jsxs("span",{className:"text-sm",children:["Funciones de Supabase: ",a?"Disponibles":"No disponibles"]})]}),s&&m.jsxs(m.Fragment,{children:[m.jsxs("div",{className:"text-sm",children:[m.jsx("span",{className:"font-medium",children:"Total resultados:"})," ",s.totalResultados]}),m.jsxs("div",{className:"text-sm",children:[m.jsx("span",{className:"font-medium",children:"Con percentil:"})," ",s.conPercentil]}),m.jsxs("div",{className:"text-sm",children:[m.jsx("span",{className:"font-medium",children:"Sin percentil:"})," ",s.sinPercentil]}),m.jsxs("div",{className:"text-sm",children:[m.jsx("span",{className:"font-medium",children:"% Convertido:"})," ",s.porcentajeConvertido,"%"]})]})]})})]}),m.jsxs(P,{children:[m.jsx(E,{children:m.jsxs("h3",{className:"text-lg font-semibold text-gray-800",children:[m.jsx("i",{className:"fas fa-tools mr-2 text-green-600"}),"Acciones de Conversión"]})}),m.jsx(D,{children:m.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[m.jsxs(A,{onClick:()=>d(void 0,null,function*(){t(!0);try{yield L.configurarConversionAutomatica(),yield u(),yield x()}catch(e){}finally{t(!1)}}),disabled:e,variant:"primary",className:"w-full",children:[e?m.jsx("i",{className:"fas fa-spinner fa-spin mr-2"}):m.jsx("i",{className:"fas fa-play mr-2"}),"Configurar Conversión Automática"]}),m.jsxs(A,{onClick:()=>d(void 0,null,function*(){t(!0);try{yield L.recalcularTodosLosPercentiles(),yield u()}catch(e){}finally{t(!1)}}),disabled:e,variant:"secondary",className:"w-full",children:[e?m.jsx("i",{className:"fas fa-spinner fa-spin mr-2"}):m.jsx("i",{className:"fas fa-sync mr-2"}),"Recalcular Percentiles Existentes"]}),m.jsxs(A,{onClick:()=>d(void 0,null,function*(){t(!0);try{(yield L.verificarBaremos()).success&&alert("Baremos verificados correctamente. Ver consola para detalles.")}catch(e){}finally{t(!1)}}),disabled:e,variant:"outline",className:"w-full",children:[e?m.jsx("i",{className:"fas fa-spinner fa-spin mr-2"}):m.jsx("i",{className:"fas fa-check mr-2"}),"Verificar Baremos"]}),m.jsxs(A,{onClick:u,disabled:e,variant:"outline",className:"w-full",children:[e?m.jsx("i",{className:"fas fa-spinner fa-spin mr-2"}):m.jsx("i",{className:"fas fa-refresh mr-2"}),"Actualizar Estadísticas"]})]})})]}),m.jsxs(P,{children:[m.jsx(E,{children:m.jsxs("h3",{className:"text-lg font-semibold text-gray-800",children:[m.jsx("i",{className:"fas fa-flask mr-2 text-purple-600"}),"Probar Conversión PD → PC"]})}),m.jsxs(D,{children:[m.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-4 gap-4 mb-4",children:[m.jsxs("div",{children:[m.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Puntaje PD"}),m.jsx("input",{type:"number",value:n.puntajeDirecto,onChange:e=>c(o(l({},n),{puntajeDirecto:e.target.value})),className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent",min:"0",max:"100"})]}),m.jsxs("div",{children:[m.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Aptitud"}),m.jsxs("select",{value:n.aptitudCodigo,onChange:e=>c(o(l({},n),{aptitudCodigo:e.target.value})),className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent",children:[m.jsx("option",{value:"V",children:"V - Aptitud Verbal"}),m.jsx("option",{value:"E",children:"E - Aptitud Espacial"}),m.jsx("option",{value:"A",children:"A - Atención"}),m.jsx("option",{value:"R",children:"R - Razonamiento"}),m.jsx("option",{value:"N",children:"N - Aptitud Numérica"}),m.jsx("option",{value:"M",children:"M - Aptitud Mecánica"}),m.jsx("option",{value:"O",children:"O - Ortografía"})]})]}),m.jsxs("div",{children:[m.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Edad"}),m.jsxs("select",{value:n.edad,onChange:e=>c(o(l({},n),{edad:e.target.value})),className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent",children:[m.jsx("option",{value:"12",children:"12 años"}),m.jsx("option",{value:"13",children:"13 años"}),m.jsx("option",{value:"14",children:"14 años"})]})]}),m.jsx("div",{className:"flex items-end",children:m.jsxs(A,{onClick:()=>d(void 0,null,function*(){t(!0);try{const e=yield L.probarConversion(parseInt(n.puntajeDirecto),n.aptitudCodigo,parseInt(n.edad));e.success?alert(`Conversión exitosa: PD ${n.puntajeDirecto} → PC ${e.percentil}`):alert("Error en la conversión")}catch(e){}finally{t(!1)}}),disabled:e,variant:"primary",className:"w-full",children:[e?m.jsx("i",{className:"fas fa-spinner fa-spin mr-2"}):m.jsx("i",{className:"fas fa-calculator mr-2"}),"Probar"]})})]}),m.jsx("div",{className:"text-sm text-gray-600",children:m.jsxs("p",{children:[m.jsx("strong",{children:"Ejemplo:"})," PD 25 en Aptitud Verbal para 13 años debería dar PC 50 aproximadamente"]})})]})]}),m.jsxs(P,{children:[m.jsx(E,{children:m.jsxs("h3",{className:"text-lg font-semibold text-gray-800",children:[m.jsx("i",{className:"fas fa-info-circle mr-2 text-blue-600"}),"Información del Sistema"]})}),m.jsx(D,{children:m.jsxs("div",{className:"text-sm text-gray-700 space-y-2",children:[m.jsxs("p",{children:[m.jsx("strong",{children:"Conversión Automática:"})," Los nuevos resultados se convierten automáticamente usando triggers de Supabase."]}),m.jsxs("p",{children:[m.jsx("strong",{children:"Baremos:"})," Se utilizan las tablas de baremos para edades 12-13 y 13-14 años."]}),m.jsxs("p",{children:[m.jsx("strong",{children:"Recálculo:"})," Permite actualizar resultados existentes que no tienen percentil calculado."]}),m.jsxs("p",{children:[m.jsx("strong",{children:"Verificación:"})," Comprueba que las funciones y baremos estén correctamente configurados en Supabase."]})]})})]})]})},V=Object.freeze(Object.defineProperty({__proto__:null,default:()=>{const[e,t]=C.useState("institutions"),[s,i]=C.useState(!0),[a,r]=C.useState(!1),n=S(),l=new URLSearchParams(n.search).get("tab");C.useEffect(()=>{l&&["institutions","psychologists","patients","supabase","conversion"].includes(l)&&t(l);return(()=>{let e=document.getElementById("modal-root");e||(e=document.createElement("div"),e.id="modal-root",document.body.appendChild(e),e.style.position="relative",e.style.zIndex="9999")})(),setTimeout(()=>{i(!1)},1e3),()=>{const e=document.getElementById("modal-root");e&&0===e.childElementCount&&document.body.removeChild(e)}},[l]);const o=e=>{t(e)};return s?m.jsxs("div",{className:"flex flex-col items-center justify-center min-h-screen bg-gray-100",children:[m.jsx(x,{className:"animate-spin text-blue-600 text-4xl mb-4"}),m.jsx("h2",{className:"text-xl font-semibold text-gray-700",children:"Cargando Panel de Administración..."}),m.jsx("p",{className:"text-gray-500 mt-2",children:"Verificando permisos y cargando datos"})]}):m.jsxs("div",{className:"bg-gray-100 min-h-screen",children:[m.jsx(O,{title:"Panel de Administración",subtitle:"Gestión centralizada de recursos de la plataforma",icon:j}),m.jsx("main",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6",children:m.jsxs("div",{className:"bg-white rounded-lg shadow overflow-hidden",children:[m.jsx("div",{className:"border-b border-gray-200",children:m.jsxs("nav",{className:"flex -mb-px",children:[m.jsx("button",{className:`px-4 py-4 text-center text-sm font-medium ${"institutions"===e?"border-b-2 border-blue-500 text-amber-600":"text-gray-500 hover:text-gray-700 hover:border-gray-300"} focus:outline-none transition-colors w-1/5`,onClick:()=>o("institutions"),children:m.jsxs("div",{className:"flex items-center justify-center",children:[m.jsx(h,{className:"mr-2"}),m.jsx("span",{children:"Instituciones"})]})}),m.jsx("button",{className:`px-4 py-4 text-center text-sm font-medium ${"psychologists"===e?"border-b-2 border-blue-500 text-amber-600":"text-gray-500 hover:text-gray-700 hover:border-gray-300"} focus:outline-none transition-colors w-1/5`,onClick:()=>o("psychologists"),children:m.jsxs("div",{className:"flex items-center justify-center",children:[m.jsx(v,{className:"mr-2"}),m.jsx("span",{children:"Psicólogos"})]})}),m.jsx("button",{className:`px-4 py-4 text-center text-sm font-medium ${"patients"===e?"border-b-2 border-blue-500 text-amber-600":"text-gray-500 hover:text-gray-700 hover:border-gray-300"} focus:outline-none transition-colors w-1/5`,onClick:()=>o("patients"),children:m.jsxs("div",{className:"flex items-center justify-center",children:[m.jsx(N,{className:"mr-2"}),m.jsx("span",{children:"Pacientes"})]})}),m.jsx("button",{className:`px-4 py-4 text-center text-sm font-medium ${"conversion"===e?"border-b-2 border-blue-500 text-green-600":"text-gray-500 hover:text-gray-700 hover:border-gray-300"} focus:outline-none transition-colors w-1/5`,onClick:()=>o("conversion"),children:m.jsxs("div",{className:"flex items-center justify-center",children:[m.jsx("i",{className:"fas fa-exchange-alt mr-2"}),m.jsx("span",{children:"Conversión PD→PC"})]})}),m.jsx("button",{className:`px-4 py-4 text-center text-sm font-medium ${"supabase"===e?"border-b-2 border-blue-500 text-blue-600":"text-gray-500 hover:text-gray-700 hover:border-gray-300"} focus:outline-none transition-colors w-1/5`,onClick:()=>o("supabase"),children:m.jsxs("div",{className:"flex items-center justify-center",children:[m.jsx(w,{className:"mr-2"}),m.jsx("span",{children:"Supabase"})]})})]})}),m.jsx("div",{className:"p-6",children:(()=>{switch(e){case"institutions":default:return m.jsx(B,{});case"psychologists":return m.jsx(M,{});case"patients":return m.jsx(R,{});case"supabase":return m.jsx(T,{onConnectionChange:r});case"conversion":return m.jsx(G,{})}})()})]})}),m.jsx("footer",{className:"bg-white border-t border-gray-200 py-8",children:m.jsx("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8",children:m.jsxs("p",{className:"text-center text-gray-500 text-sm",children:["© ",(new Date).getFullYear()," Sistema de Gestión Psicológica - Panel de Administración"]})})})]})}},Symbol.toStringTag,{value:"Module"}));export{V as A,A as B,P as C,q as I,O as P,L as S,D as a,E as b,I as c};
