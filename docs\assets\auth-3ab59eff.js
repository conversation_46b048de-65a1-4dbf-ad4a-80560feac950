var e=(e,t,r)=>new Promise((s,i)=>{var n=e=>{try{a(r.next(e))}catch(t){i(t)}},o=e=>{try{a(r.throw(e))}catch(t){i(t)}},a=e=>e.done?s(e.value):Promise.resolve(e.value).then(n,o);a((r=r.apply(e,t)).next())});import{G as t}from"./ui-vendor-9705a4a1.js";import{r,g as s,c as i}from"./react-vendor-99be060c.js";import"./utils-vendor-4d1206d7.js";var n={exports:{}},o={},a=r,l=Symbol.for("react.element"),c=Symbol.for("react.fragment"),h=Object.prototype.hasOwnProperty,u=a.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED.ReactCurrentOwner,d={key:!0,ref:!0,__self:!0,__source:!0};function f(e,t,r){var s,i={},n=null,o=null;for(s in void 0!==r&&(n=""+r),void 0!==t.key&&(n=""+t.key),void 0!==t.ref&&(o=t.ref),t)h.call(t,s)&&!d.hasOwnProperty(s)&&(i[s]=t[s]);if(e&&e.defaultProps)for(s in t=e.defaultProps)void 0===i[s]&&(i[s]=t[s]);return{$$typeof:l,type:e,key:n,ref:o,props:i,_owner:u.current}}o.Fragment=c,o.jsx=f,o.jsxs=f,n.exports=o;var p=n.exports;const g=r.createContext(),v=({children:e})=>{const[t,s]=r.useState("administrador"),i={user:{id:"dev-user",email:"<EMAIL>",nombre:"Usuario",apellido:"Desarrollo",tipo_usuario:t},loading:!1,error:null,login:()=>Promise.resolve({success:!0}),logout:()=>Promise.resolve({success:!0}),isAuthenticated:!0,userRole:t,isAdmin:"administrador"===t,isPsicologo:"psicologo"===t,isCandidato:"candidato"===t,setUserType:s};return p.jsx(g.Provider,{value:i,children:e})},y=()=>{const e=r.useContext(g);if(!e)throw new Error("useNoAuth debe usarse dentro de NoAuthProvider");return e},m={},_=function(e,t,r){if(!t||0===t.length)return e();const s=document.getElementsByTagName("link");return Promise.all(t.map(e=>{if((e=function(e){return"/Bat-7/"+e}(e))in m)return;m[e]=!0;const t=e.endsWith(".css"),i=t?'[rel="stylesheet"]':"";if(!!r)for(let r=s.length-1;r>=0;r--){const i=s[r];if(i.href===e&&(!t||"stylesheet"===i.rel))return}else if(document.querySelector(`link[href="${e}"]${i}`))return;const n=document.createElement("link");return n.rel=t?"stylesheet":"modulepreload",t||(n.as="script",n.crossOrigin=""),n.href=e,document.head.appendChild(n),t?new Promise((t,r)=>{n.addEventListener("load",t),n.addEventListener("error",()=>r(new Error(`Unable to preload CSS for ${e}`)))}):void 0})).then(()=>e()).catch(e=>{const t=new Event("vite:preloadError",{cancelable:!0});if(t.payload=e,window.dispatchEvent(t),!t.defaultPrevented)throw e})};function b(e){return t({tag:"svg",attr:{viewBox:"0 0 448 512"},child:[{tag:"path",attr:{d:"M34.9 289.5l-22.2-22.2c-9.4-9.4-9.4-24.6 0-33.9L207 39c9.4-9.4 24.6-9.4 33.9 0l194.3 194.3c9.4 9.4 9.4 24.6 0 33.9L413 289.4c-9.5 9.5-25 9.3-34.3-.4L264 168.6V456c0 13.3-10.7 24-24 24h-32c-13.3 0-24-10.7-24-24V168.6L69.2 289.1c-9.3 9.8-24.8 10-34.3.4z"}}]})(e)}function w(e){return t({tag:"svg",attr:{viewBox:"0 0 448 512"},child:[{tag:"path",attr:{d:"M224 512c35.32 0 63.97-28.65 63.97-64H160.03c0 35.35 28.65 64 63.97 64zm215.39-149.71c-19.32-20.76-55.47-51.99-55.47-154.29 0-77.7-54.48-139.9-127.94-155.16V32c0-17.67-14.32-32-31.98-32s-31.98 14.33-31.98 32v20.84C118.56 68.1 64.08 130.3 64.08 208c0 102.3-36.15 133.53-55.47 154.29-6 6.45-8.66 14.16-8.61 21.71.11 16.4 12.98 32 32.1 32h383.8c19.12 0 32-15.6 32.1-32 .05-7.55-2.61-15.27-8.61-21.71z"}}]})(e)}function k(e){return t({tag:"svg",attr:{viewBox:"0 0 448 512"},child:[{tag:"path",attr:{d:"M448 360V24c0-13.3-10.7-24-24-24H96C43 0 0 43 0 96v320c0 53 43 96 96 96h328c13.3 0 24-10.7 24-24v-16c0-7.5-3.5-14.3-8.9-18.7-4.2-15.4-4.2-59.3 0-74.7 5.4-4.3 8.9-11.1 8.9-18.6zM128 134c0-3.3 2.7-6 6-6h212c3.3 0 6 2.7 6 6v20c0 3.3-2.7 6-6 6H134c-3.3 0-6-2.7-6-6v-20zm0 64c0-3.3 2.7-6 6-6h212c3.3 0 6 2.7 6 6v20c0 3.3-2.7 6-6 6H134c-3.3 0-6-2.7-6-6v-20zm253.4 250H96c-17.7 0-32-14.3-32-32 0-17.6 14.4-32 32-32h285.4c-1.9 17.1-1.9 46.9 0 64z"}}]})(e)}function T(e){return t({tag:"svg",attr:{viewBox:"0 0 448 512"},child:[{tag:"path",attr:{d:"M436 480h-20V24c0-13.255-10.745-24-24-24H56C42.745 0 32 10.745 32 24v456H12c-6.627 0-12 5.373-12 12v20h448v-20c0-6.627-5.373-12-12-12zM128 76c0-6.627 5.373-12 12-12h40c6.627 0 12 5.373 12 12v40c0 6.627-5.373 12-12 12h-40c-6.627 0-12-5.373-12-12V76zm0 96c0-6.627 5.373-12 12-12h40c6.627 0 12 5.373 12 12v40c0 6.627-5.373 12-12 12h-40c-6.627 0-12-5.373-12-12v-40zm52 148h-40c-6.627 0-12-5.373-12-12v-40c0-6.627 5.373-12 12-12h40c6.627 0 12 5.373 12 12v40c0 6.627-5.373 12-12 12zm76 160h-64v-84c0-6.627 5.373-12 12-12h40c6.627 0 12 5.373 12 12v84zm64-172c0 6.627-5.373 12-12 12h-40c-6.627 0-12-5.373-12-12v-40c0-6.627 5.373-12 12-12h40c6.627 0 12 5.373 12 12v40zm0-96c0 6.627-5.373 12-12 12h-40c-6.627 0-12-5.373-12-12v-40c0-6.627 5.373-12 12-12h40c6.627 0 12 5.373 12 12v40zm0-96c0 6.627-5.373 12-12 12h-40c-6.627 0-12-5.373-12-12V76c0-6.627 5.373-12 12-12h40c6.627 0 12 5.373 12 12v40z"}}]})(e)}function S(e){return t({tag:"svg",attr:{viewBox:"0 0 448 512"},child:[{tag:"path",attr:{d:"M0 464c0 26.5 21.5 48 48 48h352c26.5 0 48-21.5 48-48V192H0v272zm320-196c0-6.6 5.4-12 12-12h40c6.6 0 12 5.4 12 12v40c0 6.6-5.4 12-12 12h-40c-6.6 0-12-5.4-12-12v-40zm0 128c0-6.6 5.4-12 12-12h40c6.6 0 12 5.4 12 12v40c0 6.6-5.4 12-12 12h-40c-6.6 0-12-5.4-12-12v-40zM192 268c0-6.6 5.4-12 12-12h40c6.6 0 12 5.4 12 12v40c0 6.6-5.4 12-12 12h-40c-6.6 0-12-5.4-12-12v-40zm0 128c0-6.6 5.4-12 12-12h40c6.6 0 12 5.4 12 12v40c0 6.6-5.4 12-12 12h-40c-6.6 0-12-5.4-12-12v-40zM64 268c0-6.6 5.4-12 12-12h40c6.6 0 12 5.4 12 12v40c0 6.6-5.4 12-12 12H76c-6.6 0-12-5.4-12-12v-40zm0 128c0-6.6 5.4-12 12-12h40c6.6 0 12 5.4 12 12v40c0 6.6-5.4 12-12 12H76c-6.6 0-12-5.4-12-12v-40zM400 64h-48V16c0-8.8-7.2-16-16-16h-32c-8.8 0-16 7.2-16 16v48H160V16c0-8.8-7.2-16-16-16h-32c-8.8 0-16 7.2-16 16v48H48C21.5 64 0 85.5 0 112v48h448v-48c0-26.5-21.5-48-48-48z"}}]})(e)}function E(e){return t({tag:"svg",attr:{viewBox:"0 0 512 512"},child:[{tag:"path",attr:{d:"M332.8 320h38.4c6.4 0 12.8-6.4 12.8-12.8V172.8c0-6.4-6.4-12.8-12.8-12.8h-38.4c-6.4 0-12.8 6.4-12.8 12.8v134.4c0 6.4 6.4 12.8 12.8 12.8zm96 0h38.4c6.4 0 12.8-6.4 12.8-12.8V76.8c0-6.4-6.4-12.8-12.8-12.8h-38.4c-6.4 0-12.8 6.4-12.8 12.8v230.4c0 6.4 6.4 12.8 12.8 12.8zm-288 0h38.4c6.4 0 12.8-6.4 12.8-12.8v-70.4c0-6.4-6.4-12.8-12.8-12.8h-38.4c-6.4 0-12.8 6.4-12.8 12.8v70.4c0 6.4 6.4 12.8 12.8 12.8zm96 0h38.4c6.4 0 12.8-6.4 12.8-12.8V108.8c0-6.4-6.4-12.8-12.8-12.8h-38.4c-6.4 0-12.8 6.4-12.8 12.8v198.4c0 6.4 6.4 12.8 12.8 12.8zM496 384H64V80c0-8.84-7.16-16-16-16H16C7.16 64 0 71.16 0 80v336c0 17.67 14.33 32 32 32h464c8.84 0 16-7.16 16-16v-32c0-8.84-7.16-16-16-16z"}}]})(e)}function j(e){return t({tag:"svg",attr:{viewBox:"0 0 512 512"},child:[{tag:"path",attr:{d:"M496 384H64V80c0-8.84-7.16-16-16-16H16C7.16 64 0 71.16 0 80v336c0 17.67 14.33 32 32 32h464c8.84 0 16-7.16 16-16v-32c0-8.84-7.16-16-16-16zM464 96H345.94c-21.38 0-32.09 25.85-16.97 40.97l32.4 32.4L288 242.75l-73.37-73.37c-12.5-12.5-32.76-12.5-45.25 0l-68.69 68.69c-6.25 6.25-6.25 16.38 0 22.63l22.62 22.62c6.25 6.25 16.38 6.25 22.63 0L192 237.25l73.37 73.37c12.5 12.5 32.76 12.5 45.25 0l96-96 32.4 32.4c15.12 15.12 40.97 4.41 40.97-16.97V112c.01-8.84-7.15-16-15.99-16z"}}]})(e)}function P(e){return t({tag:"svg",attr:{viewBox:"0 0 512 512"},child:[{tag:"path",attr:{d:"M173.898 439.404l-166.4-166.4c-9.997-9.997-9.997-26.206 0-36.204l36.203-36.204c9.997-9.998 26.207-9.998 36.204 0L192 312.69 432.095 72.596c9.997-9.997 26.207-9.997 36.204 0l36.203 36.204c9.997 9.997 9.997 26.206 0 36.204l-294.4 294.401c-9.998 9.997-26.207 9.997-36.204-.001z"}}]})(e)}function O(e){return t({tag:"svg",attr:{viewBox:"0 0 448 512"},child:[{tag:"path",attr:{d:"M207.029 381.476L12.686 187.132c-9.373-9.373-9.373-24.569 0-33.941l22.667-22.667c9.357-9.357 24.522-9.375 33.901-.04L224 284.505l154.745-154.021c9.379-9.335 24.544-9.317 33.901.04l22.667 22.667c9.373 9.373 9.373 24.569 0 33.941L240.971 381.476c-9.373 9.372-24.569 9.372-33.942 0z"}}]})(e)}function x(e){return t({tag:"svg",attr:{viewBox:"0 0 320 512"},child:[{tag:"path",attr:{d:"M34.52 239.03L228.87 44.69c9.37-9.37 24.57-9.37 33.94 0l22.67 22.67c9.36 9.36 9.37 24.52.04 33.9L131.49 256l154.02 154.75c9.34 9.38 9.32 24.54-.04 33.9l-22.67 22.67c-9.37 9.37-24.57 9.37-33.94 0L34.52 272.97c-9.37-9.37-9.37-24.57 0-33.94z"}}]})(e)}function C(e){return t({tag:"svg",attr:{viewBox:"0 0 320 512"},child:[{tag:"path",attr:{d:"M285.476 272.971L91.132 467.314c-9.373 9.373-24.569 9.373-33.941 0l-22.667-22.667c-9.357-9.357-9.375-24.522-.04-33.901L188.505 256 34.484 101.255c-9.335-9.379-9.317-24.544.04-33.901l22.667-22.667c9.373-9.373 24.569-9.373 33.941 0L285.475 239.03c9.373 9.372 9.373 24.568.001 33.941z"}}]})(e)}function A(e){return t({tag:"svg",attr:{viewBox:"0 0 448 512"},child:[{tag:"path",attr:{d:"M240.971 130.524l194.343 194.343c9.373 9.373 9.373 24.569 0 33.941l-22.667 22.667c-9.357 9.357-24.522 9.375-33.901.04L224 227.495 69.255 381.516c-9.379 9.335-24.544 9.317-33.901-.04l-22.667-22.667c-9.373-9.373-9.373-24.569 0-33.941L207.03 130.525c9.372-9.373 24.568-9.373 33.941-.001z"}}]})(e)}function $(e){return t({tag:"svg",attr:{viewBox:"0 0 384 512"},child:[{tag:"path",attr:{d:"M336 64h-80c0-35.3-28.7-64-64-64s-64 28.7-64 64H48C21.5 64 0 85.5 0 112v352c0 26.5 21.5 48 48 48h288c26.5 0 48-21.5 48-48V112c0-26.5-21.5-48-48-48zM192 40c13.3 0 24 10.7 24 24s-10.7 24-24 24-24-10.7-24-24 10.7-24 24-24zm121.2 231.8l-143 141.8c-4.7 4.7-12.3 4.6-17-.1l-82.6-83.3c-4.7-4.7-4.6-12.3.1-17L99.1 285c4.7-4.7 12.3-4.6 17 .1l46 46.4 106-105.2c4.7-4.7 12.3-4.6 17 .1l28.2 28.4c4.7 4.8 4.6 12.3-.1 17z"}}]})(e)}function z(e){return t({tag:"svg",attr:{viewBox:"0 0 384 512"},child:[{tag:"path",attr:{d:"M336 64h-80c0-35.3-28.7-64-64-64s-64 28.7-64 64H48C21.5 64 0 85.5 0 112v352c0 26.5 21.5 48 48 48h288c26.5 0 48-21.5 48-48V112c0-26.5-21.5-48-48-48zM96 424c-13.3 0-24-10.7-24-24s10.7-24 24-24 24 10.7 24 24-10.7 24-24 24zm0-96c-13.3 0-24-10.7-24-24s10.7-24 24-24 24 10.7 24 24-10.7 24-24 24zm0-96c-13.3 0-24-10.7-24-24s10.7-24 24-24 24 10.7 24 24-10.7 24-24 24zm96-192c13.3 0 24 10.7 24 24s-10.7 24-24 24-24-10.7-24-24 10.7-24 24-24zm128 368c0 4.4-3.6 8-8 8H168c-4.4 0-8-3.6-8-8v-16c0-4.4 3.6-8 8-8h144c4.4 0 8 3.6 8 8v16zm0-96c0 4.4-3.6 8-8 8H168c-4.4 0-8-3.6-8-8v-16c0-4.4 3.6-8 8-8h144c4.4 0 8 3.6 8 8v16zm0-96c0 4.4-3.6 8-8 8H168c-4.4 0-8-3.6-8-8v-16c0-4.4 3.6-8 8-8h144c4.4 0 8 3.6 8 8v16z"}}]})(e)}function R(e){return t({tag:"svg",attr:{viewBox:"0 0 512 512"},child:[{tag:"path",attr:{d:"M256,8C119,8,8,119,8,256S119,504,256,504,504,393,504,256,393,8,256,8Zm92.49,313h0l-20,25a16,16,0,0,1-22.49,2.5h0l-67-49.72a40,40,0,0,1-15-31.23V112a16,16,0,0,1,16-16h32a16,16,0,0,1,16,16V256l58,42.5A16,16,0,0,1,348.49,321Z"}}]})(e)}function I(e){return t({tag:"svg",attr:{viewBox:"0 0 512 512"},child:[{tag:"path",attr:{d:"M487.4 315.7l-42.6-24.6c4.3-23.2 4.3-47 0-70.2l42.6-24.6c4.9-2.8 7.1-8.6 5.5-14-11.1-35.6-30-67.8-54.7-94.6-3.8-4.1-10-5.1-14.8-2.3L380.8 110c-17.9-15.4-38.5-27.3-60.8-35.1V25.8c0-5.6-3.9-10.5-9.4-11.7-36.7-8.2-74.3-7.8-109.2 0-5.5 1.2-9.4 6.1-9.4 11.7V75c-22.2 7.9-42.8 19.8-60.8 35.1L88.7 85.5c-4.9-2.8-11-1.9-14.8 2.3-24.7 26.7-43.6 58.9-54.7 94.6-1.7 5.4.6 11.2 5.5 14L67.3 221c-4.3 23.2-4.3 47 0 70.2l-42.6 24.6c-4.9 2.8-7.1 8.6-5.5 14 11.1 35.6 30 67.8 54.7 94.6 3.8 4.1 10 5.1 14.8 2.3l42.6-24.6c17.9 15.4 38.5 27.3 60.8 35.1v49.2c0 5.6 3.9 10.5 9.4 11.7 36.7 8.2 74.3 7.8 109.2 0 5.5-1.2 9.4-6.1 9.4-11.7v-49.2c22.2-7.9 42.8-19.8 60.8-35.1l42.6 24.6c4.9 2.8 11 1.9 14.8-2.3 24.7-26.7 43.6-58.9 54.7-94.6 1.5-5.5-.7-11.3-5.6-14.1zM256 336c-44.1 0-80-35.9-80-80s35.9-80 80-80 80 35.9 80 80-35.9 80-80 80z"}}]})(e)}function L(e){return t({tag:"svg",attr:{viewBox:"0 0 448 512"},child:[{tag:"path",attr:{d:"M448 73.143v45.714C448 159.143 347.667 192 224 192S0 159.143 0 118.857V73.143C0 32.857 100.333 0 224 0s224 32.857 224 73.143zM448 176v102.857C448 319.143 347.667 352 224 352S0 319.143 0 278.857V176c48.125 33.143 136.208 48.572 224 48.572S399.874 209.143 448 176zm0 160v102.857C448 479.143 347.667 512 224 512S0 479.143 0 438.857V336c48.125 33.143 136.208 48.572 224 48.572S399.874 369.143 448 336z"}}]})(e)}function M(e){return t({tag:"svg",attr:{viewBox:"0 0 512 512"},child:[{tag:"path",attr:{d:"M216 0h80c13.3 0 24 10.7 24 24v168h87.7c17.8 0 26.7 21.5 14.1 34.1L269.7 378.3c-7.5 7.5-19.8 7.5-27.3 0L90.1 226.1c-12.6-12.6-3.7-34.1 14.1-34.1H192V24c0-13.3 10.7-24 24-24zm296 376v112c0 13.3-10.7 24-24 24H24c-13.3 0-24-10.7-24-24V376c0-13.3 10.7-24 24-24h146.7l49 49c20.1 20.1 52.5 20.1 72.6 0l49-49H488c13.3 0 24 10.7 24 24zm-124 88c0-11-9-20-20-20s-20 9-20 20 9 20 20 20 20-9 20-20zm64 0c0-11-9-20-20-20s-20 9-20 20 9 20 20 20 20-9 20-20z"}}]})(e)}function U(e){return t({tag:"svg",attr:{viewBox:"0 0 576 512"},child:[{tag:"path",attr:{d:"M402.6 83.2l90.2 90.2c3.8 3.8 3.8 10 0 13.8L274.4 405.6l-92.8 10.3c-12.4 1.4-22.9-9.1-21.5-21.5l10.3-92.8L388.8 83.2c3.8-3.8 10-3.8 13.8 0zm162-22.9l-48.8-48.8c-15.2-15.2-39.9-15.2-55.2 0l-35.4 35.4c-3.8 3.8-3.8 10 0 13.8l90.2 90.2c3.8 3.8 10 3.8 13.8 0l35.4-35.4c15.2-15.3 15.2-40 0-55.2zM384 346.2V448H64V128h229.8c3.2 0 6.2-1.3 8.5-3.5l40-40c7.6-7.6 2.2-20.5-8.5-20.5H48C21.5 64 0 85.5 0 112v352c0 26.5 21.5 48 48 48h352c26.5 0 48-21.5 48-48V306.2c0-10.7-12.9-16-20.5-8.5l-40 40c-2.2 2.3-3.5 5.3-3.5 8.5z"}}]})(e)}function B(e){return t({tag:"svg",attr:{viewBox:"0 0 512 512"},child:[{tag:"path",attr:{d:"M502.3 190.8c3.9-3.1 9.7-.2 9.7 4.7V400c0 26.5-21.5 48-48 48H48c-26.5 0-48-21.5-48-48V195.6c0-5 5.7-7.8 9.7-4.7 22.4 17.4 52.1 39.5 154.1 113.6 21.1 15.4 56.7 47.8 92.2 47.6 35.7.3 72-32.8 92.3-47.6 102-74.1 131.6-96.3 154-113.7zM256 320c23.2.4 56.6-29.2 73.4-41.4 132.7-96.3 142.8-104.7 173.4-128.7 5.8-4.5 9.2-11.5 9.2-18.9v-19c0-26.5-21.5-48-48-48H48C21.5 64 0 85.5 0 112v19c0 7.4 3.4 14.3 9.2 18.9 30.6 23.9 40.7 32.4 173.4 128.7 16.8 12.2 50.2 41.8 73.4 41.4z"}}]})(e)}function D(e){return t({tag:"svg",attr:{viewBox:"0 0 576 512"},child:[{tag:"path",attr:{d:"M569.517 440.013C587.975 472.007 564.806 512 527.94 512H48.054c-36.937 0-59.999-40.055-41.577-71.987L246.423 23.985c18.467-32.009 64.72-31.951 83.154 0l239.94 416.028zM288 354c-25.405 0-46 20.595-46 46s20.595 46 46 46 46-20.595 46-46-20.595-46-46-46zm-43.673-165.346l7.418 136c.347 6.364 5.609 11.346 11.982 11.346h48.546c6.373 0 11.635-4.982 11.982-11.346l7.418-136c.375-6.874-5.098-12.654-11.982-12.654h-63.383c-6.884 0-12.356 5.78-11.981 12.654z"}}]})(e)}function H(e){return t({tag:"svg",attr:{viewBox:"0 0 576 512"},child:[{tag:"path",attr:{d:"M572.52 241.4C518.29 135.59 410.93 64 288 64S57.68 135.64 3.48 241.41a32.35 32.35 0 0 0 0 29.19C57.71 376.41 165.07 448 288 448s230.32-71.64 284.52-177.41a32.35 32.35 0 0 0 0-29.19zM288 400a144 144 0 1 1 144-144 143.93 143.93 0 0 1-144 144zm0-240a95.31 95.31 0 0 0-25.31 3.79 47.85 47.85 0 0 1-66.9 66.9A95.78 95.78 0 1 0 288 160z"}}]})(e)}function N(e){return t({tag:"svg",attr:{viewBox:"0 0 256 512"},child:[{tag:"path",attr:{d:"M128 0c35.346 0 64 28.654 64 64s-28.654 64-64 64c-35.346 0-64-28.654-64-64S92.654 0 128 0m119.283 354.179l-48-192A24 24 0 0 0 176 144h-11.36c-22.711 10.443-49.59 10.894-73.28 0H80a24 24 0 0 0-23.283 18.179l-48 192C4.935 369.305 16.383 384 32 384h56v104c0 13.255 10.745 24 24 24h32c13.255 0 24-10.745 24-24V384h56c15.591 0 27.071-14.671 23.283-29.821z"}}]})(e)}function V(e){return t({tag:"svg",attr:{viewBox:"0 0 384 512"},child:[{tag:"path",attr:{d:"M224 136V0H24C10.7 0 0 10.7 0 24v464c0 13.3 10.7 24 24 24h336c13.3 0 24-10.7 24-24V160H248c-13.2 0-24-10.8-24-24zm64 236c0 6.6-5.4 12-12 12H108c-6.6 0-12-5.4-12-12v-8c0-6.6 5.4-12 12-12h168c6.6 0 12 5.4 12 12v8zm0-64c0 6.6-5.4 12-12 12H108c-6.6 0-12-5.4-12-12v-8c0-6.6 5.4-12 12-12h168c6.6 0 12 5.4 12 12v8zm0-72v8c0 6.6-5.4 12-12 12H108c-6.6 0-12-5.4-12-12v-8c0-6.6 5.4-12 12-12h168c6.6 0 12 5.4 12 12zm96-114.1v6.1H256V0h6.1c6.4 0 12.5 2.5 17 7l97.9 98c4.5 4.5 7 10.6 7 16.9z"}}]})(e)}function q(e){return t({tag:"svg",attr:{viewBox:"0 0 512 512"},child:[{tag:"path",attr:{d:"M487.976 0H24.028C2.71 0-8.047 25.866 7.058 40.971L192 225.941V432c0 7.831 3.821 15.17 10.237 19.662l80 55.98C298.02 518.69 320 507.493 320 487.98V225.941l184.947-184.97C520.021 25.896 509.338 0 487.976 0z"}}]})(e)}function F(e){return t({tag:"svg",attr:{viewBox:"0 0 192 512"},child:[{tag:"path",attr:{d:"M20 424.229h20V279.771H20c-11.046 0-20-8.954-20-20V212c0-11.046 8.954-20 20-20h112c11.046 0 20 8.954 20 20v212.229h20c11.046 0 20 8.954 20 20V492c0 11.046-8.954 20-20 20H20c-11.046 0-20-8.954-20-20v-47.771c0-11.046 8.954-20 20-20zM96 0C56.235 0 24 32.235 24 72s32.235 72 72 72 72-32.235 72-72S135.764 0 96 0z"}}]})(e)}function J(e){return t({tag:"svg",attr:{viewBox:"0 0 512 512"},child:[{tag:"path",attr:{d:"M512 176.001C512 273.203 433.202 352 336 352c-11.22 0-22.19-1.062-32.827-3.069l-24.012 27.014A23.999 23.999 0 0 1 261.223 384H224v40c0 13.255-10.745 24-24 24h-40v40c0 13.255-10.745 24-24 24H24c-13.255 0-24-10.745-24-24v-78.059c0-6.365 2.529-12.47 7.029-16.971l161.802-161.802C163.108 213.814 160 195.271 160 176 160 78.798 238.797.001 335.999 0 433.488-.001 512 78.511 512 176.001zM336 128c0 26.51 21.49 48 48 48s48-21.49 48-48-21.49-48-48-48-48 21.49-48 48z"}}]})(e)}function K(e){return t({tag:"svg",attr:{viewBox:"0 0 640 512"},child:[{tag:"path",attr:{d:"M152.1 236.2c-3.5-12.1-7.8-33.2-7.8-33.2h-.5s-4.3 21.1-7.8 33.2l-11.1 37.5H163zM616 96H336v320h280c13.3 0 24-10.7 24-24V120c0-13.3-10.7-24-24-24zm-24 120c0 6.6-5.4 12-12 12h-11.4c-6.9 23.6-21.7 47.4-42.7 69.9 8.4 6.4 17.1 12.5 26.1 18 5.5 3.4 7.3 10.5 4.1 16.2l-7.9 13.9c-3.4 5.9-10.9 7.8-16.7 4.3-12.6-7.8-24.5-16.1-35.4-24.9-10.9 8.7-22.7 17.1-35.4 24.9-5.8 3.5-13.3 1.6-16.7-4.3l-7.9-13.9c-3.2-5.6-1.4-12.8 4.2-16.2 9.3-5.7 18-11.7 26.1-18-7.9-8.4-14.9-17-21-25.7-4-5.7-2.2-13.6 3.7-17.1l6.5-3.9 7.3-4.3c5.4-3.2 12.4-1.7 16 3.4 5 7 10.8 14 17.4 20.9 13.5-14.2 23.8-28.9 30-43.2H412c-6.6 0-12-5.4-12-12v-16c0-6.6 5.4-12 12-12h64v-16c0-6.6 5.4-12 12-12h16c6.6 0 12 5.4 12 12v16h64c6.6 0 12 5.4 12 12zM0 120v272c0 13.3 10.7 24 24 24h280V96H24c-13.3 0-24 10.7-24 24zm58.9 216.1L116.4 167c1.7-4.9 6.2-8.1 11.4-8.1h32.5c5.1 0 9.7 3.3 11.4 8.1l57.5 169.1c2.6 7.8-3.1 15.9-11.4 15.9h-22.9a12 12 0 0 1-11.5-8.6l-9.4-31.9h-60.2l-9.1 31.8c-1.5 5.1-6.2 8.7-11.5 8.7H70.3c-8.2 0-14-8.1-11.4-15.9z"}}]})(e)}function G(e){return t({tag:"svg",attr:{viewBox:"0 0 512 512"},child:[{tag:"path",attr:{d:"M326.612 185.391c59.747 59.809 58.927 155.698.36 214.59-.11.12-.24.25-.36.37l-67.2 67.2c-59.27 59.27-155.699 59.262-214.96 0-59.27-59.26-59.27-155.7 0-214.96l37.106-37.106c9.84-9.84 26.786-3.3 27.294 10.606.648 17.722 3.826 35.527 9.69 52.721 1.986 5.822.567 12.262-3.783 16.612l-13.087 13.087c-28.026 28.026-28.905 73.66-1.155 101.96 28.024 28.579 74.086 28.749 102.325.51l67.2-67.19c28.191-28.191 28.073-73.757 0-101.83-3.701-3.694-7.429-6.564-10.341-8.569a16.037 16.037 0 0 1-6.947-12.606c-.396-10.567 3.348-21.456 11.698-29.806l21.054-21.055c5.521-5.521 14.182-6.199 20.584-1.731a152.482 152.482 0 0 1 20.522 17.197zM467.547 44.449c-59.261-59.262-155.69-59.27-214.96 0l-67.2 67.2c-.12.12-.25.25-.36.37-58.566 58.892-59.387 154.781.36 214.59a152.454 152.454 0 0 0 20.521 17.196c6.402 4.468 15.064 3.789 20.584-1.731l21.054-21.055c8.35-8.35 12.094-19.239 11.698-29.806a16.037 16.037 0 0 0-6.947-12.606c-2.912-2.005-6.64-4.875-10.341-8.569-28.073-28.073-28.191-73.639 0-101.83l67.2-67.19c28.239-28.239 74.3-28.069 102.325.51 27.75 28.3 26.872 73.934-1.155 101.96l-13.087 13.087c-4.35 4.35-5.769 10.79-3.783 16.612 5.864 17.194 9.042 34.999 9.69 52.721.509 13.906 17.454 20.446 27.294 10.606l37.106-37.106c59.271-59.259 59.271-155.699.001-214.959z"}}]})(e)}function W(e){return t({tag:"svg",attr:{viewBox:"0 0 448 512"},child:[{tag:"path",attr:{d:"M400 224h-24v-72C376 68.2 307.8 0 224 0S72 68.2 72 152v72H48c-26.5 0-48 21.5-48 48v192c0 26.5 21.5 48 48 48h352c26.5 0 48-21.5 48-48V272c0-26.5-21.5-48-48-48zm-104 0H152v-72c0-39.7 32.3-72 72-72s72 32.3 72 72v72z"}}]})(e)}function Y(e){return t({tag:"svg",attr:{viewBox:"0 0 192 512"},child:[{tag:"path",attr:{d:"M96 0c35.346 0 64 28.654 64 64s-28.654 64-64 64-64-28.654-64-64S60.654 0 96 0m48 144h-11.36c-22.711 10.443-49.59 10.894-73.28 0H48c-26.51 0-48 21.49-48 48v136c0 13.255 10.745 24 24 24h16v136c0 13.255 10.745 24 24 24h64c13.255 0 24-10.745 24-24V352h16c13.255 0 24-10.745 24-24V192c0-26.51-21.49-48-48-48z"}}]})(e)}function X(e){return t({tag:"svg",attr:{viewBox:"0 0 384 512"},child:[{tag:"path",attr:{d:"M372 64h-79c-10.7 0-16 12.9-8.5 20.5l16.9 16.9-80.7 80.7c-22.2-14-48.5-22.1-76.7-22.1C64.5 160 0 224.5 0 304s64.5 144 144 144 144-64.5 144-144c0-28.2-8.1-54.5-22.1-76.7l80.7-80.7 16.9 16.9c7.6 7.6 20.5 2.2 20.5-8.5V76c0-6.6-5.4-12-12-12zM144 384c-44.1 0-80-35.9-80-80s35.9-80 80-80 80 35.9 80 80-35.9 80-80 80z"}}]})(e)}function Z(e){return t({tag:"svg",attr:{viewBox:"0 0 512 512"},child:[{tag:"path",attr:{d:"M283.211 512c78.962 0 151.079-35.925 198.857-94.792 7.068-8.708-.639-21.43-11.562-19.35-124.203 23.654-238.262-71.576-238.262-196.954 0-72.222 38.662-138.635 101.498-174.394 9.686-5.512 7.25-20.197-3.756-22.23A258.156 258.156 0 0 0 283.211 0c-141.309 0-256 114.511-256 256 0 141.309 114.511 256 256 256z"}}]})(e)}function Q(e){return t({tag:"svg",attr:{viewBox:"0 0 512 512"},child:[{tag:"path",attr:{d:"M493.4 24.6l-104-24c-11.3-2.6-22.9 3.3-27.5 13.9l-48 112c-4.2 9.8-1.4 21.3 6.9 28l60.6 49.6c-36 76.7-98.9 140.5-177.2 177.2l-49.6-60.6c-6.8-8.3-18.2-11.1-28-6.9l-112 48C3.9 366.5-2 378.1.6 389.4l24 104C27.1 504.2 36.7 512 48 512c256.1 0 464-207.5 464-464 0-11.2-7.7-20.9-18.6-23.4z"}}]})(e)}function ee(e){return t({tag:"svg",attr:{viewBox:"0 0 448 512"},child:[{tag:"path",attr:{d:"M424.4 214.7L72.4 6.6C43.8-10.3 0 6.1 0 47.9V464c0 37.5 40.7 60.1 72.4 41.3l352-208c31.4-18.5 31.5-64.1 0-82.6z"}}]})(e)}function te(e){return t({tag:"svg",attr:{viewBox:"0 0 448 512"},child:[{tag:"path",attr:{d:"M416 208H272V64c0-17.67-14.33-32-32-32h-32c-17.67 0-32 14.33-32 32v144H32c-17.67 0-32 14.33-32 32v32c0 17.67 14.33 32 32 32h144v144c0 17.67 14.33 32 32 32h32c17.67 0 32-14.33 32-32V304h144c17.67 0 32-14.33 32-32v-32c0-17.67-14.33-32-32-32z"}}]})(e)}function re(e){return t({tag:"svg",attr:{viewBox:"0 0 512 512"},child:[{tag:"path",attr:{d:"M504 256c0 136.997-111.043 248-248 248S8 392.997 8 256C8 119.083 119.043 8 256 8s248 111.083 248 248zM262.655 90c-54.497 0-89.255 22.957-116.549 63.758-3.536 5.286-2.353 12.415 2.715 16.258l34.699 26.31c5.205 3.947 12.621 3.008 16.665-2.122 17.864-22.658 30.113-35.797 57.303-35.797 20.429 0 45.698 13.148 45.698 32.958 0 14.976-12.363 22.667-32.534 33.976C247.128 238.528 216 254.941 216 296v4c0 6.627 5.373 12 12 12h56c6.627 0 12-5.373 12-12v-1.333c0-28.462 83.186-29.647 83.186-106.667 0-58.002-60.165-102-116.531-102zM256 338c-25.365 0-46 20.635-46 46 0 25.364 20.635 46 46 46s46-20.636 46-46c0-25.365-20.635-46-46-46z"}}]})(e)}function se(e){return t({tag:"svg",attr:{viewBox:"0 0 448 512"},child:[{tag:"path",attr:{d:"M433.941 129.941l-83.882-83.882A48 48 0 0 0 316.118 32H48C21.49 32 0 53.49 0 80v352c0 26.51 21.49 48 48 48h352c26.51 0 48-21.49 48-48V163.882a48 48 0 0 0-14.059-33.941zM224 416c-35.346 0-64-28.654-64-64 0-35.346 28.654-64 64-64s64 28.654 64 64c0 35.346-28.654 64-64 64zm96-304.52V212c0 6.627-5.373 12-12 12H76c-6.627 0-12-5.373-12-12V108c0-6.627 5.373-12 12-12h228.52c3.183 0 6.235 1.264 8.485 3.515l3.48 3.48A11.996 11.996 0 0 1 320 111.48z"}}]})(e)}function ie(e){return t({tag:"svg",attr:{viewBox:"0 0 512 512"},child:[{tag:"path",attr:{d:"M505 442.7L405.3 343c-4.5-4.5-10.6-7-17-7H372c27.6-35.3 44-79.7 44-128C416 93.1 322.9 0 208 0S0 93.1 0 208s93.1 208 208 208c48.3 0 92.7-16.4 128-44v16.3c0 6.4 2.5 12.5 7 17l99.7 99.7c9.4 9.4 24.6 9.4 33.9 0l28.3-28.3c9.4-9.4 9.4-24.6.1-34zM208 336c-70.7 0-128-57.2-128-128 0-70.7 57.2-128 128-128 70.7 0 128 57.2 128 128 0 70.7-57.2 128-128 128z"}}]})(e)}function ne(e){return t({tag:"svg",attr:{viewBox:"0 0 512 512"},child:[{tag:"path",attr:{d:"M466.5 83.7l-192-80a48.15 48.15 0 0 0-36.9 0l-192 80C27.7 91.1 16 108.6 16 128c0 198.5 114.5 335.7 221.5 380.3 11.8 4.9 25.1 4.9 36.9 0C360.1 472.6 496 349.3 496 128c0-19.4-11.7-36.9-29.5-44.3zM256.1 446.3l-.1-381 175.9 73.3c-3.3 151.4-82.1 261.1-175.8 307.7z"}}]})(e)}function oe(e){return t({tag:"svg",attr:{viewBox:"0 0 512 512"},child:[{tag:"path",attr:{d:"M416 448h-84c-6.6 0-12-5.4-12-12v-40c0-6.6 5.4-12 12-12h84c17.7 0 32-14.3 32-32V160c0-17.7-14.3-32-32-32h-84c-6.6 0-12-5.4-12-12V76c0-6.6 5.4-12 12-12h84c53 0 96 43 96 96v192c0 53-43 96-96 96zm-47-201L201 79c-15-15-41-4.5-41 17v96H24c-13.3 0-24 10.7-24 24v96c0 13.3 10.7 24 24 24h136v96c0 21.5 26 32 41 17l168-168c9.3-9.4 9.3-24.6 0-34z"}}]})(e)}function ae(e){return t({tag:"svg",attr:{viewBox:"0 0 448 512"},child:[{tag:"path",attr:{d:"M176 352h-48V48a16 16 0 0 0-16-16H80a16 16 0 0 0-16 16v304H16c-14.19 0-21.36 17.24-11.29 27.31l80 96a16 16 0 0 0 22.62 0l80-96C197.35 369.26 190.22 352 176 352zm240-64H288a16 16 0 0 0-16 16v32a16 16 0 0 0 16 16h56l-61.26 70.45A32 32 0 0 0 272 446.37V464a16 16 0 0 0 16 16h128a16 16 0 0 0 16-16v-32a16 16 0 0 0-16-16h-56l61.26-70.45A32 32 0 0 0 432 321.63V304a16 16 0 0 0-16-16zm31.06-85.38l-59.27-160A16 16 0 0 0 372.72 32h-41.44a16 16 0 0 0-15.07 10.62l-59.27 160A16 16 0 0 0 272 224h24.83a16 16 0 0 0 15.23-11.08l4.42-12.92h71l4.41 12.92A16 16 0 0 0 407.16 224H432a16 16 0 0 0 15.06-21.38zM335.61 144L352 96l16.39 48z"}}]})(e)}function le(e){return t({tag:"svg",attr:{viewBox:"0 0 448 512"},child:[{tag:"path",attr:{d:"M16 160h48v304a16 16 0 0 0 16 16h32a16 16 0 0 0 16-16V160h48c14.21 0 21.38-17.24 11.31-27.31l-80-96a16 16 0 0 0-22.62 0l-80 96C-5.35 142.74 1.78 160 16 160zm400 128H288a16 16 0 0 0-16 16v32a16 16 0 0 0 16 16h56l-61.26 70.45A32 32 0 0 0 272 446.37V464a16 16 0 0 0 16 16h128a16 16 0 0 0 16-16v-32a16 16 0 0 0-16-16h-56l61.26-70.45A32 32 0 0 0 432 321.63V304a16 16 0 0 0-16-16zm31.06-85.38l-59.27-160A16 16 0 0 0 372.72 32h-41.44a16 16 0 0 0-15.07 10.62l-59.27 160A16 16 0 0 0 272 224h24.83a16 16 0 0 0 15.23-11.08l4.42-12.92h71l4.41 12.92A16 16 0 0 0 407.16 224H432a16 16 0 0 0 15.06-21.38zM335.61 144L352 96l16.39 48z"}}]})(e)}function ce(e){return t({tag:"svg",attr:{viewBox:"0 0 512 512"},child:[{tag:"path",attr:{d:"M304 48c0 26.51-21.49 48-48 48s-48-21.49-48-48 21.49-48 48-48 48 21.49 48 48zm-48 368c-26.51 0-48 21.49-48 48s21.49 48 48 48 48-21.49 48-48-21.49-48-48-48zm208-208c-26.51 0-48 21.49-48 48s21.49 48 48 48 48-21.49 48-48-21.49-48-48-48zM96 256c0-26.51-21.49-48-48-48S0 229.49 0 256s21.49 48 48 48 48-21.49 48-48zm12.922 99.078c-26.51 0-48 21.49-48 48s21.49 48 48 48 48-21.49 48-48c0-26.509-21.491-48-48-48zm294.156 0c-26.51 0-48 21.49-48 48s21.49 48 48 48 48-21.49 48-48c0-26.509-21.49-48-48-48zM108.922 60.922c-26.51 0-48 21.49-48 48s21.49 48 48 48 48-21.49 48-48-21.491-48-48-48z"}}]})(e)}function he(e){return t({tag:"svg",attr:{viewBox:"0 0 512 512"},child:[{tag:"path",attr:{d:"M256 160c-52.9 0-96 43.1-96 96s43.1 96 96 96 96-43.1 96-96-43.1-96-96-96zm246.4 80.5l-94.7-47.3 33.5-100.4c4.5-13.6-8.4-26.5-21.9-21.9l-100.4 33.5-47.4-94.8c-6.4-12.8-24.6-12.8-31 0l-47.3 94.7L92.7 70.8c-13.6-4.5-26.5 8.4-21.9 21.9l33.5 100.4-94.7 47.4c-12.8 6.4-12.8 24.6 0 31l94.7 47.3-33.5 100.5c-4.5 13.6 8.4 26.5 21.9 21.9l100.4-33.5 47.3 94.7c6.4 12.8 24.6 12.8 31 0l47.3-94.7 100.4 33.5c13.6 4.5 26.5-8.4 21.9-21.9l-33.5-100.4 94.7-47.3c13-6.5 13-24.7.2-31.1zm-155.9 106c-49.9 49.9-131.1 49.9-181 0-49.9-49.9-49.9-131.1 0-181 49.9-49.9 131.1-49.9 181 0 49.9 49.9 49.9 131.1 0 181z"}}]})(e)}function ue(e){return t({tag:"svg",attr:{viewBox:"0 0 512 512"},child:[{tag:"path",attr:{d:"M464 32H48C21.49 32 0 53.49 0 80v352c0 26.51 21.49 48 48 48h416c26.51 0 48-21.49 48-48V80c0-26.51-21.49-48-48-48zM224 416H64v-96h160v96zm0-160H64v-96h160v96zm224 160H288v-96h160v96zm0-160H288v-96h160v96z"}}]})(e)}function de(e){return t({tag:"svg",attr:{viewBox:"0 0 512 512"},child:[{tag:"path",attr:{d:"M149.333 56v80c0 13.255-10.745 24-24 24H24c-13.255 0-24-10.745-24-24V56c0-13.255 10.745-24 24-24h101.333c13.255 0 24 10.745 24 24zm181.334 240v-80c0-13.255-10.745-24-24-24H205.333c-13.255 0-24 10.745-24 24v80c0 13.255 10.745 24 24 24h101.333c13.256 0 24.001-10.745 24.001-24zm32-240v80c0 13.255 10.745 24 24 24H488c13.255 0 24-10.745 24-24V56c0-13.255-10.745-24-24-24H386.667c-13.255 0-24 10.745-24 24zm-32 80V56c0-13.255-10.745-24-24-24H205.333c-13.255 0-24 10.745-24 24v80c0 13.255 10.745 24 24 24h101.333c13.256 0 24.001-10.745 24.001-24zm-205.334 56H24c-13.255 0-24 10.745-24 24v80c0 13.255 10.745 24 24 24h101.333c13.255 0 24-10.745 24-24v-80c0-13.255-10.745-24-24-24zM0 376v80c0 13.255 10.745 24 24 24h101.333c13.255 0 24-10.745 24-24v-80c0-13.255-10.745-24-24-24H24c-13.255 0-24 10.745-24 24zm386.667-56H488c13.255 0 24-10.745 24-24v-80c0-13.255-10.745-24-24-24H386.667c-13.255 0-24 10.745-24 24v80c0 13.255 10.745 24 24 24zm0 160H488c13.255 0 24-10.745 24-24v-80c0-13.255-10.745-24-24-24H386.667c-13.255 0-24 10.745-24 24v80c0 13.255 10.745 24 24 24zM181.333 376v80c0 13.255 10.745 24 24 24h101.333c13.255 0 24-10.745 24-24v-80c0-13.255-10.745-24-24-24H205.333c-13.255 0-24 10.745-24 24z"}}]})(e)}function fe(e){return t({tag:"svg",attr:{viewBox:"0 0 352 512"},child:[{tag:"path",attr:{d:"M242.72 256l100.07-100.07c12.28-12.28 12.28-32.19 0-44.48l-22.24-22.24c-12.28-12.28-32.19-12.28-44.48 0L176 189.28 75.93 89.21c-12.28-12.28-32.19-12.28-44.48 0L9.21 111.45c-12.28 12.28-12.28 32.19 0 44.48L109.28 256 9.21 356.07c-12.28 12.28-12.28 32.19 0 44.48l22.24 22.24c12.28 12.28 32.2 12.28 44.48 0L176 322.72l100.07 100.07c12.28 12.28 32.2 12.28 44.48 0l22.24-22.24c12.28-12.28 12.28-32.19 0-44.48L242.72 256z"}}]})(e)}function pe(e){return t({tag:"svg",attr:{viewBox:"0 0 448 512"},child:[{tag:"path",attr:{d:"M432 32H312l-9.4-18.7A24 24 0 0 0 281.1 0H166.8a23.72 23.72 0 0 0-21.4 13.3L136 32H16A16 16 0 0 0 0 48v32a16 16 0 0 0 16 16h416a16 16 0 0 0 16-16V48a16 16 0 0 0-16-16zM53.2 467a48 48 0 0 0 47.9 45h245.8a48 48 0 0 0 47.9-45L416 128H32z"}}]})(e)}function ge(e){return t({tag:"svg",attr:{viewBox:"0 0 512 512"},child:[{tag:"path",attr:{d:"M304.083 405.907c4.686 4.686 4.686 12.284 0 16.971l-44.674 44.674c-59.263 59.262-155.693 59.266-214.961 0-59.264-59.265-59.264-155.696 0-214.96l44.675-44.675c4.686-4.686 12.284-4.686 16.971 0l39.598 39.598c4.686 4.686 4.686 12.284 0 16.971l-44.675 44.674c-28.072 28.073-28.072 73.75 0 101.823 28.072 28.072 73.75 28.073 101.824 0l44.674-44.674c4.686-4.686 12.284-4.686 16.971 0l39.597 39.598zm-56.568-260.216c4.686 4.686 12.284 4.686 16.971 0l44.674-44.674c28.072-28.075 73.75-28.073 101.824 0 28.072 28.073 28.072 73.75 0 101.823l-44.675 44.674c-4.686 4.686-4.686 12.284 0 16.971l39.598 39.598c4.686 4.686 12.284 4.686 16.971 0l44.675-44.675c59.265-59.265 59.265-155.695 0-214.96-59.266-59.264-155.695-59.264-214.961 0l-44.674 44.674c-4.686 4.686-4.686 12.284 0 16.971l39.597 39.598zm234.828 359.28l22.627-22.627c9.373-9.373 9.373-24.569 0-33.941L63.598 7.029c-9.373-9.373-24.569-9.373-33.941 0L7.029 29.657c-9.373 9.373-9.373 24.569 0 33.941l441.373 441.373c9.373 9.372 24.569 9.372 33.941 0z"}}]})(e)}function ve(e){return t({tag:"svg",attr:{viewBox:"0 0 640 512"},child:[{tag:"path",attr:{d:"M224 256c70.7 0 128-57.3 128-128S294.7 0 224 0 96 57.3 96 128s57.3 128 128 128zm89.6 32h-16.7c-22.2 10.2-46.9 16-72.9 16s-50.6-5.8-72.9-16h-16.7C60.2 288 0 348.2 0 422.4V464c0 26.5 21.5 48 48 48h352c26.5 0 48-21.5 48-48v-41.6c0-74.2-60.2-134.4-134.4-134.4zm323-128.4l-27.8-28.1c-4.6-4.7-12.1-4.7-16.8-.1l-104.8 104-45.5-45.8c-4.6-4.7-12.1-4.7-16.8-.1l-28.1 27.9c-4.7 4.6-4.7 12.1-.1 16.8l81.7 82.3c4.6 4.7 12.1 4.7 16.8.1l141.3-140.2c4.6-4.7 4.7-12.2.1-16.8z"}}]})(e)}function ye(e){return t({tag:"svg",attr:{viewBox:"0 0 448 512"},child:[{tag:"path",attr:{d:"M224 256c70.7 0 128-57.3 128-128S294.7 0 224 0 96 57.3 96 128s57.3 128 128 128zM104 424c0 13.3 10.7 24 24 24s24-10.7 24-24-10.7-24-24-24-24 10.7-24 24zm216-135.4v49c36.5 7.4 64 39.8 64 78.4v41.7c0 7.6-5.4 14.2-12.9 15.7l-32.2 6.4c-4.3.9-8.5-1.9-9.4-6.3l-3.1-15.7c-.9-4.3 1.9-8.6 6.3-9.4l19.3-3.9V416c0-62.8-96-65.1-96 1.9v26.7l19.3 3.9c4.3.9 7.1 5.1 6.3 9.4l-3.1 15.7c-.9 4.3-5.1 7.1-9.4 6.3l-31.2-4.2c-7.9-1.1-13.8-7.8-13.8-15.9V416c0-38.6 27.5-70.9 64-78.4v-45.2c-2.2.7-4.4 1.1-6.6 1.9-18 6.3-37.3 9.8-57.4 9.8s-39.4-3.5-57.4-9.8c-7.4-2.6-14.9-4.2-22.6-5.2v81.6c23.1 6.9 40 28.1 40 53.4 0 30.9-25.1 56-56 56s-56-25.1-56-56c0-25.3 16.9-46.5 40-53.4v-80.4C48.5 301 0 355.8 0 422.4v44.8C0 491.9 20.1 512 44.8 512h358.4c24.7 0 44.8-20.1 44.8-44.8v-44.8c0-72-56.8-130.3-128-133.8z"}}]})(e)}function me(e){return t({tag:"svg",attr:{viewBox:"0 0 640 512"},child:[{tag:"path",attr:{d:"M624 208h-64v-64c0-8.8-7.2-16-16-16h-32c-8.8 0-16 7.2-16 16v64h-64c-8.8 0-16 7.2-16 16v32c0 8.8 7.2 16 16 16h64v64c0 8.8 7.2 16 16 16h32c8.8 0 16-7.2 16-16v-64h64c8.8 0 16-7.2 16-16v-32c0-8.8-7.2-16-16-16zm-400 48c70.7 0 128-57.3 128-128S294.7 0 224 0 96 57.3 96 128s57.3 128 128 128zm89.6 32h-16.7c-22.2 10.2-46.9 16-72.9 16s-50.6-5.8-72.9-16h-16.7C60.2 288 0 348.2 0 422.4V464c0 26.5 21.5 48 48 48h352c26.5 0 48-21.5 48-48v-41.6c0-74.2-60.2-134.4-134.4-134.4z"}}]})(e)}function _e(e){return t({tag:"svg",attr:{viewBox:"0 0 640 512"},child:[{tag:"path",attr:{d:"M589.6 240l45.6-45.6c6.3-6.3 6.3-16.5 0-22.8l-22.8-22.8c-6.3-6.3-16.5-6.3-22.8 0L544 194.4l-45.6-45.6c-6.3-6.3-16.5-6.3-22.8 0l-22.8 22.8c-6.3 6.3-6.3 16.5 0 22.8l45.6 45.6-45.6 45.6c-6.3 6.3-6.3 16.5 0 22.8l22.8 22.8c6.3 6.3 16.5 6.3 22.8 0l45.6-45.6 45.6 45.6c6.3 6.3 16.5 6.3 22.8 0l22.8-22.8c6.3-6.3 6.3-16.5 0-22.8L589.6 240zM224 256c70.7 0 128-57.3 128-128S294.7 0 224 0 96 57.3 96 128s57.3 128 128 128zm89.6 32h-16.7c-22.2 10.2-46.9 16-72.9 16s-50.6-5.8-72.9-16h-16.7C60.2 288 0 348.2 0 422.4V464c0 26.5 21.5 48 48 48h352c26.5 0 48-21.5 48-48v-41.6c0-74.2-60.2-134.4-134.4-134.4z"}}]})(e)}function be(e){return t({tag:"svg",attr:{viewBox:"0 0 448 512"},child:[{tag:"path",attr:{d:"M224 256c70.7 0 128-57.3 128-128S294.7 0 224 0 96 57.3 96 128s57.3 128 128 128zm89.6 32h-16.7c-22.2 10.2-46.9 16-72.9 16s-50.6-5.8-72.9-16h-16.7C60.2 288 0 348.2 0 422.4V464c0 26.5 21.5 48 48 48h352c26.5 0 48-21.5 48-48v-41.6c0-74.2-60.2-134.4-134.4-134.4z"}}]})(e)}function we(e){return t({tag:"svg",attr:{viewBox:"0 0 640 512"},child:[{tag:"path",attr:{d:"M96 224c35.3 0 64-28.7 64-64s-28.7-64-64-64-64 28.7-64 64 28.7 64 64 64zm448 0c35.3 0 64-28.7 64-64s-28.7-64-64-64-64 28.7-64 64 28.7 64 64 64zm32 32h-64c-17.6 0-33.5 7.1-45.1 18.6 40.3 22.1 68.9 62 75.1 109.4h66c17.7 0 32-14.3 32-32v-32c0-35.3-28.7-64-64-64zm-256 0c61.9 0 112-50.1 112-112S381.9 32 320 32 208 82.1 208 144s50.1 112 112 112zm76.8 32h-8.3c-20.8 10-43.9 16-68.5 16s-47.6-6-68.5-16h-8.3C179.6 288 128 339.6 128 403.2V432c0 26.5 21.5 48 48 48h288c26.5 0 48-21.5 48-48v-28.8c0-63.6-51.6-115.2-115.2-115.2zm-223.7-13.4C161.5 263.1 145.6 256 128 256H64c-35.3 0-64 28.7-64 64v32c0 17.7 14.3 32 32 32h65.9c6.3-47.4 34.9-87.3 75.2-109.4z"}}]})(e)}function ke(e){return t({tag:"svg",attr:{viewBox:"0 0 288 512"},child:[{tag:"path",attr:{d:"M288 176c0-79.5-64.5-144-144-144S0 96.5 0 176c0 68.5 47.9 125.9 112 140.4V368H76c-6.6 0-12 5.4-12 12v40c0 6.6 5.4 12 12 12h36v36c0 6.6 5.4 12 12 12h40c6.6 0 12-5.4 12-12v-36h36c6.6 0 12-5.4 12-12v-40c0-6.6-5.4-12-12-12h-36v-51.6c64.1-14.5 112-71.9 112-140.4zm-224 0c0-44.1 35.9-80 80-80s80 35.9 80 80-35.9 80-80 80-80-35.9-80-80z"}}]})(e)}class Te extends Error{constructor(e,t="FunctionsError",r){super(e),this.name=t,this.context=r}}class Se extends Te{constructor(e){super("Failed to send a request to the Edge Function","FunctionsFetchError",e)}}class Ee extends Te{constructor(e){super("Relay Error invoking the Edge Function","FunctionsRelayError",e)}}class je extends Te{constructor(e){super("Edge Function returned a non-2xx status code","FunctionsHttpError",e)}}var Pe,Oe;(Oe=Pe||(Pe={})).Any="any",Oe.ApNortheast1="ap-northeast-1",Oe.ApNortheast2="ap-northeast-2",Oe.ApSouth1="ap-south-1",Oe.ApSoutheast1="ap-southeast-1",Oe.ApSoutheast2="ap-southeast-2",Oe.CaCentral1="ca-central-1",Oe.EuCentral1="eu-central-1",Oe.EuWest1="eu-west-1",Oe.EuWest2="eu-west-2",Oe.EuWest3="eu-west-3",Oe.SaEast1="sa-east-1",Oe.UsEast1="us-east-1",Oe.UsWest1="us-west-1",Oe.UsWest2="us-west-2";var xe=globalThis&&globalThis.__awaiter||function(e,t,r,s){return new(r||(r=Promise))(function(i,n){function o(e){try{l(s.next(e))}catch(t){n(t)}}function a(e){try{l(s.throw(e))}catch(t){n(t)}}function l(e){var t;e.done?i(e.value):(t=e.value,t instanceof r?t:new r(function(e){e(t)})).then(o,a)}l((s=s.apply(e,t||[])).next())})};class Ce{constructor(e,{headers:t={},customFetch:r,region:s=Pe.Any}={}){this.url=e,this.headers=t,this.region=s,this.fetch=(e=>{let t;return t=e||("undefined"==typeof fetch?(...e)=>_(()=>Promise.resolve().then(()=>Ve),void 0).then(({default:t})=>t(...e)):fetch),(...e)=>t(...e)})(r)}setAuth(e){this.headers.Authorization=`Bearer ${e}`}invoke(e,t={}){var r;return xe(this,void 0,void 0,function*(){try{const{headers:s,method:i,body:n}=t;let o,a={},{region:l}=t;l||(l=this.region),l&&"any"!==l&&(a["x-region"]=l),n&&(s&&!Object.prototype.hasOwnProperty.call(s,"Content-Type")||!s)&&("undefined"!=typeof Blob&&n instanceof Blob||n instanceof ArrayBuffer?(a["Content-Type"]="application/octet-stream",o=n):"string"==typeof n?(a["Content-Type"]="text/plain",o=n):"undefined"!=typeof FormData&&n instanceof FormData?o=n:(a["Content-Type"]="application/json",o=JSON.stringify(n)));const c=yield this.fetch(`${this.url}/${e}`,{method:i||"POST",headers:Object.assign(Object.assign(Object.assign({},a),this.headers),s),body:o}).catch(e=>{throw new Se(e)}),h=c.headers.get("x-relay-error");if(h&&"true"===h)throw new Ee(c);if(!c.ok)throw new je(c);let u,d=(null!==(r=c.headers.get("Content-Type"))&&void 0!==r?r:"text/plain").split(";")[0].trim();return u="application/json"===d?yield c.json():"application/octet-stream"===d?yield c.blob():"text/event-stream"===d?c:"multipart/form-data"===d?yield c.formData():yield c.text(),{data:u,error:null}}catch(s){return{data:null,error:s}}})}}var Ae={},$e={},ze={},Re={},Ie={},Le={},Me=function(){if("undefined"!=typeof self)return self;if("undefined"!=typeof window)return window;if("undefined"!=typeof global)return global;throw new Error("unable to locate global object")}();const Ue=Me.fetch,Be=Me.fetch.bind(Me),De=Me.Headers,He=Me.Request,Ne=Me.Response,Ve=Object.freeze(Object.defineProperty({__proto__:null,Headers:De,Request:He,Response:Ne,default:Be,fetch:Ue},Symbol.toStringTag,{value:"Module"})),qe=s(Ve);var Fe={};Object.defineProperty(Fe,"__esModule",{value:!0});let Je=class extends Error{constructor(e){super(e.message),this.name="PostgrestError",this.details=e.details,this.hint=e.hint,this.code=e.code}};Fe.default=Je;var Ke=i&&i.__importDefault||function(e){return e&&e.__esModule?e:{default:e}};Object.defineProperty(Le,"__esModule",{value:!0});const Ge=Ke(qe),We=Ke(Fe);Le.default=class{constructor(e){this.shouldThrowOnError=!1,this.method=e.method,this.url=e.url,this.headers=e.headers,this.schema=e.schema,this.body=e.body,this.shouldThrowOnError=e.shouldThrowOnError,this.signal=e.signal,this.isMaybeSingle=e.isMaybeSingle,e.fetch?this.fetch=e.fetch:"undefined"==typeof fetch?this.fetch=Ge.default:this.fetch=fetch}throwOnError(){return this.shouldThrowOnError=!0,this}setHeader(e,t){return this.headers=Object.assign({},this.headers),this.headers[e]=t,this}then(t,r){void 0===this.schema||(["GET","HEAD"].includes(this.method)?this.headers["Accept-Profile"]=this.schema:this.headers["Content-Profile"]=this.schema),"GET"!==this.method&&"HEAD"!==this.method&&(this.headers["Content-Type"]="application/json");let s=(0,this.fetch)(this.url.toString(),{method:this.method,headers:this.headers,body:JSON.stringify(this.body),signal:this.signal}).then(t=>e(this,null,function*(){var e,r,s;let i=null,n=null,o=null,a=t.status,l=t.statusText;if(t.ok){if("HEAD"!==this.method){const e=yield t.text();""===e||(n="text/csv"===this.headers.Accept||this.headers.Accept&&this.headers.Accept.includes("application/vnd.pgrst.plan+text")?e:JSON.parse(e))}const s=null===(e=this.headers.Prefer)||void 0===e?void 0:e.match(/count=(exact|planned|estimated)/),c=null===(r=t.headers.get("content-range"))||void 0===r?void 0:r.split("/");s&&c&&c.length>1&&(o=parseInt(c[1])),this.isMaybeSingle&&"GET"===this.method&&Array.isArray(n)&&(n.length>1?(i={code:"PGRST116",details:`Results contain ${n.length} rows, application/vnd.pgrst.object+json requires 1 row`,hint:null,message:"JSON object requested, multiple (or no) rows returned"},n=null,o=null,a=406,l="Not Acceptable"):n=1===n.length?n[0]:null)}else{const e=yield t.text();try{i=JSON.parse(e),Array.isArray(i)&&404===t.status&&(n=[],i=null,a=200,l="OK")}catch(c){404===t.status&&""===e?(a=204,l="No Content"):i={message:e}}if(i&&this.isMaybeSingle&&(null===(s=null==i?void 0:i.details)||void 0===s?void 0:s.includes("0 rows"))&&(i=null,a=200,l="OK"),i&&this.shouldThrowOnError)throw new We.default(i)}return{error:i,data:n,count:o,status:a,statusText:l}}));return this.shouldThrowOnError||(s=s.catch(e=>{var t,r,s;return{error:{message:`${null!==(t=null==e?void 0:e.name)&&void 0!==t?t:"FetchError"}: ${null==e?void 0:e.message}`,details:`${null!==(r=null==e?void 0:e.stack)&&void 0!==r?r:""}`,hint:"",code:`${null!==(s=null==e?void 0:e.code)&&void 0!==s?s:""}`},data:null,count:null,status:0,statusText:""}})),s.then(t,r)}returns(){return this}overrideTypes(){return this}};var Ye=i&&i.__importDefault||function(e){return e&&e.__esModule?e:{default:e}};Object.defineProperty(Ie,"__esModule",{value:!0});const Xe=Ye(Le);let Ze=class extends Xe.default{select(e){let t=!1;const r=(null!=e?e:"*").split("").map(e=>/\s/.test(e)&&!t?"":('"'===e&&(t=!t),e)).join("");return this.url.searchParams.set("select",r),this.headers.Prefer&&(this.headers.Prefer+=","),this.headers.Prefer+="return=representation",this}order(e,{ascending:t=!0,nullsFirst:r,foreignTable:s,referencedTable:i=s}={}){const n=i?`${i}.order`:"order",o=this.url.searchParams.get(n);return this.url.searchParams.set(n,`${o?`${o},`:""}${e}.${t?"asc":"desc"}${void 0===r?"":r?".nullsfirst":".nullslast"}`),this}limit(e,{foreignTable:t,referencedTable:r=t}={}){const s=void 0===r?"limit":`${r}.limit`;return this.url.searchParams.set(s,`${e}`),this}range(e,t,{foreignTable:r,referencedTable:s=r}={}){const i=void 0===s?"offset":`${s}.offset`,n=void 0===s?"limit":`${s}.limit`;return this.url.searchParams.set(i,`${e}`),this.url.searchParams.set(n,""+(t-e+1)),this}abortSignal(e){return this.signal=e,this}single(){return this.headers.Accept="application/vnd.pgrst.object+json",this}maybeSingle(){return"GET"===this.method?this.headers.Accept="application/json":this.headers.Accept="application/vnd.pgrst.object+json",this.isMaybeSingle=!0,this}csv(){return this.headers.Accept="text/csv",this}geojson(){return this.headers.Accept="application/geo+json",this}explain({analyze:e=!1,verbose:t=!1,settings:r=!1,buffers:s=!1,wal:i=!1,format:n="text"}={}){var o;const a=[e?"analyze":null,t?"verbose":null,r?"settings":null,s?"buffers":null,i?"wal":null].filter(Boolean).join("|"),l=null!==(o=this.headers.Accept)&&void 0!==o?o:"application/json";return this.headers.Accept=`application/vnd.pgrst.plan+${n}; for="${l}"; options=${a};`,this}rollback(){var e;return(null!==(e=this.headers.Prefer)&&void 0!==e?e:"").trim().length>0?this.headers.Prefer+=",tx=rollback":this.headers.Prefer="tx=rollback",this}returns(){return this}};Ie.default=Ze;var Qe=i&&i.__importDefault||function(e){return e&&e.__esModule?e:{default:e}};Object.defineProperty(Re,"__esModule",{value:!0});const et=Qe(Ie);let tt=class extends et.default{eq(e,t){return this.url.searchParams.append(e,`eq.${t}`),this}neq(e,t){return this.url.searchParams.append(e,`neq.${t}`),this}gt(e,t){return this.url.searchParams.append(e,`gt.${t}`),this}gte(e,t){return this.url.searchParams.append(e,`gte.${t}`),this}lt(e,t){return this.url.searchParams.append(e,`lt.${t}`),this}lte(e,t){return this.url.searchParams.append(e,`lte.${t}`),this}like(e,t){return this.url.searchParams.append(e,`like.${t}`),this}likeAllOf(e,t){return this.url.searchParams.append(e,`like(all).{${t.join(",")}}`),this}likeAnyOf(e,t){return this.url.searchParams.append(e,`like(any).{${t.join(",")}}`),this}ilike(e,t){return this.url.searchParams.append(e,`ilike.${t}`),this}ilikeAllOf(e,t){return this.url.searchParams.append(e,`ilike(all).{${t.join(",")}}`),this}ilikeAnyOf(e,t){return this.url.searchParams.append(e,`ilike(any).{${t.join(",")}}`),this}is(e,t){return this.url.searchParams.append(e,`is.${t}`),this}in(e,t){const r=Array.from(new Set(t)).map(e=>"string"==typeof e&&new RegExp("[,()]").test(e)?`"${e}"`:`${e}`).join(",");return this.url.searchParams.append(e,`in.(${r})`),this}contains(e,t){return"string"==typeof t?this.url.searchParams.append(e,`cs.${t}`):Array.isArray(t)?this.url.searchParams.append(e,`cs.{${t.join(",")}}`):this.url.searchParams.append(e,`cs.${JSON.stringify(t)}`),this}containedBy(e,t){return"string"==typeof t?this.url.searchParams.append(e,`cd.${t}`):Array.isArray(t)?this.url.searchParams.append(e,`cd.{${t.join(",")}}`):this.url.searchParams.append(e,`cd.${JSON.stringify(t)}`),this}rangeGt(e,t){return this.url.searchParams.append(e,`sr.${t}`),this}rangeGte(e,t){return this.url.searchParams.append(e,`nxl.${t}`),this}rangeLt(e,t){return this.url.searchParams.append(e,`sl.${t}`),this}rangeLte(e,t){return this.url.searchParams.append(e,`nxr.${t}`),this}rangeAdjacent(e,t){return this.url.searchParams.append(e,`adj.${t}`),this}overlaps(e,t){return"string"==typeof t?this.url.searchParams.append(e,`ov.${t}`):this.url.searchParams.append(e,`ov.{${t.join(",")}}`),this}textSearch(e,t,{config:r,type:s}={}){let i="";"plain"===s?i="pl":"phrase"===s?i="ph":"websearch"===s&&(i="w");const n=void 0===r?"":`(${r})`;return this.url.searchParams.append(e,`${i}fts${n}.${t}`),this}match(e){return Object.entries(e).forEach(([e,t])=>{this.url.searchParams.append(e,`eq.${t}`)}),this}not(e,t,r){return this.url.searchParams.append(e,`not.${t}.${r}`),this}or(e,{foreignTable:t,referencedTable:r=t}={}){const s=r?`${r}.or`:"or";return this.url.searchParams.append(s,`(${e})`),this}filter(e,t,r){return this.url.searchParams.append(e,`${t}.${r}`),this}};Re.default=tt;var rt=i&&i.__importDefault||function(e){return e&&e.__esModule?e:{default:e}};Object.defineProperty(ze,"__esModule",{value:!0});const st=rt(Re);ze.default=class{constructor(e,{headers:t={},schema:r,fetch:s}){this.url=e,this.headers=t,this.schema=r,this.fetch=s}select(e,{head:t=!1,count:r}={}){const s=t?"HEAD":"GET";let i=!1;const n=(null!=e?e:"*").split("").map(e=>/\s/.test(e)&&!i?"":('"'===e&&(i=!i),e)).join("");return this.url.searchParams.set("select",n),r&&(this.headers.Prefer=`count=${r}`),new st.default({method:s,url:this.url,headers:this.headers,schema:this.schema,fetch:this.fetch,allowEmpty:!1})}insert(e,{count:t,defaultToNull:r=!0}={}){const s=[];if(this.headers.Prefer&&s.push(this.headers.Prefer),t&&s.push(`count=${t}`),r||s.push("missing=default"),this.headers.Prefer=s.join(","),Array.isArray(e)){const t=e.reduce((e,t)=>e.concat(Object.keys(t)),[]);if(t.length>0){const e=[...new Set(t)].map(e=>`"${e}"`);this.url.searchParams.set("columns",e.join(","))}}return new st.default({method:"POST",url:this.url,headers:this.headers,schema:this.schema,body:e,fetch:this.fetch,allowEmpty:!1})}upsert(e,{onConflict:t,ignoreDuplicates:r=!1,count:s,defaultToNull:i=!0}={}){const n=[`resolution=${r?"ignore":"merge"}-duplicates`];if(void 0!==t&&this.url.searchParams.set("on_conflict",t),this.headers.Prefer&&n.push(this.headers.Prefer),s&&n.push(`count=${s}`),i||n.push("missing=default"),this.headers.Prefer=n.join(","),Array.isArray(e)){const t=e.reduce((e,t)=>e.concat(Object.keys(t)),[]);if(t.length>0){const e=[...new Set(t)].map(e=>`"${e}"`);this.url.searchParams.set("columns",e.join(","))}}return new st.default({method:"POST",url:this.url,headers:this.headers,schema:this.schema,body:e,fetch:this.fetch,allowEmpty:!1})}update(e,{count:t}={}){const r=[];return this.headers.Prefer&&r.push(this.headers.Prefer),t&&r.push(`count=${t}`),this.headers.Prefer=r.join(","),new st.default({method:"PATCH",url:this.url,headers:this.headers,schema:this.schema,body:e,fetch:this.fetch,allowEmpty:!1})}delete({count:e}={}){const t=[];return e&&t.push(`count=${e}`),this.headers.Prefer&&t.unshift(this.headers.Prefer),this.headers.Prefer=t.join(","),new st.default({method:"DELETE",url:this.url,headers:this.headers,schema:this.schema,fetch:this.fetch,allowEmpty:!1})}};var it={},nt={};Object.defineProperty(nt,"__esModule",{value:!0}),nt.version=void 0,nt.version="0.0.0-automated",Object.defineProperty(it,"__esModule",{value:!0}),it.DEFAULT_HEADERS=void 0;const ot=nt;it.DEFAULT_HEADERS={"X-Client-Info":`postgrest-js/${ot.version}`};var at=i&&i.__importDefault||function(e){return e&&e.__esModule?e:{default:e}};Object.defineProperty($e,"__esModule",{value:!0});const lt=at(ze),ct=at(Re),ht=it;$e.default=class e{constructor(e,{headers:t={},schema:r,fetch:s}={}){this.url=e,this.headers=Object.assign(Object.assign({},ht.DEFAULT_HEADERS),t),this.schemaName=r,this.fetch=s}from(e){const t=new URL(`${this.url}/${e}`);return new lt.default(t,{headers:Object.assign({},this.headers),schema:this.schemaName,fetch:this.fetch})}schema(t){return new e(this.url,{headers:this.headers,schema:t,fetch:this.fetch})}rpc(e,t={},{head:r=!1,get:s=!1,count:i}={}){let n;const o=new URL(`${this.url}/rpc/${e}`);let a;r||s?(n=r?"HEAD":"GET",Object.entries(t).filter(([e,t])=>void 0!==t).map(([e,t])=>[e,Array.isArray(t)?`{${t.join(",")}}`:`${t}`]).forEach(([e,t])=>{o.searchParams.append(e,t)})):(n="POST",a=t);const l=Object.assign({},this.headers);return i&&(l.Prefer=`count=${i}`),new ct.default({method:n,url:o,headers:l,schema:this.schemaName,body:a,fetch:this.fetch,allowEmpty:!1})}};var ut=i&&i.__importDefault||function(e){return e&&e.__esModule?e:{default:e}};Object.defineProperty(Ae,"__esModule",{value:!0}),Ae.PostgrestError=Ae.PostgrestBuilder=Ae.PostgrestTransformBuilder=Ae.PostgrestFilterBuilder=Ae.PostgrestQueryBuilder=Ae.PostgrestClient=void 0;const dt=ut($e);Ae.PostgrestClient=dt.default;const ft=ut(ze);Ae.PostgrestQueryBuilder=ft.default;const pt=ut(Re);Ae.PostgrestFilterBuilder=pt.default;const gt=ut(Ie);Ae.PostgrestTransformBuilder=gt.default;const vt=ut(Le);Ae.PostgrestBuilder=vt.default;const yt=ut(Fe);Ae.PostgrestError=yt.default;var mt=Ae.default={PostgrestClient:dt.default,PostgrestQueryBuilder:ft.default,PostgrestFilterBuilder:pt.default,PostgrestTransformBuilder:gt.default,PostgrestBuilder:vt.default,PostgrestError:yt.default};const{PostgrestClient:_t,PostgrestQueryBuilder:bt,PostgrestFilterBuilder:wt,PostgrestTransformBuilder:kt,PostgrestBuilder:Tt,PostgrestError:St}=mt,Et={"X-Client-Info":"realtime-js/2.11.2"};var jt,Pt,Ot,xt,Ct,At,$t,zt,Rt,It,Lt;(Pt=jt||(jt={}))[Pt.connecting=0]="connecting",Pt[Pt.open=1]="open",Pt[Pt.closing=2]="closing",Pt[Pt.closed=3]="closed",(xt=Ot||(Ot={})).closed="closed",xt.errored="errored",xt.joined="joined",xt.joining="joining",xt.leaving="leaving",(At=Ct||(Ct={})).close="phx_close",At.error="phx_error",At.join="phx_join",At.reply="phx_reply",At.leave="phx_leave",At.access_token="access_token",($t||($t={})).websocket="websocket",(Rt=zt||(zt={})).Connecting="connecting",Rt.Open="open",Rt.Closing="closing",Rt.Closed="closed";class Mt{constructor(){this.HEADER_LENGTH=1}decode(e,t){return e.constructor===ArrayBuffer?t(this._binaryDecode(e)):t("string"==typeof e?JSON.parse(e):{})}_binaryDecode(e){const t=new DataView(e),r=new TextDecoder;return this._decodeBroadcast(e,t,r)}_decodeBroadcast(e,t,r){const s=t.getUint8(1),i=t.getUint8(2);let n=this.HEADER_LENGTH+2;const o=r.decode(e.slice(n,n+s));n+=s;const a=r.decode(e.slice(n,n+i));n+=i;return{ref:null,topic:o,event:a,payload:JSON.parse(r.decode(e.slice(n,e.byteLength)))}}}class Ut{constructor(e,t){this.callback=e,this.timerCalc=t,this.timer=void 0,this.tries=0,this.callback=e,this.timerCalc=t}reset(){this.tries=0,clearTimeout(this.timer)}scheduleTimeout(){clearTimeout(this.timer),this.timer=setTimeout(()=>{this.tries=this.tries+1,this.callback()},this.timerCalc(this.tries+1))}}(Lt=It||(It={})).abstime="abstime",Lt.bool="bool",Lt.date="date",Lt.daterange="daterange",Lt.float4="float4",Lt.float8="float8",Lt.int2="int2",Lt.int4="int4",Lt.int4range="int4range",Lt.int8="int8",Lt.int8range="int8range",Lt.json="json",Lt.jsonb="jsonb",Lt.money="money",Lt.numeric="numeric",Lt.oid="oid",Lt.reltime="reltime",Lt.text="text",Lt.time="time",Lt.timestamp="timestamp",Lt.timestamptz="timestamptz",Lt.timetz="timetz",Lt.tsrange="tsrange",Lt.tstzrange="tstzrange";const Bt=(e,t,r={})=>{var s;const i=null!==(s=r.skipTypes)&&void 0!==s?s:[];return Object.keys(t).reduce((r,s)=>(r[s]=Dt(s,e,t,i),r),{})},Dt=(e,t,r,s)=>{const i=t.find(t=>t.name===e),n=null==i?void 0:i.type,o=r[e];return n&&!s.includes(n)?Ht(n,o):Nt(o)},Ht=(e,t)=>{if("_"===e.charAt(0)){const r=e.slice(1,e.length);return Jt(t,r)}switch(e){case It.bool:return Vt(t);case It.float4:case It.float8:case It.int2:case It.int4:case It.int8:case It.numeric:case It.oid:return qt(t);case It.json:case It.jsonb:return Ft(t);case It.timestamp:return Kt(t);case It.abstime:case It.date:case It.daterange:case It.int4range:case It.int8range:case It.money:case It.reltime:case It.text:case It.time:case It.timestamptz:case It.timetz:case It.tsrange:case It.tstzrange:default:return Nt(t)}},Nt=e=>e,Vt=e=>{switch(e){case"t":return!0;case"f":return!1;default:return e}},qt=e=>{if("string"==typeof e){const t=parseFloat(e);if(!Number.isNaN(t))return t}return e},Ft=e=>{if("string"==typeof e)try{return JSON.parse(e)}catch(t){return e}return e},Jt=(e,t)=>{if("string"!=typeof e)return e;const r=e.length-1,s=e[r];if("{"===e[0]&&"}"===s){let s;const n=e.slice(1,r);try{s=JSON.parse("["+n+"]")}catch(i){s=n?n.split(","):[]}return s.map(e=>Ht(t,e))}return e},Kt=e=>"string"==typeof e?e.replace(" ","T"):e,Gt=e=>{let t=e;return t=t.replace(/^ws/i,"http"),t=t.replace(/(\/socket\/websocket|\/socket|\/websocket)\/?$/i,""),t.replace(/\/+$/,"")};class Wt{constructor(e,t,r={},s=1e4){this.channel=e,this.event=t,this.payload=r,this.timeout=s,this.sent=!1,this.timeoutTimer=void 0,this.ref="",this.receivedResp=null,this.recHooks=[],this.refEvent=null}resend(e){this.timeout=e,this._cancelRefEvent(),this.ref="",this.refEvent=null,this.receivedResp=null,this.sent=!1,this.send()}send(){this._hasReceived("timeout")||(this.startTimeout(),this.sent=!0,this.channel.socket.push({topic:this.channel.topic,event:this.event,payload:this.payload,ref:this.ref,join_ref:this.channel._joinRef()}))}updatePayload(e){this.payload=Object.assign(Object.assign({},this.payload),e)}receive(e,t){var r;return this._hasReceived(e)&&t(null===(r=this.receivedResp)||void 0===r?void 0:r.response),this.recHooks.push({status:e,callback:t}),this}startTimeout(){if(this.timeoutTimer)return;this.ref=this.channel.socket._makeRef(),this.refEvent=this.channel._replyEventName(this.ref);this.channel._on(this.refEvent,{},e=>{this._cancelRefEvent(),this._cancelTimeout(),this.receivedResp=e,this._matchReceive(e)}),this.timeoutTimer=setTimeout(()=>{this.trigger("timeout",{})},this.timeout)}trigger(e,t){this.refEvent&&this.channel._trigger(this.refEvent,{status:e,response:t})}destroy(){this._cancelRefEvent(),this._cancelTimeout()}_cancelRefEvent(){this.refEvent&&this.channel._off(this.refEvent,{})}_cancelTimeout(){clearTimeout(this.timeoutTimer),this.timeoutTimer=void 0}_matchReceive({status:e,response:t}){this.recHooks.filter(t=>t.status===e).forEach(e=>e.callback(t))}_hasReceived(e){return this.receivedResp&&this.receivedResp.status===e}}var Yt,Xt,Zt,Qt,er,tr,rr,sr;(Xt=Yt||(Yt={})).SYNC="sync",Xt.JOIN="join",Xt.LEAVE="leave";class ir{constructor(e,t){this.channel=e,this.state={},this.pendingDiffs=[],this.joinRef=null,this.caller={onJoin:()=>{},onLeave:()=>{},onSync:()=>{}};const r=(null==t?void 0:t.events)||{state:"presence_state",diff:"presence_diff"};this.channel._on(r.state,{},e=>{const{onJoin:t,onLeave:r,onSync:s}=this.caller;this.joinRef=this.channel._joinRef(),this.state=ir.syncState(this.state,e,t,r),this.pendingDiffs.forEach(e=>{this.state=ir.syncDiff(this.state,e,t,r)}),this.pendingDiffs=[],s()}),this.channel._on(r.diff,{},e=>{const{onJoin:t,onLeave:r,onSync:s}=this.caller;this.inPendingSyncState()?this.pendingDiffs.push(e):(this.state=ir.syncDiff(this.state,e,t,r),s())}),this.onJoin((e,t,r)=>{this.channel._trigger("presence",{event:"join",key:e,currentPresences:t,newPresences:r})}),this.onLeave((e,t,r)=>{this.channel._trigger("presence",{event:"leave",key:e,currentPresences:t,leftPresences:r})}),this.onSync(()=>{this.channel._trigger("presence",{event:"sync"})})}static syncState(e,t,r,s){const i=this.cloneDeep(e),n=this.transformState(t),o={},a={};return this.map(i,(e,t)=>{n[e]||(a[e]=t)}),this.map(n,(e,t)=>{const r=i[e];if(r){const s=t.map(e=>e.presence_ref),i=r.map(e=>e.presence_ref),n=t.filter(e=>i.indexOf(e.presence_ref)<0),l=r.filter(e=>s.indexOf(e.presence_ref)<0);n.length>0&&(o[e]=n),l.length>0&&(a[e]=l)}else o[e]=t}),this.syncDiff(i,{joins:o,leaves:a},r,s)}static syncDiff(e,t,r,s){const{joins:i,leaves:n}={joins:this.transformState(t.joins),leaves:this.transformState(t.leaves)};return r||(r=()=>{}),s||(s=()=>{}),this.map(i,(t,s)=>{var i;const n=null!==(i=e[t])&&void 0!==i?i:[];if(e[t]=this.cloneDeep(s),n.length>0){const r=e[t].map(e=>e.presence_ref),s=n.filter(e=>r.indexOf(e.presence_ref)<0);e[t].unshift(...s)}r(t,n,s)}),this.map(n,(t,r)=>{let i=e[t];if(!i)return;const n=r.map(e=>e.presence_ref);i=i.filter(e=>n.indexOf(e.presence_ref)<0),e[t]=i,s(t,i,r),0===i.length&&delete e[t]}),e}static map(e,t){return Object.getOwnPropertyNames(e).map(r=>t(r,e[r]))}static transformState(e){return e=this.cloneDeep(e),Object.getOwnPropertyNames(e).reduce((t,r)=>{const s=e[r];return t[r]="metas"in s?s.metas.map(e=>(e.presence_ref=e.phx_ref,delete e.phx_ref,delete e.phx_ref_prev,e)):s,t},{})}static cloneDeep(e){return JSON.parse(JSON.stringify(e))}onJoin(e){this.caller.onJoin=e}onLeave(e){this.caller.onLeave=e}onSync(e){this.caller.onSync=e}inPendingSyncState(){return!this.joinRef||this.joinRef!==this.channel._joinRef()}}(Qt=Zt||(Zt={})).ALL="*",Qt.INSERT="INSERT",Qt.UPDATE="UPDATE",Qt.DELETE="DELETE",(tr=er||(er={})).BROADCAST="broadcast",tr.PRESENCE="presence",tr.POSTGRES_CHANGES="postgres_changes",tr.SYSTEM="system",(sr=rr||(rr={})).SUBSCRIBED="SUBSCRIBED",sr.TIMED_OUT="TIMED_OUT",sr.CLOSED="CLOSED",sr.CHANNEL_ERROR="CHANNEL_ERROR";class nr{constructor(e,t={config:{}},r){this.topic=e,this.params=t,this.socket=r,this.bindings={},this.state=Ot.closed,this.joinedOnce=!1,this.pushBuffer=[],this.subTopic=e.replace(/^realtime:/i,""),this.params.config=Object.assign({broadcast:{ack:!1,self:!1},presence:{key:""},private:!1},t.config),this.timeout=this.socket.timeout,this.joinPush=new Wt(this,Ct.join,this.params,this.timeout),this.rejoinTimer=new Ut(()=>this._rejoinUntilConnected(),this.socket.reconnectAfterMs),this.joinPush.receive("ok",()=>{this.state=Ot.joined,this.rejoinTimer.reset(),this.pushBuffer.forEach(e=>e.send()),this.pushBuffer=[]}),this._onClose(()=>{this.rejoinTimer.reset(),this.socket.log("channel",`close ${this.topic} ${this._joinRef()}`),this.state=Ot.closed,this.socket._remove(this)}),this._onError(e=>{this._isLeaving()||this._isClosed()||(this.socket.log("channel",`error ${this.topic}`,e),this.state=Ot.errored,this.rejoinTimer.scheduleTimeout())}),this.joinPush.receive("timeout",()=>{this._isJoining()&&(this.socket.log("channel",`timeout ${this.topic}`,this.joinPush.timeout),this.state=Ot.errored,this.rejoinTimer.scheduleTimeout())}),this._on(Ct.reply,{},(e,t)=>{this._trigger(this._replyEventName(t),e)}),this.presence=new ir(this),this.broadcastEndpointURL=Gt(this.socket.endPoint)+"/api/broadcast",this.private=this.params.config.private||!1}subscribe(t,r=this.timeout){var s,i;if(this.socket.isConnected()||this.socket.connect(),this.joinedOnce)throw"tried to subscribe multiple times. 'subscribe' can only be called a single time per channel instance";{const{config:{broadcast:n,presence:o,private:a}}=this.params;this._onError(e=>null==t?void 0:t(rr.CHANNEL_ERROR,e)),this._onClose(()=>null==t?void 0:t(rr.CLOSED));const l={},c={broadcast:n,presence:o,postgres_changes:null!==(i=null===(s=this.bindings.postgres_changes)||void 0===s?void 0:s.map(e=>e.filter))&&void 0!==i?i:[],private:a};this.socket.accessTokenValue&&(l.access_token=this.socket.accessTokenValue),this.updateJoinPayload(Object.assign({config:c},l)),this.joinedOnce=!0,this._rejoin(r),this.joinPush.receive("ok",r=>e(this,[r],function*({postgres_changes:e}){var r;if(this.socket.setAuth(),void 0!==e){const s=this.bindings.postgres_changes,i=null!==(r=null==s?void 0:s.length)&&void 0!==r?r:0,n=[];for(let r=0;r<i;r++){const i=s[r],{filter:{event:o,schema:a,table:l,filter:c}}=i,h=e&&e[r];if(!h||h.event!==o||h.schema!==a||h.table!==l||h.filter!==c)return this.unsubscribe(),void(null==t||t(rr.CHANNEL_ERROR,new Error("mismatch between server and client bindings for postgres changes")));n.push(Object.assign(Object.assign({},i),{id:h.id}))}return this.bindings.postgres_changes=n,void(t&&t(rr.SUBSCRIBED))}null==t||t(rr.SUBSCRIBED)})).receive("error",e=>{null==t||t(rr.CHANNEL_ERROR,new Error(JSON.stringify(Object.values(e).join(", ")||"error")))}).receive("timeout",()=>{null==t||t(rr.TIMED_OUT)})}return this}presenceState(){return this.presence.state}track(t){return e(this,arguments,function*(e,t={}){return yield this.send({type:"presence",event:"track",payload:e},t.timeout||this.timeout)})}untrack(){return e(this,arguments,function*(e={}){return yield this.send({type:"presence",event:"untrack"},e)})}on(e,t,r){return this._on(e,t,r)}send(t){return e(this,arguments,function*(e,t={}){var r,s;if(this._canPush()||"broadcast"!==e.type)return new Promise(r=>{var s,i,n;const o=this._push(e.type,e,t.timeout||this.timeout);"broadcast"!==e.type||(null===(n=null===(i=null===(s=this.params)||void 0===s?void 0:s.config)||void 0===i?void 0:i.broadcast)||void 0===n?void 0:n.ack)||r("ok"),o.receive("ok",()=>r("ok")),o.receive("error",()=>r("error")),o.receive("timeout",()=>r("timed out"))});{const{event:n,payload:o}=e,a={method:"POST",headers:{Authorization:this.socket.accessTokenValue?`Bearer ${this.socket.accessTokenValue}`:"",apikey:this.socket.apiKey?this.socket.apiKey:"","Content-Type":"application/json"},body:JSON.stringify({messages:[{topic:this.subTopic,event:n,payload:o,private:this.private}]})};try{const e=yield this._fetchWithTimeout(this.broadcastEndpointURL,a,null!==(r=t.timeout)&&void 0!==r?r:this.timeout);return yield null===(s=e.body)||void 0===s?void 0:s.cancel(),e.ok?"ok":"error"}catch(i){return"AbortError"===i.name?"timed out":"error"}}})}updateJoinPayload(e){this.joinPush.updatePayload(e)}unsubscribe(e=this.timeout){this.state=Ot.leaving;const t=()=>{this.socket.log("channel",`leave ${this.topic}`),this._trigger(Ct.close,"leave",this._joinRef())};return this.rejoinTimer.reset(),this.joinPush.destroy(),new Promise(r=>{const s=new Wt(this,Ct.leave,{},e);s.receive("ok",()=>{t(),r("ok")}).receive("timeout",()=>{t(),r("timed out")}).receive("error",()=>{r("error")}),s.send(),this._canPush()||s.trigger("ok",{})})}_fetchWithTimeout(t,r,s){return e(this,null,function*(){const e=new AbortController,i=setTimeout(()=>e.abort(),s),n=yield this.socket.fetch(t,Object.assign(Object.assign({},r),{signal:e.signal}));return clearTimeout(i),n})}_push(e,t,r=this.timeout){if(!this.joinedOnce)throw`tried to push '${e}' to '${this.topic}' before joining. Use channel.subscribe() before pushing events`;let s=new Wt(this,e,t,r);return this._canPush()?s.send():(s.startTimeout(),this.pushBuffer.push(s)),s}_onMessage(e,t,r){return t}_isMember(e){return this.topic===e}_joinRef(){return this.joinPush.ref}_trigger(e,t,r){var s,i;const n=e.toLocaleLowerCase(),{close:o,error:a,leave:l,join:c}=Ct;if(r&&[o,a,l,c].indexOf(n)>=0&&r!==this._joinRef())return;let h=this._onMessage(n,t,r);if(t&&!h)throw"channel onMessage callbacks must return the payload, modified or unmodified";["insert","update","delete"].includes(n)?null===(s=this.bindings.postgres_changes)||void 0===s||s.filter(e=>{var t,r,s;return"*"===(null===(t=e.filter)||void 0===t?void 0:t.event)||(null===(s=null===(r=e.filter)||void 0===r?void 0:r.event)||void 0===s?void 0:s.toLocaleLowerCase())===n}).map(e=>e.callback(h,r)):null===(i=this.bindings[n])||void 0===i||i.filter(e=>{var r,s,i,o,a,l;if(["broadcast","presence","postgres_changes"].includes(n)){if("id"in e){const n=e.id,o=null===(r=e.filter)||void 0===r?void 0:r.event;return n&&(null===(s=t.ids)||void 0===s?void 0:s.includes(n))&&("*"===o||(null==o?void 0:o.toLocaleLowerCase())===(null===(i=t.data)||void 0===i?void 0:i.type.toLocaleLowerCase()))}{const r=null===(a=null===(o=null==e?void 0:e.filter)||void 0===o?void 0:o.event)||void 0===a?void 0:a.toLocaleLowerCase();return"*"===r||r===(null===(l=null==t?void 0:t.event)||void 0===l?void 0:l.toLocaleLowerCase())}}return e.type.toLocaleLowerCase()===n}).map(e=>{if("object"==typeof h&&"ids"in h){const e=h.data,{schema:t,table:r,commit_timestamp:s,type:i,errors:n}=e,o={schema:t,table:r,commit_timestamp:s,eventType:i,new:{},old:{},errors:n};h=Object.assign(Object.assign({},o),this._getPayloadRecords(e))}e.callback(h,r)})}_isClosed(){return this.state===Ot.closed}_isJoined(){return this.state===Ot.joined}_isJoining(){return this.state===Ot.joining}_isLeaving(){return this.state===Ot.leaving}_replyEventName(e){return`chan_reply_${e}`}_on(e,t,r){const s=e.toLocaleLowerCase(),i={type:s,filter:t,callback:r};return this.bindings[s]?this.bindings[s].push(i):this.bindings[s]=[i],this}_off(e,t){const r=e.toLocaleLowerCase();return this.bindings[r]=this.bindings[r].filter(e=>{var s;return!((null===(s=e.type)||void 0===s?void 0:s.toLocaleLowerCase())===r&&nr.isEqual(e.filter,t))}),this}static isEqual(e,t){if(Object.keys(e).length!==Object.keys(t).length)return!1;for(const r in e)if(e[r]!==t[r])return!1;return!0}_rejoinUntilConnected(){this.rejoinTimer.scheduleTimeout(),this.socket.isConnected()&&this._rejoin()}_onClose(e){this._on(Ct.close,{},e)}_onError(e){this._on(Ct.error,{},t=>e(t))}_canPush(){return this.socket.isConnected()&&this._isJoined()}_rejoin(e=this.timeout){this._isLeaving()||(this.socket._leaveOpenTopic(this.topic),this.state=Ot.joining,this.joinPush.resend(e))}_getPayloadRecords(e){const t={new:{},old:{}};return"INSERT"!==e.type&&"UPDATE"!==e.type||(t.new=Bt(e.columns,e.record)),"UPDATE"!==e.type&&"DELETE"!==e.type||(t.old=Bt(e.columns,e.old_record)),t}}const or=()=>{},ar="undefined"!=typeof WebSocket;class lr{constructor(t,r){var s;this.accessTokenValue=null,this.apiKey=null,this.channels=[],this.endPoint="",this.httpEndpoint="",this.headers=Et,this.params={},this.timeout=1e4,this.heartbeatIntervalMs=3e4,this.heartbeatTimer=void 0,this.pendingHeartbeatRef=null,this.ref=0,this.logger=or,this.conn=null,this.sendBuffer=[],this.serializer=new Mt,this.stateChangeCallbacks={open:[],close:[],error:[],message:[]},this.accessToken=null,this._resolveFetch=e=>{let t;return t=e||("undefined"==typeof fetch?(...e)=>_(()=>Promise.resolve().then(()=>Ve),void 0).then(({default:t})=>t(...e)):fetch),(...e)=>t(...e)},this.endPoint=`${t}/${$t.websocket}`,this.httpEndpoint=Gt(t),(null==r?void 0:r.transport)?this.transport=r.transport:this.transport=null,(null==r?void 0:r.params)&&(this.params=r.params),(null==r?void 0:r.headers)&&(this.headers=Object.assign(Object.assign({},this.headers),r.headers)),(null==r?void 0:r.timeout)&&(this.timeout=r.timeout),(null==r?void 0:r.logger)&&(this.logger=r.logger),(null==r?void 0:r.heartbeatIntervalMs)&&(this.heartbeatIntervalMs=r.heartbeatIntervalMs);const i=null===(s=null==r?void 0:r.params)||void 0===s?void 0:s.apikey;if(i&&(this.accessTokenValue=i,this.apiKey=i),this.reconnectAfterMs=(null==r?void 0:r.reconnectAfterMs)?r.reconnectAfterMs:e=>[1e3,2e3,5e3,1e4][e-1]||1e4,this.encode=(null==r?void 0:r.encode)?r.encode:(e,t)=>t(JSON.stringify(e)),this.decode=(null==r?void 0:r.decode)?r.decode:this.serializer.decode.bind(this.serializer),this.reconnectTimer=new Ut(()=>e(this,null,function*(){this.disconnect(),this.connect()}),this.reconnectAfterMs),this.fetch=this._resolveFetch(null==r?void 0:r.fetch),null==r?void 0:r.worker){if("undefined"!=typeof window&&!window.Worker)throw new Error("Web Worker is not supported");this.worker=(null==r?void 0:r.worker)||!1,this.workerUrl=null==r?void 0:r.workerUrl}this.accessToken=(null==r?void 0:r.accessToken)||null}connect(){if(!this.conn)if(this.transport)this.conn=new this.transport(this.endpointURL(),void 0,{headers:this.headers});else{if(ar)return this.conn=new WebSocket(this.endpointURL()),void this.setupConnection();this.conn=new cr(this.endpointURL(),void 0,{close:()=>{this.conn=null}}),_(()=>import("./browser-f0dabffb.js").then(e=>e.b),["assets/browser-f0dabffb.js","assets/react-vendor-99be060c.js"]).then(({default:e})=>{this.conn=new e(this.endpointURL(),void 0,{headers:this.headers}),this.setupConnection()})}}endpointURL(){return this._appendParams(this.endPoint,Object.assign({},this.params,{vsn:"1.0.0"}))}disconnect(e,t){this.conn&&(this.conn.onclose=function(){},e?this.conn.close(e,null!=t?t:""):this.conn.close(),this.conn=null,this.heartbeatTimer&&clearInterval(this.heartbeatTimer),this.reconnectTimer.reset())}getChannels(){return this.channels}removeChannel(t){return e(this,null,function*(){const e=yield t.unsubscribe();return 0===this.channels.length&&this.disconnect(),e})}removeAllChannels(){return e(this,null,function*(){const e=yield Promise.all(this.channels.map(e=>e.unsubscribe()));return this.disconnect(),e})}log(e,t,r){this.logger(e,t,r)}connectionState(){switch(this.conn&&this.conn.readyState){case jt.connecting:return zt.Connecting;case jt.open:return zt.Open;case jt.closing:return zt.Closing;default:return zt.Closed}}isConnected(){return this.connectionState()===zt.Open}channel(e,t={config:{}}){const r=new nr(`realtime:${e}`,t,this);return this.channels.push(r),r}push(e){const{topic:t,event:r,payload:s,ref:i}=e,n=()=>{this.encode(e,e=>{var t;null===(t=this.conn)||void 0===t||t.send(e)})};this.log("push",`${t} ${r} (${i})`,s),this.isConnected()?n():this.sendBuffer.push(n)}setAuth(t=null){return e(this,null,function*(){let e=t||this.accessToken&&(yield this.accessToken())||this.accessTokenValue;if(e){let t=null;try{t=JSON.parse(atob(e.split(".")[1]))}catch(r){}if(t&&t.exp){if(!(Math.floor(Date.now()/1e3)-t.exp<0))return this.log("auth",`InvalidJWTToken: Invalid value for JWT claim "exp" with value ${t.exp}`),Promise.reject(`InvalidJWTToken: Invalid value for JWT claim "exp" with value ${t.exp}`)}this.accessTokenValue=e,this.channels.forEach(t=>{e&&t.updateJoinPayload({access_token:e}),t.joinedOnce&&t._isJoined()&&t._push(Ct.access_token,{access_token:e})})}})}sendHeartbeat(){return e(this,null,function*(){var e;if(this.isConnected()){if(this.pendingHeartbeatRef)return this.pendingHeartbeatRef=null,this.log("transport","heartbeat timeout. Attempting to re-establish connection"),void(null===(e=this.conn)||void 0===e||e.close(1e3,"hearbeat timeout"));this.pendingHeartbeatRef=this._makeRef(),this.push({topic:"phoenix",event:"heartbeat",payload:{},ref:this.pendingHeartbeatRef}),this.setAuth()}})}flushSendBuffer(){this.isConnected()&&this.sendBuffer.length>0&&(this.sendBuffer.forEach(e=>e()),this.sendBuffer=[])}_makeRef(){let e=this.ref+1;return e===this.ref?this.ref=0:this.ref=e,this.ref.toString()}_leaveOpenTopic(e){let t=this.channels.find(t=>t.topic===e&&(t._isJoined()||t._isJoining()));t&&(this.log("transport",`leaving duplicate topic "${e}"`),t.unsubscribe())}_remove(e){this.channels=this.channels.filter(t=>t._joinRef()!==e._joinRef())}setupConnection(){this.conn&&(this.conn.binaryType="arraybuffer",this.conn.onopen=()=>this._onConnOpen(),this.conn.onerror=e=>this._onConnError(e),this.conn.onmessage=e=>this._onConnMessage(e),this.conn.onclose=e=>this._onConnClose(e))}_onConnMessage(e){this.decode(e.data,e=>{let{topic:t,event:r,payload:s,ref:i}=e;i&&i===this.pendingHeartbeatRef&&(this.pendingHeartbeatRef=null),this.log("receive",`${s.status||""} ${t} ${r} ${i&&"("+i+")"||""}`,s),this.channels.filter(e=>e._isMember(t)).forEach(e=>e._trigger(r,s,i)),this.stateChangeCallbacks.message.forEach(t=>t(e))})}_onConnOpen(){return e(this,null,function*(){if(this.log("transport",`connected to ${this.endpointURL()}`),this.flushSendBuffer(),this.reconnectTimer.reset(),this.worker){this.workerUrl?this.log("worker",`starting worker for from ${this.workerUrl}`):this.log("worker","starting default worker");const e=this._workerObjectUrl(this.workerUrl);this.workerRef=new Worker(e),this.workerRef.onerror=e=>{this.log("worker","worker error",e.message),this.workerRef.terminate()},this.workerRef.onmessage=e=>{"keepAlive"===e.data.event&&this.sendHeartbeat()},this.workerRef.postMessage({event:"start",interval:this.heartbeatIntervalMs})}else this.heartbeatTimer&&clearInterval(this.heartbeatTimer),this.heartbeatTimer=setInterval(()=>this.sendHeartbeat(),this.heartbeatIntervalMs);this.stateChangeCallbacks.open.forEach(e=>e())})}_onConnClose(e){this.log("transport","close",e),this._triggerChanError(),this.heartbeatTimer&&clearInterval(this.heartbeatTimer),this.reconnectTimer.scheduleTimeout(),this.stateChangeCallbacks.close.forEach(t=>t(e))}_onConnError(e){this.log("transport",e.message),this._triggerChanError(),this.stateChangeCallbacks.error.forEach(t=>t(e))}_triggerChanError(){this.channels.forEach(e=>e._trigger(Ct.error))}_appendParams(e,t){if(0===Object.keys(t).length)return e;const r=e.match(/\?/)?"&":"?";return`${e}${r}${new URLSearchParams(t)}`}_workerObjectUrl(e){let t;if(e)t=e;else{const e=new Blob(['\n  addEventListener("message", (e) => {\n    if (e.data.event === "start") {\n      setInterval(() => postMessage({ event: "keepAlive" }), e.data.interval);\n    }\n  });'],{type:"application/javascript"});t=URL.createObjectURL(e)}return t}}class cr{constructor(e,t,r){this.binaryType="arraybuffer",this.onclose=()=>{},this.onerror=()=>{},this.onmessage=()=>{},this.onopen=()=>{},this.readyState=jt.connecting,this.send=()=>{},this.url=null,this.url=e,this.close=r.close}}class hr extends Error{constructor(e){super(e),this.__isStorageError=!0,this.name="StorageError"}}function ur(e){return"object"==typeof e&&null!==e&&"__isStorageError"in e}class dr extends hr{constructor(e,t){super(e),this.name="StorageApiError",this.status=t}toJSON(){return{name:this.name,message:this.message,status:this.status}}}class fr extends hr{constructor(e,t){super(e),this.name="StorageUnknownError",this.originalError=t}}var pr=globalThis&&globalThis.__awaiter||function(e,t,r,s){return new(r||(r=Promise))(function(i,n){function o(e){try{l(s.next(e))}catch(t){n(t)}}function a(e){try{l(s.throw(e))}catch(t){n(t)}}function l(e){var t;e.done?i(e.value):(t=e.value,t instanceof r?t:new r(function(e){e(t)})).then(o,a)}l((s=s.apply(e,t||[])).next())})};const gr=e=>{let t;return t=e||("undefined"==typeof fetch?(...e)=>_(()=>Promise.resolve().then(()=>Ve),void 0).then(({default:t})=>t(...e)):fetch),(...e)=>t(...e)},vr=e=>{if(Array.isArray(e))return e.map(e=>vr(e));if("function"==typeof e||e!==Object(e))return e;const t={};return Object.entries(e).forEach(([e,r])=>{const s=e.replace(/([-_][a-z])/gi,e=>e.toUpperCase().replace(/[-_]/g,""));t[s]=vr(r)}),t};var yr=globalThis&&globalThis.__awaiter||function(e,t,r,s){return new(r||(r=Promise))(function(i,n){function o(e){try{l(s.next(e))}catch(t){n(t)}}function a(e){try{l(s.throw(e))}catch(t){n(t)}}function l(e){var t;e.done?i(e.value):(t=e.value,t instanceof r?t:new r(function(e){e(t)})).then(o,a)}l((s=s.apply(e,t||[])).next())})};const mr=e=>e.msg||e.message||e.error_description||e.error||JSON.stringify(e),_r=(e,t,r)=>yr(void 0,void 0,void 0,function*(){const s=yield pr(void 0,void 0,void 0,function*(){return"undefined"==typeof Response?(yield _(()=>Promise.resolve().then(()=>Ve),void 0)).Response:Response});e instanceof s&&!(null==r?void 0:r.noResolveJson)?e.json().then(r=>{t(new dr(mr(r),e.status||500))}).catch(e=>{t(new fr(mr(e),e))}):t(new fr(mr(e),e))});function br(e,t,r,s,i,n){return yr(this,void 0,void 0,function*(){return new Promise((o,a)=>{e(r,((e,t,r,s)=>{const i={method:e,headers:(null==t?void 0:t.headers)||{}};return"GET"===e?i:(i.headers=Object.assign({"Content-Type":"application/json"},null==t?void 0:t.headers),s&&(i.body=JSON.stringify(s)),Object.assign(Object.assign({},i),r))})(t,s,i,n)).then(e=>{if(!e.ok)throw e;return(null==s?void 0:s.noResolveJson)?e:e.json()}).then(e=>o(e)).catch(e=>_r(e,a,s))})})}function wr(e,t,r,s){return yr(this,void 0,void 0,function*(){return br(e,"GET",t,r,s)})}function kr(e,t,r,s,i){return yr(this,void 0,void 0,function*(){return br(e,"POST",t,s,i,r)})}function Tr(e,t,r,s,i){return yr(this,void 0,void 0,function*(){return br(e,"DELETE",t,s,i,r)})}var Sr=globalThis&&globalThis.__awaiter||function(e,t,r,s){return new(r||(r=Promise))(function(i,n){function o(e){try{l(s.next(e))}catch(t){n(t)}}function a(e){try{l(s.throw(e))}catch(t){n(t)}}function l(e){var t;e.done?i(e.value):(t=e.value,t instanceof r?t:new r(function(e){e(t)})).then(o,a)}l((s=s.apply(e,t||[])).next())})};const Er={limit:100,offset:0,sortBy:{column:"name",order:"asc"}},jr={cacheControl:"3600",contentType:"text/plain;charset=UTF-8",upsert:!1};class Pr{constructor(e,t={},r,s){this.url=e,this.headers=t,this.bucketId=r,this.fetch=gr(s)}uploadOrUpdate(e,t,r,s){return Sr(this,void 0,void 0,function*(){try{let i;const n=Object.assign(Object.assign({},jr),s);let o=Object.assign(Object.assign({},this.headers),"POST"===e&&{"x-upsert":String(n.upsert)});const a=n.metadata;"undefined"!=typeof Blob&&r instanceof Blob?(i=new FormData,i.append("cacheControl",n.cacheControl),a&&i.append("metadata",this.encodeMetadata(a)),i.append("",r)):"undefined"!=typeof FormData&&r instanceof FormData?(i=r,i.append("cacheControl",n.cacheControl),a&&i.append("metadata",this.encodeMetadata(a))):(i=r,o["cache-control"]=`max-age=${n.cacheControl}`,o["content-type"]=n.contentType,a&&(o["x-metadata"]=this.toBase64(this.encodeMetadata(a)))),(null==s?void 0:s.headers)&&(o=Object.assign(Object.assign({},o),s.headers));const l=this._removeEmptyFolders(t),c=this._getFinalPath(l),h=yield this.fetch(`${this.url}/object/${c}`,Object.assign({method:e,body:i,headers:o},(null==n?void 0:n.duplex)?{duplex:n.duplex}:{})),u=yield h.json();if(h.ok)return{data:{path:l,id:u.Id,fullPath:u.Key},error:null};return{data:null,error:u}}catch(i){if(ur(i))return{data:null,error:i};throw i}})}upload(e,t,r){return Sr(this,void 0,void 0,function*(){return this.uploadOrUpdate("POST",e,t,r)})}uploadToSignedUrl(e,t,r,s){return Sr(this,void 0,void 0,function*(){const i=this._removeEmptyFolders(e),n=this._getFinalPath(i),o=new URL(this.url+`/object/upload/sign/${n}`);o.searchParams.set("token",t);try{let e;const t=Object.assign({upsert:jr.upsert},s),n=Object.assign(Object.assign({},this.headers),{"x-upsert":String(t.upsert)});"undefined"!=typeof Blob&&r instanceof Blob?(e=new FormData,e.append("cacheControl",t.cacheControl),e.append("",r)):"undefined"!=typeof FormData&&r instanceof FormData?(e=r,e.append("cacheControl",t.cacheControl)):(e=r,n["cache-control"]=`max-age=${t.cacheControl}`,n["content-type"]=t.contentType);const a=yield this.fetch(o.toString(),{method:"PUT",body:e,headers:n}),l=yield a.json();if(a.ok)return{data:{path:i,fullPath:l.Key},error:null};return{data:null,error:l}}catch(a){if(ur(a))return{data:null,error:a};throw a}})}createSignedUploadUrl(e,t){return Sr(this,void 0,void 0,function*(){try{let r=this._getFinalPath(e);const s=Object.assign({},this.headers);(null==t?void 0:t.upsert)&&(s["x-upsert"]="true");const i=yield kr(this.fetch,`${this.url}/object/upload/sign/${r}`,{},{headers:s}),n=new URL(this.url+i.url),o=n.searchParams.get("token");if(!o)throw new hr("No token returned by API");return{data:{signedUrl:n.toString(),path:e,token:o},error:null}}catch(r){if(ur(r))return{data:null,error:r};throw r}})}update(e,t,r){return Sr(this,void 0,void 0,function*(){return this.uploadOrUpdate("PUT",e,t,r)})}move(e,t,r){return Sr(this,void 0,void 0,function*(){try{return{data:yield kr(this.fetch,`${this.url}/object/move`,{bucketId:this.bucketId,sourceKey:e,destinationKey:t,destinationBucket:null==r?void 0:r.destinationBucket},{headers:this.headers}),error:null}}catch(s){if(ur(s))return{data:null,error:s};throw s}})}copy(e,t,r){return Sr(this,void 0,void 0,function*(){try{return{data:{path:(yield kr(this.fetch,`${this.url}/object/copy`,{bucketId:this.bucketId,sourceKey:e,destinationKey:t,destinationBucket:null==r?void 0:r.destinationBucket},{headers:this.headers})).Key},error:null}}catch(s){if(ur(s))return{data:null,error:s};throw s}})}createSignedUrl(e,t,r){return Sr(this,void 0,void 0,function*(){try{let s=this._getFinalPath(e),i=yield kr(this.fetch,`${this.url}/object/sign/${s}`,Object.assign({expiresIn:t},(null==r?void 0:r.transform)?{transform:r.transform}:{}),{headers:this.headers});const n=(null==r?void 0:r.download)?`&download=${!0===r.download?"":r.download}`:"";return i={signedUrl:encodeURI(`${this.url}${i.signedURL}${n}`)},{data:i,error:null}}catch(s){if(ur(s))return{data:null,error:s};throw s}})}createSignedUrls(e,t,r){return Sr(this,void 0,void 0,function*(){try{const s=yield kr(this.fetch,`${this.url}/object/sign/${this.bucketId}`,{expiresIn:t,paths:e},{headers:this.headers}),i=(null==r?void 0:r.download)?`&download=${!0===r.download?"":r.download}`:"";return{data:s.map(e=>Object.assign(Object.assign({},e),{signedUrl:e.signedURL?encodeURI(`${this.url}${e.signedURL}${i}`):null})),error:null}}catch(s){if(ur(s))return{data:null,error:s};throw s}})}download(e,t){return Sr(this,void 0,void 0,function*(){const r=void 0!==(null==t?void 0:t.transform)?"render/image/authenticated":"object",s=this.transformOptsToQueryString((null==t?void 0:t.transform)||{}),i=s?`?${s}`:"";try{const t=this._getFinalPath(e),s=yield wr(this.fetch,`${this.url}/${r}/${t}${i}`,{headers:this.headers,noResolveJson:!0});return{data:yield s.blob(),error:null}}catch(n){if(ur(n))return{data:null,error:n};throw n}})}info(e){return Sr(this,void 0,void 0,function*(){const t=this._getFinalPath(e);try{const e=yield wr(this.fetch,`${this.url}/object/info/${t}`,{headers:this.headers});return{data:vr(e),error:null}}catch(r){if(ur(r))return{data:null,error:r};throw r}})}exists(e){return Sr(this,void 0,void 0,function*(){const t=this._getFinalPath(e);try{return yield function(e,t,r,s){return yr(this,void 0,void 0,function*(){return br(e,"HEAD",t,Object.assign(Object.assign({},r),{noResolveJson:!0}),s)})}(this.fetch,`${this.url}/object/${t}`,{headers:this.headers}),{data:!0,error:null}}catch(r){if(ur(r)&&r instanceof fr){const e=r.originalError;if([400,404].includes(null==e?void 0:e.status))return{data:!1,error:r}}throw r}})}getPublicUrl(e,t){const r=this._getFinalPath(e),s=[],i=(null==t?void 0:t.download)?`download=${!0===t.download?"":t.download}`:"";""!==i&&s.push(i);const n=void 0!==(null==t?void 0:t.transform)?"render/image":"object",o=this.transformOptsToQueryString((null==t?void 0:t.transform)||{});""!==o&&s.push(o);let a=s.join("&");return""!==a&&(a=`?${a}`),{data:{publicUrl:encodeURI(`${this.url}/${n}/public/${r}${a}`)}}}remove(e){return Sr(this,void 0,void 0,function*(){try{return{data:yield Tr(this.fetch,`${this.url}/object/${this.bucketId}`,{prefixes:e},{headers:this.headers}),error:null}}catch(t){if(ur(t))return{data:null,error:t};throw t}})}list(e,t,r){return Sr(this,void 0,void 0,function*(){try{const s=Object.assign(Object.assign(Object.assign({},Er),t),{prefix:e||""});return{data:yield kr(this.fetch,`${this.url}/object/list/${this.bucketId}`,s,{headers:this.headers},r),error:null}}catch(s){if(ur(s))return{data:null,error:s};throw s}})}encodeMetadata(e){return JSON.stringify(e)}toBase64(e){return"undefined"!=typeof Buffer?Buffer.from(e).toString("base64"):btoa(e)}_getFinalPath(e){return`${this.bucketId}/${e}`}_removeEmptyFolders(e){return e.replace(/^\/|\/$/g,"").replace(/\/+/g,"/")}transformOptsToQueryString(e){const t=[];return e.width&&t.push(`width=${e.width}`),e.height&&t.push(`height=${e.height}`),e.resize&&t.push(`resize=${e.resize}`),e.format&&t.push(`format=${e.format}`),e.quality&&t.push(`quality=${e.quality}`),t.join("&")}}const Or={"X-Client-Info":"storage-js/2.7.1"};var xr=globalThis&&globalThis.__awaiter||function(e,t,r,s){return new(r||(r=Promise))(function(i,n){function o(e){try{l(s.next(e))}catch(t){n(t)}}function a(e){try{l(s.throw(e))}catch(t){n(t)}}function l(e){var t;e.done?i(e.value):(t=e.value,t instanceof r?t:new r(function(e){e(t)})).then(o,a)}l((s=s.apply(e,t||[])).next())})};class Cr{constructor(e,t={},r){this.url=e,this.headers=Object.assign(Object.assign({},Or),t),this.fetch=gr(r)}listBuckets(){return xr(this,void 0,void 0,function*(){try{return{data:yield wr(this.fetch,`${this.url}/bucket`,{headers:this.headers}),error:null}}catch(e){if(ur(e))return{data:null,error:e};throw e}})}getBucket(e){return xr(this,void 0,void 0,function*(){try{return{data:yield wr(this.fetch,`${this.url}/bucket/${e}`,{headers:this.headers}),error:null}}catch(t){if(ur(t))return{data:null,error:t};throw t}})}createBucket(e,t={public:!1}){return xr(this,void 0,void 0,function*(){try{return{data:yield kr(this.fetch,`${this.url}/bucket`,{id:e,name:e,public:t.public,file_size_limit:t.fileSizeLimit,allowed_mime_types:t.allowedMimeTypes},{headers:this.headers}),error:null}}catch(r){if(ur(r))return{data:null,error:r};throw r}})}updateBucket(e,t){return xr(this,void 0,void 0,function*(){try{const r=yield function(e,t,r,s,i){return yr(this,void 0,void 0,function*(){return br(e,"PUT",t,s,i,r)})}(this.fetch,`${this.url}/bucket/${e}`,{id:e,name:e,public:t.public,file_size_limit:t.fileSizeLimit,allowed_mime_types:t.allowedMimeTypes},{headers:this.headers});return{data:r,error:null}}catch(r){if(ur(r))return{data:null,error:r};throw r}})}emptyBucket(e){return xr(this,void 0,void 0,function*(){try{return{data:yield kr(this.fetch,`${this.url}/bucket/${e}/empty`,{},{headers:this.headers}),error:null}}catch(t){if(ur(t))return{data:null,error:t};throw t}})}deleteBucket(e){return xr(this,void 0,void 0,function*(){try{return{data:yield Tr(this.fetch,`${this.url}/bucket/${e}`,{},{headers:this.headers}),error:null}}catch(t){if(ur(t))return{data:null,error:t};throw t}})}}class Ar extends Cr{constructor(e,t={},r){super(e,t,r)}from(e){return new Pr(this.url,this.headers,e,this.fetch)}}let $r="";$r="undefined"!=typeof Deno?"deno":"undefined"!=typeof document?"web":"undefined"!=typeof navigator&&"ReactNative"===navigator.product?"react-native":"node";const zr={headers:{"X-Client-Info":`supabase-js-${$r}/2.49.4`}},Rr={schema:"public"},Ir={autoRefreshToken:!0,persistSession:!0,detectSessionInUrl:!0,flowType:"implicit"},Lr={};var Mr=globalThis&&globalThis.__awaiter||function(e,t,r,s){return new(r||(r=Promise))(function(i,n){function o(e){try{l(s.next(e))}catch(t){n(t)}}function a(e){try{l(s.throw(e))}catch(t){n(t)}}function l(e){var t;e.done?i(e.value):(t=e.value,t instanceof r?t:new r(function(e){e(t)})).then(o,a)}l((s=s.apply(e,t||[])).next())})};const Ur=(e,t,r)=>{const s=(e=>{let t;return t=e||("undefined"==typeof fetch?Be:fetch),(...e)=>t(...e)})(r),i="undefined"==typeof Headers?De:Headers;return(r,n)=>Mr(void 0,void 0,void 0,function*(){var o;const a=null!==(o=yield t())&&void 0!==o?o:e;let l=new i(null==n?void 0:n.headers);return l.has("apikey")||l.set("apikey",e),l.has("Authorization")||l.set("Authorization",`Bearer ${a}`),s(r,Object.assign(Object.assign({},n),{headers:l}))})};var Br=globalThis&&globalThis.__awaiter||function(e,t,r,s){return new(r||(r=Promise))(function(i,n){function o(e){try{l(s.next(e))}catch(t){n(t)}}function a(e){try{l(s.throw(e))}catch(t){n(t)}}function l(e){var t;e.done?i(e.value):(t=e.value,t instanceof r?t:new r(function(e){e(t)})).then(o,a)}l((s=s.apply(e,t||[])).next())})};const Dr="2.69.1",Hr=3e4,Nr=9e4,Vr={"X-Client-Info":`gotrue-js/${Dr}`},qr="X-Supabase-Api-Version",Fr={timestamp:Date.parse("2024-01-01T00:00:00.0Z"),name:"2024-01-01"},Jr=/^([a-z0-9_-]{4})*($|[a-z0-9_-]{3}$|[a-z0-9_-]{2}$)$/i;class Kr extends Error{constructor(e,t,r){super(e),this.__isAuthError=!0,this.name="AuthError",this.status=t,this.code=r}}function Gr(e){return"object"==typeof e&&null!==e&&"__isAuthError"in e}class Wr extends Kr{constructor(e,t,r){super(e,t,r),this.name="AuthApiError",this.status=t,this.code=r}}class Yr extends Kr{constructor(e,t){super(e),this.name="AuthUnknownError",this.originalError=t}}class Xr extends Kr{constructor(e,t,r,s){super(e,r,s),this.name=t,this.status=r}}class Zr extends Xr{constructor(){super("Auth session missing!","AuthSessionMissingError",400,void 0)}}class Qr extends Xr{constructor(){super("Auth session or user missing","AuthInvalidTokenResponseError",500,void 0)}}class es extends Xr{constructor(e){super(e,"AuthInvalidCredentialsError",400,void 0)}}class ts extends Xr{constructor(e,t=null){super(e,"AuthImplicitGrantRedirectError",500,void 0),this.details=null,this.details=t}toJSON(){return{name:this.name,message:this.message,status:this.status,details:this.details}}}class rs extends Xr{constructor(e,t=null){super(e,"AuthPKCEGrantCodeExchangeError",500,void 0),this.details=null,this.details=t}toJSON(){return{name:this.name,message:this.message,status:this.status,details:this.details}}}class ss extends Xr{constructor(e,t){super(e,"AuthRetryableFetchError",t,void 0)}}function is(e){return Gr(e)&&"AuthRetryableFetchError"===e.name}class ns extends Xr{constructor(e,t,r){super(e,"AuthWeakPasswordError",t,"weak_password"),this.reasons=r}}class os extends Xr{constructor(e){super(e,"AuthInvalidJwtError",400,"invalid_jwt")}}const as="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789-_".split(""),ls=" \t\n\r=".split(""),cs=(()=>{const e=new Array(128);for(let t=0;t<e.length;t+=1)e[t]=-1;for(let t=0;t<ls.length;t+=1)e[ls[t].charCodeAt(0)]=-2;for(let t=0;t<as.length;t+=1)e[as[t].charCodeAt(0)]=t;return e})();function hs(e,t,r){const s=cs[e];if(!(s>-1)){if(-2===s)return;throw new Error(`Invalid Base64-URL character "${String.fromCharCode(e)}"`)}for(t.queue=t.queue<<6|s,t.queuedBits+=6;t.queuedBits>=8;)r(t.queue>>t.queuedBits-8&255),t.queuedBits-=8}function us(e){const t=[],r=e=>{t.push(String.fromCodePoint(e))},s={utf8seq:0,codepoint:0},i={queue:0,queuedBits:0},n=e=>{!function(e,t,r){if(0===t.utf8seq){if(e<=127)return void r(e);for(let r=1;r<6;r+=1)if(!(e>>7-r&1)){t.utf8seq=r;break}if(2===t.utf8seq)t.codepoint=31&e;else if(3===t.utf8seq)t.codepoint=15&e;else{if(4!==t.utf8seq)throw new Error("Invalid UTF-8 sequence");t.codepoint=7&e}t.utf8seq-=1}else if(t.utf8seq>0){if(e<=127)throw new Error("Invalid UTF-8 sequence");t.codepoint=t.codepoint<<6|63&e,t.utf8seq-=1,0===t.utf8seq&&r(t.codepoint)}}(e,s,r)};for(let o=0;o<e.length;o+=1)hs(e.charCodeAt(o),i,n);return t.join("")}function ds(e,t){if(!(e<=127)){if(e<=2047)return t(192|e>>6),void t(128|63&e);if(e<=65535)return t(224|e>>12),t(128|e>>6&63),void t(128|63&e);if(e<=1114111)return t(240|e>>18),t(128|e>>12&63),t(128|e>>6&63),void t(128|63&e);throw new Error(`Unrecognized Unicode codepoint: ${e.toString(16)}`)}t(e)}function fs(e){const t=[],r={queue:0,queuedBits:0},s=e=>{t.push(e)};for(let i=0;i<e.length;i+=1)hs(e.charCodeAt(i),r,s);return new Uint8Array(t)}function ps(e){const t=[];return function(e,t){for(let r=0;r<e.length;r+=1){let s=e.charCodeAt(r);if(s>55295&&s<=56319){const t=1024*(s-55296)&65535;s=65536+(e.charCodeAt(r+1)-56320&65535|t),r+=1}ds(s,t)}}(e,e=>t.push(e)),new Uint8Array(t)}const gs=()=>"undefined"!=typeof window&&"undefined"!=typeof document,vs={tested:!1,writable:!1},ys=()=>{if(!gs())return!1;try{if("object"!=typeof globalThis.localStorage)return!1}catch(t){return!1}if(vs.tested)return vs.writable;const e=`lswt-${Math.random()}${Math.random()}`;try{globalThis.localStorage.setItem(e,e),globalThis.localStorage.removeItem(e),vs.tested=!0,vs.writable=!0}catch(t){vs.tested=!0,vs.writable=!1}return vs.writable};const ms=e=>{let t;return t=e||("undefined"==typeof fetch?(...e)=>_(()=>Promise.resolve().then(()=>Ve),void 0).then(({default:t})=>t(...e)):fetch),(...e)=>t(...e)},_s=(t,r,s)=>e(void 0,null,function*(){yield t.setItem(r,JSON.stringify(s))}),bs=(t,r)=>e(void 0,null,function*(){const e=yield t.getItem(r);if(!e)return null;try{return JSON.parse(e)}catch(s){return e}}),ws=(t,r)=>e(void 0,null,function*(){yield t.removeItem(r)});class ks{constructor(){this.promise=new ks.promiseConstructor((e,t)=>{this.resolve=e,this.reject=t})}}function Ts(e){const t=e.split(".");if(3!==t.length)throw new os("Invalid JWT structure");for(let r=0;r<t.length;r++)if(!Jr.test(t[r]))throw new os("JWT not in base64url format");return{header:JSON.parse(us(t[0])),payload:JSON.parse(us(t[1])),signature:fs(t[2]),raw:{header:t[0],payload:t[1]}}}function Ss(e){return("0"+e.toString(16)).substr(-2)}function Es(t){return e(this,null,function*(){if(!("undefined"!=typeof crypto&&void 0!==crypto.subtle&&"undefined"!=typeof TextEncoder))return t;const r=yield function(t){return e(this,null,function*(){const e=(new TextEncoder).encode(t),r=yield crypto.subtle.digest("SHA-256",e),s=new Uint8Array(r);return Array.from(s).map(e=>String.fromCharCode(e)).join("")})}(t);return btoa(r).replace(/\+/g,"-").replace(/\//g,"_").replace(/=+$/,"")})}function js(t,r,s=!1){return e(this,null,function*(){const e=function(){const e=new Uint32Array(56);if("undefined"==typeof crypto){const e="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789-._~",t=e.length;let r="";for(let s=0;s<56;s++)r+=e.charAt(Math.floor(Math.random()*t));return r}return crypto.getRandomValues(e),Array.from(e,Ss).join("")}();let i=e;s&&(i+="/PASSWORD_RECOVERY"),yield _s(t,`${r}-code-verifier`,i);const n=yield Es(e);return[n,e===n?"plain":"s256"]})}ks.promiseConstructor=Promise;const Ps=/^2[0-9]{3}-(0[1-9]|1[0-2])-(0[1-9]|1[0-9]|2[0-9]|3[0-1])$/i;var Os=globalThis&&globalThis.__rest||function(e,t){var r={};for(var s in e)Object.prototype.hasOwnProperty.call(e,s)&&t.indexOf(s)<0&&(r[s]=e[s]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols){var i=0;for(s=Object.getOwnPropertySymbols(e);i<s.length;i++)t.indexOf(s[i])<0&&Object.prototype.propertyIsEnumerable.call(e,s[i])&&(r[s[i]]=e[s[i]])}return r};const xs=e=>e.msg||e.message||e.error_description||e.error||JSON.stringify(e),Cs=[502,503,504];function As(t){return e(this,null,function*(){var e,r;if(!("object"==typeof(r=t)&&null!==r&&"status"in r&&"ok"in r&&"json"in r&&"function"==typeof r.json))throw new ss(xs(t),0);if(Cs.includes(t.status))throw new ss(xs(t),t.status);let s,i;try{s=yield t.json()}catch(o){throw new Yr(xs(o),o)}const n=function(e){const t=e.headers.get(qr);if(!t)return null;if(!t.match(Ps))return null;try{return new Date(`${t}T00:00:00.0Z`)}catch(o){return null}}(t);if(n&&n.getTime()>=Fr.timestamp&&"object"==typeof s&&s&&"string"==typeof s.code?i=s.code:"object"==typeof s&&s&&"string"==typeof s.error_code&&(i=s.error_code),i){if("weak_password"===i)throw new ns(xs(s),t.status,(null===(e=s.weak_password)||void 0===e?void 0:e.reasons)||[]);if("session_not_found"===i)throw new Zr}else if("object"==typeof s&&s&&"object"==typeof s.weak_password&&s.weak_password&&Array.isArray(s.weak_password.reasons)&&s.weak_password.reasons.length&&s.weak_password.reasons.reduce((e,t)=>e&&"string"==typeof t,!0))throw new ns(xs(s),t.status,s.weak_password.reasons);throw new Wr(xs(s),t.status||500,i)})}function $s(t,r,s,i){return e(this,null,function*(){var n;const o=Object.assign({},null==i?void 0:i.headers);o[qr]||(o[qr]=Fr.name),(null==i?void 0:i.jwt)&&(o.Authorization=`Bearer ${i.jwt}`);const a=null!==(n=null==i?void 0:i.query)&&void 0!==n?n:{};(null==i?void 0:i.redirectTo)&&(a.redirect_to=i.redirectTo);const l=Object.keys(a).length?"?"+new URLSearchParams(a).toString():"",c=yield function(t,r,s,i,n,o){return e(this,null,function*(){const e=((e,t,r,s)=>{const i={method:e,headers:(null==t?void 0:t.headers)||{}};return"GET"===e?i:(i.headers=Object.assign({"Content-Type":"application/json;charset=UTF-8"},null==t?void 0:t.headers),i.body=JSON.stringify(s),Object.assign(Object.assign({},i),r))})(r,i,n,o);let a;try{a=yield t(s,Object.assign({},e))}catch(l){throw new ss(xs(l),0)}if(a.ok||(yield As(a)),null==i?void 0:i.noResolveJson)return a;try{return yield a.json()}catch(l){yield As(l)}})}(t,r,s+l,{headers:o,noResolveJson:null==i?void 0:i.noResolveJson},{},null==i?void 0:i.body);return(null==i?void 0:i.xform)?null==i?void 0:i.xform(c):{data:Object.assign({},c),error:null}})}function zs(e){var t;let r=null;var s;(function(e){return e.access_token&&e.refresh_token&&e.expires_in})(e)&&(r=Object.assign({},e),e.expires_at||(r.expires_at=(s=e.expires_in,Math.round(Date.now()/1e3)+s)));return{data:{session:r,user:null!==(t=e.user)&&void 0!==t?t:e},error:null}}function Rs(e){const t=zs(e);return!t.error&&e.weak_password&&"object"==typeof e.weak_password&&Array.isArray(e.weak_password.reasons)&&e.weak_password.reasons.length&&e.weak_password.message&&"string"==typeof e.weak_password.message&&e.weak_password.reasons.reduce((e,t)=>e&&"string"==typeof t,!0)&&(t.data.weak_password=e.weak_password),t}function Is(e){var t;return{data:{user:null!==(t=e.user)&&void 0!==t?t:e},error:null}}function Ls(e){return{data:e,error:null}}function Ms(e){const{action_link:t,email_otp:r,hashed_token:s,redirect_to:i,verification_type:n}=e,o=Os(e,["action_link","email_otp","hashed_token","redirect_to","verification_type"]);return{data:{properties:{action_link:t,email_otp:r,hashed_token:s,redirect_to:i,verification_type:n},user:Object.assign({},o)},error:null}}function Us(e){return e}var Bs=globalThis&&globalThis.__rest||function(e,t){var r={};for(var s in e)Object.prototype.hasOwnProperty.call(e,s)&&t.indexOf(s)<0&&(r[s]=e[s]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols){var i=0;for(s=Object.getOwnPropertySymbols(e);i<s.length;i++)t.indexOf(s[i])<0&&Object.prototype.propertyIsEnumerable.call(e,s[i])&&(r[s[i]]=e[s[i]])}return r};class Ds{constructor({url:e="",headers:t={},fetch:r}){this.url=e,this.headers=t,this.fetch=ms(r),this.mfa={listFactors:this._listFactors.bind(this),deleteFactor:this._deleteFactor.bind(this)}}signOut(t,r="global"){return e(this,null,function*(){try{return yield $s(this.fetch,"POST",`${this.url}/logout?scope=${r}`,{headers:this.headers,jwt:t,noResolveJson:!0}),{data:null,error:null}}catch(e){if(Gr(e))return{data:null,error:e};throw e}})}inviteUserByEmail(t){return e(this,arguments,function*(e,t={}){try{return yield $s(this.fetch,"POST",`${this.url}/invite`,{body:{email:e,data:t.data},headers:this.headers,redirectTo:t.redirectTo,xform:Is})}catch(r){if(Gr(r))return{data:{user:null},error:r};throw r}})}generateLink(t){return e(this,null,function*(){try{const{options:e}=t,r=Bs(t,["options"]),s=Object.assign(Object.assign({},r),e);return"newEmail"in r&&(s.new_email=null==r?void 0:r.newEmail,delete s.newEmail),yield $s(this.fetch,"POST",`${this.url}/admin/generate_link`,{body:s,headers:this.headers,xform:Ms,redirectTo:null==e?void 0:e.redirectTo})}catch(e){if(Gr(e))return{data:{properties:null,user:null},error:e};throw e}})}createUser(t){return e(this,null,function*(){try{return yield $s(this.fetch,"POST",`${this.url}/admin/users`,{body:t,headers:this.headers,xform:Is})}catch(e){if(Gr(e))return{data:{user:null},error:e};throw e}})}listUsers(t){return e(this,null,function*(){var e,r,s,i,n,o,a;try{const l={nextPage:null,lastPage:0,total:0},c=yield $s(this.fetch,"GET",`${this.url}/admin/users`,{headers:this.headers,noResolveJson:!0,query:{page:null!==(r=null===(e=null==t?void 0:t.page)||void 0===e?void 0:e.toString())&&void 0!==r?r:"",per_page:null!==(i=null===(s=null==t?void 0:t.perPage)||void 0===s?void 0:s.toString())&&void 0!==i?i:""},xform:Us});if(c.error)throw c.error;const h=yield c.json(),u=null!==(n=c.headers.get("x-total-count"))&&void 0!==n?n:0,d=null!==(a=null===(o=c.headers.get("link"))||void 0===o?void 0:o.split(","))&&void 0!==a?a:[];return d.length>0&&(d.forEach(e=>{const t=parseInt(e.split(";")[0].split("=")[1].substring(0,1)),r=JSON.parse(e.split(";")[1].split("=")[1]);l[`${r}Page`]=t}),l.total=parseInt(u)),{data:Object.assign(Object.assign({},h),l),error:null}}catch(l){if(Gr(l))return{data:{users:[]},error:l};throw l}})}getUserById(t){return e(this,null,function*(){try{return yield $s(this.fetch,"GET",`${this.url}/admin/users/${t}`,{headers:this.headers,xform:Is})}catch(e){if(Gr(e))return{data:{user:null},error:e};throw e}})}updateUserById(t,r){return e(this,null,function*(){try{return yield $s(this.fetch,"PUT",`${this.url}/admin/users/${t}`,{body:r,headers:this.headers,xform:Is})}catch(e){if(Gr(e))return{data:{user:null},error:e};throw e}})}deleteUser(t,r=!1){return e(this,null,function*(){try{return yield $s(this.fetch,"DELETE",`${this.url}/admin/users/${t}`,{headers:this.headers,body:{should_soft_delete:r},xform:Is})}catch(e){if(Gr(e))return{data:{user:null},error:e};throw e}})}_listFactors(t){return e(this,null,function*(){try{const{data:e,error:r}=yield $s(this.fetch,"GET",`${this.url}/admin/users/${t.userId}/factors`,{headers:this.headers,xform:e=>({data:{factors:e},error:null})});return{data:e,error:r}}catch(e){if(Gr(e))return{data:null,error:e};throw e}})}_deleteFactor(t){return e(this,null,function*(){try{return{data:yield $s(this.fetch,"DELETE",`${this.url}/admin/users/${t.userId}/factors/${t.id}`,{headers:this.headers}),error:null}}catch(e){if(Gr(e))return{data:null,error:e};throw e}})}}const Hs={getItem:e=>ys()?globalThis.localStorage.getItem(e):null,setItem:(e,t)=>{ys()&&globalThis.localStorage.setItem(e,t)},removeItem:e=>{ys()&&globalThis.localStorage.removeItem(e)}};function Ns(e={}){return{getItem:t=>e[t]||null,setItem:(t,r)=>{e[t]=r},removeItem:t=>{delete e[t]}}}const Vs=!!(globalThis&&ys()&&globalThis.localStorage&&"true"===globalThis.localStorage.getItem("supabase.gotrue-js.locks.debug"));class qs extends Error{constructor(e){super(e),this.isAcquireTimeout=!0}}class Fs extends qs{}function Js(t,r,s){return e(this,null,function*(){const i=new globalThis.AbortController;return r>0&&setTimeout(()=>{i.abort()},r),yield Promise.resolve().then(()=>globalThis.navigator.locks.request(t,0===r?{mode:"exclusive",ifAvailable:!0}:{mode:"exclusive",signal:i.signal},i=>e(this,null,function*(){if(!i){if(0===r)throw new Fs(`Acquiring an exclusive Navigator LockManager lock "${t}" immediately failed`);if(Vs)try{yield globalThis.navigator.locks.query()}catch(e){}return yield s()}try{return yield s()}finally{}})))})}!function(){if("object"!=typeof globalThis)try{Object.defineProperty(Object.prototype,"__magic__",{get:function(){return this},configurable:!0}),__magic__.globalThis=__magic__,delete Object.prototype.__magic__}catch(e){"undefined"!=typeof self&&(self.globalThis=self)}}();const Ks={url:"http://localhost:9999",storageKey:"supabase.auth.token",autoRefreshToken:!0,persistSession:!0,detectSessionInUrl:!0,headers:Vr,flowType:"implicit",debug:!1,hasCustomAuthorizationHeader:!1};function Gs(t,r,s){return e(this,null,function*(){return yield s()})}class Ws{constructor(t){var r,s;this.memoryStorage=null,this.stateChangeEmitters=new Map,this.autoRefreshTicker=null,this.visibilityChangedCallback=null,this.refreshingDeferred=null,this.initializePromise=null,this.detectSessionInUrl=!0,this.hasCustomAuthorizationHeader=!1,this.suppressGetSessionWarning=!1,this.lockAcquired=!1,this.pendingInLock=[],this.broadcastChannel=null,this.logger=console.log,this.instanceID=Ws.nextInstanceID,Ws.nextInstanceID+=1,this.instanceID>0&&gs();const i=Object.assign(Object.assign({},Ks),t);if(this.logDebugMessages=!!i.debug,"function"==typeof i.debug&&(this.logger=i.debug),this.persistSession=i.persistSession,this.storageKey=i.storageKey,this.autoRefreshToken=i.autoRefreshToken,this.admin=new Ds({url:i.url,headers:i.headers,fetch:i.fetch}),this.url=i.url,this.headers=i.headers,this.fetch=ms(i.fetch),this.lock=i.lock||Gs,this.detectSessionInUrl=i.detectSessionInUrl,this.flowType=i.flowType,this.hasCustomAuthorizationHeader=i.hasCustomAuthorizationHeader,i.lock?this.lock=i.lock:gs()&&(null===(r=null===globalThis||void 0===globalThis?void 0:globalThis.navigator)||void 0===r?void 0:r.locks)?this.lock=Js:this.lock=Gs,this.jwks={keys:[]},this.jwks_cached_at=Number.MIN_SAFE_INTEGER,this.mfa={verify:this._verify.bind(this),enroll:this._enroll.bind(this),unenroll:this._unenroll.bind(this),challenge:this._challenge.bind(this),listFactors:this._listFactors.bind(this),challengeAndVerify:this._challengeAndVerify.bind(this),getAuthenticatorAssuranceLevel:this._getAuthenticatorAssuranceLevel.bind(this)},this.persistSession?i.storage?this.storage=i.storage:ys()?this.storage=Hs:(this.memoryStorage={},this.storage=Ns(this.memoryStorage)):(this.memoryStorage={},this.storage=Ns(this.memoryStorage)),gs()&&globalThis.BroadcastChannel&&this.persistSession&&this.storageKey){try{this.broadcastChannel=new globalThis.BroadcastChannel(this.storageKey)}catch(n){}null===(s=this.broadcastChannel)||void 0===s||s.addEventListener("message",t=>e(this,null,function*(){this._debug("received broadcast notification from other tab or client",t),yield this._notifyAllSubscribers(t.data.event,t.data.session,!1)}))}this.initialize()}_debug(...e){return this.logDebugMessages&&this.logger(`GoTrueClient@${this.instanceID} (${Dr}) ${(new Date).toISOString()}`,...e),this}initialize(){return e(this,null,function*(){return this.initializePromise||(this.initializePromise=(()=>e(this,null,function*(){return yield this._acquireLock(-1,()=>e(this,null,function*(){return yield this._initialize()}))}))()),yield this.initializePromise})}_initialize(){return e(this,null,function*(){var t;try{const r=function(e){const t={},r=new URL(e);if(r.hash&&"#"===r.hash[0])try{new URLSearchParams(r.hash.substring(1)).forEach((e,r)=>{t[r]=e})}catch(s){}return r.searchParams.forEach((e,r)=>{t[r]=e}),t}(window.location.href);let s="none";if(this._isImplicitGrantCallback(r)?s="implicit":(yield this._isPKCECallback(r))&&(s="pkce"),gs()&&this.detectSessionInUrl&&"none"!==s){const{data:i,error:n}=yield this._getSessionFromURL(r,s);if(n){if(this._debug("#_initialize()","error detecting session from URL",n),function(e){return Gr(e)&&"AuthImplicitGrantRedirectError"===e.name}(n)){const e=null===(t=n.details)||void 0===t?void 0:t.code;if("identity_already_exists"===e||"identity_not_found"===e||"single_identity_not_deletable"===e)return{error:n}}return yield this._removeSession(),{error:n}}const{session:o,redirectType:a}=i;return this._debug("#_initialize()","detected session in URL",o,"redirect type",a),yield this._saveSession(o),setTimeout(()=>e(this,null,function*(){"recovery"===a?yield this._notifyAllSubscribers("PASSWORD_RECOVERY",o):yield this._notifyAllSubscribers("SIGNED_IN",o)}),0),{error:null}}return yield this._recoverAndRefresh(),{error:null}}catch(r){return Gr(r)?{error:r}:{error:new Yr("Unexpected error during initialization",r)}}finally{yield this._handleVisibilityChange(),this._debug("#_initialize()","end")}})}signInAnonymously(t){return e(this,null,function*(){var e,r,s;try{const i=yield $s(this.fetch,"POST",`${this.url}/signup`,{headers:this.headers,body:{data:null!==(r=null===(e=null==t?void 0:t.options)||void 0===e?void 0:e.data)&&void 0!==r?r:{},gotrue_meta_security:{captcha_token:null===(s=null==t?void 0:t.options)||void 0===s?void 0:s.captchaToken}},xform:zs}),{data:n,error:o}=i;if(o||!n)return{data:{user:null,session:null},error:o};const a=n.session,l=n.user;return n.session&&(yield this._saveSession(n.session),yield this._notifyAllSubscribers("SIGNED_IN",a)),{data:{user:l,session:a},error:null}}catch(i){if(Gr(i))return{data:{user:null,session:null},error:i};throw i}})}signUp(t){return e(this,null,function*(){var e,r,s;try{let i;if("email"in t){const{email:r,password:s,options:n}=t;let o=null,a=null;"pkce"===this.flowType&&([o,a]=yield js(this.storage,this.storageKey)),i=yield $s(this.fetch,"POST",`${this.url}/signup`,{headers:this.headers,redirectTo:null==n?void 0:n.emailRedirectTo,body:{email:r,password:s,data:null!==(e=null==n?void 0:n.data)&&void 0!==e?e:{},gotrue_meta_security:{captcha_token:null==n?void 0:n.captchaToken},code_challenge:o,code_challenge_method:a},xform:zs})}else{if(!("phone"in t))throw new es("You must provide either an email or phone number and a password");{const{phone:e,password:n,options:o}=t;i=yield $s(this.fetch,"POST",`${this.url}/signup`,{headers:this.headers,body:{phone:e,password:n,data:null!==(r=null==o?void 0:o.data)&&void 0!==r?r:{},channel:null!==(s=null==o?void 0:o.channel)&&void 0!==s?s:"sms",gotrue_meta_security:{captcha_token:null==o?void 0:o.captchaToken}},xform:zs})}}const{data:n,error:o}=i;if(o||!n)return{data:{user:null,session:null},error:o};const a=n.session,l=n.user;return n.session&&(yield this._saveSession(n.session),yield this._notifyAllSubscribers("SIGNED_IN",a)),{data:{user:l,session:a},error:null}}catch(i){if(Gr(i))return{data:{user:null,session:null},error:i};throw i}})}signInWithPassword(t){return e(this,null,function*(){try{let e;if("email"in t){const{email:r,password:s,options:i}=t;e=yield $s(this.fetch,"POST",`${this.url}/token?grant_type=password`,{headers:this.headers,body:{email:r,password:s,gotrue_meta_security:{captcha_token:null==i?void 0:i.captchaToken}},xform:Rs})}else{if(!("phone"in t))throw new es("You must provide either an email or phone number and a password");{const{phone:r,password:s,options:i}=t;e=yield $s(this.fetch,"POST",`${this.url}/token?grant_type=password`,{headers:this.headers,body:{phone:r,password:s,gotrue_meta_security:{captcha_token:null==i?void 0:i.captchaToken}},xform:Rs})}}const{data:r,error:s}=e;return s?{data:{user:null,session:null},error:s}:r&&r.session&&r.user?(r.session&&(yield this._saveSession(r.session),yield this._notifyAllSubscribers("SIGNED_IN",r.session)),{data:Object.assign({user:r.user,session:r.session},r.weak_password?{weakPassword:r.weak_password}:null),error:s}):{data:{user:null,session:null},error:new Qr}}catch(e){if(Gr(e))return{data:{user:null,session:null},error:e};throw e}})}signInWithOAuth(t){return e(this,null,function*(){var e,r,s,i;return yield this._handleProviderSignIn(t.provider,{redirectTo:null===(e=t.options)||void 0===e?void 0:e.redirectTo,scopes:null===(r=t.options)||void 0===r?void 0:r.scopes,queryParams:null===(s=t.options)||void 0===s?void 0:s.queryParams,skipBrowserRedirect:null===(i=t.options)||void 0===i?void 0:i.skipBrowserRedirect})})}exchangeCodeForSession(t){return e(this,null,function*(){return yield this.initializePromise,this._acquireLock(-1,()=>e(this,null,function*(){return this._exchangeCodeForSession(t)}))})}_exchangeCodeForSession(t){return e(this,null,function*(){const e=yield bs(this.storage,`${this.storageKey}-code-verifier`),[r,s]=(null!=e?e:"").split("/");try{const{data:e,error:i}=yield $s(this.fetch,"POST",`${this.url}/token?grant_type=pkce`,{headers:this.headers,body:{auth_code:t,code_verifier:r},xform:zs});if(yield ws(this.storage,`${this.storageKey}-code-verifier`),i)throw i;return e&&e.session&&e.user?(e.session&&(yield this._saveSession(e.session),yield this._notifyAllSubscribers("SIGNED_IN",e.session)),{data:Object.assign(Object.assign({},e),{redirectType:null!=s?s:null}),error:i}):{data:{user:null,session:null,redirectType:null},error:new Qr}}catch(i){if(Gr(i))return{data:{user:null,session:null,redirectType:null},error:i};throw i}})}signInWithIdToken(t){return e(this,null,function*(){try{const{options:e,provider:r,token:s,access_token:i,nonce:n}=t,o=yield $s(this.fetch,"POST",`${this.url}/token?grant_type=id_token`,{headers:this.headers,body:{provider:r,id_token:s,access_token:i,nonce:n,gotrue_meta_security:{captcha_token:null==e?void 0:e.captchaToken}},xform:zs}),{data:a,error:l}=o;return l?{data:{user:null,session:null},error:l}:a&&a.session&&a.user?(a.session&&(yield this._saveSession(a.session),yield this._notifyAllSubscribers("SIGNED_IN",a.session)),{data:a,error:l}):{data:{user:null,session:null},error:new Qr}}catch(e){if(Gr(e))return{data:{user:null,session:null},error:e};throw e}})}signInWithOtp(t){return e(this,null,function*(){var e,r,s,i,n;try{if("email"in t){const{email:s,options:i}=t;let n=null,o=null;"pkce"===this.flowType&&([n,o]=yield js(this.storage,this.storageKey));const{error:a}=yield $s(this.fetch,"POST",`${this.url}/otp`,{headers:this.headers,body:{email:s,data:null!==(e=null==i?void 0:i.data)&&void 0!==e?e:{},create_user:null===(r=null==i?void 0:i.shouldCreateUser)||void 0===r||r,gotrue_meta_security:{captcha_token:null==i?void 0:i.captchaToken},code_challenge:n,code_challenge_method:o},redirectTo:null==i?void 0:i.emailRedirectTo});return{data:{user:null,session:null},error:a}}if("phone"in t){const{phone:e,options:r}=t,{data:o,error:a}=yield $s(this.fetch,"POST",`${this.url}/otp`,{headers:this.headers,body:{phone:e,data:null!==(s=null==r?void 0:r.data)&&void 0!==s?s:{},create_user:null===(i=null==r?void 0:r.shouldCreateUser)||void 0===i||i,gotrue_meta_security:{captcha_token:null==r?void 0:r.captchaToken},channel:null!==(n=null==r?void 0:r.channel)&&void 0!==n?n:"sms"}});return{data:{user:null,session:null,messageId:null==o?void 0:o.message_id},error:a}}throw new es("You must provide either an email or phone number.")}catch(o){if(Gr(o))return{data:{user:null,session:null},error:o};throw o}})}verifyOtp(t){return e(this,null,function*(){var e,r;try{let s,i;"options"in t&&(s=null===(e=t.options)||void 0===e?void 0:e.redirectTo,i=null===(r=t.options)||void 0===r?void 0:r.captchaToken);const{data:n,error:o}=yield $s(this.fetch,"POST",`${this.url}/verify`,{headers:this.headers,body:Object.assign(Object.assign({},t),{gotrue_meta_security:{captcha_token:i}}),redirectTo:s,xform:zs});if(o)throw o;if(!n)throw new Error("An error occurred on token verification.");const a=n.session,l=n.user;return(null==a?void 0:a.access_token)&&(yield this._saveSession(a),yield this._notifyAllSubscribers("recovery"==t.type?"PASSWORD_RECOVERY":"SIGNED_IN",a)),{data:{user:l,session:a},error:null}}catch(s){if(Gr(s))return{data:{user:null,session:null},error:s};throw s}})}signInWithSSO(t){return e(this,null,function*(){var e,r,s;try{let i=null,n=null;return"pkce"===this.flowType&&([i,n]=yield js(this.storage,this.storageKey)),yield $s(this.fetch,"POST",`${this.url}/sso`,{body:Object.assign(Object.assign(Object.assign(Object.assign(Object.assign({},"providerId"in t?{provider_id:t.providerId}:null),"domain"in t?{domain:t.domain}:null),{redirect_to:null!==(r=null===(e=t.options)||void 0===e?void 0:e.redirectTo)&&void 0!==r?r:void 0}),(null===(s=null==t?void 0:t.options)||void 0===s?void 0:s.captchaToken)?{gotrue_meta_security:{captcha_token:t.options.captchaToken}}:null),{skip_http_redirect:!0,code_challenge:i,code_challenge_method:n}),headers:this.headers,xform:Ls})}catch(i){if(Gr(i))return{data:null,error:i};throw i}})}reauthenticate(){return e(this,null,function*(){return yield this.initializePromise,yield this._acquireLock(-1,()=>e(this,null,function*(){return yield this._reauthenticate()}))})}_reauthenticate(){return e(this,null,function*(){try{return yield this._useSession(t=>e(this,null,function*(){const{data:{session:e},error:r}=t;if(r)throw r;if(!e)throw new Zr;const{error:s}=yield $s(this.fetch,"GET",`${this.url}/reauthenticate`,{headers:this.headers,jwt:e.access_token});return{data:{user:null,session:null},error:s}}))}catch(t){if(Gr(t))return{data:{user:null,session:null},error:t};throw t}})}resend(t){return e(this,null,function*(){try{const e=`${this.url}/resend`;if("email"in t){const{email:r,type:s,options:i}=t,{error:n}=yield $s(this.fetch,"POST",e,{headers:this.headers,body:{email:r,type:s,gotrue_meta_security:{captcha_token:null==i?void 0:i.captchaToken}},redirectTo:null==i?void 0:i.emailRedirectTo});return{data:{user:null,session:null},error:n}}if("phone"in t){const{phone:r,type:s,options:i}=t,{data:n,error:o}=yield $s(this.fetch,"POST",e,{headers:this.headers,body:{phone:r,type:s,gotrue_meta_security:{captcha_token:null==i?void 0:i.captchaToken}}});return{data:{user:null,session:null,messageId:null==n?void 0:n.message_id},error:o}}throw new es("You must provide either an email or phone number and a type")}catch(e){if(Gr(e))return{data:{user:null,session:null},error:e};throw e}})}getSession(){return e(this,null,function*(){yield this.initializePromise;return yield this._acquireLock(-1,()=>e(this,null,function*(){return this._useSession(t=>e(this,null,function*(){return t}))}))})}_acquireLock(t,r){return e(this,null,function*(){this._debug("#_acquireLock","begin",t);try{if(this.lockAcquired){const t=this.pendingInLock.length?this.pendingInLock[this.pendingInLock.length-1]:Promise.resolve(),s=(()=>e(this,null,function*(){return yield t,yield r()}))();return this.pendingInLock.push((()=>e(this,null,function*(){try{yield s}catch(e){}}))()),s}return yield this.lock(`lock:${this.storageKey}`,t,()=>e(this,null,function*(){this._debug("#_acquireLock","lock acquired for storage key",this.storageKey);try{this.lockAcquired=!0;const t=r();for(this.pendingInLock.push((()=>e(this,null,function*(){try{yield t}catch(e){}}))()),yield t;this.pendingInLock.length;){const e=[...this.pendingInLock];yield Promise.all(e),this.pendingInLock.splice(0,e.length)}return yield t}finally{this._debug("#_acquireLock","lock released for storage key",this.storageKey),this.lockAcquired=!1}}))}finally{this._debug("#_acquireLock","end")}})}_useSession(t){return e(this,null,function*(){this._debug("#_useSession","begin");try{const e=yield this.__loadSession();return yield t(e)}finally{this._debug("#_useSession","end")}})}__loadSession(){return e(this,null,function*(){this._debug("#__loadSession()","begin"),this.lockAcquired||this._debug("#__loadSession()","used outside of an acquired lock!",(new Error).stack);try{let e=null;const t=yield bs(this.storage,this.storageKey);if(this._debug("#getSession()","session from storage",t),null!==t&&(this._isValidSession(t)?e=t:(this._debug("#getSession()","session from storage is not valid"),yield this._removeSession())),!e)return{data:{session:null},error:null};const r=!!e.expires_at&&1e3*e.expires_at-Date.now()<Nr;if(this._debug("#__loadSession()",`session has${r?"":" not"} expired`,"expires_at",e.expires_at),!r){if(this.storage.isServer){let t=this.suppressGetSessionWarning;e=new Proxy(e,{get:(e,r,s)=>(t||"user"!==r||(t=!0,this.suppressGetSessionWarning=!0),Reflect.get(e,r,s))})}return{data:{session:e},error:null}}const{session:s,error:i}=yield this._callRefreshToken(e.refresh_token);return i?{data:{session:null},error:i}:{data:{session:s},error:null}}finally{this._debug("#__loadSession()","end")}})}getUser(t){return e(this,null,function*(){if(t)return yield this._getUser(t);yield this.initializePromise;return yield this._acquireLock(-1,()=>e(this,null,function*(){return yield this._getUser()}))})}_getUser(t){return e(this,null,function*(){try{return t?yield $s(this.fetch,"GET",`${this.url}/user`,{headers:this.headers,jwt:t,xform:Is}):yield this._useSession(t=>e(this,null,function*(){var e,r,s;const{data:i,error:n}=t;if(n)throw n;return(null===(e=i.session)||void 0===e?void 0:e.access_token)||this.hasCustomAuthorizationHeader?yield $s(this.fetch,"GET",`${this.url}/user`,{headers:this.headers,jwt:null!==(s=null===(r=i.session)||void 0===r?void 0:r.access_token)&&void 0!==s?s:void 0,xform:Is}):{data:{user:null},error:new Zr}}))}catch(r){if(Gr(r))return function(e){return Gr(e)&&"AuthSessionMissingError"===e.name}(r)&&(yield this._removeSession(),yield ws(this.storage,`${this.storageKey}-code-verifier`)),{data:{user:null},error:r};throw r}})}updateUser(t){return e(this,arguments,function*(t,r={}){return yield this.initializePromise,yield this._acquireLock(-1,()=>e(this,null,function*(){return yield this._updateUser(t,r)}))})}_updateUser(t){return e(this,arguments,function*(t,r={}){try{return yield this._useSession(s=>e(this,null,function*(){const{data:e,error:i}=s;if(i)throw i;if(!e.session)throw new Zr;const n=e.session;let o=null,a=null;"pkce"===this.flowType&&null!=t.email&&([o,a]=yield js(this.storage,this.storageKey));const{data:l,error:c}=yield $s(this.fetch,"PUT",`${this.url}/user`,{headers:this.headers,redirectTo:null==r?void 0:r.emailRedirectTo,body:Object.assign(Object.assign({},t),{code_challenge:o,code_challenge_method:a}),jwt:n.access_token,xform:Is});if(c)throw c;return n.user=l.user,yield this._saveSession(n),yield this._notifyAllSubscribers("USER_UPDATED",n),{data:{user:n.user},error:null}}))}catch(s){if(Gr(s))return{data:{user:null},error:s};throw s}})}setSession(t){return e(this,null,function*(){return yield this.initializePromise,yield this._acquireLock(-1,()=>e(this,null,function*(){return yield this._setSession(t)}))})}_setSession(t){return e(this,null,function*(){try{if(!t.access_token||!t.refresh_token)throw new Zr;const e=Date.now()/1e3;let r=e,s=!0,i=null;const{payload:n}=Ts(t.access_token);if(n.exp&&(r=n.exp,s=r<=e),s){const{session:e,error:r}=yield this._callRefreshToken(t.refresh_token);if(r)return{data:{user:null,session:null},error:r};if(!e)return{data:{user:null,session:null},error:null};i=e}else{const{data:s,error:n}=yield this._getUser(t.access_token);if(n)throw n;i={access_token:t.access_token,refresh_token:t.refresh_token,user:s.user,token_type:"bearer",expires_in:r-e,expires_at:r},yield this._saveSession(i),yield this._notifyAllSubscribers("SIGNED_IN",i)}return{data:{user:i.user,session:i},error:null}}catch(e){if(Gr(e))return{data:{session:null,user:null},error:e};throw e}})}refreshSession(t){return e(this,null,function*(){return yield this.initializePromise,yield this._acquireLock(-1,()=>e(this,null,function*(){return yield this._refreshSession(t)}))})}_refreshSession(t){return e(this,null,function*(){try{return yield this._useSession(r=>e(this,null,function*(){var e;if(!t){const{data:s,error:i}=r;if(i)throw i;t=null!==(e=s.session)&&void 0!==e?e:void 0}if(!(null==t?void 0:t.refresh_token))throw new Zr;const{session:s,error:i}=yield this._callRefreshToken(t.refresh_token);return i?{data:{user:null,session:null},error:i}:s?{data:{user:s.user,session:s},error:null}:{data:{user:null,session:null},error:null}}))}catch(r){if(Gr(r))return{data:{user:null,session:null},error:r};throw r}})}_getSessionFromURL(t,r){return e(this,null,function*(){try{if(!gs())throw new ts("No browser detected.");if(t.error||t.error_description||t.error_code)throw new ts(t.error_description||"Error in URL with unspecified error_description",{error:t.error||"unspecified_error",code:t.error_code||"unspecified_code"});switch(r){case"implicit":if("pkce"===this.flowType)throw new rs("Not a valid PKCE flow url.");break;case"pkce":if("implicit"===this.flowType)throw new ts("Not a valid implicit grant flow url.")}if("pkce"===r){if(this._debug("#_initialize()","begin","is PKCE flow",!0),!t.code)throw new rs("No code detected.");const{data:e,error:r}=yield this._exchangeCodeForSession(t.code);if(r)throw r;const s=new URL(window.location.href);return s.searchParams.delete("code"),window.history.replaceState(window.history.state,"",s.toString()),{data:{session:e.session,redirectType:null},error:null}}const{provider_token:e,provider_refresh_token:s,access_token:i,refresh_token:n,expires_in:o,expires_at:a,token_type:l}=t;if(!(i&&o&&n&&l))throw new ts("No session defined in URL");const c=Math.round(Date.now()/1e3),h=parseInt(o);let u=c+h;a&&(u=parseInt(a));const{data:d,error:f}=yield this._getUser(i);if(f)throw f;const p={provider_token:e,provider_refresh_token:s,access_token:i,expires_in:h,expires_at:u,refresh_token:n,token_type:l,user:d.user};return window.location.hash="",this._debug("#_getSessionFromURL()","clearing window.location.hash"),{data:{session:p,redirectType:t.type},error:null}}catch(e){if(Gr(e))return{data:{session:null,redirectType:null},error:e};throw e}})}_isImplicitGrantCallback(e){return Boolean(e.access_token||e.error_description)}_isPKCECallback(t){return e(this,null,function*(){const e=yield bs(this.storage,`${this.storageKey}-code-verifier`);return!(!t.code||!e)})}signOut(){return e(this,arguments,function*(t={scope:"global"}){return yield this.initializePromise,yield this._acquireLock(-1,()=>e(this,null,function*(){return yield this._signOut(t)}))})}_signOut(){return e(this,arguments,function*({scope:t}={scope:"global"}){return yield this._useSession(r=>e(this,null,function*(){var e;const{data:s,error:i}=r;if(i)return{error:i};const n=null===(e=s.session)||void 0===e?void 0:e.access_token;if(n){const{error:e}=yield this.admin.signOut(n,t);if(e&&(!function(e){return Gr(e)&&"AuthApiError"===e.name}(e)||404!==e.status&&401!==e.status&&403!==e.status))return{error:e}}return"others"!==t&&(yield this._removeSession(),yield ws(this.storage,`${this.storageKey}-code-verifier`)),{error:null}}))})}onAuthStateChange(t){const r="xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx".replace(/[xy]/g,function(e){const t=16*Math.random()|0;return("x"==e?t:3&t|8).toString(16)}),s={id:r,callback:t,unsubscribe:()=>{this._debug("#unsubscribe()","state change callback with id removed",r),this.stateChangeEmitters.delete(r)}};return this._debug("#onAuthStateChange()","registered callback with id",r),this.stateChangeEmitters.set(r,s),(()=>{e(this,null,function*(){yield this.initializePromise,yield this._acquireLock(-1,()=>e(this,null,function*(){this._emitInitialSession(r)}))})})(),{data:{subscription:s}}}_emitInitialSession(t){return e(this,null,function*(){return yield this._useSession(r=>e(this,null,function*(){var e,s;try{const{data:{session:s},error:i}=r;if(i)throw i;yield null===(e=this.stateChangeEmitters.get(t))||void 0===e?void 0:e.callback("INITIAL_SESSION",s),this._debug("INITIAL_SESSION","callback id",t,"session",s)}catch(i){yield null===(s=this.stateChangeEmitters.get(t))||void 0===s?void 0:s.callback("INITIAL_SESSION",null),this._debug("INITIAL_SESSION","callback id",t,"error",i)}}))})}resetPasswordForEmail(t){return e(this,arguments,function*(e,t={}){let r=null,s=null;"pkce"===this.flowType&&([r,s]=yield js(this.storage,this.storageKey,!0));try{return yield $s(this.fetch,"POST",`${this.url}/recover`,{body:{email:e,code_challenge:r,code_challenge_method:s,gotrue_meta_security:{captcha_token:t.captchaToken}},headers:this.headers,redirectTo:t.redirectTo})}catch(i){if(Gr(i))return{data:null,error:i};throw i}})}getUserIdentities(){return e(this,null,function*(){var e;try{const{data:t,error:r}=yield this.getUser();if(r)throw r;return{data:{identities:null!==(e=t.user.identities)&&void 0!==e?e:[]},error:null}}catch(t){if(Gr(t))return{data:null,error:t};throw t}})}linkIdentity(t){return e(this,null,function*(){var r;try{const{data:s,error:i}=yield this._useSession(r=>e(this,null,function*(){var e,s,i,n,o;const{data:a,error:l}=r;if(l)throw l;const c=yield this._getUrlForProvider(`${this.url}/user/identities/authorize`,t.provider,{redirectTo:null===(e=t.options)||void 0===e?void 0:e.redirectTo,scopes:null===(s=t.options)||void 0===s?void 0:s.scopes,queryParams:null===(i=t.options)||void 0===i?void 0:i.queryParams,skipBrowserRedirect:!0});return yield $s(this.fetch,"GET",c,{headers:this.headers,jwt:null!==(o=null===(n=a.session)||void 0===n?void 0:n.access_token)&&void 0!==o?o:void 0})}));if(i)throw i;return gs()&&!(null===(r=t.options)||void 0===r?void 0:r.skipBrowserRedirect)&&window.location.assign(null==s?void 0:s.url),{data:{provider:t.provider,url:null==s?void 0:s.url},error:null}}catch(s){if(Gr(s))return{data:{provider:t.provider,url:null},error:s};throw s}})}unlinkIdentity(t){return e(this,null,function*(){try{return yield this._useSession(r=>e(this,null,function*(){var e,s;const{data:i,error:n}=r;if(n)throw n;return yield $s(this.fetch,"DELETE",`${this.url}/user/identities/${t.identity_id}`,{headers:this.headers,jwt:null!==(s=null===(e=i.session)||void 0===e?void 0:e.access_token)&&void 0!==s?s:void 0})}))}catch(r){if(Gr(r))return{data:null,error:r};throw r}})}_refreshAccessToken(t){return e(this,null,function*(){const r=`#_refreshAccessToken(${t.substring(0,5)}...)`;this._debug(r,"begin");try{const s=Date.now();return yield function(t,r){return new Promise((s,i)=>{(()=>{e(this,null,function*(){for(let n=0;n<1/0;n++)try{const e=yield t(n);if(!r(n,null,e))return void s(e)}catch(e){if(!r(n,e))return void i(e)}})})()})}(s=>e(this,null,function*(){return s>0&&(yield function(t){return e(this,null,function*(){return yield new Promise(e=>{setTimeout(()=>e(null),t)})})}(200*Math.pow(2,s-1))),this._debug(r,"refreshing attempt",s),yield $s(this.fetch,"POST",`${this.url}/token?grant_type=refresh_token`,{body:{refresh_token:t},headers:this.headers,xform:zs})}),(e,t)=>{const r=200*Math.pow(2,e);return t&&is(t)&&Date.now()+r-s<Hr})}catch(s){if(this._debug(r,"error",s),Gr(s))return{data:{session:null,user:null},error:s};throw s}finally{this._debug(r,"end")}})}_isValidSession(e){return"object"==typeof e&&null!==e&&"access_token"in e&&"refresh_token"in e&&"expires_at"in e}_handleProviderSignIn(t,r){return e(this,null,function*(){const e=yield this._getUrlForProvider(`${this.url}/authorize`,t,{redirectTo:r.redirectTo,scopes:r.scopes,queryParams:r.queryParams});return this._debug("#_handleProviderSignIn()","provider",t,"options",r,"url",e),gs()&&!r.skipBrowserRedirect&&window.location.assign(e),{data:{provider:t,url:e},error:null}})}_recoverAndRefresh(){return e(this,null,function*(){var e;const t="#_recoverAndRefresh()";this._debug(t,"begin");try{const r=yield bs(this.storage,this.storageKey);if(this._debug(t,"session from storage",r),!this._isValidSession(r))return this._debug(t,"session is not valid"),void(null!==r&&(yield this._removeSession()));const s=1e3*(null!==(e=r.expires_at)&&void 0!==e?e:1/0)-Date.now()<Nr;if(this._debug(t,`session has${s?"":" not"} expired with margin of 90000s`),s){if(this.autoRefreshToken&&r.refresh_token){const{error:e}=yield this._callRefreshToken(r.refresh_token);e&&(is(e)||(this._debug(t,"refresh failed with a non-retryable error, removing the session",e),yield this._removeSession()))}}else yield this._notifyAllSubscribers("SIGNED_IN",r)}catch(r){return void this._debug(t,"error",r)}finally{this._debug(t,"end")}})}_callRefreshToken(t){return e(this,null,function*(){var e,r;if(!t)throw new Zr;if(this.refreshingDeferred)return this.refreshingDeferred.promise;const s=`#_callRefreshToken(${t.substring(0,5)}...)`;this._debug(s,"begin");try{this.refreshingDeferred=new ks;const{data:e,error:r}=yield this._refreshAccessToken(t);if(r)throw r;if(!e.session)throw new Zr;yield this._saveSession(e.session),yield this._notifyAllSubscribers("TOKEN_REFRESHED",e.session);const s={session:e.session,error:null};return this.refreshingDeferred.resolve(s),s}catch(i){if(this._debug(s,"error",i),Gr(i)){const t={session:null,error:i};return is(i)||(yield this._removeSession()),null===(e=this.refreshingDeferred)||void 0===e||e.resolve(t),t}throw null===(r=this.refreshingDeferred)||void 0===r||r.reject(i),i}finally{this.refreshingDeferred=null,this._debug(s,"end")}})}_notifyAllSubscribers(t,r,s=!0){return e(this,null,function*(){const i=`#_notifyAllSubscribers(${t})`;this._debug(i,"begin",r,`broadcast = ${s}`);try{this.broadcastChannel&&s&&this.broadcastChannel.postMessage({event:t,session:r});const i=[],n=Array.from(this.stateChangeEmitters.values()).map(s=>e(this,null,function*(){try{yield s.callback(t,r)}catch(e){i.push(e)}}));if(yield Promise.all(n),i.length>0){for(let e=0;e<i.length;e+=1);throw i[0]}}finally{this._debug(i,"end")}})}_saveSession(t){return e(this,null,function*(){this._debug("#_saveSession()",t),this.suppressGetSessionWarning=!0,yield _s(this.storage,this.storageKey,t)})}_removeSession(){return e(this,null,function*(){this._debug("#_removeSession()"),yield ws(this.storage,this.storageKey),yield this._notifyAllSubscribers("SIGNED_OUT",null)})}_removeVisibilityChangedCallback(){this._debug("#_removeVisibilityChangedCallback()");const e=this.visibilityChangedCallback;this.visibilityChangedCallback=null;try{e&&gs()&&(null===window||void 0===window?void 0:window.removeEventListener)&&window.removeEventListener("visibilitychange",e)}catch(t){}}_startAutoRefresh(){return e(this,null,function*(){yield this._stopAutoRefresh(),this._debug("#_startAutoRefresh()");const t=setInterval(()=>this._autoRefreshTokenTick(),Hr);this.autoRefreshTicker=t,t&&"object"==typeof t&&"function"==typeof t.unref?t.unref():"undefined"!=typeof Deno&&"function"==typeof Deno.unrefTimer&&Deno.unrefTimer(t),setTimeout(()=>e(this,null,function*(){yield this.initializePromise,yield this._autoRefreshTokenTick()}),0)})}_stopAutoRefresh(){return e(this,null,function*(){this._debug("#_stopAutoRefresh()");const e=this.autoRefreshTicker;this.autoRefreshTicker=null,e&&clearInterval(e)})}startAutoRefresh(){return e(this,null,function*(){this._removeVisibilityChangedCallback(),yield this._startAutoRefresh()})}stopAutoRefresh(){return e(this,null,function*(){this._removeVisibilityChangedCallback(),yield this._stopAutoRefresh()})}_autoRefreshTokenTick(){return e(this,null,function*(){this._debug("#_autoRefreshTokenTick()","begin");try{yield this._acquireLock(0,()=>e(this,null,function*(){try{const r=Date.now();try{return yield this._useSession(t=>e(this,null,function*(){const{data:{session:e}}=t;if(!e||!e.refresh_token||!e.expires_at)return void this._debug("#_autoRefreshTokenTick()","no session");const s=Math.floor((1e3*e.expires_at-r)/Hr);this._debug("#_autoRefreshTokenTick()",`access token expires in ${s} ticks, a tick lasts 30000ms, refresh threshold is 3 ticks`),s<=3&&(yield this._callRefreshToken(e.refresh_token))}))}catch(t){}}finally{this._debug("#_autoRefreshTokenTick()","end")}}))}catch(t){if(!(t.isAcquireTimeout||t instanceof qs))throw t;this._debug("auto refresh token tick lock not available")}})}_handleVisibilityChange(){return e(this,null,function*(){if(this._debug("#_handleVisibilityChange()"),!gs()||!(null===window||void 0===window?void 0:window.addEventListener))return this.autoRefreshToken&&this.startAutoRefresh(),!1;try{this.visibilityChangedCallback=()=>e(this,null,function*(){return yield this._onVisibilityChanged(!1)}),null===window||void 0===window||window.addEventListener("visibilitychange",this.visibilityChangedCallback),yield this._onVisibilityChanged(!0)}catch(t){}})}_onVisibilityChanged(t){return e(this,null,function*(){const r=`#_onVisibilityChanged(${t})`;this._debug(r,"visibilityState",document.visibilityState),"visible"===document.visibilityState?(this.autoRefreshToken&&this._startAutoRefresh(),t||(yield this.initializePromise,yield this._acquireLock(-1,()=>e(this,null,function*(){"visible"===document.visibilityState?yield this._recoverAndRefresh():this._debug(r,"acquired the lock to recover the session, but the browser visibilityState is no longer visible, aborting")})))):"hidden"===document.visibilityState&&this.autoRefreshToken&&this._stopAutoRefresh()})}_getUrlForProvider(t,r,s){return e(this,null,function*(){const e=[`provider=${encodeURIComponent(r)}`];if((null==s?void 0:s.redirectTo)&&e.push(`redirect_to=${encodeURIComponent(s.redirectTo)}`),(null==s?void 0:s.scopes)&&e.push(`scopes=${encodeURIComponent(s.scopes)}`),"pkce"===this.flowType){const[t,r]=yield js(this.storage,this.storageKey),s=new URLSearchParams({code_challenge:`${encodeURIComponent(t)}`,code_challenge_method:`${encodeURIComponent(r)}`});e.push(s.toString())}if(null==s?void 0:s.queryParams){const t=new URLSearchParams(s.queryParams);e.push(t.toString())}return(null==s?void 0:s.skipBrowserRedirect)&&e.push(`skip_http_redirect=${s.skipBrowserRedirect}`),`${t}?${e.join("&")}`})}_unenroll(t){return e(this,null,function*(){try{return yield this._useSession(r=>e(this,null,function*(){var e;const{data:s,error:i}=r;return i?{data:null,error:i}:yield $s(this.fetch,"DELETE",`${this.url}/factors/${t.factorId}`,{headers:this.headers,jwt:null===(e=null==s?void 0:s.session)||void 0===e?void 0:e.access_token})}))}catch(r){if(Gr(r))return{data:null,error:r};throw r}})}_enroll(t){return e(this,null,function*(){try{return yield this._useSession(r=>e(this,null,function*(){var e,s;const{data:i,error:n}=r;if(n)return{data:null,error:n};const o=Object.assign({friendly_name:t.friendlyName,factor_type:t.factorType},"phone"===t.factorType?{phone:t.phone}:{issuer:t.issuer}),{data:a,error:l}=yield $s(this.fetch,"POST",`${this.url}/factors`,{body:o,headers:this.headers,jwt:null===(e=null==i?void 0:i.session)||void 0===e?void 0:e.access_token});return l?{data:null,error:l}:("totp"===t.factorType&&(null===(s=null==a?void 0:a.totp)||void 0===s?void 0:s.qr_code)&&(a.totp.qr_code=`data:image/svg+xml;utf-8,${a.totp.qr_code}`),{data:a,error:null})}))}catch(r){if(Gr(r))return{data:null,error:r};throw r}})}_verify(t){return e(this,null,function*(){return this._acquireLock(-1,()=>e(this,null,function*(){try{return yield this._useSession(r=>e(this,null,function*(){var e;const{data:s,error:i}=r;if(i)return{data:null,error:i};const{data:n,error:o}=yield $s(this.fetch,"POST",`${this.url}/factors/${t.factorId}/verify`,{body:{code:t.code,challenge_id:t.challengeId},headers:this.headers,jwt:null===(e=null==s?void 0:s.session)||void 0===e?void 0:e.access_token});return o?{data:null,error:o}:(yield this._saveSession(Object.assign({expires_at:Math.round(Date.now()/1e3)+n.expires_in},n)),yield this._notifyAllSubscribers("MFA_CHALLENGE_VERIFIED",n),{data:n,error:o})}))}catch(r){if(Gr(r))return{data:null,error:r};throw r}}))})}_challenge(t){return e(this,null,function*(){return this._acquireLock(-1,()=>e(this,null,function*(){try{return yield this._useSession(r=>e(this,null,function*(){var e;const{data:s,error:i}=r;return i?{data:null,error:i}:yield $s(this.fetch,"POST",`${this.url}/factors/${t.factorId}/challenge`,{body:{channel:t.channel},headers:this.headers,jwt:null===(e=null==s?void 0:s.session)||void 0===e?void 0:e.access_token})}))}catch(r){if(Gr(r))return{data:null,error:r};throw r}}))})}_challengeAndVerify(t){return e(this,null,function*(){const{data:e,error:r}=yield this._challenge({factorId:t.factorId});return r?{data:null,error:r}:yield this._verify({factorId:t.factorId,challengeId:e.id,code:t.code})})}_listFactors(){return e(this,null,function*(){const{data:{user:e},error:t}=yield this.getUser();if(t)return{data:null,error:t};const r=(null==e?void 0:e.factors)||[],s=r.filter(e=>"totp"===e.factor_type&&"verified"===e.status),i=r.filter(e=>"phone"===e.factor_type&&"verified"===e.status);return{data:{all:r,totp:s,phone:i},error:null}})}_getAuthenticatorAssuranceLevel(){return e(this,null,function*(){return this._acquireLock(-1,()=>e(this,null,function*(){return yield this._useSession(t=>e(this,null,function*(){var e,r;const{data:{session:s},error:i}=t;if(i)return{data:null,error:i};if(!s)return{data:{currentLevel:null,nextLevel:null,currentAuthenticationMethods:[]},error:null};const{payload:n}=Ts(s.access_token);let o=null;n.aal&&(o=n.aal);let a=o;(null!==(r=null===(e=s.user.factors)||void 0===e?void 0:e.filter(e=>"verified"===e.status))&&void 0!==r?r:[]).length>0&&(a="aal2");return{data:{currentLevel:o,nextLevel:a,currentAuthenticationMethods:n.amr||[]},error:null}}))}))})}fetchJwk(t){return e(this,arguments,function*(e,t={keys:[]}){let r=t.keys.find(t=>t.kid===e);if(r)return r;if(r=this.jwks.keys.find(t=>t.kid===e),r&&this.jwks_cached_at+6e5>Date.now())return r;const{data:s,error:i}=yield $s(this.fetch,"GET",`${this.url}/.well-known/jwks.json`,{headers:this.headers});if(i)throw i;if(!s.keys||0===s.keys.length)throw new os("JWKS is empty");if(this.jwks=s,this.jwks_cached_at=Date.now(),r=s.keys.find(t=>t.kid===e),!r)throw new os("No matching signing key found in JWKS");return r})}getClaims(t){return e(this,arguments,function*(e,t={keys:[]}){try{let r=e;if(!r){const{data:e,error:t}=yield this.getSession();if(t||!e.session)return{data:null,error:t};r=e.session.access_token}const{header:s,payload:i,signature:n,raw:{header:o,payload:a}}=Ts(r);if(function(e){if(!e)throw new Error("Missing exp claim");if(e<=Math.floor(Date.now()/1e3))throw new Error("JWT has expired")}(i.exp),!s.kid||"HS256"===s.alg||!("crypto"in globalThis)||!("subtle"in globalThis.crypto)){const{error:e}=yield this.getUser(r);if(e)throw e;return{data:{claims:i,header:s,signature:n},error:null}}const l=function(e){switch(e){case"RS256":return{name:"RSASSA-PKCS1-v1_5",hash:{name:"SHA-256"}};case"ES256":return{name:"ECDSA",namedCurve:"P-256",hash:{name:"SHA-256"}};default:throw new Error("Invalid alg claim")}}(s.alg),c=yield this.fetchJwk(s.kid,t),h=yield crypto.subtle.importKey("jwk",c,l,!0,["verify"]);if(!(yield crypto.subtle.verify(l,h,n,ps(`${o}.${a}`))))throw new os("Invalid JWT signature");return{data:{claims:i,header:s,signature:n},error:null}}catch(r){if(Gr(r))return{data:null,error:r};throw r}})}}Ws.nextInstanceID=0;const Ys=Ws;class Xs extends Ys{constructor(e){super(e)}}var Zs=globalThis&&globalThis.__awaiter||function(e,t,r,s){return new(r||(r=Promise))(function(i,n){function o(e){try{l(s.next(e))}catch(t){n(t)}}function a(e){try{l(s.throw(e))}catch(t){n(t)}}function l(e){var t;e.done?i(e.value):(t=e.value,t instanceof r?t:new r(function(e){e(t)})).then(o,a)}l((s=s.apply(e,t||[])).next())})};class Qs{constructor(e,t,r){var s,i,n;if(this.supabaseUrl=e,this.supabaseKey=t,!e)throw new Error("supabaseUrl is required.");if(!t)throw new Error("supabaseKey is required.");const o=e.replace(/\/$/,"");this.realtimeUrl=`${o}/realtime/v1`.replace(/^http/i,"ws"),this.authUrl=`${o}/auth/v1`,this.storageUrl=`${o}/storage/v1`,this.functionsUrl=`${o}/functions/v1`;const a=`sb-${new URL(this.authUrl).hostname.split(".")[0]}-auth-token`,l=function(e,t){const{db:r,auth:s,realtime:i,global:n}=e,{db:o,auth:a,realtime:l,global:c}=t,h={db:Object.assign(Object.assign({},o),r),auth:Object.assign(Object.assign({},a),s),realtime:Object.assign(Object.assign({},l),i),global:Object.assign(Object.assign({},c),n),accessToken:()=>Br(this,void 0,void 0,function*(){return""})};return e.accessToken?h.accessToken=e.accessToken:delete h.accessToken,h}(null!=r?r:{},{db:Rr,realtime:Lr,auth:Object.assign(Object.assign({},Ir),{storageKey:a}),global:zr});this.storageKey=null!==(s=l.auth.storageKey)&&void 0!==s?s:"",this.headers=null!==(i=l.global.headers)&&void 0!==i?i:{},l.accessToken?(this.accessToken=l.accessToken,this.auth=new Proxy({},{get:(e,t)=>{throw new Error(`@supabase/supabase-js: Supabase Client is configured with the accessToken option, accessing supabase.auth.${String(t)} is not possible`)}})):this.auth=this._initSupabaseAuthClient(null!==(n=l.auth)&&void 0!==n?n:{},this.headers,l.global.fetch),this.fetch=Ur(t,this._getAccessToken.bind(this),l.global.fetch),this.realtime=this._initRealtimeClient(Object.assign({headers:this.headers,accessToken:this._getAccessToken.bind(this)},l.realtime)),this.rest=new _t(`${o}/rest/v1`,{headers:this.headers,schema:l.db.schema,fetch:this.fetch}),l.accessToken||this._listenForAuthEvents()}get functions(){return new Ce(this.functionsUrl,{headers:this.headers,customFetch:this.fetch})}get storage(){return new Ar(this.storageUrl,this.headers,this.fetch)}from(e){return this.rest.from(e)}schema(e){return this.rest.schema(e)}rpc(e,t={},r={}){return this.rest.rpc(e,t,r)}channel(e,t={config:{}}){return this.realtime.channel(e,t)}getChannels(){return this.realtime.getChannels()}removeChannel(e){return this.realtime.removeChannel(e)}removeAllChannels(){return this.realtime.removeAllChannels()}_getAccessToken(){var e,t;return Zs(this,void 0,void 0,function*(){if(this.accessToken)return yield this.accessToken();const{data:r}=yield this.auth.getSession();return null!==(t=null===(e=r.session)||void 0===e?void 0:e.access_token)&&void 0!==t?t:null})}_initSupabaseAuthClient({autoRefreshToken:e,persistSession:t,detectSessionInUrl:r,storage:s,storageKey:i,flowType:n,lock:o,debug:a},l,c){const h={Authorization:`Bearer ${this.supabaseKey}`,apikey:`${this.supabaseKey}`};return new Xs({url:this.authUrl,headers:Object.assign(Object.assign({},h),l),storageKey:i,autoRefreshToken:e,persistSession:t,detectSessionInUrl:r,storage:s,flowType:n,lock:o,debug:a,fetch:c,hasCustomAuthorizationHeader:"Authorization"in this.headers})}_initRealtimeClient(e){return new lr(this.realtimeUrl,Object.assign(Object.assign({},e),{params:Object.assign({apikey:this.supabaseKey},null==e?void 0:e.params)}))}_listenForAuthEvents(){return this.auth.onAuthStateChange((e,t)=>{this._handleTokenChanged(e,"CLIENT",null==t?void 0:t.access_token)})}_handleTokenChanged(e,t,r){"TOKEN_REFRESHED"!==e&&"SIGNED_IN"!==e||this.changedAccessToken===r?"SIGNED_OUT"===e&&(this.realtime.setAuth(),"STORAGE"==t&&this.auth.signOut(),this.changedAccessToken=void 0):this.changedAccessToken=r}}const ei=new Qs("https://ydglduxhgwajqdseqzpy.supabase.co","eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.*******************************************************************************************************************************.HEFdJm5qnXU1PQFbF-HkZ-bLez9LuPi3LepirU0nz4c",{auth:{autoRefreshToken:!0,persistSession:!0,detectSessionInUrl:!0}});const ti=r.createContext(),ri=()=>{const e=r.useContext(ti);if(!e)throw new Error("useAuth debe ser usado dentro de un AuthProvider");return e};export{P as $,Q as A,B,R as C,y as D,j as E,ce as F,z as G,be as H,W as I,J,D as K,q as L,ae as M,le as N,ue as O,de as P,x as Q,C as R,b as S,me as T,$ as U,ve as V,_e as W,F as X,G as Y,ge as Z,fe as _,te as a,S as a0,M as a1,H as a2,oe as a3,ne as a4,ke as a5,X as a6,_ as a7,v as a8,T as b,U as c,pe as d,N as e,Y as f,I as g,ye as h,we as i,p as j,L as k,E as l,he as m,Z as n,K as o,w as p,se as q,re as r,ei as s,ie as t,ri as u,ee as v,V as w,k as x,A as y,O as z};
