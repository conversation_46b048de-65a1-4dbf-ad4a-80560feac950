var e=Object.defineProperty,t=Object.defineProperties,a=Object.getOwnPropertyDescriptors,r=Object.getOwnPropertySymbols,s=Object.prototype.hasOwnProperty,n=Object.prototype.propertyIsEnumerable,i=(t,a,r)=>a in t?e(t,a,{enumerable:!0,configurable:!0,writable:!0,value:r}):t[a]=r,o=(e,t)=>{for(var a in t||(t={}))s.call(t,a)&&i(e,a,t[a]);if(r)for(var a of r(t))n.call(t,a)&&i(e,a,t[a]);return e},c=(e,r)=>t(e,a(r)),l=(e,t)=>{var a={};for(var i in e)s.call(e,i)&&t.indexOf(i)<0&&(a[i]=e[i]);if(null!=e&&r)for(var i of r(e))t.indexOf(i)<0&&n.call(e,i)&&(a[i]=e[i]);return a},d=(e,t,a)=>(i(e,"symbol"!=typeof t?t+"":t,a),a),u=(e,t,a)=>new Promise((r,s)=>{var n=e=>{try{o(a.next(e))}catch(t){s(t)}},i=e=>{try{o(a.throw(e))}catch(t){s(t)}},o=e=>e.done?r(e.value):Promise.resolve(e.value).then(n,i);o((a=a.apply(e,t)).next())});import{j as p,D as m,s as x,a5 as h,a6 as g,H as f,a7 as b,F as y,a8 as j}from"./auth-3ab59eff.js";import{e as v,r as N,a as w,L as A,O as E,u as C,b as _,d as S,f as T,h as z,N as q,B as O,R as P}from"./react-vendor-99be060c.js";import{k as D,Q as k}from"./ui-vendor-9705a4a1.js";import{C as R,b as I,a as M,c as B,B as L,S as $}from"./admin-168d579d.js";import"./utils-vendor-4d1206d7.js";!function(){const e=document.createElement("link").relList;if(!(e&&e.supports&&e.supports("modulepreload"))){for(const e of document.querySelectorAll('link[rel="modulepreload"]'))t(e);new MutationObserver(e=>{for(const a of e)if("childList"===a.type)for(const e of a.addedNodes)"LINK"===e.tagName&&"modulepreload"===e.rel&&t(e)}).observe(document,{childList:!0,subtree:!0})}function t(e){if(e.ep)return;e.ep=!0;const t=function(e){const t={};return e.integrity&&(t.integrity=e.integrity),e.referrerPolicy&&(t.referrerPolicy=e.referrerPolicy),"use-credentials"===e.crossOrigin?t.credentials="include":"anonymous"===e.crossOrigin?t.credentials="omit":t.credentials="same-origin",t}(e);fetch(e.href,t)}}();var F={},V=v;F.createRoot=V.createRoot,F.hydrateRoot=V.hydrateRoot;var U=N;
/**
 * @license React
 * use-sync-external-store-with-selector.production.js
 *
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */"function"==typeof Object.is&&Object.is,U.useSyncExternalStore,U.useRef,U.useEffect,U.useMemo,U.useDebugValue;var Q={notify(){},get:()=>[]};var W=(()=>!("undefined"==typeof window||void 0===window.document||void 0===window.document.createElement))(),H=(()=>"undefined"!=typeof navigator&&"ReactNative"===navigator.product)(),G=(()=>W||H?N.useLayoutEffect:N.useEffect)(),X=Symbol.for("react-redux-context"),K="undefined"!=typeof globalThis?globalThis:{};function J(){var e;if(!N.createContext)return{};const t=null!=(e=K[X])?e:K[X]=new Map;let a=t.get(N.createContext);return a||(a=N.createContext(null),t.set(N.createContext,a)),a}var Y=J();var Z=function(e){const{children:t,context:a,serverState:r,store:s}=e,n=N.useMemo(()=>{const e=function(e,t){let a,r=Q,s=0,n=!1;function i(){l.onStateChange&&l.onStateChange()}function o(){s++,a||(a=t?t.addNestedSub(i):e.subscribe(i),r=function(){let e=null,t=null;return{clear(){e=null,t=null},notify(){(()=>{let t=e;for(;t;)t.callback(),t=t.next})()},get(){const t=[];let a=e;for(;a;)t.push(a),a=a.next;return t},subscribe(a){let r=!0;const s=t={callback:a,next:null,prev:t};return s.prev?s.prev.next=s:e=s,function(){r&&null!==e&&(r=!1,s.next?s.next.prev=s.prev:t=s.prev,s.prev?s.prev.next=s.next:e=s.next)}}}}())}function c(){s--,a&&0===s&&(a(),a=void 0,r.clear(),r=Q)}const l={addNestedSub:function(e){o();const t=r.subscribe(e);let a=!1;return()=>{a||(a=!0,t(),c())}},notifyNestedSubs:function(){r.notify()},handleChangeWrapper:i,isSubscribed:function(){return n},trySubscribe:function(){n||(n=!0,o())},tryUnsubscribe:function(){n&&(n=!1,c())},getListeners:()=>r};return l}(s);return{store:s,subscription:e,getServerState:r?()=>r:void 0}},[s,r]),i=N.useMemo(()=>s.getState(),[s]);G(()=>{const{subscription:e}=n;return e.onStateChange=e.notifyNestedSubs,e.trySubscribe(),i!==s.getState()&&e.notifyNestedSubs(),()=>{e.tryUnsubscribe(),e.onStateChange=void 0}},[n,i]);const o=a||Y;return N.createElement(o.Provider,{value:n},t)};function ee(e){return`Minified Redux error #${e}; visit https://redux.js.org/Errors?code=${e} for the full message or use the non-minified dev environment for full errors. `}var te=(()=>"function"==typeof Symbol&&Symbol.observable||"@@observable")(),ae=()=>Math.random().toString(36).substring(7).split("").join("."),re={INIT:`@@redux/INIT${ae()}`,REPLACE:`@@redux/REPLACE${ae()}`,PROBE_UNKNOWN_ACTION:()=>`@@redux/PROBE_UNKNOWN_ACTION${ae()}`};function se(e){if("object"!=typeof e||null===e)return!1;let t=e;for(;null!==Object.getPrototypeOf(t);)t=Object.getPrototypeOf(t);return Object.getPrototypeOf(e)===t||null===Object.getPrototypeOf(e)}function ne(e,t,a){if("function"!=typeof e)throw new Error(ee(2));if("function"==typeof t&&"function"==typeof a||"function"==typeof a&&"function"==typeof arguments[3])throw new Error(ee(0));if("function"==typeof t&&void 0===a&&(a=t,t=void 0),void 0!==a){if("function"!=typeof a)throw new Error(ee(1));return a(ne)(e,t)}let r=e,s=t,n=new Map,i=n,o=0,c=!1;function l(){i===n&&(i=new Map,n.forEach((e,t)=>{i.set(t,e)}))}function d(){if(c)throw new Error(ee(3));return s}function u(e){if("function"!=typeof e)throw new Error(ee(4));if(c)throw new Error(ee(5));let t=!0;l();const a=o++;return i.set(a,e),function(){if(t){if(c)throw new Error(ee(6));t=!1,l(),i.delete(a),n=null}}}function p(e){if(!se(e))throw new Error(ee(7));if(void 0===e.type)throw new Error(ee(8));if("string"!=typeof e.type)throw new Error(ee(17));if(c)throw new Error(ee(9));try{c=!0,s=r(s,e)}finally{c=!1}return(n=i).forEach(e=>{e()}),e}p({type:re.INIT});return{dispatch:p,subscribe:u,getState:d,replaceReducer:function(e){if("function"!=typeof e)throw new Error(ee(10));r=e,p({type:re.REPLACE})},[te]:function(){const e=u;return{subscribe(t){if("object"!=typeof t||null===t)throw new Error(ee(11));function a(){const e=t;e.next&&e.next(d())}a();return{unsubscribe:e(a)}},[te](){return this}}}}}function ie(e){const t=Object.keys(e),a={};for(let i=0;i<t.length;i++){const r=t[i];"function"==typeof e[r]&&(a[r]=e[r])}const r=Object.keys(a);let s;try{!function(e){Object.keys(e).forEach(t=>{const a=e[t];if(void 0===a(void 0,{type:re.INIT}))throw new Error(ee(12));if(void 0===a(void 0,{type:re.PROBE_UNKNOWN_ACTION()}))throw new Error(ee(13))})}(a)}catch(n){s=n}return function(e={},t){if(s)throw s;let n=!1;const i={};for(let s=0;s<r.length;s++){const o=r[s],c=a[o],l=e[o],d=c(l,t);if(void 0===d)throw t&&t.type,new Error(ee(14));i[o]=d,n=n||d!==l}return n=n||r.length!==Object.keys(e).length,n?i:e}}function oe(e,t){return function(...a){return t(e.apply(this,a))}}function ce(...e){return 0===e.length?e=>e:1===e.length?e[0]:e.reduce((e,t)=>(...a)=>e(t(...a)))}function le(...e){return t=>(a,r)=>{const s=t(a,r);let n=()=>{throw new Error(ee(15))};const i={getState:s.getState,dispatch:(e,...t)=>n(e,...t)},l=e.map(e=>e(i));return n=ce(...l)(s.dispatch),c(o({},s),{dispatch:n})}}function de(e){return se(e)&&"type"in e&&"string"==typeof e.type}const ue=Object.freeze(Object.defineProperty({__proto__:null,__DO_NOT_USE__ActionTypes:re,applyMiddleware:le,bindActionCreators:function(e,t){if("function"==typeof e)return oe(e,t);if("object"!=typeof e||null===e)throw new Error(ee(16));const a={};for(const r in e){const s=e[r];"function"==typeof s&&(a[r]=oe(s,t))}return a},combineReducers:ie,compose:ce,createStore:ne,isAction:de,isPlainObject:se,legacy_createStore:function(e,t,a){return ne(e,t,a)}},Symbol.toStringTag,{value:"Module"}));var pe=Symbol.for("immer-nothing"),me=Symbol.for("immer-draftable"),xe=Symbol.for("immer-state");function he(e,...t){throw new Error(`[Immer] minified error nr: ${e}. Full error at: https://bit.ly/3cXEKWf`)}var ge=Object.getPrototypeOf;function fe(e){return!!e&&!!e[xe]}function be(e){var t;return!!e&&(je(e)||Array.isArray(e)||!!e[me]||!!(null==(t=e.constructor)?void 0:t[me])||Ce(e)||_e(e))}var ye=Object.prototype.constructor.toString();function je(e){if(!e||"object"!=typeof e)return!1;const t=ge(e);if(null===t)return!0;const a=Object.hasOwnProperty.call(t,"constructor")&&t.constructor;return a===Object||"function"==typeof a&&Function.toString.call(a)===ye}function ve(e){return fe(e)||he(15),e[xe].base_}function Ne(e,t){0===we(e)?Reflect.ownKeys(e).forEach(a=>{t(a,e[a],e)}):e.forEach((a,r)=>t(r,a,e))}function we(e){const t=e[xe];return t?t.type_:Array.isArray(e)?1:Ce(e)?2:_e(e)?3:0}function Ae(e,t){return 2===we(e)?e.has(t):Object.prototype.hasOwnProperty.call(e,t)}function Ee(e,t,a){const r=we(e);2===r?e.set(t,a):3===r?e.add(a):e[t]=a}function Ce(e){return e instanceof Map}function _e(e){return e instanceof Set}function Se(e){return e.copy_||e.base_}function Te(e,t){if(Ce(e))return new Map(e);if(_e(e))return new Set(e);if(Array.isArray(e))return Array.prototype.slice.call(e);const a=je(e);if(!0===t||"class_only"===t&&!a){const t=Object.getOwnPropertyDescriptors(e);delete t[xe];let a=Reflect.ownKeys(t);for(let r=0;r<a.length;r++){const s=a[r],n=t[s];!1===n.writable&&(n.writable=!0,n.configurable=!0),(n.get||n.set)&&(t[s]={configurable:!0,writable:!0,enumerable:n.enumerable,value:e[s]})}return Object.create(ge(e),t)}{const t=ge(e);if(null!==t&&a)return o({},e);const r=Object.create(t);return Object.assign(r,e)}}function ze(e,t=!1){return Oe(e)||fe(e)||!be(e)||(we(e)>1&&(e.set=e.add=e.clear=e.delete=qe),Object.freeze(e),t&&Object.entries(e).forEach(([e,t])=>ze(t,!0))),e}function qe(){he(2)}function Oe(e){return Object.isFrozen(e)}var Pe,De={};function ke(e){const t=De[e];return t||he(0),t}function Re(){return Pe}function Ie(e,t){t&&(ke("Patches"),e.patches_=[],e.inversePatches_=[],e.patchListener_=t)}function Me(e){Be(e),e.drafts_.forEach($e),e.drafts_=null}function Be(e){e===Pe&&(Pe=e.parent_)}function Le(e){return Pe={drafts_:[],parent_:Pe,immer_:e,canAutoFreeze_:!0,unfinalizedDrafts_:0}}function $e(e){const t=e[xe];0===t.type_||1===t.type_?t.revoke_():t.revoked_=!0}function Fe(e,t){t.unfinalizedDrafts_=t.drafts_.length;const a=t.drafts_[0];return void 0!==e&&e!==a?(a[xe].modified_&&(Me(t),he(4)),be(e)&&(e=Ve(t,e),t.parent_||Qe(t,e)),t.patches_&&ke("Patches").generateReplacementPatches_(a[xe].base_,e,t.patches_,t.inversePatches_)):e=Ve(t,a,[]),Me(t),t.patches_&&t.patchListener_(t.patches_,t.inversePatches_),e!==pe?e:void 0}function Ve(e,t,a){if(Oe(t))return t;const r=t[xe];if(!r)return Ne(t,(s,n)=>Ue(e,r,t,s,n,a)),t;if(r.scope_!==e)return t;if(!r.modified_)return Qe(e,r.base_,!0),r.base_;if(!r.finalized_){r.finalized_=!0,r.scope_.unfinalizedDrafts_--;const t=r.copy_;let s=t,n=!1;3===r.type_&&(s=new Set(t),t.clear(),n=!0),Ne(s,(s,i)=>Ue(e,r,t,s,i,a,n)),Qe(e,t,!1),a&&e.patches_&&ke("Patches").generatePatches_(r,a,e.patches_,e.inversePatches_)}return r.copy_}function Ue(e,t,a,r,s,n,i){if(fe(s)){const i=Ve(e,s,n&&t&&3!==t.type_&&!Ae(t.assigned_,r)?n.concat(r):void 0);if(Ee(a,r,i),!fe(i))return;e.canAutoFreeze_=!1}else i&&a.add(s);if(be(s)&&!Oe(s)){if(!e.immer_.autoFreeze_&&e.unfinalizedDrafts_<1)return;Ve(e,s),t&&t.scope_.parent_||"symbol"==typeof r||!Object.prototype.propertyIsEnumerable.call(a,r)||Qe(e,s)}}function Qe(e,t,a=!1){!e.parent_&&e.immer_.autoFreeze_&&e.canAutoFreeze_&&ze(t,a)}var We={get(e,t){if(t===xe)return e;const a=Se(e);if(!Ae(a,t))return function(e,t,a){var r;const s=Xe(t,a);return s?"value"in s?s.value:null==(r=s.get)?void 0:r.call(e.draft_):void 0}(e,a,t);const r=a[t];return e.finalized_||!be(r)?r:r===Ge(e.base_,t)?(Je(e),e.copy_[t]=Ye(r,e)):r},has:(e,t)=>t in Se(e),ownKeys:e=>Reflect.ownKeys(Se(e)),set(e,t,a){const r=Xe(Se(e),t);if(null==r?void 0:r.set)return r.set.call(e.draft_,a),!0;if(!e.modified_){const r=Ge(Se(e),t),i=null==r?void 0:r[xe];if(i&&i.base_===a)return e.copy_[t]=a,e.assigned_[t]=!1,!0;if(((s=a)===(n=r)?0!==s||1/s==1/n:s!=s&&n!=n)&&(void 0!==a||Ae(e.base_,t)))return!0;Je(e),Ke(e)}var s,n;return e.copy_[t]===a&&(void 0!==a||t in e.copy_)||Number.isNaN(a)&&Number.isNaN(e.copy_[t])||(e.copy_[t]=a,e.assigned_[t]=!0),!0},deleteProperty:(e,t)=>(void 0!==Ge(e.base_,t)||t in e.base_?(e.assigned_[t]=!1,Je(e),Ke(e)):delete e.assigned_[t],e.copy_&&delete e.copy_[t],!0),getOwnPropertyDescriptor(e,t){const a=Se(e),r=Reflect.getOwnPropertyDescriptor(a,t);return r?{writable:!0,configurable:1!==e.type_||"length"!==t,enumerable:r.enumerable,value:a[t]}:r},defineProperty(){he(11)},getPrototypeOf:e=>ge(e.base_),setPrototypeOf(){he(12)}},He={};function Ge(e,t){const a=e[xe];return(a?Se(a):e)[t]}function Xe(e,t){if(!(t in e))return;let a=ge(e);for(;a;){const e=Object.getOwnPropertyDescriptor(a,t);if(e)return e;a=ge(a)}}function Ke(e){e.modified_||(e.modified_=!0,e.parent_&&Ke(e.parent_))}function Je(e){e.copy_||(e.copy_=Te(e.base_,e.scope_.immer_.useStrictShallowCopy_))}Ne(We,(e,t)=>{He[e]=function(){return arguments[0]=arguments[0][0],t.apply(this,arguments)}}),He.deleteProperty=function(e,t){return He.set.call(this,e,t,void 0)},He.set=function(e,t,a){return We.set.call(this,e[0],t,a,e[0])};function Ye(e,t){const a=Ce(e)?ke("MapSet").proxyMap_(e,t):_e(e)?ke("MapSet").proxySet_(e,t):function(e,t){const a=Array.isArray(e),r={type_:a?1:0,scope_:t?t.scope_:Re(),modified_:!1,finalized_:!1,assigned_:{},parent_:t,base_:e,draft_:null,copy_:null,revoke_:null,isManual_:!1};let s=r,n=We;a&&(s=[r],n=He);const{revoke:i,proxy:o}=Proxy.revocable(s,n);return r.draft_=o,r.revoke_=i,o}(e,t);return(t?t.scope_:Re()).drafts_.push(a),a}function Ze(e){return fe(e)||he(10),et(e)}function et(e){if(!be(e)||Oe(e))return e;const t=e[xe];let a;if(t){if(!t.modified_)return t.base_;t.finalized_=!0,a=Te(e,t.scope_.immer_.useStrictShallowCopy_)}else a=Te(e,!0);return Ne(a,(e,t)=>{Ee(a,e,et(t))}),t&&(t.finalized_=!1),a}var tt=new class{constructor(e){this.autoFreeze_=!0,this.useStrictShallowCopy_=!1,this.produce=(e,t,a)=>{if("function"==typeof e&&"function"!=typeof t){const a=t;t=e;const r=this;return function(e=a,...s){return r.produce(e,e=>t.call(this,e,...s))}}let r;if("function"!=typeof t&&he(6),void 0!==a&&"function"!=typeof a&&he(7),be(e)){const s=Le(this),n=Ye(e,void 0);let i=!0;try{r=t(n),i=!1}finally{i?Me(s):Be(s)}return Ie(s,a),Fe(r,s)}if(!e||"object"!=typeof e){if(r=t(e),void 0===r&&(r=e),r===pe&&(r=void 0),this.autoFreeze_&&ze(r,!0),a){const t=[],s=[];ke("Patches").generateReplacementPatches_(e,r,t,s),a(t,s)}return r}he(1)},this.produceWithPatches=(e,t)=>{if("function"==typeof e)return(t,...a)=>this.produceWithPatches(t,t=>e(t,...a));let a,r;return[this.produce(e,t,(e,t)=>{a=e,r=t}),a,r]},"boolean"==typeof(null==e?void 0:e.autoFreeze)&&this.setAutoFreeze(e.autoFreeze),"boolean"==typeof(null==e?void 0:e.useStrictShallowCopy)&&this.setUseStrictShallowCopy(e.useStrictShallowCopy)}createDraft(e){be(e)||he(8),fe(e)&&(e=Ze(e));const t=Le(this),a=Ye(e,void 0);return a[xe].isManual_=!0,Be(t),a}finishDraft(e,t){const a=e&&e[xe];a&&a.isManual_||he(9);const{scope_:r}=a;return Ie(r,t),Fe(void 0,r)}setAutoFreeze(e){this.autoFreeze_=e}setUseStrictShallowCopy(e){this.useStrictShallowCopy_=e}applyPatches(e,t){let a;for(a=t.length-1;a>=0;a--){const r=t[a];if(0===r.path.length&&"replace"===r.op){e=r.value;break}}a>-1&&(t=t.slice(a+1));const r=ke("Patches").applyPatches_;return fe(e)?r(e,t):this.produce(e,e=>r(e,t))}},at=tt.produce;tt.produceWithPatches.bind(tt),tt.setAutoFreeze.bind(tt),tt.setUseStrictShallowCopy.bind(tt),tt.applyPatches.bind(tt),tt.createDraft.bind(tt),tt.finishDraft.bind(tt);var rt=Symbol("NOT_FOUND");var st=e=>Array.isArray(e)?e:[e];function nt(e){const t=Array.isArray(e[0])?e[0]:e;return function(e,t="expected all items to be functions, instead received the following types: "){if(!e.every(e=>"function"==typeof e)){const a=e.map(e=>"function"==typeof e?`function ${e.name||"unnamed"}()`:typeof e).join(", ");throw new TypeError(`${t}[${a}]`)}}(t,"createSelector expects all input-selectors to be functions, but received the following types: "),t}var it=(e,t)=>e===t;function ot(e,t){const a="object"==typeof t?t:{equalityCheck:t},{equalityCheck:r=it,maxSize:s=1,resultEqualityCheck:n}=a,i=function(e){return function(t,a){if(null===t||null===a||t.length!==a.length)return!1;const{length:r}=t;for(let s=0;s<r;s++)if(!e(t[s],a[s]))return!1;return!0}}(r);let o=0;const c=s<=1?function(e){let t;return{get:a=>t&&e(t.key,a)?t.value:rt,put(e,a){t={key:e,value:a}},getEntries:()=>t?[t]:[],clear(){t=void 0}}}(i):function(e,t){let a=[];function r(e){const r=a.findIndex(a=>t(e,a.key));if(r>-1){const e=a[r];return r>0&&(a.splice(r,1),a.unshift(e)),e.value}return rt}return{get:r,put:function(t,s){r(t)===rt&&(a.unshift({key:t,value:s}),a.length>e&&a.pop())},getEntries:function(){return a},clear:function(){a=[]}}}(s,i);function l(){let t=c.get(arguments);if(t===rt){if(t=e.apply(null,arguments),o++,n){const e=c.getEntries().find(e=>n(e.value,t));e&&(t=e.value,0!==o&&o--)}c.put(arguments,t)}return t}return l.clearCache=()=>{c.clear(),l.resetResultsCount()},l.resultsCount=()=>o,l.resetResultsCount=()=>{o=0},l}var ct="undefined"!=typeof WeakRef?WeakRef:class{constructor(e){this.value=e}deref(){return this.value}};function lt(){return{s:0,v:void 0,o:null,p:null}}function dt(e,t={}){let a={s:0,v:void 0,o:null,p:null};const{resultEqualityCheck:r}=t;let s,n=0;function i(){var t,i;let o=a;const{length:c}=arguments;for(let e=0,a=c;e<a;e++){const t=arguments[e];if("function"==typeof t||"object"==typeof t&&null!==t){let e=o.o;null===e&&(o.o=e=new WeakMap);const a=e.get(t);void 0===a?(o=lt(),e.set(t,o)):o=a}else{let e=o.p;null===e&&(o.p=e=new Map);const a=e.get(t);void 0===a?(o=lt(),e.set(t,o)):o=a}}const l=o;let d;if(1===o.s)d=o.v;else if(d=e.apply(null,arguments),n++,r){const e=null!=(i=null==(t=null==s?void 0:s.deref)?void 0:t.call(s))?i:s;null!=e&&r(e,d)&&(d=e,0!==n&&n--);s="object"==typeof d&&null!==d||"function"==typeof d?new ct(d):d}return l.s=1,l.v=d,d}return i.clearCache=()=>{a={s:0,v:void 0,o:null,p:null},i.resetResultsCount()},i.resultsCount=()=>n,i.resetResultsCount=()=>{n=0},i}function ut(e,...t){const a="function"==typeof e?{memoize:e,memoizeOptions:t}:e,r=(...e)=>{let t,r=0,s=0,n={},i=e.pop();"object"==typeof i&&(n=i,i=e.pop()),function(e,t="expected a function, instead received "+typeof e){if("function"!=typeof e)throw new TypeError(t)}(i,`createSelector expects an output function after the inputs, but received: [${typeof i}]`);const c=o(o({},a),n),{memoize:l,memoizeOptions:d=[],argsMemoize:u=dt,argsMemoizeOptions:p=[],devModeChecks:m={}}=c,x=st(d),h=st(p),g=nt(e),f=l(function(){return r++,i.apply(null,arguments)},...x),b=u(function(){s++;const e=function(e,t){const a=[],{length:r}=e;for(let s=0;s<r;s++)a.push(e[s].apply(null,t));return a}(g,arguments);return t=f.apply(null,e),t},...h);return Object.assign(b,{resultFunc:i,memoizedResultFunc:f,dependencies:g,dependencyRecomputations:()=>s,resetDependencyRecomputations:()=>{s=0},lastResult:()=>t,recomputations:()=>r,resetRecomputations:()=>{r=0},memoize:l,argsMemoize:u})};return Object.assign(r,{withTypes:()=>r}),r}var pt=ut(dt),mt=Object.assign((e,t=pt)=>{!function(e,t="expected an object, instead received "+typeof e){if("object"!=typeof e)throw new TypeError(t)}(e,"createStructuredSelector expects first argument to be an object where each property is a selector, instead received a "+typeof e);const a=Object.keys(e);return t(a.map(t=>e[t]),(...e)=>e.reduce((e,t,r)=>(e[a[r]]=t,e),{}))},{withTypes:()=>mt});function xt(e){return({dispatch:t,getState:a})=>r=>s=>"function"==typeof s?s(t,a,e):r(s)}var ht=xt(),gt=xt,ft="undefined"!=typeof window&&window.__REDUX_DEVTOOLS_EXTENSION_COMPOSE__?window.__REDUX_DEVTOOLS_EXTENSION_COMPOSE__:function(){if(0!==arguments.length)return"object"==typeof arguments[0]?ce:ce.apply(null,arguments)};function bt(e,t){function a(...a){if(t){let r=t(...a);if(!r)throw new Error(qt(0));return o(o({type:e,payload:r.payload},"meta"in r&&{meta:r.meta}),"error"in r&&{error:r.error})}return{type:e,payload:a[0]}}return a.toString=()=>`${e}`,a.type=e,a.match=t=>de(t)&&t.type===e,a}var yt=class e extends Array{constructor(...t){super(...t),Object.setPrototypeOf(this,e.prototype)}static get[Symbol.species](){return e}concat(...e){return super.concat.apply(this,e)}prepend(...t){return 1===t.length&&Array.isArray(t[0])?new e(...t[0].concat(this)):new e(...t.concat(this))}};function jt(e){return be(e)?at(e,()=>{}):e}function vt(e,t,a){return e.has(t)?e.get(t):e.set(t,a(t)).get(t)}var Nt=e=>t=>{setTimeout(t,e)},wt=e=>function(t){const{autoBatch:a=!0}=null!=t?t:{};let r=new yt(e);return a&&r.push(((e={type:"raf"})=>t=>(...a)=>{const r=t(...a);let s=!0,n=!1,i=!1;const o=new Set,c="tick"===e.type?queueMicrotask:"raf"===e.type?"undefined"!=typeof window&&window.requestAnimationFrame?window.requestAnimationFrame:Nt(10):"callback"===e.type?e.queueNotification:Nt(e.timeout),l=()=>{i=!1,n&&(n=!1,o.forEach(e=>e()))};return Object.assign({},r,{subscribe(e){const t=r.subscribe(()=>s&&e());return o.add(e),()=>{t(),o.delete(e)}},dispatch(e){var t;try{return s=!(null==(t=null==e?void 0:e.meta)?void 0:t.RTK_autoBatch),n=!s,n&&(i||(i=!0,c(l))),r.dispatch(e)}finally{s=!0}}})})("object"==typeof a?a:void 0)),r};function At(e){const t={},a=[];let r;const s={addCase(e,a){const r="string"==typeof e?e:e.type;if(!r)throw new Error(qt(28));if(r in t)throw new Error(qt(29));return t[r]=a,s},addMatcher:(e,t)=>(a.push({matcher:e,reducer:t}),s),addDefaultCase:e=>(r=e,s)};return e(s),[t,a,r]}var Et=Symbol.for("rtk-slice-createasyncthunk");function Ct(e,t){return`${e}/${t}`}function _t({creators:e}={}){var t;const a=null==(t=null==e?void 0:e.asyncThunk)?void 0:t[Et];return function(e){const{name:t,reducerPath:r=t}=e;if(!t)throw new Error(qt(11));const s=("function"==typeof e.reducers?e.reducers(function(){function e(e,t){return o({_reducerDefinitionType:"asyncThunk",payloadCreator:e},t)}return e.withTypes=()=>e,{reducer:e=>Object.assign({[e.name]:(...t)=>e(...t)}[e.name],{_reducerDefinitionType:"reducer"}),preparedReducer:(e,t)=>({_reducerDefinitionType:"reducerWithPrepare",prepare:e,reducer:t}),asyncThunk:e}}()):e.reducers)||{},n=Object.keys(s),i={sliceCaseReducersByName:{},sliceCaseReducersByType:{},actionCreators:{},sliceMatchers:[]},d={addCase(e,t){const a="string"==typeof e?e:e.type;if(!a)throw new Error(qt(12));if(a in i.sliceCaseReducersByType)throw new Error(qt(13));return i.sliceCaseReducersByType[a]=t,d},addMatcher:(e,t)=>(i.sliceMatchers.push({matcher:e,reducer:t}),d),exposeAction:(e,t)=>(i.actionCreators[e]=t,d),exposeCaseReducer:(e,t)=>(i.sliceCaseReducersByName[e]=t,d)};function u(){const[t={},a=[],r]="function"==typeof e.extraReducers?At(e.extraReducers):[e.extraReducers],s=o(o({},t),i.sliceCaseReducersByType);return function(e,t){let a,[r,s,n]=At(t);if("function"==typeof e)a=()=>jt(e());else{const t=jt(e);a=()=>t}function i(e=a(),t){let i=[r[t.type],...s.filter(({matcher:e})=>e(t)).map(({reducer:e})=>e)];return 0===i.filter(e=>!!e).length&&(i=[n]),i.reduce((e,a)=>{if(a){if(fe(e)){const r=a(e,t);return void 0===r?e:r}if(be(e))return at(e,e=>a(e,t));{const r=a(e,t);if(void 0===r){if(null===e)return e;throw Error("A case reducer on a non-draftable value must not return undefined")}return r}}return e},e)}return i.getInitialState=a,i}(e.initialState,e=>{for(let t in s)e.addCase(t,s[t]);for(let t of i.sliceMatchers)e.addMatcher(t.matcher,t.reducer);for(let t of a)e.addMatcher(t.matcher,t.reducer);r&&e.addDefaultCase(r)})}n.forEach(r=>{const n=s[r],i={reducerName:r,type:Ct(t,r),createNotation:"function"==typeof e.reducers};!function(e){return"asyncThunk"===e._reducerDefinitionType}(n)?function({type:e,reducerName:t,createNotation:a},r,s){let n,i;if("reducer"in r){if(a&&!function(e){return"reducerWithPrepare"===e._reducerDefinitionType}(r))throw new Error(qt(17));n=r.reducer,i=r.prepare}else n=r;s.addCase(e,n).exposeCaseReducer(t,n).exposeAction(t,i?bt(e,i):bt(e))}(i,n,d):function({type:e,reducerName:t},a,r,s){if(!s)throw new Error(qt(18));const{payloadCreator:n,fulfilled:i,pending:o,rejected:c,settled:l,options:d}=a,u=s(e,n,d);r.exposeAction(t,u),i&&r.addCase(u.fulfilled,i);o&&r.addCase(u.pending,o);c&&r.addCase(u.rejected,c);l&&r.addMatcher(u.settled,l);r.exposeCaseReducer(t,{fulfilled:i||zt,pending:o||zt,rejected:c||zt,settled:l||zt})}(i,n,d,a)});const p=e=>e,m=new Map,x=new WeakMap;let h;function g(e,t){return h||(h=u()),h(e,t)}function f(){return h||(h=u()),h.getInitialState()}function b(t,a=!1){function r(e){let s=e[t];return void 0===s&&a&&(s=vt(x,r,f)),s}function s(t=p){const r=vt(m,a,()=>new WeakMap);return vt(r,t,()=>{var r;const s={};for(const[n,i]of Object.entries(null!=(r=e.selectors)?r:{}))s[n]=St(i,t,()=>vt(x,t,f),a);return s})}return{reducerPath:t,getSelectors:s,get selectors(){return s(r)},selectSlice:r}}const y=c(o({name:t,reducer:g,actions:i.actionCreators,caseReducers:i.sliceCaseReducersByName,getInitialState:f},b(r)),{injectInto(e,t={}){var a=t,{reducerPath:s}=a,n=l(a,["reducerPath"]);const i=null!=s?s:r;return e.inject({reducerPath:i,reducer:g},n),o(o({},y),b(i,!0))}});return y}}function St(e,t,a,r){function s(s,...n){let i=t(s);return void 0===i&&r&&(i=a()),e(i,...n)}return s.unwrapped=e,s}var Tt=_t();function zt(){}function qt(e){return`Minified Redux Toolkit error #${e}; visit https://redux-toolkit.js.org/Errors?code=${e} for the full message or use the non-minified dev environment for full errors. `}var Ot,Pt,Dt=Object.defineProperty,kt=Object.getOwnPropertyDescriptor,Rt=Object.getOwnPropertyNames,It=Object.prototype.hasOwnProperty,Mt=(e,t,a,r)=>{if(t&&"object"==typeof t||"function"==typeof t)for(let s of Rt(t))It.call(e,s)||s===a||Dt(e,s,{get:()=>t[s],enumerable:!(r=kt(t,s))||r.enumerable});return e},Bt={};((e,t)=>{for(var a in t)Dt(e,a,{get:t[a],enumerable:!0})})(Bt,{ReducerType:()=>Pa,SHOULD_AUTOBATCH:()=>na,TaskAbortError:()=>ar,Tuple:()=>Xt,addListener:()=>Nr,asyncThunkCreator:()=>Oa,autoBatchEnhancer:()=>ca,buildCreateSlice:()=>ka,clearAllListeners:()=>wr,combineSlices:()=>Rr,configureStore:()=>da,createAction:()=>Ut,createActionCreatorInvariantMiddleware:()=>Gt,createAsyncThunk:()=>Ta,createDraftSafeSelector:()=>$t,createDraftSafeSelectorCreator:()=>Lt,createDynamicMiddleware:()=>Sr,createEntityAdapter:()=>Ga,createImmutableStateInvariantMiddleware:()=>Zt,createListenerMiddleware:()=>Cr,createNextState:()=>at,createReducer:()=>pa,createSelector:()=>pt,createSelectorCreator:()=>ut,createSerializableStateInvariantMiddleware:()=>ra,createSlice:()=>Ia,current:()=>Ze,findNonSerializableValue:()=>ta,formatProdErrorMessage:()=>Ir,freeze:()=>ze,isActionCreator:()=>Qt,isAllOf:()=>ha,isAnyOf:()=>xa,isAsyncThunkAction:()=>Na,isDraft:()=>fe,isFluxStandardAction:()=>Wt,isFulfilled:()=>va,isImmutableDefault:()=>Yt,isPending:()=>ba,isPlain:()=>ea,isRejected:()=>ya,isRejectedWithValue:()=>ja,lruMemoize:()=>ot,miniSerializeError:()=>_a,nanoid:()=>wa,original:()=>ve,prepareAutoBatched:()=>ia,removeListener:()=>Ar,unwrapResult:()=>za,weakMapMemoize:()=>dt}),Mt(Bt,Ot=ue,"default"),Pt&&Mt(Pt,Ot,"default");var Lt=(...e)=>{const t=ut(...e),a=Object.assign((...e)=>{const a=t(...e),r=(e,...t)=>a(fe(e)?Ze(e):e,...t);return Object.assign(r,a),r},{withTypes:()=>a});return a},$t=Lt(dt),Ft="undefined"!=typeof window&&window.__REDUX_DEVTOOLS_EXTENSION_COMPOSE__?window.__REDUX_DEVTOOLS_EXTENSION_COMPOSE__:function(){if(0!==arguments.length)return"object"==typeof arguments[0]?ce:ce.apply(null,arguments)},Vt=e=>e&&"function"==typeof e.match;function Ut(e,t){function a(...a){if(t){let r=t(...a);if(!r)throw new Error(Ir(0));return o(o({type:e,payload:r.payload},"meta"in r&&{meta:r.meta}),"error"in r&&{error:r.error})}return{type:e,payload:a[0]}}return a.toString=()=>`${e}`,a.type=e,a.match=t=>de(t)&&t.type===e,a}function Qt(e){return"function"==typeof e&&"type"in e&&Vt(e)}function Wt(e){return de(e)&&Object.keys(e).every(Ht)}function Ht(e){return["type","payload","error","meta"].indexOf(e)>-1}function Gt(e={}){return()=>e=>t=>e(t)}var Xt=class e extends Array{constructor(...t){super(...t),Object.setPrototypeOf(this,e.prototype)}static get[Symbol.species](){return e}concat(...e){return super.concat.apply(this,e)}prepend(...t){return 1===t.length&&Array.isArray(t[0])?new e(...t[0].concat(this)):new e(...t.concat(this))}};function Kt(e){return be(e)?at(e,()=>{}):e}function Jt(e,t,a){return e.has(t)?e.get(t):e.set(t,a(t)).get(t)}function Yt(e){return"object"!=typeof e||null==e||Object.isFrozen(e)}function Zt(e={}){return()=>e=>t=>e(t)}function ea(e){const t=typeof e;return null==e||"string"===t||"boolean"===t||"number"===t||Array.isArray(e)||se(e)}function ta(e,t="",a=ea,r,s=[],n){let i;if(!a(e))return{keyPath:t||"<root>",value:e};if("object"!=typeof e||null===e)return!1;if(null==n?void 0:n.has(e))return!1;const o=null!=r?r(e):Object.entries(e),c=s.length>0;for(const[l,d]of o){const e=t?t+"."+l:l;if(c){if(s.some(t=>t instanceof RegExp?t.test(e):e===t))continue}if(!a(d))return{keyPath:e,value:d};if("object"==typeof d&&(i=ta(d,e,a,r,s,n),i))return i}return n&&aa(e)&&n.add(e),!1}function aa(e){if(!Object.isFrozen(e))return!1;for(const t of Object.values(e))if("object"==typeof t&&null!==t&&!aa(t))return!1;return!0}function ra(e={}){return()=>e=>t=>e(t)}var sa=()=>function(e){const{thunk:t=!0,immutableCheck:a=!0,serializableCheck:r=!0,actionCreatorCheck:s=!0}=null!=e?e:{};let n=new Xt;return t&&("boolean"==typeof t?n.push(ht):n.push(gt(t.extraArgument))),n},na="RTK_autoBatch",ia=()=>e=>({payload:e,meta:{[na]:!0}}),oa=e=>t=>{setTimeout(t,e)},ca=(e={type:"raf"})=>t=>(...a)=>{const r=t(...a);let s=!0,n=!1,i=!1;const o=new Set,c="tick"===e.type?queueMicrotask:"raf"===e.type?"undefined"!=typeof window&&window.requestAnimationFrame?window.requestAnimationFrame:oa(10):"callback"===e.type?e.queueNotification:oa(e.timeout),l=()=>{i=!1,n&&(n=!1,o.forEach(e=>e()))};return Object.assign({},r,{subscribe(e){const t=r.subscribe(()=>s&&e());return o.add(e),()=>{t(),o.delete(e)}},dispatch(e){var t;try{return s=!(null==(t=null==e?void 0:e.meta)?void 0:t[na]),n=!s,n&&(i||(i=!0,c(l))),r.dispatch(e)}finally{s=!0}}})},la=e=>function(t){const{autoBatch:a=!0}=null!=t?t:{};let r=new Xt(e);return a&&r.push(ca("object"==typeof a?a:void 0)),r};function da(e){const t=sa(),{reducer:a,middleware:r,devTools:s=!0,duplicateMiddlewareCheck:n=!0,preloadedState:i,enhancers:c}=e||{};let l,d;if("function"==typeof a)l=a;else{if(!se(a))throw new Error(Ir(1));l=ie(a)}d="function"==typeof r?r(t):t();let u=ce;s&&(u=Ft(o({trace:!1},"object"==typeof s&&s)));const p=le(...d),m=la(p);return ne(l,i,u(..."function"==typeof c?c(m):m()))}function ua(e){const t={},a=[];let r;const s={addCase(e,a){const r="string"==typeof e?e:e.type;if(!r)throw new Error(Ir(28));if(r in t)throw new Error(Ir(29));return t[r]=a,s},addMatcher:(e,t)=>(a.push({matcher:e,reducer:t}),s),addDefaultCase:e=>(r=e,s)};return e(s),[t,a,r]}function pa(e,t){let a,[r,s,n]=ua(t);if("function"==typeof e)a=()=>Kt(e());else{const t=Kt(e);a=()=>t}function i(e=a(),t){let i=[r[t.type],...s.filter(({matcher:e})=>e(t)).map(({reducer:e})=>e)];return 0===i.filter(e=>!!e).length&&(i=[n]),i.reduce((e,a)=>{if(a){if(fe(e)){const r=a(e,t);return void 0===r?e:r}if(be(e))return at(e,e=>a(e,t));{const r=a(e,t);if(void 0===r){if(null===e)return e;throw Error("A case reducer on a non-draftable value must not return undefined")}return r}}return e},e)}return i.getInitialState=a,i}var ma=(e,t)=>Vt(e)?e.match(t):e(t);function xa(...e){return t=>e.some(e=>ma(e,t))}function ha(...e){return t=>e.every(e=>ma(e,t))}function ga(e,t){if(!e||!e.meta)return!1;const a="string"==typeof e.meta.requestId,r=t.indexOf(e.meta.requestStatus)>-1;return a&&r}function fa(e){return"function"==typeof e[0]&&"pending"in e[0]&&"fulfilled"in e[0]&&"rejected"in e[0]}function ba(...e){return 0===e.length?e=>ga(e,["pending"]):fa(e)?xa(...e.map(e=>e.pending)):ba()(e[0])}function ya(...e){return 0===e.length?e=>ga(e,["rejected"]):fa(e)?xa(...e.map(e=>e.rejected)):ya()(e[0])}function ja(...e){const t=e=>e&&e.meta&&e.meta.rejectedWithValue;return 0===e.length||fa(e)?ha(ya(...e),t):ja()(e[0])}function va(...e){return 0===e.length?e=>ga(e,["fulfilled"]):fa(e)?xa(...e.map(e=>e.fulfilled)):va()(e[0])}function Na(...e){return 0===e.length?e=>ga(e,["pending","fulfilled","rejected"]):fa(e)?xa(...e.flatMap(e=>[e.pending,e.rejected,e.fulfilled])):Na()(e[0])}var wa=(e=21)=>{let t="",a=e;for(;a--;)t+="ModuleSymbhasOwnPr-0123456789ABCDEFGHNRVfgctiUvz_KqYTJkLxpZXIjQW"[64*Math.random()|0];return t},Aa=["name","message","stack","code"],Ea=class{constructor(e,t){d(this,"_type"),this.payload=e,this.meta=t}},Ca=class{constructor(e,t){d(this,"_type"),this.payload=e,this.meta=t}},_a=e=>{if("object"==typeof e&&null!==e){const t={};for(const a of Aa)"string"==typeof e[a]&&(t[a]=e[a]);return t}return{message:String(e)}},Sa="External signal was aborted",Ta=(()=>{function e(e,t,a){const r=Ut(e+"/fulfilled",(e,t,a,r)=>({payload:e,meta:c(o({},r||{}),{arg:a,requestId:t,requestStatus:"fulfilled"})})),s=Ut(e+"/pending",(e,t,a)=>({payload:void 0,meta:c(o({},a||{}),{arg:t,requestId:e,requestStatus:"pending"})})),n=Ut(e+"/rejected",(e,t,r,s,n)=>({payload:s,error:(a&&a.serializeError||_a)(e||"Rejected"),meta:c(o({},n||{}),{arg:r,requestId:t,rejectedWithValue:!!s,requestStatus:"rejected",aborted:"AbortError"===(null==e?void 0:e.name),condition:"ConditionError"===(null==e?void 0:e.name)})}));return Object.assign(function(e,{signal:i}={}){return(o,c,l)=>{const d=(null==a?void 0:a.idGenerator)?a.idGenerator(e):wa(),p=new AbortController;let m,x;function h(e){x=e,p.abort()}i&&(i.aborted?h(Sa):i.addEventListener("abort",()=>h(Sa),{once:!0}));const g=function(){return u(this,null,function*(){var i,u;let g;try{let n=null==(i=null==a?void 0:a.condition)?void 0:i.call(a,e,{getState:c,extra:l});if(null!==(f=n)&&"object"==typeof f&&"function"==typeof f.then&&(n=yield n),!1===n||p.signal.aborted)throw{name:"ConditionError",message:"Aborted due to condition callback returning false."};const b=new Promise((e,t)=>{m=()=>{t({name:"AbortError",message:x||"Aborted"})},p.signal.addEventListener("abort",m)});o(s(d,e,null==(u=null==a?void 0:a.getPendingMeta)?void 0:u.call(a,{requestId:d,arg:e},{getState:c,extra:l}))),g=yield Promise.race([b,Promise.resolve(t(e,{dispatch:o,getState:c,extra:l,requestId:d,signal:p.signal,abort:h,rejectWithValue:(e,t)=>new Ea(e,t),fulfillWithValue:(e,t)=>new Ca(e,t)})).then(t=>{if(t instanceof Ea)throw t;return t instanceof Ca?r(t.payload,d,e,t.meta):r(t,d,e)})])}catch(b){g=b instanceof Ea?n(null,d,e,b.payload,b.meta):n(b,d,e)}finally{m&&p.signal.removeEventListener("abort",m)}var f;return a&&!a.dispatchConditionRejection&&n.match(g)&&g.meta.condition||o(g),g})}();return Object.assign(g,{abort:h,requestId:d,arg:e,unwrap:()=>g.then(za)})}},{pending:s,rejected:n,fulfilled:r,settled:xa(n,r),typePrefix:e})}return e.withTypes=()=>e,e})();function za(e){if(e.meta&&e.meta.rejectedWithValue)throw e.payload;if(e.error)throw e.error;return e.payload}var qa=Symbol.for("rtk-slice-createasyncthunk"),Oa={[qa]:Ta},Pa=(e=>(e.reducer="reducer",e.reducerWithPrepare="reducerWithPrepare",e.asyncThunk="asyncThunk",e))(Pa||{});function Da(e,t){return`${e}/${t}`}function ka({creators:e}={}){var t;const a=null==(t=null==e?void 0:e.asyncThunk)?void 0:t[qa];return function(e){const{name:t,reducerPath:r=t}=e;if(!t)throw new Error(Ir(11));const s=("function"==typeof e.reducers?e.reducers(function(){function e(e,t){return o({_reducerDefinitionType:"asyncThunk",payloadCreator:e},t)}return e.withTypes=()=>e,{reducer:e=>Object.assign({[e.name]:(...t)=>e(...t)}[e.name],{_reducerDefinitionType:"reducer"}),preparedReducer:(e,t)=>({_reducerDefinitionType:"reducerWithPrepare",prepare:e,reducer:t}),asyncThunk:e}}()):e.reducers)||{},n=Object.keys(s),i={sliceCaseReducersByName:{},sliceCaseReducersByType:{},actionCreators:{},sliceMatchers:[]},d={addCase(e,t){const a="string"==typeof e?e:e.type;if(!a)throw new Error(Ir(12));if(a in i.sliceCaseReducersByType)throw new Error(Ir(13));return i.sliceCaseReducersByType[a]=t,d},addMatcher:(e,t)=>(i.sliceMatchers.push({matcher:e,reducer:t}),d),exposeAction:(e,t)=>(i.actionCreators[e]=t,d),exposeCaseReducer:(e,t)=>(i.sliceCaseReducersByName[e]=t,d)};function u(){const[t={},a=[],r]="function"==typeof e.extraReducers?ua(e.extraReducers):[e.extraReducers],s=o(o({},t),i.sliceCaseReducersByType);return pa(e.initialState,e=>{for(let t in s)e.addCase(t,s[t]);for(let t of i.sliceMatchers)e.addMatcher(t.matcher,t.reducer);for(let t of a)e.addMatcher(t.matcher,t.reducer);r&&e.addDefaultCase(r)})}n.forEach(r=>{const n=s[r],i={reducerName:r,type:Da(t,r),createNotation:"function"==typeof e.reducers};!function(e){return"asyncThunk"===e._reducerDefinitionType}(n)?function({type:e,reducerName:t,createNotation:a},r,s){let n,i;if("reducer"in r){if(a&&!function(e){return"reducerWithPrepare"===e._reducerDefinitionType}(r))throw new Error(Ir(17));n=r.reducer,i=r.prepare}else n=r;s.addCase(e,n).exposeCaseReducer(t,n).exposeAction(t,i?Ut(e,i):Ut(e))}(i,n,d):function({type:e,reducerName:t},a,r,s){if(!s)throw new Error(Ir(18));const{payloadCreator:n,fulfilled:i,pending:o,rejected:c,settled:l,options:d}=a,u=s(e,n,d);r.exposeAction(t,u),i&&r.addCase(u.fulfilled,i);o&&r.addCase(u.pending,o);c&&r.addCase(u.rejected,c);l&&r.addMatcher(u.settled,l);r.exposeCaseReducer(t,{fulfilled:i||Ma,pending:o||Ma,rejected:c||Ma,settled:l||Ma})}(i,n,d,a)});const p=e=>e,m=new Map,x=new WeakMap;let h;function g(e,t){return h||(h=u()),h(e,t)}function f(){return h||(h=u()),h.getInitialState()}function b(t,a=!1){function r(e){let s=e[t];return void 0===s&&a&&(s=Jt(x,r,f)),s}function s(t=p){const r=Jt(m,a,()=>new WeakMap);return Jt(r,t,()=>{var r;const s={};for(const[n,i]of Object.entries(null!=(r=e.selectors)?r:{}))s[n]=Ra(i,t,()=>Jt(x,t,f),a);return s})}return{reducerPath:t,getSelectors:s,get selectors(){return s(r)},selectSlice:r}}const y=c(o({name:t,reducer:g,actions:i.actionCreators,caseReducers:i.sliceCaseReducersByName,getInitialState:f},b(r)),{injectInto(e,t={}){var a=t,{reducerPath:s}=a,n=l(a,["reducerPath"]);const i=null!=s?s:r;return e.inject({reducerPath:i,reducer:g},n),o(o({},y),b(i,!0))}});return y}}function Ra(e,t,a,r){function s(s,...n){let i=t(s);return void 0===i&&r&&(i=a()),e(i,...n)}return s.unwrapped=e,s}var Ia=ka();function Ma(){}var Ba=fe;function La(e){const t=$a((t,a)=>e(a));return function(e){return t(e,void 0)}}function $a(e){return function(t,a){const r=t=>{Wt(a)?e(a.payload,t):e(a,t)};return Ba(t)?(r(t),t):at(t,r)}}function Fa(e,t){return t(e)}function Va(e){return Array.isArray(e)||(e=Object.values(e)),e}function Ua(e){return fe(e)?Ze(e):e}function Qa(e,t,a){e=Va(e);const r=Ua(a.ids),s=new Set(r),n=[],i=new Set([]),o=[];for(const c of e){const e=Fa(c,t);s.has(e)||i.has(e)?o.push({id:e,changes:c}):(i.add(e),n.push(c))}return[n,o,r]}function Wa(e){function t(t,a){const r=Fa(t,e);r in a.entities||(a.ids.push(r),a.entities[r]=t)}function a(e,a){e=Va(e);for(const r of e)t(r,a)}function r(t,a){const r=Fa(t,e);r in a.entities||a.ids.push(r),a.entities[r]=t}function s(e,t){let a=!1;e.forEach(e=>{e in t.entities&&(delete t.entities[e],a=!0)}),a&&(t.ids=t.ids.filter(e=>e in t.entities))}function n(t,a){const r={},s={};t.forEach(e=>{var t;e.id in a.entities&&(s[e.id]={id:e.id,changes:o(o({},null==(t=s[e.id])?void 0:t.changes),e.changes)})});if((t=Object.values(s)).length>0){const s=t.filter(t=>function(t,a,r){const s=r.entities[a.id];if(void 0===s)return!1;const n=Object.assign({},s,a.changes),i=Fa(n,e),o=i!==a.id;return o&&(t[a.id]=i,delete r.entities[a.id]),r.entities[i]=n,o}(r,t,a)).length>0;s&&(a.ids=Object.values(a.entities).map(t=>Fa(t,e)))}}function i(t,r){const[s,i]=Qa(t,e,r);a(s,r),n(i,r)}return{removeAll:La(function(e){Object.assign(e,{ids:[],entities:{}})}),addOne:$a(t),addMany:$a(a),setOne:$a(r),setMany:$a(function(e,t){e=Va(e);for(const a of e)r(a,t)}),setAll:$a(function(e,t){e=Va(e),t.ids=[],t.entities={},a(e,t)}),updateOne:$a(function(e,t){return n([e],t)}),updateMany:$a(n),upsertOne:$a(function(e,t){return i([e],t)}),upsertMany:$a(i),removeOne:$a(function(e,t){return s([e],t)}),removeMany:$a(s)}}function Ha(e,t,a){const r=function(e,t,a){let r=0,s=e.length;for(;r<s;){let n=r+s>>>1;a(t,e[n])>=0?r=n+1:s=n}return r}(e,t,a);return e.splice(r,0,t),e}function Ga(e={}){const{selectId:t,sortComparer:a}=o({sortComparer:!1,selectId:e=>e.id},e),r=a?function(e,t){const{removeOne:a,removeMany:r,removeAll:s}=Wa(e);function n(t,a,r){t=Va(t);const s=new Set(null!=r?r:Ua(a.ids)),n=t.filter(t=>!s.has(Fa(t,e)));0!==n.length&&l(a,n)}function i(t,a){if(0!==(t=Va(t)).length){for(const r of t)delete a.entities[e(r)];l(a,t)}}function o(t,a){let r=!1,s=!1;for(let n of t){const t=a.entities[n.id];if(!t)continue;r=!0,Object.assign(t,n.changes);const i=e(t);if(n.id!==i){s=!0,delete a.entities[n.id];const e=a.ids.indexOf(n.id);a.ids[e]=i,a.entities[i]=t}}r&&l(a,[],r,s)}function c(t,a){const[r,s,i]=Qa(t,e,a);r.length&&n(r,a,i),s.length&&o(s,a)}const l=(a,r,s,n)=>{const i=Ua(a.entities),o=Ua(a.ids),c=a.entities;let l=o;n&&(l=new Set(o));let d=[];for(const e of l){const t=i[e];t&&d.push(t)}const u=0===d.length;for(const m of r)c[e(m)]=m,u||Ha(d,m,t);u?d=r.slice().sort(t):s&&d.sort(t);const p=d.map(e);(function(e,t){if(e.length!==t.length)return!1;for(let a=0;a<e.length;a++)if(e[a]!==t[a])return!1;return!0})(o,p)||(a.ids=p)};return{removeOne:a,removeMany:r,removeAll:s,addOne:$a(function(e,t){return n([e],t)}),updateOne:$a(function(e,t){return o([e],t)}),upsertOne:$a(function(e,t){return c([e],t)}),setOne:$a(function(e,t){return i([e],t)}),setMany:$a(i),setAll:$a(function(e,t){e=Va(e),t.entities={},t.ids=[],n(e,t,[])}),addMany:$a(n),updateMany:$a(o),upsertMany:$a(c)}}(t,a):Wa(t),s=function(e){return{getInitialState:function(t={},a){const r=Object.assign({ids:[],entities:{}},t);return a?e.setAll(r,a):r}}}(r),n={getSelectors:function(e,t={}){const{createSelector:a=$t}=t,r=e=>e.ids,s=e=>e.entities,n=a(r,s,(e,t)=>e.map(e=>t[e])),i=(e,t)=>t,o=(e,t)=>e[t],c=a(r,e=>e.length);if(!e)return{selectIds:r,selectEntities:s,selectAll:n,selectTotal:c,selectById:a(s,i,o)};const l=a(e,s);return{selectIds:a(e,r),selectEntities:l,selectAll:a(e,n),selectTotal:a(e,c),selectById:a(l,i,o)}}};return o(o(o({selectId:t,sortComparer:a},s),n),r)}var Xa="listener",Ka="completed",Ja="cancelled",Ya=`task-${Ja}`,Za=`task-${Ka}`,er=`${Xa}-${Ja}`,tr=`${Xa}-${Ka}`,ar=class{constructor(e){d(this,"name","TaskAbortError"),d(this,"message"),this.code=e,this.message=`task ${Ja} (reason: ${e})`}},rr=(e,t)=>{if("function"!=typeof e)throw new TypeError(Ir(32))},sr=()=>{},nr=(e,t=sr)=>(e.catch(t),e),ir=(e,t)=>(e.addEventListener("abort",t,{once:!0}),()=>e.removeEventListener("abort",t)),or=(e,t)=>{const a=e.signal;a.aborted||("reason"in a||Object.defineProperty(a,"reason",{enumerable:!0,value:t,configurable:!0,writable:!0}),e.abort(t))},cr=e=>{if(e.aborted){const{reason:t}=e;throw new ar(t)}};function lr(e,t){let a=sr;return new Promise((r,s)=>{const n=()=>s(new ar(e.reason));e.aborted?n():(a=ir(e,n),t.finally(()=>a()).then(r,s))}).finally(()=>{a=sr})}var dr=e=>t=>nr(lr(e,t).then(t=>(cr(e),t))),ur=e=>{const t=dr(e);return e=>t(new Promise(t=>setTimeout(t,e)))},{assign:pr}=Object,mr={},xr="listenerMiddleware",hr=(e,t)=>(a,r)=>{rr(a);const s=new AbortController;var n;n=s,ir(e,()=>or(n,e.reason));const i=(o=()=>u(void 0,null,function*(){cr(e),cr(s.signal);const t=yield a({pause:dr(s.signal),delay:ur(s.signal),signal:s.signal});return cr(s.signal),t}),c=()=>or(s,Za),u(void 0,null,function*(){try{return yield Promise.resolve(),{status:"ok",value:yield o()}}catch(e){return{status:e instanceof ar?"cancelled":"rejected",error:e}}finally{null==c||c()}}));var o,c;return(null==r?void 0:r.autoJoin)&&t.push(i.catch(sr)),{result:dr(e)(i),cancel(){or(s,Ya)}}},gr=(e,t)=>(a,r)=>nr(((a,r)=>u(void 0,null,function*(){cr(t);let s=()=>{};const n=[new Promise((t,r)=>{let n=e({predicate:a,effect:(e,a)=>{a.unsubscribe(),t([e,a.getState(),a.getOriginalState()])}});s=()=>{n(),r()}})];null!=r&&n.push(new Promise(e=>setTimeout(e,r,null)));try{const e=yield lr(t,Promise.race(n));return cr(t),e}finally{s()}}))(a,r)),fr=e=>{let{type:t,actionCreator:a,matcher:r,predicate:s,effect:n}=e;if(t)s=Ut(t).match;else if(a)t=a.type,s=a.match;else if(r)s=r;else if(!s)throw new Error(Ir(21));return rr(n),{predicate:s,type:t,effect:n}},br=pr(e=>{const{type:t,predicate:a,effect:r}=fr(e);return{id:wa(),effect:r,type:t,predicate:a,pending:new Set,unsubscribe:()=>{throw new Error(Ir(22))}}},{withTypes:()=>br}),yr=(e,t)=>{const{type:a,effect:r,predicate:s}=fr(t);return Array.from(e.values()).find(e=>("string"==typeof a?e.type===a:e.predicate===s)&&e.effect===r)},jr=e=>{e.pending.forEach(e=>{or(e,er)})},vr=(e,t,a)=>{try{e(t,a)}catch(r){setTimeout(()=>{throw r},0)}},Nr=pr(Ut(`${xr}/add`),{withTypes:()=>Nr}),wr=Ut(`${xr}/removeAll`),Ar=pr(Ut(`${xr}/remove`),{withTypes:()=>Ar}),Er=(...e)=>{},Cr=(e={})=>{const t=new Map,{extra:a,onError:r=Er}=e;rr(r);const s=e=>{var a;return(e=>(e.unsubscribe=()=>t.delete(e.id),t.set(e.id,e),t=>{e.unsubscribe(),(null==t?void 0:t.cancelActive)&&jr(e)}))(null!=(a=yr(t,e))?a:br(e))};pr(s,{withTypes:()=>s});const n=e=>{const a=yr(t,e);return a&&(a.unsubscribe(),e.cancelActive&&jr(a)),!!a};pr(n,{withTypes:()=>n});const i=(e,n,i,o)=>u(void 0,null,function*(){const c=new AbortController,l=gr(s,c.signal),d=[];try{e.pending.add(c),yield Promise.resolve(e.effect(n,pr({},i,{getOriginalState:o,condition:(e,t)=>l(e,t).then(Boolean),take:l,delay:ur(c.signal),pause:dr(c.signal),extra:a,signal:c.signal,fork:hr(c.signal,d),unsubscribe:e.unsubscribe,subscribe:()=>{t.set(e.id,e)},cancelActiveListeners:()=>{e.pending.forEach((e,t,a)=>{e!==c&&(or(e,er),a.delete(e))})},cancel:()=>{or(c,er),e.pending.delete(c)},throwIfCancelled:()=>{cr(c.signal)}})))}catch(u){u instanceof ar||vr(r,u,{raisedBy:"effect"})}finally{yield Promise.all(d),or(c,tr),e.pending.delete(c)}}),o=(e=>()=>{e.forEach(jr),e.clear()})(t);return{middleware:e=>a=>c=>{if(!de(c))return a(c);if(Nr.match(c))return s(c.payload);if(wr.match(c))return void o();if(Ar.match(c))return n(c.payload);let l=e.getState();const d=()=>{if(l===mr)throw new Error(Ir(23));return l};let u;try{if(u=a(c),t.size>0){const a=e.getState(),s=Array.from(t.values());for(const t of s){let s=!1;try{s=t.predicate(c,a,l)}catch(p){s=!1,vr(r,p,{raisedBy:"predicate"})}s&&i(t,c,e,d)}}}finally{l=mr}return u},startListening:s,stopListening:n,clearListeners:o}},_r=e=>({middleware:e,applied:new Map}),Sr=()=>{const e=wa(),t=new Map,a=Object.assign(Ut("dynamicMiddleware/add",(...t)=>({payload:t,meta:{instanceId:e}})),{withTypes:()=>a}),r=Object.assign(function(...e){e.forEach(e=>{Jt(t,e,_r)})},{withTypes:()=>r}),s=ha(a,(e=>t=>{var a;return(null==(a=null==t?void 0:t.meta)?void 0:a.instanceId)===e})(e));return{middleware:e=>a=>n=>s(n)?(r(...n.payload),e.dispatch):(e=>ce(...Array.from(t.values()).map(t=>Jt(t.applied,e,t.middleware))))(e)(a)(n),addMiddleware:r,withMiddleware:a,instanceId:e}},Tr=e=>e.flatMap(e=>{return"reducerPath"in(t=e)&&"string"==typeof t.reducerPath?[[e.reducerPath,e.reducer]]:Object.entries(e);var t}),zr=Symbol.for("rtk-state-proxy-original"),qr=new WeakMap,Or=(e,t,a)=>Jt(qr,e,()=>new Proxy(e,{get:(e,r,s)=>{if(r===zr)return e;const n=Reflect.get(e,r,s);if(void 0===n){const e=a[r];if(void 0!==e)return e;const s=t[r];if(s){const e=s(void 0,{type:wa()});if(void 0===e)throw new Error(Ir(24));return a[r]=e,e}}return n}})),Pr=e=>{if(!(t=e)||!t[zr])throw new Error(Ir(25));var t;return e[zr]},Dr={},kr=(e=Dr)=>e;function Rr(...e){const t=Object.fromEntries(Tr(e)),a=()=>Object.keys(t).length?ie(t):kr;let r=a();function s(e,t){return r(e,t)}s.withLazyLoadedSlices=()=>s;const n={},i=Object.assign(function(e,a){return function(r,...s){return e(Or(a?a(r,...s):r,t,n),...s)}},{original:Pr});return Object.assign(s,{inject:(e,i={})=>{const{reducerPath:o,reducer:c}=e,l=t[o];return!i.overrideExisting&&l&&l!==c||(i.overrideExisting&&l!==c&&delete n[o],t[o]=c,r=a()),s},selector:i})}function Ir(e){return`Minified Redux Toolkit error #${e}; visit https://redux-toolkit.js.org/Errors?code=${e} for the full message or use the non-minified dev environment for full errors. `}var Mr=Ut("__rtkq/focused"),Br=Ut("__rtkq/unfocused"),Lr=Ut("__rtkq/online"),$r=Ut("__rtkq/offline"),Fr=!1;const Vr={currentTest:null,testResults:null,answeredQuestions:{},timeRemaining:0,testStarted:!1,testCompleted:!1,loading:!1,error:null},Ur=Tt({name:"test",initialState:Vr,reducers:{setCurrentTest:(e,t)=>{var a;e.currentTest=t.payload,e.answeredQuestions={},e.testStarted=!1,e.testCompleted=!1,e.timeRemaining=(null==(a=t.payload)?void 0:a.duration)?60*t.payload.duration:0},startTest:e=>{e.testStarted=!0},updateTimeRemaining:(e,t)=>{e.timeRemaining=t.payload},answerQuestion:(e,t)=>{const{questionId:a,answerId:r}=t.payload;e.answeredQuestions[a]=r},completeTest:e=>{e.testCompleted=!0},setTestResults:(e,t)=>{e.testResults=t.payload},setLoading:(e,t)=>{e.loading=t.payload},setError:(e,t)=>{e.error=t.payload},resetTestState:e=>Vr}});Ur.actions;const Qr=function(e){const t=function(e){const{thunk:t=!0,immutableCheck:a=!0,serializableCheck:r=!0,actionCreatorCheck:s=!0}=null!=e?e:{};let n=new yt;return t&&("boolean"==typeof t?n.push(ht):n.push(gt(t.extraArgument))),n},{reducer:a,middleware:r,devTools:s=!0,duplicateMiddlewareCheck:n=!0,preloadedState:i,enhancers:c}=e||{};let l,d;if("function"==typeof a)l=a;else{if(!se(a))throw new Error(qt(1));l=ie(a)}d="function"==typeof r?r(t):t();let u=ce;s&&(u=ft(o({trace:!1},"object"==typeof s&&s)));const p=le(...d),m=wt(p);return ne(l,i,u(..."function"==typeof c?c(m):m()))}({reducer:{test:Ur.reducer},middleware:e=>e({serializableCheck:{ignoredActions:["persist/PERSIST"]}}),devTools:!1});var Wr,Hr;Wr=Qr.dispatch,Hr?Hr(Wr,{onFocus:Mr,onFocusLost:Br,onOffline:$r,onOnline:Lr}):function(){const e=()=>Wr(Mr()),t=()=>Wr(Lr()),a=()=>Wr($r()),r=()=>{"visible"===window.document.visibilityState?e():Wr(Br())};Fr||"undefined"!=typeof window&&window.addEventListener&&(window.addEventListener("visibilitychange",r,!1),window.addEventListener("focus",e,!1),window.addEventListener("online",t,!1),window.addEventListener("offline",a,!1),Fr=!0)}(),Qr.getState,Qr.dispatch;const Gr=({children:e})=>p.jsx("div",{className:"page-content opacity-100 transition-opacity duration-200 ease-in-out",children:e}),Xr=({className:e=""})=>p.jsxs("div",{className:`animated-title-container ${e}`,children:[p.jsxs("div",{className:"relative inline-flex items-center",children:[p.jsx("h1",{className:"animated-title text-2xl font-bold",children:p.jsx("span",{className:"title-complete",children:"BAT-7 Batería de Aptitudes"})}),p.jsx("div",{className:"absolute inset-0 bg-gradient-to-r from-blue-400/20 via-purple-400/20 to-blue-600/20 rounded-lg blur-sm animate-pulse"}),p.jsx("div",{className:"absolute -top-1 -right-1 w-2 h-2 bg-yellow-400 rounded-full animate-ping"}),p.jsx("div",{className:"absolute -bottom-1 -left-1 w-1.5 h-1.5 bg-blue-400 rounded-full animate-bounce delay-300"})]}),p.jsx("style",{jsx:!0,children:"\n        .animated-title-container {\n          position: relative;\n          display: inline-block;\n        }\n        \n        .animated-title {\n          position: relative;\n          z-index: 10;\n          background: linear-gradient(\n            45deg,\n            #1d387a 0%,\n            #FFFFFF 25%,\n            #1d387a 50%,\n            #afcbc4 75%,\n            #1d387a 100%\n          );\n          background-size: 300% 300%;\n          background-clip: text;\n          -webkit-background-clip: text;\n          -webkit-text-fill-color: transparent;\n          animation: gradientShift 3s ease-in-out infinite, titleEntrance 1.5s ease-out;\n          text-shadow: 0 0 20px rgba(59, 130, 246, 0.3);\n          font-weight: 800;\n          letter-spacing: 1px;\n        }\n\n        .title-complete {\n          display: inline-block;\n          animation: titleSlideIn 1.5s ease-out;\n        }\n        \n        @keyframes gradientShift {\n          0% {\n            background-position: 0% 50%;\n          }\n          50% {\n            background-position: 100% 50%;\n          }\n          100% {\n            background-position: 0% 50%;\n          }\n        }\n\n        @keyframes titleEntrance {\n          0% {\n            transform: translateY(-30px) scale(0.8);\n            opacity: 0;\n            filter: blur(10px);\n          }\n          50% {\n            transform: translateY(-10px) scale(0.9);\n            opacity: 0.7;\n            filter: blur(5px);\n          }\n          100% {\n            transform: translateY(0) scale(1);\n            opacity: 1;\n            filter: blur(0px);\n          }\n        }\n\n        @keyframes titleSlideIn {\n          0% {\n            transform: translateX(-100px);\n            opacity: 0;\n          }\n          60% {\n            transform: translateX(10px);\n            opacity: 0.8;\n          }\n          100% {\n            transform: translateX(0);\n            opacity: 1;\n          }\n        }\n        \n        /* Efectos hover */\n        .animated-title-container:hover .animated-title {\n          animation-duration: 1.5s;\n          transform: scale(1.05);\n          transition: transform 0.4s ease;\n          text-shadow: 0 0 40px rgba(100, 138, 199, 0.6);\n        }\n\n        .animated-title-container:hover .title-complete {\n          animation: titlePulse 0.6s ease-in-out;\n        }\n\n        @keyframes titlePulse {\n          0%, 100% {\n            transform: scale(1);\n          }\n          50% {\n            transform: scale(1.02);\n          }\n        }\n        \n        /* Responsive */\n        @media (max-width: 768px) {\n          .animated-title {\n            font-size: 1.5rem;\n          }\n        }\n\n        @media (max-width: 480px) {\n          .animated-title {\n            font-size: 1.25rem;\n          }\n\n          .title-complete {\n            text-align: center;\n            line-height: 1.2;\n          }\n        }\n\n        @media (max-width: 360px) {\n          .animated-title {\n            font-size: 1rem;\n          }\n        }\n      "})]}),Kr=({isOpen:e,toggleSidebar:t,onLogout:a})=>{const r=C(),[s,n]=N.useState(()=>{const e=localStorage.getItem("sidebarFavorites");return e?JSON.parse(e):{dashboard:!1,home:!1,patients:!1,tests:!1,reports:!1,administration:!1,settings:!1,help:!1}});N.useEffect(()=>{localStorage.setItem("sidebarFavorites",JSON.stringify(s))},[s]);const i=(e,t)=>{t.preventDefault(),t.stopPropagation(),n(t=>c(o({},t),{[e]:!t[e]}))},l=e=>"/"===e?"/"===r.pathname:r.pathname===e||r.pathname.startsWith(e)&&(r.pathname.length===e.length||"/"===r.pathname[e.length]),d=[{title:"Navegación Principal",items:[{name:"Inicio",path:"/home",icon:"home",key:"home"},{name:"Pacientes",path:"/admin/patients",icon:"users",key:"patients"},{name:"Cuestionario",path:"/student/questionnaire",icon:"clipboard-list",key:"tests"},{name:"Resultados",path:"/admin/reports",icon:"chart-bar",key:"reports"}]},{title:"Administración",items:[{name:"Panel Admin",path:"/admin/administration",icon:"shield-alt",key:"administration"},{name:"Configuración",path:"/configuracion",icon:"cog",key:"settings"}]},{title:"Soporte",items:[{name:"Ayuda",path:"/help",icon:"question-circle",key:"help"}]}],u=d.flatMap(e=>e.items).filter(e=>s[e.key]);return p.jsxs("div",{className:"sidebar bg-[#121940] text-[#a4b1cd] fixed top-0 left-0 h-full z-50 transition-all duration-300 ease-in-out\n                     "+(e?"w-64":"w-[70px]"),children:[p.jsxs("div",{className:"sidebar-header p-5 flex justify-between items-center border-b border-opacity-10 border-white",children:[e&&p.jsxs("h1",{className:"sidebar-logo text-3xl font-bold text-white text-center flex-1",children:["Activatu",p.jsx("span",{className:"text-[#ffda0a]",children:"mente"})]}),p.jsx("button",{onClick:t,className:"collapse-button text-[#a4b1cd] cursor-pointer hover:text-white",title:e?"Contraer menú":"Expandir menú","aria-label":e?"Contraer menú":"Expandir menú",children:p.jsx("i",{className:"fas "+(e?"fa-chevron-left":"fa-chevron-right")})})]}),u.length>0&&p.jsxs("div",{className:"sidebar-section py-2 border-b border-opacity-10 border-white",children:[e&&p.jsx("h2",{className:"uppercase text-xs px-5 py-2 tracking-wider font-semibold text-gray-400",children:"FAVORITOS"}),p.jsx("ul",{className:"menu-list",children:u.map(t=>p.jsx("li",{className:"py-3 px-5 hover:bg-opacity-10 hover:bg-white transition-all duration-300 relative transform hover:translate-x-1\n                          "+(l(t.path)?"bg-[#ffda0a] bg-opacity-20 border-l-4 border-[#ffda0a] shadow-lg":""),children:p.jsxs("div",{className:"flex items-center justify-between w-full",children:[p.jsxs(A,{to:t.path,className:"flex items-center flex-grow transition-colors duration-200 "+(l(t.path)?"text-[#ffda0a] font-semibold":"text-[#a4b1cd] hover:text-white"),children:[p.jsx("i",{className:`fas fa-${t.icon} ${e?"mr-3":""} w-5 text-center transition-colors duration-200 ${l(t.path)?"text-[#ffda0a]":""}`}),e&&p.jsx("span",{children:t.name})]}),e&&p.jsx("span",{className:"text-[#ffda0a] cursor-pointer hover:scale-110 transition-transform duration-200",onClick:e=>i(t.key,e),title:"Quitar de favoritos",children:p.jsx("i",{className:"fas fa-star"})})]})},`fav-${t.key}`))})]}),p.jsx("div",{className:"sidebar-content py-2 flex-1 overflow-y-auto",children:d.map((t,a)=>p.jsxs("div",{className:"mb-4",children:[e&&p.jsx("div",{className:"px-5 py-2 mb-2",children:p.jsx("h3",{className:"section-title text-xs font-semibold text-gray-400 uppercase tracking-wider",children:t.title})}),!e&&a>0&&p.jsx("div",{className:"section-separator"}),p.jsx("ul",{className:"menu-list space-y-1",children:t.items.map(t=>p.jsx("li",{className:"menu-item mx-2 rounded-lg transition-all duration-300 relative transform hover:translate-x-1\n                            "+(l(t.path)?"active bg-[#ffda0a] bg-opacity-20 border-l-4 border-[#ffda0a] shadow-lg":"hover:bg-white hover:bg-opacity-10"),children:p.jsxs("div",{className:"flex items-center justify-between w-full px-3 py-3",children:[p.jsxs(A,{to:t.path,className:"flex items-center flex-grow transition-colors duration-200 "+(l(t.path)?"text-[#ffda0a] font-semibold":"text-[#a4b1cd] hover:text-white"),children:[p.jsx("i",{className:`menu-icon fas fa-${t.icon} ${e?"mr-4":"text-center"} w-5 transition-colors duration-200 ${l(t.path)?"text-[#ffda0a]":""}`}),e&&p.jsx("span",{className:"font-medium",children:t.name})]}),e&&p.jsx("span",{className:"favorite-star cursor-pointer hover:text-[#ffda0a] transition-all duration-200 ml-2 "+(s[t.key]?"active text-[#ffda0a]":"text-gray-400"),onClick:e=>i(t.key,e),title:s[t.key]?"Quitar de favoritos":"Añadir a favoritos",children:p.jsx("i",{className:(s[t.key]?"fas":"far")+" fa-star text-sm"})})]})},t.name))})]},t.title))}),p.jsx("div",{className:"mt-auto p-5 border-t border-opacity-10 border-white",children:e?p.jsxs("button",{className:"logout-button flex items-center w-full text-gray-400 hover:text-red-400 cursor-pointer transition-all duration-200 hover:bg-red-500 hover:bg-opacity-10 rounded-lg p-3 border border-transparent hover:border-red-500 hover:border-opacity-30",onClick:a,"aria-label":"Cerrar sesión",children:[p.jsx("i",{className:"fas fa-door-open mr-3 transition-transform duration-200"}),p.jsx("span",{className:"font-medium",children:"Cerrar Sesión"})]}):p.jsx("button",{className:"logout-button flex justify-center w-full text-gray-400 hover:text-red-400 cursor-pointer transition-all duration-200 p-3 rounded-lg border border-transparent hover:border-red-500 hover:border-opacity-30 hover:bg-red-500 hover:bg-opacity-10",onClick:a,title:"Cerrar Sesión","aria-label":"Cerrar sesión",children:p.jsx("i",{className:"fas fa-door-open transition-transform duration-200"})})})]})},Jr=()=>{const{user:e,isAdmin:t,isPsicologo:a,isCandidato:r,logout:s}=m(),[n,i]=N.useState(!0),[o,c]=N.useState(!1),l=N.useRef(null),d=w(),x=a,h=e?`${e.nombre||""} ${e.apellido||""}`.trim():"",g=null==e?void 0:e.email,f=()=>u(void 0,null,function*(){try{yield s(),d("/login")}catch(e){window.location.href="/login"}});return N.useEffect(()=>{const e=e=>{l.current&&!l.current.contains(e.target)&&c(!1)};return document.addEventListener("mousedown",e),()=>{document.removeEventListener("mousedown",e)}},[]),p.jsxs("div",{className:"min-h-screen bg-gray-50 flex",children:[p.jsx(Kr,{isOpen:n,toggleSidebar:()=>{i(!n)},onLogout:f}),p.jsxs("div",{className:"flex-1 transition-all duration-300 ease-in-out\n                    "+(n?"ml-64":"ml-[70px]"),children:[p.jsx("header",{className:"bg-white shadow-sm",children:p.jsx("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8",children:p.jsxs("div",{className:"flex justify-between h-16 items-center",children:[p.jsx("div",{className:"flex items-center",children:p.jsx(Xr,{})}),p.jsxs("div",{className:"flex items-center relative",ref:l,children:[p.jsxs("button",{className:"flex items-center space-x-3 cursor-pointer hover:bg-gray-50 rounded-lg px-3 py-2 transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2",onClick:()=>{c(!o)},id:"user-menu-button","aria-expanded":o,"aria-haspopup":"true","aria-label":"Abrir menú de usuario",children:[p.jsxs("div",{className:"flex flex-col items-end",children:[p.jsx("span",{className:"text-sm font-medium text-gray-800",children:h||g||"Usuario"}),p.jsx("span",{className:"text-xs text-gray-500",children:p.jsxs("span",{className:"inline-flex items-center",children:[p.jsx("span",{className:"w-1.5 h-1.5 bg-green-500 rounded-full mr-1.5 animate-pulse"}),p.jsx("span",{className:"text-green-600 font-semibold",children:"Activo"}),p.jsx("span",{className:"mx-2",children:"•"}),p.jsx("span",{className:"text-amber-600 font-medium",children:t?"Administrador":x?"Psicólogo":"Candidato"})]})})]}),p.jsx("div",{className:"h-10 w-10 rounded-full bg-gradient-to-br from-blue-500 to-blue-600 flex items-center justify-center text-white shadow-md",children:p.jsx("i",{className:"fas fa-user-shield"})}),p.jsx("div",{className:"text-gray-400",children:p.jsx("i",{className:`fas fa-chevron-${o?"up":"down"} text-xs transition-transform duration-200 ${o?"rotate-180":""}`})})]}),o&&p.jsxs("div",{className:"absolute right-0 top-full mt-2 w-72 bg-white rounded-xl menu-shadow border border-gray-200 z-50 overflow-hidden animate-in user-menu-dropdown",role:"menu","aria-orientation":"vertical","aria-labelledby":"user-menu-button",children:[p.jsx("div",{className:"px-5 py-4 border-b border-gray-100 bg-gradient-to-r from-gray-50 to-blue-50",children:p.jsxs("div",{className:"flex items-start space-x-4",children:[p.jsx("div",{className:"h-14 w-14 rounded-full bg-gradient-to-br from-blue-500 to-blue-600 flex items-center justify-center text-white shadow-lg ring-2 ring-blue-100",children:p.jsx("i",{className:"fas fa-user-shield text-lg"})}),p.jsxs("div",{className:"flex-1 min-w-0",children:[p.jsx("p",{className:"text-base font-semibold text-gray-900 truncate",children:h||g||"Usuario Desarrollo"}),p.jsx("p",{className:"text-sm text-gray-600 truncate mt-0.5",children:g||"<EMAIL>"}),p.jsxs("div",{className:"flex items-center mt-2 space-x-2",children:[p.jsxs("span",{className:"inline-flex items-center px-2.5 py-1 rounded-full text-xs font-semibold bg-green-100 text-green-800 border border-green-200",children:[p.jsx("span",{className:"w-2 h-2 bg-green-500 rounded-full mr-1.5 animate-pulse"}),"Activo"]}),p.jsx("span",{className:"inline-flex items-center px-2.5 py-1 rounded-full text-xs font-medium bg-amber-100 text-amber-800 border border-amber-200",children:t?"Administrador":x?"Psicólogo":"Candidato"})]})]})]})}),p.jsxs("div",{className:"py-2",children:[p.jsxs(A,{to:"/profile",className:"flex items-center px-5 py-3 text-sm font-medium text-gray-700 hover:bg-blue-50 hover:text-blue-700 transition-all duration-200 focus:outline-none focus:bg-blue-50 focus:text-blue-700 focus:ring-2 focus:ring-blue-500 focus:ring-inset",onClick:()=>c(!1),role:"menuitem",tabIndex:0,"aria-label":"Ir a mi perfil",children:[p.jsx("i",{className:"fas fa-user-cog mr-4 text-gray-400 w-4 text-center"}),p.jsx("span",{children:"Mi Perfil"})]}),p.jsxs(A,{to:"/configuracion",className:"flex items-center px-5 py-3 text-sm font-medium text-gray-700 hover:bg-amber-50 hover:text-amber-700 transition-all duration-200 focus:outline-none focus:bg-amber-50 focus:text-amber-700 focus:ring-2 focus:ring-amber-500 focus:ring-inset",onClick:()=>c(!1),role:"menuitem",tabIndex:0,"aria-label":"Ir a configuración del sistema",children:[p.jsx("i",{className:"fas fa-cog mr-4 text-gray-400 w-4 text-center"}),p.jsx("span",{children:"Configuración"})]}),p.jsxs(A,{to:"/help",className:"flex items-center px-5 py-3 text-sm font-medium text-gray-700 hover:bg-green-50 hover:text-green-700 transition-all duration-200 focus:outline-none focus:bg-green-50 focus:text-green-700 focus:ring-2 focus:ring-green-500 focus:ring-inset",onClick:()=>c(!1),role:"menuitem",tabIndex:0,"aria-label":"Obtener ayuda y soporte",children:[p.jsx("i",{className:"fas fa-question-circle mr-4 text-gray-400 w-4 text-center"}),p.jsx("span",{children:"Ayuda"})]}),p.jsx("div",{className:"border-t border-gray-200 my-2 mx-2"}),p.jsxs("button",{className:"flex w-full items-center px-5 py-3 text-sm font-medium text-red-700 hover:bg-red-50 hover:text-red-800 transition-all duration-200 focus:outline-none focus:bg-red-50 focus:text-red-800 focus:ring-2 focus:ring-red-500 focus:ring-inset group",onClick:()=>{c(!1),f()},role:"menuitem",tabIndex:0,"aria-label":"Cerrar sesión y salir del sistema",children:[p.jsx("i",{className:"fas fa-door-open mr-4 text-red-500 w-4 text-center group-hover:animate-pulse"}),p.jsx("span",{children:"Cerrar Sesión"})]})]})]})]})]})})}),p.jsx("main",{className:"py-10",children:p.jsx("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8",children:p.jsx(Gr,{children:p.jsx(E,{})})})}),p.jsx("footer",{className:"bg-white border-t border-gray-200 py-8",children:p.jsx("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8",children:p.jsxs("p",{className:"text-center text-gray-500 text-sm",children:["© ",(new Date).getFullYear()," BAT-7 Evaluaciones. Todos los derechos reservados."]})})}),p.jsx(D,{position:"top-right",autoClose:5e3,hideProgressBar:!1,newestOnTop:!0,closeOnClick:!0,rtl:!1,pauseOnFocusLoss:!0,draggable:!0,pauseOnHover:!0})]})]})},Yr=({fullScreen:e=!1,message:t="Cargando..."})=>{const a=e?"fixed inset-0 flex items-center justify-center bg-white bg-opacity-80 z-50":"flex flex-col items-center justify-center py-8";return p.jsx("div",{className:a,children:p.jsxs("div",{className:"flex flex-col items-center",children:[p.jsx("div",{className:"animate-spin rounded-full h-12 w-12 border-b-2 border-blue-500 mb-4"}),t&&p.jsx("p",{className:"text-gray-600",children:t})]})})};class Zr extends N.Component{constructor(e){super(e),d(this,"handleReset",()=>{this.setState({hasError:!1,error:null,errorInfo:null}),window.location.href="/"}),this.state={hasError:!1,error:null,errorInfo:null}}static getDerivedStateFromError(e){return{hasError:!0,error:e}}componentDidCatch(e,t){this.setState({errorInfo:t})}render(){var e;return this.state.hasError?p.jsx("div",{className:"container mx-auto py-12 px-4",children:p.jsxs("div",{className:"bg-white shadow-md rounded-md overflow-hidden",children:[p.jsx("div",{className:"bg-red-600 text-white px-4 py-2",children:p.jsx("h2",{className:"text-xl font-medium",children:"Algo salió mal"})}),p.jsxs("div",{className:"p-4",children:[p.jsxs("div",{className:"bg-red-50 p-4 rounded-md mb-4",children:[p.jsx("p",{className:"text-red-700 mb-2",children:"Se ha producido un error al cargar este componente."}),p.jsxs("details",{className:"text-sm",children:[p.jsx("summary",{className:"cursor-pointer text-red-500 font-medium mb-2",children:"Detalles técnicos"}),p.jsxs("div",{className:"p-3 bg-gray-800 text-gray-200 rounded overflow-auto",children:[p.jsx("p",{className:"whitespace-pre-wrap",children:null==(e=this.state.error)?void 0:e.toString()}),this.state.errorInfo&&p.jsx("p",{className:"whitespace-pre-wrap mt-2",children:this.state.errorInfo.componentStack})]})]})]}),p.jsx("div",{className:"flex justify-center",children:p.jsx("button",{onClick:this.handleReset,className:"px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500",children:"Volver al inicio"})})]})]})}):this.props.children}}const es={verbal:{id:"verbal",name:"Test de Aptitud Verbal",type:"verbal",description:"Test V - Evaluación de analogías verbales. Este test evalúa la capacidad para identificar relaciones entre palabras y conceptos, midiendo el razonamiento verbal y la comprensión de relaciones lógicas.",duration:12,numberOfQuestions:32,instructions:["Lee cada pregunta detenidamente antes de responder.","En cada ejercicio, debes encontrar la palabra que completa la frase dotándola de sentido.","Para las analogías verbales, identifica la relación exacta entre el primer par de palabras.","Entre las cuatro opciones, solo UNA es la correcta.","Marca la letra correspondiente (A, B, C o D) en la hoja de respuestas.","Trabaja rápidamente, ya que el tiempo es limitado.","Si no estás completamente seguro de una respuesta, elige la opción que creas más correcta; no se penalizan los errores."],additionalInfo:"Este test evalúa tu capacidad para comprender relaciones entre conceptos expresados a través de palabras. Implica el dominio del lenguaje y la habilidad para entender relaciones lógicas entre conceptos verbales.",components:[{name:"Analogías Verbales",description:"Mide tu capacidad para identificar relaciones entre conceptos"},{name:"Razonamiento Verbal",description:"Evalúa tu habilidad para entender relaciones lógicas"},{name:"Comprensión Lingüística",description:"Mide tu dominio del lenguaje y vocabulario"},{name:"Pensamiento Abstracto",description:"Evalúa tu capacidad para identificar patrones conceptuales"}],recommendations:["Fíjate bien en la relación entre el primer par de palabras para identificar el patrón que debes aplicar.","Si no encuentras la respuesta inmediatamente, analiza cada opción eliminando las que claramente no cumplen con la relación buscada.","Recuerda que las relaciones pueden ser de diversos tipos: causa-efecto, parte-todo, función, oposición, etc.","Si terminas antes del tiempo concedido, aprovecha para revisar tus respuestas."]},ortografia:{id:"ortografia",name:"Test de Ortografía",type:"ortografia",description:"Test O - Evaluación de la capacidad para identificar errores ortográficos en palabras.",duration:10,numberOfQuestions:32,instructions:["En cada grupo de cuatro palabras, identificar la única palabra que está mal escrita (intencionadamente).","La falta de ortografía puede ser de cualquier tipo, incluyendo errores en letras o la ausencia/presencia incorrecta de una tilde.","Marcar la letra correspondiente (A, B, C o D) a la palabra mal escrita.","Trabajar rápidamente. Si no se está seguro, elegir la opción que parezca más correcta (no se penaliza el error).","Si se termina antes, repasar las respuestas."],additionalInfo:"Este test evalúa tu dominio de las reglas ortográficas del español, incluyendo acentuación, uso de letras específicas y formación de palabras.",components:[{name:"Ortografía General",description:"Mide tu conocimiento de las reglas básicas de escritura"},{name:"Acentuación",description:"Evalúa tu dominio de las reglas de acentuación"},{name:"Uso de Letras",description:"Mide tu conocimiento del uso correcto de letras que pueden confundirse"},{name:"Atención al Detalle",description:"Evalúa tu capacidad para detectar errores sutiles"}],recommendations:["Revisa visualmente cada palabra con atención.","Recuerda las reglas de acentuación de palabras agudas, llanas y esdrújulas.","Presta especial atención a las letras que suelen causar confusión: b/v, g/j, h, etc.","Observa la presencia o ausencia de tildes en las palabras."],examples:[{question:"A. año, B. berso, C. vuelo, D. campana",answer:"B",explanation:'La grafía correcta es "verso".'},{question:"A. bosque, B. armario, C. telon, D. libro",answer:"C",explanation:'La palabra correcta es "telón", lleva tilde en la "o".'}]},razonamiento:{id:"razonamiento",name:"Test de Razonamiento",type:"razonamiento",description:"Test R - Evaluación de la capacidad para identificar patrones y continuar series lógicas de figuras.",duration:20,numberOfQuestions:32,instructions:["Observar una serie de figuras y determinar qué figura (A, B, C o D) debería ir a continuación, sustituyendo al interrogante, siguiendo la lógica de la serie.","Analiza cuidadosamente cómo evolucionan las figuras en cada serie.","Busca patrones como rotaciones, traslaciones, adiciones o sustracciones de elementos.","Entre las cuatro opciones, solo UNA es la correcta.","Marca la letra correspondiente (A, B, C o D) en la hoja de respuestas.","Trabaja metódicamente, ya que algunas secuencias pueden tener patrones complejos.","Si no estás completamente seguro de una respuesta, intenta descartar opciones que claramente no siguen el patrón."],additionalInfo:"Este test evalúa tu capacidad para identificar patrones lógicos y aplicarlos para predecir el siguiente elemento en una secuencia. Es una medida del razonamiento inductivo y del pensamiento lógico-abstracto.",components:[{name:"Razonamiento Inductivo",description:"Mide tu capacidad para identificar reglas a partir de ejemplos"},{name:"Pensamiento Lógico",description:"Evalúa tu habilidad para aplicar reglas sistemáticamente"},{name:"Visualización Espacial",description:"Mide tu capacidad para manipular imágenes mentalmente"},{name:"Atención al Detalle",description:"Evalúa tu capacidad para detectar patrones sutiles"}],recommendations:["Intenta identificar más de un patrón en cada serie (puede haber cambios en color, forma, tamaño y posición).","Observa si hay ciclos repetitivos en los patrones.","Analiza cada elemento individualmente si la figura es compleja.","Si encuentras dificultades, intenta verbalizar el patrón para hacerlo más claro."]},atencion:{id:"atencion",name:"Test de Atención",type:"atencion",description:"Test A - Evaluación de la rapidez y precisión en la localización de símbolos.",duration:8,numberOfQuestions:80,instructions:["En cada ejercicio aparece una fila con diferentes símbolos y tu tarea consistirá en localizar cuántas veces aparece uno determinado.","El símbolo que tienes que localizar es siempre el mismo y se presenta en la parte superior de la página.","El símbolo puede aparecer 0, 1, 2 o 3 veces en cada fila, pero nunca más de 3.","Deberás marcar cuántas veces aparece el símbolo en cada fila (0, 1, 2 o 3).","Trabaja con rapidez y precisión, asegurándote de que tu respuesta se corresponda con el número del ejercicio que estás contestando.","Avanza sistemáticamente por cada fila, de izquierda a derecha.","Presta especial atención a símbolos muy similares al modelo pero que no son idénticos."],additionalInfo:"Esta prueba trata de evaluar tu rapidez y tu precisión trabajando con símbolos. Es una medida de la atención selectiva y sostenida, así como de la velocidad y precisión en el procesamiento de información visual.",components:[{name:"Atención Selectiva",description:"Mide tu capacidad para enfocarte en elementos específicos"},{name:"Velocidad Perceptiva",description:"Evalúa tu rapidez para procesar información visual"},{name:"Discriminación Visual",description:"Mide tu habilidad para distinguir detalles visuales"},{name:"Concentración",description:"Evalúa tu capacidad para mantener el foco durante una tarea repetitiva"}],recommendations:["Mantén un ritmo constante, sin detenerte demasiado en ningún elemento.","Si en algún ejercicio no estás completamente seguro de cuál puede ser, elige la opción que creas que es más correcta; no se penalizará el error.","Si terminas antes del tiempo concedido, repasa tus respuestas.","Utiliza el dedo o un marcador para seguir las filas si te ayuda a mantener el enfoque.","Evita distracciones y mantén la concentración en la tarea."]},espacial:{id:"espacial",name:"Test de Aptitud Espacial",type:"espacial",description:"Test E - Evaluación del razonamiento espacial con cubos y redes.",duration:15,numberOfQuestions:28,instructions:["En cada ejercicio encontrarás un cubo junto con su modelo desplegado, al que se le han borrado casi todos los números y letras.","Tu tarea consistirá en averiguar qué número o letra debería aparecer en lugar del interrogante (?) y en qué orientación.","En el cubo se han representado en color gris los números o letras que se encuentran en las caras de atrás (las que no se ven directamente).","Observa cuidadosamente la orientación y posición relativa de las caras en el cubo.","Considera cómo las distintas caras del cubo se conectan entre sí en el modelo desplegado.","Visualiza mentalmente el proceso de plegado del modelo para formar el cubo.","Entre las cuatro opciones, solo UNA es la correcta.","Marca la letra correspondiente (A, B, C o D) en la hoja de respuestas."],additionalInfo:"Este test evalúa tu capacidad para manipular objetos mentalmente en el espacio tridimensional. Es una medida de la visualización espacial, rotación mental y comprensión de relaciones espaciales.",components:[{name:"Visualización Espacial",description:"Mide tu capacidad para manipular objetos en 3D mentalmente"},{name:"Rotación Mental",description:"Evalúa tu habilidad para rotar figuras en tu mente"},{name:"Relaciones Espaciales",description:"Mide tu comprensión de cómo se conectan las partes de un objeto"},{name:"Razonamiento Geométrico",description:"Evalúa tu entendimiento de principios geométricos básicos"}],recommendations:["Utiliza marcas mentales para orientarte en la ubicación de cada cara del cubo.","Fíjate en detalles específicos de los diseños en cada cara para determinar su orientación correcta.","Si es necesario, utiliza tus manos para ayudarte a visualizar el plegado del modelo.","Si en algún ejercicio no estás completamente seguro de cuál puede ser la respuesta, elige la opción que creas que es más correcta; no se penalizará el error.","Si terminas antes del tiempo concedido, repasa tus respuestas."]},mecanico:{id:"mecanico",name:"Test de Razonamiento Mecánico",type:"mecanico",description:"Test M - Evaluación de la comprensión de principios físicos y mecánicos básicos.",duration:12,numberOfQuestions:28,instructions:["Observar dibujos que representan diversas situaciones físicas o mecánicas y responder a una pregunta sobre cada situación, eligiendo la opción más adecuada.","Analiza los elementos del dibujo y cómo interactúan entre sí.","Aplica principios básicos de física y mecánica como palancas, poleas, engranajes, fuerzas, etc.","Ten en cuenta la dirección de las fuerzas, el movimiento o el equilibrio en cada situación.","Entre las opciones presentadas, selecciona la que mejor explica el fenómeno o predice el resultado.","Marca la letra correspondiente (A, B, C o D) en la hoja de respuestas.","Si no estás seguro, intenta aplicar el sentido común y los principios básicos que conozcas."],additionalInfo:"Este test evalúa tu comprensión intuitiva de principios físicos y mecánicos, así como tu capacidad para aplicar estos principios a situaciones prácticas. No requiere conocimientos técnicos avanzados, sino una comprensión básica de cómo funcionan los objetos en el mundo físico.",components:[{name:"Comprensión Física",description:"Mide tu entendimiento de principios físicos básicos"},{name:"Razonamiento Mecánico",description:"Evalúa tu capacidad para entender sistemas mecánicos"},{name:"Resolución de Problemas",description:"Mide tu habilidad para aplicar principios a situaciones nuevas"},{name:"Intuición Tecnológica",description:"Evalúa tu comprensión natural de cómo funcionan las máquinas"}],recommendations:["Recuerda principios básicos como la ley de la palanca, la transmisión de fuerzas en poleas y engranajes.","Considera factores como la gravedad, la fricción y la inercia cuando analices cada situación.","Visualiza el movimiento o la acción que ocurriría en la situación presentada.","Si tienes dificultades, intenta simplificar el problema a sus componentes más básicos."]},numerico:{id:"numerico",name:"Test de Aptitud Numérica",type:"numerico",description:"Test N - Resolución de problemas numéricos, series y tablas.",duration:20,numberOfQuestions:32,instructions:["En esta prueba encontrarás distintos ejercicios numéricos que tendrás que resolver.","Para ello tendrás que analizar la información que se presenta y determinar qué debe aparecer en lugar del interrogante.","Cuando lo hayas decidido, deberás marcar la letra de la opción correspondiente en la hoja de respuestas.","Asegúrate de que coincida con el ejercicio que estás contestando.","","El tiempo máximo para su realización es de 20 minutos, por lo que deberás trabajar rápidamente.","Esfuérzate al máximo en encontrar la respuesta correcta.","Si en algún ejercicio no estás completamente seguro de cuál puede ser, elige la opción que creas que es más correcta.","No se penalizará el error.","Si terminas antes del tiempo concedido, repasa tus respuestas, pero NO continúes con las demás pruebas."],additionalInfo:"Este test evalúa tu capacidad para resolver problemas numéricos mediante el análisis de igualdades, series y tablas. Mide el razonamiento matemático, la identificación de patrones numéricos y la habilidad para trabajar con datos organizados.",components:[{name:"Igualdades Numéricas",description:"Resolver ecuaciones con elementos faltantes"},{name:"Series Numéricas",description:"Identificar patrones y continuar secuencias"},{name:"Tablas de Datos",description:"Analizar información organizada y encontrar valores faltantes"},{name:"Cálculo Mental",description:"Realizar operaciones matemáticas con rapidez y precisión"}],recommendations:["Lee cuidadosamente cada problema antes de intentar resolverlo.","En las igualdades, calcula primero el lado conocido de la ecuación.","En las series, busca patrones simples antes de considerar reglas más complejas.","En las tablas, analiza las relaciones entre filas y columnas.","Verifica tus cálculos cuando sea posible.","Si no estás seguro, elige la opción que te parezca más lógica."]},bat7:{id:"bat7",name:"Batería Completa BAT-7",type:"battery",description:"Evaluación completa de aptitudes y habilidades cognitivas.",duration:120,numberOfQuestions:184,instructions:["Lee atentamente cada pregunta antes de responder.","Responde a todas las preguntas, aunque no estés seguro/a de la respuesta.","Administra bien tu tiempo. Si una pregunta te resulta difícil, pasa a la siguiente y vuelve a ella más tarde.","No uses calculadora ni ningún otro dispositivo o material durante el test.","Una vez iniciado el test, no podrás pausarlo. Asegúrate de disponer del tiempo necesario para completarlo.","Responde con honestidad. Este test está diseñado para evaluar tus habilidades actuales.","Cada subtest tiene instrucciones específicas que deberás leer antes de comenzar esa sección."],additionalInfo:"La batería BAT-7 está compuesta por siete pruebas independientes que evalúan diferentes aptitudes: verbal, espacial, numérica, mecánica, razonamiento, atención y ortografía. Cada prueba tiene un tiempo específico de realización y unas instrucciones particulares.",subtests:[{id:"verbal",name:"Test de Aptitud Verbal (V)",duration:12,questions:32,description:"Evaluación de analogías verbales y comprensión de relaciones entre conceptos."},{id:"ortografia",name:"Test de Ortografía (O)",duration:10,questions:32,description:"Identificación de palabras con errores ortográficos."},{id:"razonamiento",name:"Test de Razonamiento (R)",duration:20,questions:32,description:"Continuación de series lógicas de figuras."},{id:"atencion",name:"Test de Atención (A)",duration:8,questions:80,description:"Rapidez y precisión en la localización de símbolos."},{id:"espacial",name:"Test de Visualización Espacial (E)",duration:15,questions:28,description:"Razonamiento espacial con cubos y redes."},{id:"mecanico",name:"Test de Razonamiento Mecánico (M)",duration:12,questions:28,description:"Comprensión de principios físicos y mecánicos básicos."},{id:"numerico",name:"Test de Razonamiento Numérico (N)",duration:20,questions:32,description:"Resolución de problemas numéricos, series y tablas."}],recommendations:["Descansa adecuadamente antes de realizar la batería completa.","Realiza los tests en un ambiente tranquilo y sin distracciones.","Gestiona tu energía a lo largo de toda la batería, ya que algunos tests son más exigentes que otros.","Mantén una actitud positiva y confía en tus capacidades."]}},ts={bat7:{icon:"fas fa-clipboard-list",color:"text-purple-600"},verbal:{icon:"fas fa-comments",color:"text-blue-600"},espacial:{icon:"fas fa-cube",color:"text-indigo-600"},atencion:{icon:"fas fa-eye",color:"text-red-600"},razonamiento:{icon:"fas fa-puzzle-piece",color:"text-amber-600"},numerico:{icon:"fas fa-calculator",color:"text-teal-600"},mecanico:{icon:"fas fa-cogs",color:"text-slate-600"},ortografia:{icon:"fas fa-spell-check",color:"text-green-600"}},as=()=>{const{testId:e}=_(),t=w(),[a,r]=N.useState(null),[s,n]=N.useState(!0),[i,o]=N.useState(!1);N.useEffect(()=>{u(void 0,null,function*(){try{yield new Promise(e=>setTimeout(e,800)),es[e]?r(es[e]):k.warning(`No se encontraron instrucciones para el test: ${e}`),n(!1)}catch(t){k.error("Error al cargar la información del test"),n(!1)}})},[e]);const c=ts[e]||{icon:"fas fa-clipboard-list",color:"text-gray-600"};return p.jsxs("div",{className:"container mx-auto py-6 max-w-4xl",children:[p.jsxs("div",{className:"mb-6 text-center",children:[p.jsxs("h1",{className:"text-2xl font-bold text-gray-800 mb-2",children:[p.jsx("i",{className:`${c.icon} ${c.color} mr-2`}),"Instrucciones del Test"]}),!s&&a&&p.jsx("p",{className:"text-gray-600",children:a.name})]}),s?p.jsx("div",{className:"py-16 text-center",children:p.jsxs("div",{className:"flex flex-col items-center justify-center",children:[p.jsx("div",{className:"animate-spin rounded-full h-12 w-12 border-b-2 border-blue-500 mb-4"}),p.jsx("p",{className:"text-gray-500",children:"Cargando instrucciones del test..."})]})}):a?p.jsxs(p.Fragment,{children:[p.jsxs(R,{className:"mb-6",children:[p.jsx(I,{className:"text-center",children:p.jsx("h2",{className:"text-lg font-medium",children:"Información General"})}),p.jsxs(M,{children:[p.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6 mb-6",children:[p.jsxs("div",{children:[p.jsx("h3",{className:"text-md font-medium mb-2 text-center",children:"Descripción"}),p.jsx("p",{className:"text-gray-700",children:a.description})]}),p.jsxs("div",{className:"grid grid-cols-2 gap-4",children:[p.jsxs("div",{children:[p.jsx("h3",{className:"text-md font-medium mb-2 text-center",children:"Duración"}),p.jsxs("p",{className:"text-gray-700 text-center",children:[a.duration," minutos"]})]}),p.jsxs("div",{children:[p.jsx("h3",{className:"text-md font-medium mb-2 text-center",children:"Preguntas"}),p.jsxs("p",{className:"text-gray-700 text-center",children:[a.numberOfQuestions," preguntas"]})]})]})]}),a.additionalInfo&&p.jsx("div",{className:"bg-blue-50 border-l-4 border-blue-500 p-4 mb-4",children:p.jsx("p",{className:"text-blue-700",children:a.additionalInfo})}),"battery"!==a.type&&a.components&&p.jsxs("div",{className:"mt-6",children:[p.jsx("h3",{className:"text-md font-medium mb-3 text-center",children:"Componentes Evaluados"}),p.jsx("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-3",children:a.components.map((e,t)=>p.jsxs("div",{className:"border rounded p-3",children:[p.jsx("p",{className:"font-medium",children:e.name}),p.jsx("p",{className:"text-sm text-gray-600 mt-1",children:e.description})]},t))})]}),"battery"===a.type&&a.subtests&&a.subtests.length>0&&p.jsxs("div",{className:"mt-6",children:[p.jsx("h3",{className:"text-md font-medium mb-3 text-center",children:"Subtests"}),p.jsx("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-3",children:a.subtests.map((e,t)=>p.jsxs("div",{className:"border rounded p-3",children:[p.jsxs("p",{className:"font-medium",children:[t+1,". ",e.name]}),p.jsx("p",{className:"text-sm text-gray-600 mt-1",children:e.description}),p.jsxs("div",{className:"flex justify-between mt-2 text-sm text-gray-600",children:[p.jsxs("span",{children:[e.duration," min"]}),p.jsxs("span",{children:[e.questions," preguntas"]})]})]},e.id))})]})]})]}),p.jsxs(R,{className:"mb-6",children:[p.jsx(I,{className:"text-center",children:p.jsx("h2",{className:"text-lg font-medium",children:"Instrucciones"})}),p.jsxs(M,{children:[p.jsx("div",{className:"space-y-3",children:a.instructions.map((e,t)=>{if(""===e)return p.jsx("div",{className:"h-4"},t);if(e.startsWith("**")&&e.endsWith("**"))return p.jsx("h4",{className:"text-lg font-semibold text-gray-800 mt-6 mb-3 border-b border-gray-200 pb-2",children:e.replace(/\*\*/g,"")},t);const r=a.instructions.slice(0,t+1).filter(e=>""!==e&&!e.startsWith("**")).length;return p.jsxs("div",{className:"flex items-start",children:[p.jsx("div",{className:"flex-shrink-0 w-6 h-6 flex items-center justify-center rounded-full bg-blue-100 text-blue-600 mr-3 mt-0.5",children:r}),p.jsx("p",{className:"text-gray-700",children:e})]},t)})}),a.recommendations&&p.jsxs("div",{className:"mt-6 bg-yellow-50 border-l-4 border-yellow-500 p-4",children:[p.jsx("h3",{className:"text-md font-medium text-yellow-800 mb-2",children:"Recomendaciones Adicionales"}),p.jsx("ul",{className:"space-y-2 text-yellow-700",children:a.recommendations.map((e,t)=>p.jsxs("li",{children:["• ",e]},t))})]})]})]}),p.jsxs(R,{children:[p.jsx(M,{children:p.jsxs("div",{className:"flex items-start mb-4",children:[p.jsx("input",{type:"checkbox",id:"accept-conditions",checked:i,onChange:e=>o(e.target.checked),className:"h-5 w-5 text-blue-600 rounded border-gray-300 focus:ring-blue-500 mt-1"}),p.jsx("label",{htmlFor:"accept-conditions",className:"ml-3 text-gray-700",children:"He leído y acepto las instrucciones. Entiendo que una vez iniciado el test no podré pausarlo y deberé completarlo en su totalidad."})]})}),p.jsx(B,{className:"flex justify-end",children:p.jsx(L,{variant:i?"primary":"outline",onClick:()=>{i?(k.info("Iniciando test..."),"battery"===a.type?a.subtests&&a.subtests.length>0?t(`/test/${a.subtests[0].id}`):t("/student/tests"):t(`/test/${a.id}`)):k.warning("Debes aceptar las condiciones para continuar")},disabled:!i,children:"Iniciar Test"})})]})]}):p.jsx(R,{children:p.jsx(M,{children:p.jsx("div",{className:"py-8 text-center",children:p.jsx("p",{className:"text-gray-500",children:"No se encontró información para el test solicitado."})})})})]})},rs=Object.freeze(Object.defineProperty({__proto__:null,default:as},Symbol.toStringTag,{value:"Module"})),ss={"12-13":{V:[{pc:99,pd:[30,32]},{pc:97,pd:[29,29]},{pc:95,pd:[28,28]},{pc:90,pd:[27,27]},{pc:85,pd:[26,26]},{pc:80,pd:[25,25]},{pc:70,pd:[24,24]},{pc:65,pd:[23,23]},{pc:55,pd:[22,22]},{pc:50,pd:[21,21]},{pc:40,pd:[20,20]},{pc:35,pd:[19,19]},{pc:25,pd:[18,18]},{pc:20,pd:[17,17]},{pc:15,pd:[16,16]},{pc:10,pd:[15,15]},{pc:5,pd:[13,14]},{pc:4,pd:[12,12]},{pc:2,pd:[11,11]},{pc:1,pd:[0,10]}],E:[{pc:99,pd:[27,28]},{pc:96,pd:[26,26]},{pc:95,pd:[25,25]},{pc:90,pd:[24,24]},{pc:85,pd:[23,23]},{pc:80,pd:[22,22]},{pc:75,pd:[21,21]},{pc:70,pd:[20,20]},{pc:60,pd:[19,19]},{pc:45,pd:[18,18]},{pc:55,pd:[18,18]},{pc:50,pd:[17,17]},{pc:40,pd:[16,16]},{pc:35,pd:[15,15]},{pc:25,pd:[14,14]},{pc:20,pd:[13,13]},{pc:15,pd:[12,12]},{pc:10,pd:[11,11]},{pc:5,pd:[9,10]},{pc:4,pd:[8,8]},{pc:3,pd:[7,7]},{pc:2,pd:[6,6]},{pc:1,pd:[0,5]}],A:[{pc:99,pd:[49,80]},{pc:98,pd:[48,48]},{pc:97,pd:[46,47]},{pc:96,pd:[44,45]},{pc:95,pd:[43,43]},{pc:90,pd:[39,42]},{pc:85,pd:[36,38]},{pc:80,pd:[35,35]},{pc:75,pd:[34,34]},{pc:70,pd:[33,33]},{pc:65,pd:[31,32]},{pc:60,pd:[29,30]},{pc:55,pd:[28,28]},{pc:50,pd:[27,27]},{pc:45,pd:[26,26]},{pc:40,pd:[25,25]},{pc:35,pd:[24,24]},{pc:30,pd:[23,23]},{pc:25,pd:[22,22]},{pc:20,pd:[21,21]},{pc:15,pd:[19,20]},{pc:10,pd:[17,18]},{pc:5,pd:[15,16]},{pc:4,pd:[13,14]},{pc:2,pd:[12,12]},{pc:1,pd:[0,11]}],CON:[{pc:99,pd:[98,100]},{pc:97,pd:[96,97]},{pc:96,pd:[95,95]},{pc:95,pd:[94,94]},{pc:90,pd:[91,93]},{pc:85,pd:[89,90]},{pc:80,pd:[88,88]},{pc:75,pd:[85,87]},{pc:70,pd:[83,84]},{pc:65,pd:[82,82]},{pc:60,pd:[80,81]},{pc:55,pd:[78,79]},{pc:50,pd:[76,77]},{pc:45,pd:[74,75]},{pc:40,pd:[72,73]},{pc:35,pd:[69,71]},{pc:30,pd:[67,68]},{pc:25,pd:[64,66]},{pc:20,pd:[61,63]},{pc:15,pd:[56,60]},{pc:10,pd:[47,55]},{pc:5,pd:[36,46]},{pc:4,pd:[33,35]},{pc:3,pd:[29,32]},{pc:2,pd:[28,28]},{pc:1,pd:[0,27]}],R:[{pc:99,pd:[29,32]},{pc:98,pd:[28,28]},{pc:96,pd:[27,27]},{pc:95,pd:[26,26]},{pc:90,pd:[25,25]},{pc:85,pd:[24,24]},{pc:80,pd:[23,23]},{pc:70,pd:[22,22]},{pc:65,pd:[21,21]},{pc:60,pd:[20,20]},{pc:50,pd:[19,19]},{pc:45,pd:[18,18]},{pc:40,pd:[17,17]},{pc:30,pd:[16,16]},{pc:25,pd:[15,15]},{pc:20,pd:[14,14]},{pc:15,pd:[13,13]},{pc:10,pd:[11,12]},{pc:5,pd:[8,10]},{pc:3,pd:[7,7]},{pc:2,pd:[6,6]},{pc:1,pd:[0,5]}],N:[{pc:99,pd:[28,32]},{pc:98,pd:[27,27]},{pc:97,pd:[26,26]},{pc:96,pd:[25,25]},{pc:95,pd:[24,24]},{pc:90,pd:[22,23]},{pc:85,pd:[22,22]},{pc:85,pd:[20,21]},{pc:80,pd:[19,19]},{pc:75,pd:[18,18]},{pc:70,pd:[17,17]},{pc:65,pd:[16,16]},{pc:60,pd:[15,15]},{pc:55,pd:[14,14]},{pc:50,pd:[13,13]},{pc:45,pd:[12,12]},{pc:40,pd:[11,11]},{pc:35,pd:[10,10]},{pc:25,pd:[9,9]},{pc:20,pd:[8,8]},{pc:15,pd:[7,7]},{pc:10,pd:[6,6]},{pc:5,pd:[5,5]},{pc:3,pd:[4,4]},{pc:1,pd:[0,3]}],M:[{pc:99,pd:[25,28]},{pc:96,pd:[24,24]},{pc:95,pd:[23,23]},{pc:90,pd:[22,22]},{pc:85,pd:[21,21]},{pc:80,pd:[20,20]},{pc:70,pd:[19,19]},{pc:60,pd:[18,18]},{pc:50,pd:[17,17]},{pc:45,pd:[16,16]},{pc:35,pd:[15,15]},{pc:30,pd:[14,14]},{pc:20,pd:[13,13]},{pc:15,pd:[12,12]},{pc:10,pd:[11,11]},{pc:5,pd:[10,10]},{pc:4,pd:[9,9]},{pc:3,pd:[8,8]},{pc:1,pd:[0,7]}],O:[{pc:99,pd:[31,32]},{pc:98,pd:[30,30]},{pc:95,pd:[29,29]},{pc:90,pd:[27,28]},{pc:85,pd:[26,26]},{pc:80,pd:[25,25]},{pc:70,pd:[24,24]},{pc:65,pd:[23,23]},{pc:60,pd:[22,22]},{pc:55,pd:[21,21]},{pc:50,pd:[20,20]},{pc:45,pd:[19,19]},{pc:40,pd:[18,18]},{pc:35,pd:[17,17]},{pc:30,pd:[16,16]},{pc:25,pd:[15,15]},{pc:20,pd:[14,14]},{pc:15,pd:[13,13]},{pc:10,pd:[11,12]},{pc:5,pd:[9,10]},{pc:4,pd:[8,8]},{pc:3,pd:[7,7]},{pc:2,pd:[6,6]},{pc:1,pd:[0,5]}]},"13-14":{V:[{pc:99,pd:[31,32]},{pc:98,pd:[30,30]},{pc:95,pd:[29,29]},{pc:90,pd:[28,28]},{pc:85,pd:[27,27]},{pc:75,pd:[26,26]},{pc:65,pd:[25,25]},{pc:60,pd:[24,24]},{pc:50,pd:[23,23]},{pc:45,pd:[22,22]},{pc:35,pd:[21,21]},{pc:30,pd:[20,20]},{pc:25,pd:[19,19]},{pc:20,pd:[18,18]},{pc:15,pd:[16,17]},{pc:10,pd:[15,15]},{pc:5,pd:[13,14]},{pc:4,pd:[12,12]},{pc:2,pd:[11,11]},{pc:1,pd:[0,10]}],E:[{pc:99,pd:[28,28]},{pc:97,pd:[27,27]},{pc:95,pd:[26,26]},{pc:90,pd:[25,25]},{pc:85,pd:[24,24]},{pc:80,pd:[23,23]},{pc:75,pd:[22,22]},{pc:65,pd:[21,21]},{pc:60,pd:[20,20]},{pc:50,pd:[19,19]},{pc:45,pd:[18,18]},{pc:40,pd:[17,17]},{pc:35,pd:[16,16]},{pc:25,pd:[15,15]},{pc:20,pd:[14,14]},{pc:15,pd:[13,13]},{pc:10,pd:[12,12]},{pc:5,pd:[10,11]},{pc:4,pd:[9,9]},{pc:2,pd:[8,8]},{pc:1,pd:[0,7]}],A:[{pc:99,pd:[60,80]},{pc:98,pd:[55,59]},{pc:97,pd:[51,54]},{pc:96,pd:[49,50]},{pc:95,pd:[48,48]},{pc:90,pd:[42,47]},{pc:85,pd:[39,41]},{pc:80,pd:[37,38]},{pc:75,pd:[35,36]},{pc:70,pd:[34,34]},{pc:65,pd:[33,33]},{pc:60,pd:[31,32]},{pc:55,pd:[30,30]},{pc:50,pd:[29,29]},{pc:45,pd:[28,28]},{pc:40,pd:[26,27]},{pc:35,pd:[25,25]},{pc:30,pd:[24,24]},{pc:25,pd:[23,23]},{pc:20,pd:[22,22]},{pc:15,pd:[20,21]},{pc:10,pd:[18,19]},{pc:5,pd:[14,17]},{pc:3,pd:[13,13]},{pc:2,pd:[10,12]},{pc:1,pd:[0,9]}],CON:[{pc:99,pd:[100,100]},{pc:98,pd:[97,99]},{pc:97,pd:[96,96]},{pc:96,pd:[95,95]},{pc:95,pd:[94,94]},{pc:90,pd:[91,93]},{pc:85,pd:[89,90]},{pc:80,pd:[87,88]},{pc:75,pd:[85,86]},{pc:70,pd:[83,84]},{pc:65,pd:[82,82]},{pc:60,pd:[80,81]},{pc:55,pd:[78,79]},{pc:50,pd:[76,77]},{pc:45,pd:[75,75]},{pc:40,pd:[72,74]},{pc:35,pd:[70,71]},{pc:30,pd:[68,69]},{pc:25,pd:[66,67]},{pc:20,pd:[61,65]},{pc:15,pd:[57,60]},{pc:10,pd:[49,56]},{pc:5,pd:[37,48]},{pc:4,pd:[35,36]},{pc:3,pd:[31,34]},{pc:2,pd:[29,30]},{pc:1,pd:[0,28]}],R:[{pc:99,pd:[30,32]},{pc:98,pd:[29,29]},{pc:95,pd:[28,28]},{pc:90,pd:[26,27]},{pc:85,pd:[25,25]},{pc:80,pd:[24,24]},{pc:70,pd:[23,23]},{pc:65,pd:[22,22]},{pc:55,pd:[21,21]},{pc:50,pd:[20,20]},{pc:45,pd:[19,19]},{pc:40,pd:[18,18]},{pc:30,pd:[17,17]},{pc:25,pd:[16,16]},{pc:20,pd:[15,15]},{pc:15,pd:[13,14]},{pc:10,pd:[11,12]},{pc:5,pd:[9,10]},{pc:3,pd:[8,8]},{pc:2,pd:[7,7]},{pc:1,pd:[0,6]}],N:[{pc:99,pd:[29,32]},{pc:98,pd:[28,28]},{pc:97,pd:[27,27]},{pc:96,pd:[26,26]},{pc:95,pd:[25,25]},{pc:90,pd:[24,24]},{pc:85,pd:[22,23]},{pc:80,pd:[21,21]},{pc:70,pd:[19,20]},{pc:65,pd:[17,18]},{pc:50,pd:[15,15]},{pc:45,pd:[14,14]},{pc:40,pd:[13,13]},{pc:30,pd:[12,12]},{pc:25,pd:[10,11]},{pc:20,pd:[9,9]},{pc:15,pd:[8,8]},{pc:10,pd:[7,7]},{pc:5,pd:[5,6]},{pc:2,pd:[4,4]},{pc:1,pd:[0,3]}],M:[{pc:99,pd:[26,28]},{pc:97,pd:[25,25]},{pc:95,pd:[24,24]},{pc:90,pd:[23,23]},{pc:85,pd:[22,22]},{pc:75,pd:[21,21]},{pc:70,pd:[20,20]},{pc:60,pd:[19,19]},{pc:50,pd:[18,18]},{pc:45,pd:[17,17]},{pc:40,pd:[16,16]},{pc:30,pd:[15,15]},{pc:25,pd:[14,14]},{pc:20,pd:[13,13]},{pc:15,pd:[12,12]},{pc:10,pd:[11,11]},{pc:5,pd:[10,10]},{pc:4,pd:[9,9]},{pc:3,pd:[8,8]},{pc:1,pd:[0,7]}],O:[{pc:99,pd:[32,32]},{pc:98,pd:[31,31]},{pc:95,pd:[30,30]},{pc:90,pd:[29,29]},{pc:85,pd:[27,28]},{pc:80,pd:[26,26]},{pc:70,pd:[25,25]},{pc:65,pd:[24,24]},{pc:60,pd:[23,23]},{pc:50,pd:[22,22]},{pc:45,pd:[21,21]},{pc:40,pd:[20,20]},{pc:35,pd:[19,19]},{pc:30,pd:[18,18]},{pc:25,pd:[17,17]},{pc:20,pd:[16,16]},{pc:15,pd:[14,15]},{pc:10,pd:[13,13]},{pc:5,pd:[10,12]},{pc:3,pd:[9,9]},{pc:2,pd:[8,8]},{pc:1,pd:[0,7]}]}},ns=(e,t,a)=>{if(null==t)return null;let r;if("string"!=typeof t&&(t=String(t)),12===a)r="12-13";else{if(13!==a&&14!==a)return null;r="13-14"}const s=ss[r];if(!s)return null;const n=s[t.toUpperCase()];if(!n)return null;for(const i of n)if(e>=i.pd[0]&&e<=i.pd[1])return i.pc;return e<n[n.length-1].pd[0]?n[n.length-1].pc:e>n[0].pd[1]?n[0].pc:null};class is{static calcularEdad(e){if(!e)return null;const t=new Date,a=new Date(e);let r=t.getFullYear()-a.getFullYear();const s=t.getMonth()-a.getMonth();return(s<0||0===s&&t.getDate()<a.getDate())&&r--,r}static determinarGrupoEdad(e){return 12===e?"12-13":13===e||14===e?"13-14":null}static convertirPdAPC(e,t,a){return u(this,null,function*(){try{const{data:r,error:s}=yield x.from("pacientes").select("fecha_nacimiento").eq("id",a).single();if(s)return null;const n=this.calcularEdad(r.fecha_nacimiento);if(!n)return null;return{pc:ns(e,t,n),edad:n,grupoEdad:this.determinarGrupoEdad(n),pd:e}}catch(r){return null}})}static actualizarResultadoConPC(e,t){return u(this,null,function*(){try{const{data:a,error:r}=yield x.from("resultados").update({percentil:t,updated_at:(new Date).toISOString()}).eq("id",e).select();return r?null:a[0]}catch(a){return null}})}static procesarConversionAutomatica(e,t,a,r){return u(this,null,function*(){try{const s=yield this.convertirPdAPC(t,a,r);if(!s)return null;return yield this.actualizarResultadoConPC(e,s.pc,s.edad,s.grupoEdad)}catch(s){return null}})}static obtenerInterpretacionPC(e){return e>=98?{nivel:"Muy Alto",color:"text-green-700",bg:"bg-green-100"}:e>=85?{nivel:"Alto",color:"text-blue-700",bg:"bg-blue-100"}:e>=70?{nivel:"Medio-Alto",color:"text-indigo-700",bg:"bg-indigo-100"}:e>=31?{nivel:"Medio",color:"text-gray-700",bg:"bg-gray-100"}:e>=16?{nivel:"Medio-Bajo",color:"text-yellow-700",bg:"bg-yellow-100"}:e>=3?{nivel:"Bajo",color:"text-orange-700",bg:"bg-orange-100"}:{nivel:"Muy Bajo",color:"text-red-700",bg:"bg-red-100"}}static recalcularPCPaciente(e){return u(this,null,function*(){var t;try{const{data:a,error:r}=yield x.from("resultados").select("\n          id,\n          puntaje_directo,\n          aptitudes:aptitud_id (codigo)\n        ").eq("paciente_id",e).is("percentil",null);if(r)return!1;let s=0;for(const n of a){(yield this.procesarConversionAutomatica(n.id,n.puntaje_directo,null==(t=n.aptitudes)?void 0:t.codigo,e))&&s++}return!0}catch(a){return!1}})}}class os{static getAptitudeId(e){return u(this,null,function*(){try{const t=this.TEST_APTITUDE_MAP[e];if(!t)throw new Error(`Tipo de test no reconocido: ${e}`);const{data:a,error:r}=yield x.from("aptitudes").select("id").eq("codigo",t).single();if(r)throw r;return a.id}catch(t){throw t}})}static calculateConcentration(e,t=0){return e&&0!==e?e/(e+t)*100:0}static saveTestResult(e){return u(this,arguments,function*({patientId:e,testType:t,correctCount:a,incorrectCount:r,unansweredCount:s,timeUsed:n,totalQuestions:i,answers:o={},errores:c=0}){try{if(!e)throw new Error("ID del paciente es requerido");if(!t)throw new Error("Tipo de test es requerido");const r=yield this.getAptitudeId(t),s=a||0;let i=null;"atencion"===t&&(i=this.calculateConcentration(s,c));const d={paciente_id:e,aptitud_id:r,puntaje_directo:s,tiempo_segundos:n||0,respuestas:o,errores:c,concentracion:i,created_at:(new Date).toISOString(),updated_at:(new Date).toISOString()},{data:u,error:p}=yield x.from("resultados").insert([d]).select().single();if(p)throw p;try{if(u.percentil)return k.success(`Resultado guardado y convertido automáticamente (PC: ${u.percentil})`),u;const a=this.TEST_APTITUDE_MAP[t];if(a&&u.id){const t=yield $.forzarConversionResultado(u.id);if(t.success)return t.resultado;{const t=yield is.procesarConversionAutomatica(u.id,s,a,e);if(t)return k.success(`Resultado guardado y convertido (PC: ${t.percentil})`),t}}}catch(l){k.warning("Resultado guardado, pero falló la conversión automática a PC")}return k.success("Resultado guardado correctamente en la base de datos"),u}catch(d){throw k.error(`Error al guardar resultado: ${d.message}`),d}})}static getPatientResults(e){return u(this,null,function*(){try{const{data:t,error:a}=yield x.from("resultados").select("\n          *,\n          aptitudes:aptitud_id (\n            codigo,\n            nombre,\n            descripcion\n          )\n        ").eq("paciente_id",e).order("created_at",{ascending:!1});if(a)throw a;return t||[]}catch(t){throw t}})}static hasTestResult(e,t){return u(this,null,function*(){try{const a=yield this.getAptitudeId(t),{data:r,error:s}=yield x.from("resultados").select("id").eq("paciente_id",e).eq("aptitud_id",a).limit(1);if(s)throw s;return r&&r.length>0}catch(a){return!1}})}static updateTestResult(e,t){return u(this,null,function*(){try{const{data:a,error:r}=yield x.from("resultados").update(c(o({},t),{updated_at:(new Date).toISOString()})).eq("id",e).select().single();if(r)throw r;return k.success("Resultado actualizado correctamente"),a}catch(a){throw k.error(`Error al actualizar resultado: ${a.message}`),a}})}static deleteTestResult(e){return u(this,null,function*(){try{const{error:t}=yield x.from("resultados").delete().eq("id",e);if(t)throw t;return k.success("Resultado eliminado correctamente"),!0}catch(t){throw k.error(`Error al eliminar resultado: ${t.message}`),t}})}static getPatientStats(e){return u(this,null,function*(){try{const t=yield this.getPatientResults(e),a={totalTests:t.length,averageScore:0,completedTests:t.map(e=>{var t;return null==(t=e.aptitudes)?void 0:t.codigo}).filter(Boolean),lastTestDate:t.length>0?t[0].created_at:null};if(t.length>0){const e=t.reduce((e,t)=>e+(t.puntaje_directo||0),0);a.averageScore=Math.round(e/t.length)}return a}catch(t){throw t}})}}d(os,"TEST_APTITUDE_MAP",{verbal:"V",espacial:"E",atencion:"A",razonamiento:"R",numerico:"N",mecanico:"M",ortografia:"O"});const cs=()=>{var e;const t=w(),a=C(),[r,s]=N.useState(!0),[n,i]=N.useState([]),[l,d]=N.useState(0),[m,x]=N.useState({}),[h,g]=N.useState(720),[f,b]=N.useState(!1),y=null==(e=a.state)?void 0:e.patientId,j={1:"4",2:"1",3:"4",4:"2",5:"2",6:"1",7:"3",8:"2",9:"3",10:"4",11:"3",12:"4",13:"3",14:"4",15:"2",16:"3",17:"3",18:"2",19:"3",20:"2",21:"3",22:"3",23:"4",24:"3",25:"2",26:"1",27:"3",28:"1",29:"2",30:"2",31:"1",32:"1"},v={a:"1",b:"2",c:"3",d:"4"};N.useEffect(()=>{u(void 0,null,function*(){try{yield new Promise(e=>setTimeout(e,800)),i([{id:1,type:"analogies",text:"Ciudad es a hombre como colmena es a ...",options:[{id:"a",text:"Hormiga"},{id:"b",text:"Mosquito"},{id:"c",text:"Araña"},{id:"d",text:"Abeja"}],correctAnswer:"d"},{id:2,type:"analogies",text:"Batido es a batir como zumo es a ...",options:[{id:"a",text:"Exprimir"},{id:"b",text:"Aplastar"},{id:"c",text:"Machacar"},{id:"d",text:"Succionar"}],correctAnswer:"a"},{id:3,type:"analogies",text:"Consejero es a consejo como cantante es a ...",options:[{id:"a",text:"Fama"},{id:"b",text:"Éxito"},{id:"c",text:"Composición"},{id:"d",text:"Canción"}],correctAnswer:"d"},{id:4,type:"analogies",text:"Estufa es a calor como nevera es a ...",options:[{id:"a",text:"Temperatura"},{id:"b",text:"Frío"},{id:"c",text:"Conservación"},{id:"d",text:"Congelación"}],correctAnswer:"b"},{id:5,type:"analogies",text:"Martillo es a clavo como destornillador es a ...",options:[{id:"a",text:"Hierro"},{id:"b",text:"Tornillo"},{id:"c",text:"Remache"},{id:"d",text:"Herramienta"}],correctAnswer:"b"},{id:6,type:"analogies",text:"Asa es a cesta como pomo es a ...",options:[{id:"a",text:"Puerta"},{id:"b",text:"Tirador"},{id:"c",text:"Envase"},{id:"d",text:"Manillar"}],correctAnswer:"a"},{id:7,type:"analogies",text:"Líquido es a sopa como sólido es a ...",options:[{id:"a",text:"Comer"},{id:"b",text:"Bebida"},{id:"c",text:"Plátano"},{id:"d",text:"Gaseoso"}],correctAnswer:"c"},{id:8,type:"analogies",text:"Ballena es a acuático como león es a ...",options:[{id:"a",text:"Carnívoro"},{id:"b",text:"Terrestre"},{id:"c",text:"Depredador"},{id:"d",text:"Devorador"}],correctAnswer:"b"},{id:9,type:"analogies",text:"Restar es a sumar como arreglar es a ...",options:[{id:"a",text:"Incluir"},{id:"b",text:"Corregir"},{id:"c",text:"Estropear"},{id:"d",text:"Resarcir"}],correctAnswer:"c"},{id:10,type:"analogies",text:"Más es a menos como después es a ...",options:[{id:"a",text:"Tiempo"},{id:"b",text:"Siguiente"},{id:"c",text:"Pronto"},{id:"d",text:"Antes"}],correctAnswer:"d"},{id:11,type:"analogies",text:"Fémur es a hueso como corazón es a ...",options:[{id:"a",text:"Glándula"},{id:"b",text:"Vena"},{id:"c",text:"Músculo"},{id:"d",text:"Arteria"}],correctAnswer:"c"},{id:12,type:"analogies",text:"Cuatro es a cinco como cuadrado es a ...",options:[{id:"a",text:"Triángulo"},{id:"b",text:"Heptágono"},{id:"c",text:"Hexágono"},{id:"d",text:"Pentágono"}],correctAnswer:"d"},{id:13,type:"analogies",text:"Harina es a trigo como cerveza es a ...",options:[{id:"a",text:"Manzana"},{id:"b",text:"Patata"},{id:"c",text:"Cebada"},{id:"d",text:"Alfalfa"}],correctAnswer:"c"},{id:14,type:"analogies",text:"Pie es a cuerpo como bombilla es a ...",options:[{id:"a",text:"Ojos"},{id:"b",text:"Luz"},{id:"c",text:"Vela"},{id:"d",text:"Lámpara"}],correctAnswer:"d"},{id:15,type:"analogies",text:"Excavar es a cavidad como alinear es a ...",options:[{id:"a",text:"Seguido"},{id:"b",text:"Recta"},{id:"c",text:"Acodo"},{id:"d",text:"Ensamblar"}],correctAnswer:"b"},{id:16,type:"analogies",text:"Harina es a pan como leche es a ...",options:[{id:"a",text:"Vaca"},{id:"b",text:"Trigo"},{id:"c",text:"Yogur"},{id:"d",text:"Agua"}],correctAnswer:"c"},{id:17,type:"analogies",text:"Círculo es a cuadrado como esfera es a ...",options:[{id:"a",text:"Cuadrilátero"},{id:"b",text:"Rombo"},{id:"c",text:"Cubo"},{id:"d",text:"Circunferencia"}],correctAnswer:"c"},{id:18,type:"analogies",text:"Bicicleta es a avión como metal es a ...",options:[{id:"a",text:"Solidez"},{id:"b",text:"Madera"},{id:"c",text:"Velocidad"},{id:"d",text:"Fragmento"}],correctAnswer:"b"},{id:19,type:"analogies",text:"Doctora es a doctor como amazona es a ...",options:[{id:"a",text:"Piloto"},{id:"b",text:"Modisto"},{id:"c",text:"Jinete"},{id:"d",text:"Bailarín"}],correctAnswer:"c"},{id:20,type:"analogies",text:"Escultor es a estudio como actor es a ...",options:[{id:"a",text:"Arte"},{id:"b",text:"Escenario"},{id:"c",text:"Drama"},{id:"d",text:"Literatura"}],correctAnswer:"b"},{id:21,type:"analogies",text:"Perder es a ganar como reposo es a ...",options:[{id:"a",text:"Ganancia"},{id:"b",text:"Descanso"},{id:"c",text:"Actividad"},{id:"d",text:"Calma"}],correctAnswer:"c"},{id:22,type:"analogies",text:"Encubierto es a clandestino como endeble es a ...",options:[{id:"a",text:"Doblado"},{id:"b",text:"Simple"},{id:"c",text:"Delicado"},{id:"d",text:"Comprimido"}],correctAnswer:"c"},{id:23,type:"analogies",text:"Apocado es a tímido como arrogante es a ...",options:[{id:"a",text:"Listo"},{id:"b",text:"Humilde"},{id:"c",text:"Virtuoso"},{id:"d",text:"Soberbio"}],correctAnswer:"d"},{id:24,type:"analogies",text:"Rodillo es a masa como torno es a ...",options:[{id:"a",text:"Escayola"},{id:"b",text:"Goma"},{id:"c",text:"Arcilla"},{id:"d",text:"Pintura"}],correctAnswer:"c"},{id:25,type:"analogies",text:"Hora es a tiempo como litro es a ...",options:[{id:"a",text:"Peso"},{id:"b",text:"Capacidad"},{id:"c",text:"Balanza"},{id:"d",text:"Cantidad"}],correctAnswer:"b"},{id:26,type:"analogies",text:"Indefenso es a desvalido como enlazado es a ...",options:[{id:"a",text:"Conexo"},{id:"b",text:"Recorrido"},{id:"c",text:"Torcido"},{id:"d",text:"Explorado"}],correctAnswer:"a"},{id:27,type:"analogies",text:"Reparar es a enmendar como mantener es a ...",options:[{id:"a",text:"Moderar"},{id:"b",text:"Presumir"},{id:"c",text:"Proseguir"},{id:"d",text:"Ayunar"}],correctAnswer:"c"},{id:28,type:"analogies",text:"Adelantar es a demorar como anticipar es a ...",options:[{id:"a",text:"Aplazar"},{id:"b",text:"Desistir"},{id:"c",text:"Proveer"},{id:"d",text:"Achacar"}],correctAnswer:"a"},{id:29,type:"analogies",text:"Infinito es a inagotable como vasto es a ...",options:[{id:"a",text:"Expedito"},{id:"b",text:"Colosal"},{id:"c",text:"Demorado"},{id:"d",text:"Confuso"}],correctAnswer:"b"},{id:30,type:"analogies",text:"Amenazar es a intimidar como articular es a ...",options:[{id:"a",text:"Legislar"},{id:"b",text:"Pronunciar"},{id:"c",text:"Afirmar"},{id:"d",text:"Arquear"}],correctAnswer:"b"},{id:31,type:"analogies",text:"Agua es a embudo como tierra es a ...",options:[{id:"a",text:"Criba"},{id:"b",text:"Fresadora"},{id:"c",text:"Cincel"},{id:"d",text:"Escariador"}],correctAnswer:"a"},{id:32,type:"analogies",text:"Prender es a extinguir como juntar es a ...",options:[{id:"a",text:"Separar"},{id:"b",text:"Unir"},{id:"c",text:"Apagar"},{id:"d",text:"Reducir"}],correctAnswer:"a"}]),s(!1)}catch(e){k.error("Error al cargar las preguntas del test"),s(!1)}})},[]);const A=N.useCallback(()=>u(void 0,null,function*(){try{const a=Object.keys(m).length,r=n.length,s=r-a;let i=0;Object.entries(m).forEach(([e,t])=>{j[e]===v[t]&&i++});const o=a-i,c=720-h,l={correctCount:i,incorrectCount:o,unansweredCount:s,timeUsed:c,totalQuestions:r,testType:"verbal"};if(y)try{yield os.saveTestResult({patientId:y,testType:"verbal",correctCount:i,incorrectCount:o,unansweredCount:s,timeUsed:c,totalQuestions:r,answers:m,errores:o})}catch(e){}k.success(`Test completado. Has respondido ${a} de ${r} preguntas. Respuestas correctas: ${i}`),t("/test/results/verbal",{state:l})}catch(e){k.error("Error al procesar los resultados del test")}}),[m,n.length,h,t,y]);N.useEffect(()=>{if(!f||h<=0)return;const e=setInterval(()=>{g(t=>t<=1?(clearInterval(e),setTimeout(()=>A(),0),0):t-1)},1e3);return()=>clearInterval(e)},[f,h,A]);const E=e=>{const t=e%60;return`${Math.floor(e/60).toString().padStart(2,"0")}:${t.toString().padStart(2,"0")}`},_=n[l],S=!!_&&m[_.id];return p.jsxs("div",{className:"container mx-auto py-6 max-w-4xl",children:[p.jsxs("div",{className:"mb-6",children:[p.jsxs("div",{className:"text-center mb-4",children:[p.jsxs("h1",{className:"text-2xl font-bold text-gray-800 mb-2",children:[p.jsx("i",{className:"fas fa-comments mr-2 text-blue-600"}),"Test de Aptitud Verbal"]}),p.jsx("p",{className:"text-gray-600",children:"Evaluación de analogías verbales y comprensión de relaciones entre conceptos"})]}),f&&p.jsx("div",{className:"text-center",children:p.jsx("div",{className:"text-xl font-mono font-bold text-red-600",children:E(h)})})]}),r?p.jsx("div",{className:"py-16 text-center",children:p.jsxs("div",{className:"flex flex-col items-center justify-center",children:[p.jsx("div",{className:"animate-spin rounded-full h-12 w-12 border-b-2 border-blue-500 mb-4"}),p.jsx("p",{className:"text-gray-500",children:"Cargando test de razonamiento verbal..."})]})}):f?p.jsx(p.Fragment,{children:p.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-4 gap-6",children:[p.jsx("div",{className:"md:col-span-3",children:p.jsxs(R,{className:"mb-6",children:[p.jsxs(I,{className:"flex justify-between items-center",children:[p.jsxs("div",{children:[p.jsxs("h2",{className:"text-lg font-medium",children:["Pregunta ",l+1," de ",n.length]}),p.jsx("p",{className:"text-sm text-gray-500",children:_?(T=_.type,"analogies"===T?"Analogías":T):""})]}),p.jsx("div",{className:"text-sm text-gray-500",children:S?"Respondida":"Sin responder"})]}),p.jsx(M,{children:_&&p.jsxs(p.Fragment,{children:[p.jsx("div",{className:"text-gray-800 mb-6 whitespace-pre-line font-medium",children:_.text}),p.jsx("div",{className:"space-y-3",children:_.options.map(e=>p.jsx("div",{className:"border rounded-lg p-3 cursor-pointer transition-colors "+(m[_.id]===e.id&&e.id===_.correctAnswer?"bg-green-100 border-green-500":m[_.id]===e.id?"bg-blue-50 border-blue-500":"hover:bg-gray-50"),onClick:()=>{return t=_.id,a=e.id,void x(c(o({},m),{[t]:a}));var t,a},children:p.jsxs("div",{className:"flex items-center",children:[p.jsx("div",{className:"w-6 h-6 flex items-center justify-center rounded-full mr-3 "+(m[_.id]===e.id?"bg-blue-500 text-white":"bg-gray-200 text-gray-700"),children:e.id.toUpperCase()}),p.jsx("div",{className:"text-gray-700",children:e.text})]})},e.id))})]})}),p.jsxs(B,{className:"flex justify-between",children:[p.jsx(L,{variant:"outline",onClick:()=>{l>0&&d(l-1)},disabled:0===l,children:"Anterior"}),l<n.length-1?p.jsx(L,{variant:"primary",onClick:()=>{l<n.length-1&&d(l+1)},children:"Siguiente"}):p.jsx(L,{variant:"primary",onClick:A,children:"Finalizar Test"})]})]})}),p.jsx("div",{children:p.jsxs(R,{className:"sticky top-6",children:[p.jsx(I,{children:p.jsx("h2",{className:"text-md font-medium",children:"Navegación"})}),p.jsxs(M,{children:[p.jsx("div",{className:"grid grid-cols-4 gap-2",children:n.map((e,t)=>p.jsx("button",{className:"w-8 h-8 rounded-full font-medium text-sm "+(l===t?"bg-blue-500 text-white":m[e.id]?"bg-green-100 text-green-800 border border-green-300":"bg-gray-100 text-gray-800 hover:bg-gray-200"),onClick:()=>d(t),title:`Pregunta ${t+1}`,children:t+1},e.id))}),p.jsxs("div",{className:"mt-6",children:[p.jsxs("div",{className:"flex items-center justify-between mb-2 text-sm",children:[p.jsx("span",{children:"Progreso"}),p.jsxs("span",{children:[Object.keys(m).length," de ",n.length]})]}),p.jsx("div",{className:"w-full bg-gray-200 rounded-full h-2",children:p.jsx("div",{className:"bg-green-500 h-2 rounded-full",style:{width:Object.keys(m).length/n.length*100+"%"}})})]}),p.jsx("div",{className:"mt-6",children:p.jsxs("div",{className:"bg-blue-50 p-3 rounded-lg border border-blue-100 mb-4",children:[p.jsx("h3",{className:"text-sm font-medium text-blue-700 mb-1",children:"Información"}),p.jsxs("p",{className:"text-xs text-gray-600 mb-2",children:["Tiempo restante: ",p.jsx("span",{className:"font-medium",children:E(h)})]}),p.jsx("p",{className:"text-xs text-gray-600",children:"Recuerda que al responder una pregunta, puedes cambiar tu respuesta antes de finalizar el test."})]})}),p.jsx(L,{variant:"primary",className:"w-full mt-2",onClick:A,children:"Finalizar Test"})]})]})})]})}):p.jsxs(R,{children:[p.jsx(I,{children:p.jsx("h2",{className:"text-xl font-semibold text-gray-800",children:"Razonamiento Verbal: Instrucciones"})}),p.jsx(M,{children:p.jsxs("div",{className:"space-y-6",children:[p.jsxs("div",{children:[p.jsx("h3",{className:"text-lg font-medium text-gray-700 mb-2",children:"¿Qué es el Razonamiento Verbal?"}),p.jsx("p",{className:"text-gray-600 mb-2",children:"El razonamiento verbal es la capacidad para comprender y establecer relaciones lógicas entre conceptos expresados mediante palabras. Implica entender analogías, relaciones semánticas y encontrar patrones en expresiones verbales."}),p.jsx("p",{className:"text-gray-600",children:"Esta habilidad es fundamental en el ámbito académico y profesional, siendo especialmente relevante para carreras que requieren pensamiento lógico y analítico."})]}),p.jsxs("div",{children:[p.jsx("h3",{className:"text-lg font-medium text-gray-700 mb-2",children:"Instrucciones del Test"}),p.jsx("p",{className:"text-gray-600 mb-3",children:"A continuación encontrarás frases a las que les falta una palabra que ha sido sustituida por puntos suspensivos. Tu tarea consistirá en descubrir qué palabra falta para que la frase resulte verdadera y con sentido."}),p.jsx("p",{className:"text-gray-600 mb-3",children:"En cada ejercicio se proponen cuatro palabras u opciones de respuesta posibles. Entre las cuatro palabras solamente UNA es la opción correcta, la que completa mejor la frase dotándola de sentido."}),p.jsx("p",{className:"text-gray-600",children:'Las frases tienen la siguiente estructura: "A es a B como C es a D". Deberás identificar la relación entre A y B, y aplicar la misma relación entre C y la palabra que falta (D).'})]}),p.jsxs("div",{className:"bg-slate-200 p-4 rounded-lg border border-slate-300",children:[p.jsx("h3",{className:"text-lg font-medium text-orange-700 mb-2",children:"Ejemplos"}),p.jsxs("div",{className:"mb-6",children:[p.jsxs("p",{className:"text-gray-600 mb-3",children:[p.jsx("strong",{className:"text-blue-600",children:"Ejemplo 1:"})," ",p.jsx("strong",{children:"Alto es a bajo como grande es a ..."})]}),p.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-2",children:[p.jsx("div",{className:"bg-white p-3 rounded border border-gray-200",children:"A. Visible"}),p.jsx("div",{className:"bg-white p-3 rounded border border-gray-200",children:"B. Gordo"}),p.jsx("div",{className:"bg-lime-100 p-3 rounded border border-lime-300 font-medium",children:"C. Pequeño"}),p.jsx("div",{className:"bg-white p-3 rounded border border-gray-200",children:"D. Poco"})]}),p.jsxs("p",{className:"text-gray-600 mt-3",children:["La respuesta correcta es ",p.jsx("strong",{children:"C. Pequeño"}),", porque grande y pequeño se relacionan de la misma forma que alto y bajo: son opuestos."]})]}),p.jsxs("div",{children:[p.jsxs("p",{className:"text-gray-600 mb-3",children:[p.jsx("strong",{className:"text-blue-600",children:"Ejemplo 2:"})," ",p.jsx("strong",{children:"...?... es a estrella como tierra es a planeta."})]}),p.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-2",children:[p.jsx("div",{className:"bg-white p-3 rounded border border-gray-200",children:"A. Luz"}),p.jsx("div",{className:"bg-white p-3 rounded border border-gray-200",children:"B. Calor"}),p.jsx("div",{className:"bg-white p-3 rounded border border-gray-200",children:"C. Noche"}),p.jsx("div",{className:"bg-lime-100 p-3 rounded border border-lime-300 font-medium",children:"D. Sol"})]}),p.jsxs("p",{className:"text-gray-600 mt-3",children:["La respuesta correcta es ",p.jsx("strong",{children:"D. Sol"}),', porque sol y estrella guardan entre sí la misma relación que tierra y planeta: el Sol es una estrella y la Tierra es un planeta. Fíjate igualmente en que cualquiera de las otras opciones no sería correcta; por ejemplo, en la opción B, es cierto que las estrellas producen calor, pero no tiene sentido la misma relación en las dos últimas palabras ("planeta" no produce "tierra").']})]})]}),p.jsxs("div",{children:[p.jsx("h3",{className:"text-lg font-medium text-gray-700 mb-2",children:"Detalles del Test"}),p.jsxs("ul",{className:"list-disc pl-5 space-y-2 text-gray-600",children:[p.jsx("li",{children:"El test consta de 32 preguntas de analogías verbales."}),p.jsxs("li",{children:["Dispondrás de ",p.jsx("span",{className:"font-medium",children:"12 minutos"})," para completar todas las preguntas."]}),p.jsx("li",{children:"Puedes navegar libremente entre las preguntas y modificar tus respuestas durante el tiempo disponible."}),p.jsx("li",{children:'Al finalizar el tiempo o al presionar "Finalizar Test", se enviará automáticamente y no podrás realizar más cambios.'}),p.jsx("li",{children:"Cada pregunta tiene el mismo valor, por lo que te recomendamos responder a todas."}),p.jsx("li",{children:"No se penalizan las respuestas incorrectas, así que intenta responder todas las preguntas."}),p.jsx("li",{children:"Si terminas antes del tiempo concedido, repasa tus respuestas."})]})]}),p.jsxs("div",{className:"bg-yellow-50 border-l-4 border-yellow-500 p-4",children:[p.jsx("h3",{className:"text-lg font-medium text-yellow-800 mb-1",children:"Importante"}),p.jsx("p",{className:"text-yellow-700",children:"Una vez iniciado el test, el temporizador no se detendrá. Asegúrate de disponer del tiempo necesario para completarlo sin interrupciones. Encuentra un lugar tranquilo y asegúrate de tener una buena conexión a internet."})]})]})}),p.jsx(B,{className:"flex justify-end",children:p.jsx(L,{variant:"primary",onClick:()=>{b(!0),k.info("Test iniciado. ¡Buena suerte!")},className:"px-6 py-2",children:"Comenzar Test"})})]})]});var T},ls=Object.freeze(Object.defineProperty({__proto__:null,default:cs},Symbol.toStringTag,{value:"Module"}));var ds={exports:{}};function us(){}function ps(){}ps.resetWarningCache=us;ds.exports=function(){function e(e,t,a,r,s,n){if("SECRET_DO_NOT_PASS_THIS_OR_YOU_WILL_BE_FIRED"!==n){var i=new Error("Calling PropTypes validators directly is not supported by the `prop-types` package. Use PropTypes.checkPropTypes() to call them. Read more at http://fb.me/use-check-prop-types");throw i.name="Invariant Violation",i}}function t(){return e}e.isRequired=e;var a={array:e,bigint:e,bool:e,func:e,number:e,object:e,string:e,symbol:e,any:e,arrayOf:t,element:e,elementType:e,instanceOf:t,node:e,objectOf:t,oneOf:t,oneOfType:t,shape:t,exact:t,checkPropTypes:ps,resetWarningCache:us};return a.PropTypes=a,a}();const ms=S(ds.exports);ms.number.isRequired,ms.number.isRequired,ms.object.isRequired,ms.func.isRequired,ms.number,ms.number.isRequired,ms.func.isRequired;const xs=()=>{const e=w(),[t,a]=N.useState(0),[r,s]=N.useState({}),[n,i]=N.useState(!1),{timeRemaining:l,startTimer:d,stopTimer:u,formatTime:m}=(e=>{const[t,a]=N.useState(e),[r,s]=N.useState(!1),n=N.useCallback(()=>{s(!0)},[]),i=N.useCallback(()=>{s(!1)},[]),o=N.useCallback(()=>{a(e),s(!1)},[e]),c=N.useCallback(e=>{const t=e%60;return`${Math.floor(e/60)}:${t<10?"0":""}${t}`},[]);return N.useEffect(()=>{let e;return r&&t>0?e=setInterval(()=>{a(e=>e-1)},1e3):0===t&&s(!1),()=>{e&&clearInterval(e)}},[r,t]),{timeRemaining:t,isRunning:r,startTimer:n,stopTimer:i,resetTimer:o,formatTime:c}})(600);N.useEffect(()=>(d(),()=>{u()}),[d,u]),N.useEffect(()=>{0===l&&h()},[l]);const x=[{id:1,text:"Identifica la palabra mal escrita:",options:[{id:"A",text:"reloj"},{id:"B",text:"reciclaje"},{id:"C",text:"reyna"},{id:"D",text:"nube"}],correctAnswer:"C"},{id:2,text:"Identifica la palabra mal escrita:",options:[{id:"A",text:"hola"},{id:"B",text:"Zoo"},{id:"C",text:"ambos"},{id:"D",text:"vallena"}],correctAnswer:"D"},{id:3,text:"Identifica la palabra mal escrita:",options:[{id:"A",text:"adibinar"},{id:"B",text:"inmediato"},{id:"C",text:"gestar"},{id:"D",text:"anchoa"}],correctAnswer:"A"},{id:4,text:"Identifica la palabra mal escrita:",options:[{id:"A",text:"herrero"},{id:"B",text:"saver"},{id:"C",text:"cerrar"},{id:"D",text:"honrado"}],correctAnswer:"B"},{id:5,text:"Identifica la palabra mal escrita:",options:[{id:"A",text:"éxtasis"},{id:"B",text:"cesta"},{id:"C",text:"ademas"},{id:"D",text:"llevar"}],correctAnswer:"C"},{id:6,text:"Identifica la palabra mal escrita:",options:[{id:"A",text:"avión"},{id:"B",text:"abrir"},{id:"C",text:"favor"},{id:"D",text:"espionage"}],correctAnswer:"D"},{id:7,text:"Identifica la palabra mal escrita:",options:[{id:"A",text:"insecto"},{id:"B",text:"jota"},{id:"C",text:"habrigo"},{id:"D",text:"extraño"}],correctAnswer:"C"},{id:8,text:"Identifica la palabra mal escrita:",options:[{id:"A",text:"hacha"},{id:"B",text:"oler"},{id:"C",text:"polbo"},{id:"D",text:"abril"}],correctAnswer:"C"},{id:9,text:"Identifica la palabra mal escrita:",options:[{id:"A",text:"amartillar"},{id:"B",text:"desacer"},{id:"C",text:"exageración"},{id:"D",text:"humildad"}],correctAnswer:"B"},{id:10,text:"Identifica la palabra mal escrita:",options:[{id:"A",text:"bendige"},{id:"B",text:"bifurcación"},{id:"C",text:"amarrar"},{id:"D",text:"país"}],correctAnswer:"A"},{id:11,text:"Identifica la palabra mal escrita:",options:[{id:"A",text:"horrible"},{id:"B",text:"llacimiento"},{id:"C",text:"inmóvil"},{id:"D",text:"enredar"}],correctAnswer:"B"},{id:12,text:"Identifica la palabra mal escrita:",options:[{id:"A",text:"zebra"},{id:"B",text:"impaciente"},{id:"C",text:"alrededor"},{id:"D",text:"mayor"}],correctAnswer:"A"},{id:13,text:"Identifica la palabra mal escrita:",options:[{id:"A",text:"hormona"},{id:"B",text:"jirafa"},{id:"C",text:"desván"},{id:"D",text:"enpañar"}],correctAnswer:"D"},{id:14,text:"Identifica la palabra mal escrita:",options:[{id:"A",text:"abdicar"},{id:"B",text:"area"},{id:"C",text:"ombligo"},{id:"D",text:"extinguir"}],correctAnswer:"B"},{id:15,text:"Identifica la palabra mal escrita:",options:[{id:"A",text:"júbilo"},{id:"B",text:"lúz"},{id:"C",text:"quince"},{id:"D",text:"hilera"}],correctAnswer:"B"},{id:16,text:"Identifica la palabra mal escrita:",options:[{id:"A",text:"inexorable"},{id:"B",text:"coraje"},{id:"C",text:"ingerir"},{id:"D",text:"hunir"}],correctAnswer:"D"},{id:17,text:"Identifica la palabra mal escrita:",options:[{id:"A",text:"aereo"},{id:"B",text:"conserje"},{id:"C",text:"drástico"},{id:"D",text:"ataviar"}],correctAnswer:"A"},{id:18,text:"Identifica la palabra mal escrita:",options:[{id:"A",text:"grave"},{id:"B",text:"abrumar"},{id:"C",text:"contración"},{id:"D",text:"enmienda"}],correctAnswer:"C"},{id:19,text:"Identifica la palabra mal escrita:",options:[{id:"A",text:"hay"},{id:"B",text:"gemido"},{id:"C",text:"carácter"},{id:"D",text:"harpón"}],correctAnswer:"D"},{id:20,text:"Identifica la palabra mal escrita:",options:[{id:"A",text:"embarcar"},{id:"B",text:"ambiguo"},{id:"C",text:"arroyo"},{id:"D",text:"esotérico"}],correctAnswer:"D"},{id:21,text:"Identifica la palabra mal escrita:",options:[{id:"A",text:"léntamente"},{id:"B",text:"utopía"},{id:"C",text:"aprensivo"},{id:"D",text:"irascible"}],correctAnswer:"A"},{id:22,text:"Identifica la palabra mal escrita:",options:[{id:"A",text:"desahogar"},{id:"B",text:"córnea"},{id:"C",text:"convenido"},{id:"D",text:"azúl"}],correctAnswer:"D"},{id:23,text:"Identifica la palabra mal escrita:",options:[{id:"A",text:"próspero"},{id:"B",text:"fué"},{id:"C",text:"regencia"},{id:"D",text:"pelaje"}],correctAnswer:"B"},{id:24,text:"Identifica la palabra mal escrita:",options:[{id:"A",text:"savia"},{id:"B",text:"ciénaga"},{id:"C",text:"andamiage"},{id:"D",text:"inmediatamente"}],correctAnswer:"C"},{id:25,text:"Identifica la palabra mal escrita:",options:[{id:"A",text:"traspié"},{id:"B",text:"urón"},{id:"C",text:"embellecer"},{id:"D",text:"vasija"}],correctAnswer:"B"},{id:26,text:"Identifica la palabra mal escrita:",options:[{id:"A",text:"río"},{id:"B",text:"barar"},{id:"C",text:"hiena"},{id:"D",text:"buhardilla"}],correctAnswer:"B"},{id:27,text:"Identifica la palabra mal escrita:",options:[{id:"A",text:"sátira"},{id:"B",text:"crujir"},{id:"C",text:"subrayar"},{id:"D",text:"extrategia"}],correctAnswer:"D"},{id:28,text:"Identifica la palabra mal escrita:",options:[{id:"A",text:"dátil"},{id:"B",text:"imágen"},{id:"C",text:"geranio"},{id:"D",text:"anteojo"}],correctAnswer:"B"},{id:29,text:"Identifica la palabra mal escrita:",options:[{id:"A",text:"incisivo"},{id:"B",text:"baya"},{id:"C",text:"impío"},{id:"D",text:"arnes"}],correctAnswer:"D"},{id:30,text:"Identifica la palabra mal escrita:",options:[{id:"A",text:"jersey"},{id:"B",text:"berengena"},{id:"C",text:"exhibir"},{id:"D",text:"atestar"}],correctAnswer:"B"},{id:31,text:"Identifica la palabra mal escrita:",options:[{id:"A",text:"versátil"},{id:"B",text:"hogaza"},{id:"C",text:"vadear"},{id:"D",text:"hurraca"}],correctAnswer:"D"},{id:32,text:"Identifica la palabra mal escrita:",options:[{id:"A",text:"exacerbar"},{id:"B",text:"leído"},{id:"C",text:"hayar"},{id:"D",text:"hostil"}],correctAnswer:"C"}],h=()=>{u(),i(!0);let t=0,a=0,s=0;x.forEach(e=>{const n=r[e.id];n?n===e.correctAnswer?t++:a++:s++}),e("/test/results/ortografia",{state:{correctCount:t,incorrectCount:a,unansweredCount:s,timeUsed:600-l,totalQuestions:x.length,testType:"ortografia"}})};Object.keys(r).length,x.length;const g=x[t],f=void 0!==r[g.id];return p.jsxs("div",{className:"container mx-auto py-6 max-w-4xl",children:[p.jsxs("div",{className:"mb-6",children:[p.jsxs("div",{className:"text-center mb-4",children:[p.jsxs("h1",{className:"text-2xl font-bold text-gray-800 mb-2",children:[p.jsx("i",{className:"fas fa-spell-check mr-2 text-green-600"}),"Test de Ortografía"]}),p.jsx("p",{className:"text-gray-600",children:"Identificación de palabras con errores ortográficos"})]}),p.jsx("div",{className:"text-center",children:p.jsx("div",{className:"text-xl font-mono font-bold text-red-600",children:m(l)})})]}),p.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-4 gap-6",children:[p.jsx("div",{className:"md:col-span-3",children:p.jsxs("div",{className:"bg-white rounded-lg shadow border border-gray-200",children:[p.jsx("div",{className:"p-4 border-b border-gray-200",children:p.jsxs("div",{className:"flex justify-between items-center",children:[p.jsxs("div",{children:[p.jsxs("h2",{className:"text-lg font-semibold text-gray-800",children:["Pregunta ",t+1," de ",x.length]}),p.jsx("p",{className:"text-sm text-gray-600",children:"Ortografía"})]}),p.jsx("div",{className:"text-sm font-medium text-gray-500",children:f?"Respondida":"Sin responder"})]})}),p.jsxs("div",{className:"p-6",children:[p.jsx("p",{className:"text-lg font-medium text-gray-800 mb-6",children:g.text}),p.jsx("div",{className:"space-y-3",children:g.options.map(e=>p.jsx("button",{className:"w-full text-left p-4 rounded-lg border "+(r[g.id]===e.id?"bg-blue-50 border-blue-500":"bg-white border-gray-200 hover:bg-gray-50"),onClick:()=>{return t=g.id,a=e.id,void s(e=>c(o({},e),{[t]:a}));var t,a},children:p.jsxs("div",{className:"flex items-center",children:[p.jsx("div",{className:"w-8 h-8 flex items-center justify-center rounded-full border mr-3 "+(r[g.id]===e.id?"bg-blue-500 text-white border-blue-500":"text-gray-500 border-gray-300"),children:e.id}),p.jsx("span",{className:"text-gray-800 font-medium",children:e.text})]})},e.id))})]}),p.jsxs("div",{className:"px-6 py-4 border-t border-gray-200 flex justify-between",children:[p.jsx("button",{onClick:()=>{t>0&&a(t-1)},disabled:0===t,className:"px-4 py-2 rounded-md "+(0===t?"bg-gray-100 text-gray-400 cursor-not-allowed":"bg-white border border-gray-300 text-gray-700 hover:bg-gray-50"),children:"Anterior"}),p.jsx("button",{onClick:t<x.length-1?()=>{t<x.length-1&&a(t+1)}:h,className:"px-6 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700",children:t<x.length-1?"Siguiente":"Finalizar Test"})]})]})}),p.jsx("div",{children:p.jsxs("div",{className:"bg-white rounded-lg shadow border border-gray-200 sticky top-6",children:[p.jsx("div",{className:"p-4 border-b border-gray-200",children:p.jsx("h2",{className:"text-md font-medium",children:"Navegación"})}),p.jsxs("div",{className:"p-4",children:[p.jsx("div",{className:"grid grid-cols-4 gap-2",children:x.map((e,s)=>p.jsx("button",{className:"w-8 h-8 rounded-full font-medium text-sm "+(t===s?"bg-blue-500 text-white":void 0!==r[x[s].id]?"bg-green-100 text-green-800 border border-green-300":"bg-gray-100 text-gray-800 hover:bg-gray-200"),onClick:()=>a(s),title:`Pregunta ${s+1}`,children:s+1},s))}),p.jsxs("div",{className:"mt-6",children:[p.jsxs("div",{className:"flex items-center justify-between mb-2 text-sm",children:[p.jsx("span",{children:"Progreso"}),p.jsxs("span",{children:[Object.keys(r).length," de ",x.length]})]}),p.jsx("div",{className:"w-full bg-gray-200 rounded-full h-2",children:p.jsx("div",{className:"bg-green-500 h-2 rounded-full",style:{width:Object.keys(r).length/x.length*100+"%"}})})]}),p.jsx("div",{className:"mt-6",children:p.jsxs("div",{className:"bg-blue-50 p-3 rounded-lg border border-blue-100 mb-4",children:[p.jsx("h3",{className:"text-sm font-medium text-blue-700 mb-1",children:"Información"}),p.jsxs("p",{className:"text-xs text-gray-600 mb-2",children:["Tiempo restante: ",p.jsx("span",{className:"font-medium",children:m(l)})]}),p.jsx("p",{className:"text-xs text-gray-600",children:"Recuerda que al responder una pregunta, puedes cambiar tu respuesta antes de finalizar el test."})]})}),p.jsx("button",{onClick:h,className:"w-full mt-2 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 font-medium",children:"Finalizar Test"})]})]})})]})]})},hs=Object.freeze(Object.defineProperty({__proto__:null,default:xs},Symbol.toStringTag,{value:"Module"})),gs=e=>{let t=e.startsWith("/")?e.slice(1):e;return t.startsWith("assets/images/")?t=t.replace("assets/images/",""):t.startsWith("images/")&&(t=t.replace("images/","")),(e=>{const t=e.startsWith("/")?e.slice(1):e,a="/Bat-7/";return t.startsWith("assets/")?`${a}${t}`:`${a}assets/${t}`})(`images/${t}`)},fs=(e,t)=>gs(`${e}/${t}`),bs=()=>{const e=w(),[t,a]=N.useState(!0),[r,s]=N.useState([]),[n,i]=N.useState(0),[l,d]=N.useState({}),[m,x]=N.useState(1200),[h,g]=N.useState(!1),f={1:"4",2:"4",3:"4",4:"3",5:"2",6:"4",7:"3",8:"3",9:"1",10:"4",11:"3",12:"1",13:"2",14:"2",15:"3",16:"2",17:"1",18:"3",19:"3",20:"4",21:"3",22:"2",23:"2",24:"1",25:"3",26:"1",27:"1",28:"1",29:"3",30:"4",31:"3",32:"2"},b={1:"a",2:"b",3:"c",4:"d"},y={a:"1",b:"2",c:"3",d:"4"};N.useEffect(()=>{u(void 0,null,function*(){try{yield new Promise(e=>setTimeout(e,800));const e=Array.from({length:32},(e,t)=>({id:t+1,type:"series",imagePath:fs("razonamiento",`Racionamiento${t+1}.png`),options:[{id:"a",text:"Opción A"},{id:"b",text:"Opción B"},{id:"c",text:"Opción C"},{id:"d",text:"Opción D"}],correctAnswer:b[f[t+1]]}));s(e),a(!1)}catch(e){k.error("Error al cargar las preguntas del test"),a(!1)}})},[]),N.useEffect(()=>{if(!h||m<=0)return;const e=setInterval(()=>{x(t=>t<=1?(clearInterval(e),j(),0):t-1)},1e3);return()=>clearInterval(e)},[h,m]);const j=()=>{const t=Object.keys(l).length,a=r.length,s=a-t;let n=0;Object.entries(l).forEach(([e,t])=>{f[e]===y[t]&&n++});const i=t-n,o=1200-m;k.success(`Test completado. Has respondido ${t} de ${a} preguntas. Respuestas correctas: ${n}`);e("/test/results/razonamiento",{state:{correctCount:n,incorrectCount:i,unansweredCount:s,timeUsed:o,totalQuestions:a,testType:"razonamiento"}})},v=e=>{const t=e%60;return`${Math.floor(e/60).toString().padStart(2,"0")}:${t.toString().padStart(2,"0")}`},A=r[n],E=!!A&&l[A.id];return p.jsxs("div",{className:"container mx-auto py-6 max-w-4xl",children:[p.jsxs("div",{className:"mb-6",children:[p.jsxs("div",{className:"text-center mb-4",children:[p.jsxs("h1",{className:"text-2xl font-bold text-gray-800 mb-2",children:[p.jsx("i",{className:"fas fa-puzzle-piece mr-2 text-amber-600"}),"Test de Razonamiento"]}),p.jsx("p",{className:"text-gray-600",children:"Continuar series lógicas de figuras"})]}),h&&p.jsx("div",{className:"text-center",children:p.jsx("div",{className:"text-xl font-mono font-bold text-red-600",children:v(m)})})]}),t?p.jsx("div",{className:"py-16 text-center",children:p.jsxs("div",{className:"flex flex-col items-center justify-center",children:[p.jsx("div",{className:"animate-spin rounded-full h-12 w-12 border-b-2 border-amber-500 mb-4"}),p.jsx("p",{className:"text-gray-500",children:"Cargando test de razonamiento..."})]})}):h?p.jsx(p.Fragment,{children:p.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-4 gap-6",children:[p.jsx("div",{className:"md:col-span-3",children:p.jsxs(R,{className:"mb-6",children:[p.jsxs(I,{className:"flex justify-between items-center",children:[p.jsxs("div",{children:[p.jsxs("h2",{className:"text-lg font-medium",children:["Pregunta ",n+1," de ",r.length]}),p.jsx("p",{className:"text-sm text-gray-500",children:A?(C=A.type,"series"===C?"Series":C):""})]}),p.jsx("div",{className:"text-sm text-gray-500",children:E?"Respondida":"Sin responder"})]}),p.jsx(M,{children:A&&p.jsxs(p.Fragment,{children:[p.jsx("div",{className:"flex justify-center mb-6",children:p.jsx("img",{src:A.imagePath,alt:`Pregunta ${A.id}`,className:"max-w-full h-auto"})}),p.jsx("div",{className:"space-y-3",children:A.options.map(e=>p.jsx("div",{className:"border rounded-lg p-3 cursor-pointer transition-colors "+(l[A.id]===e.id?"bg-amber-50 border-amber-500":"hover:bg-gray-50"),onClick:()=>{return t=A.id,a=e.id,void d(c(o({},l),{[t]:a}));var t,a},children:p.jsxs("div",{className:"flex items-center",children:[p.jsx("div",{className:"w-6 h-6 flex items-center justify-center rounded-full mr-3 "+(l[A.id]===e.id?"bg-amber-500 text-white":"bg-gray-200 text-gray-700"),children:e.id.toUpperCase()}),p.jsx("div",{className:"text-gray-700",children:e.text})]})},e.id))})]})}),p.jsxs(B,{className:"flex justify-between",children:[p.jsx(L,{variant:"outline",onClick:()=>{n>0&&i(n-1)},disabled:0===n,children:"Anterior"}),n<r.length-1?p.jsx(L,{variant:"primary",onClick:()=>{n<r.length-1&&i(n+1)},className:"bg-amber-600 hover:bg-amber-700",children:"Siguiente"}):p.jsx(L,{variant:"primary",onClick:j,className:"bg-amber-600 hover:bg-amber-700",children:"Finalizar Test"})]})]})}),p.jsx("div",{children:p.jsxs(R,{className:"sticky top-6",children:[p.jsx(I,{children:p.jsx("h2",{className:"text-md font-medium",children:"Navegación"})}),p.jsxs(M,{children:[p.jsx("div",{className:"grid grid-cols-4 gap-2",children:r.map((e,t)=>p.jsx("button",{className:"w-8 h-8 rounded-full font-medium text-sm "+(n===t?"bg-amber-500 text-white":l[e.id]?"bg-green-100 text-green-800 border border-green-300":"bg-gray-100 text-gray-800 hover:bg-gray-200"),onClick:()=>i(t),title:`Pregunta ${t+1}`,children:t+1},e.id))}),p.jsxs("div",{className:"mt-6",children:[p.jsxs("div",{className:"flex items-center justify-between mb-2 text-sm",children:[p.jsx("span",{children:"Progreso"}),p.jsxs("span",{children:[Object.keys(l).length," de ",r.length]})]}),p.jsx("div",{className:"w-full bg-gray-200 rounded-full h-2",children:p.jsx("div",{className:"bg-green-500 h-2 rounded-full",style:{width:Object.keys(l).length/r.length*100+"%"}})})]}),p.jsx("div",{className:"mt-6",children:p.jsxs("div",{className:"bg-amber-50 p-3 rounded-lg border border-amber-100 mb-4",children:[p.jsx("h3",{className:"text-sm font-medium text-amber-700 mb-1",children:"Información"}),p.jsxs("p",{className:"text-xs text-gray-600 mb-2",children:["Tiempo restante: ",p.jsx("span",{className:"font-medium",children:v(m)})]}),p.jsx("p",{className:"text-xs text-gray-600",children:"Recuerda que al responder una pregunta, puedes cambiar tu respuesta antes de finalizar el test."})]})}),p.jsx(L,{variant:"primary",className:"w-full mt-2 bg-amber-600 hover:bg-amber-700",onClick:j,children:"Finalizar Test"})]})]})})]})}):p.jsxs(R,{children:[p.jsx(I,{children:p.jsx("h2",{className:"text-xl font-semibold text-gray-800",children:"Razonamiento: Instrucciones"})}),p.jsx(M,{children:p.jsxs("div",{className:"space-y-6",children:[p.jsxs("div",{children:[p.jsx("h3",{className:"text-lg font-medium text-gray-700 mb-2",children:"¿Qué es el Razonamiento?"}),p.jsx("p",{className:"text-gray-600 mb-2",children:"El razonamiento es la capacidad para identificar patrones, relaciones y reglas lógicas en series de figuras o dibujos. Esta habilidad es fundamental para resolver problemas, tomar decisiones y aprender nuevos conceptos."})]}),p.jsxs("div",{children:[p.jsx("h3",{className:"text-lg font-medium text-gray-700 mb-2",children:"Instrucciones del Test"}),p.jsx("p",{className:"text-gray-600 mb-3",children:"En esta prueba se trabaja con series de figuras o dibujos, ordenados de acuerdo con una ley. Tu tarea consistirá en averiguar la ley que ordena las figuras y elegir entre las opciones de respuesta la que continúa la serie."}),p.jsx("p",{className:"text-gray-600 mb-3",children:"En todos los ejercicios se presenta la serie en la parte superior y las opciones de respuesta en la parte inferior. Cuando hayas decidido qué opción es la única correcta, selecciona la letra correspondiente (A, B, C o D)."})]}),p.jsxs("div",{className:"bg-slate-200 p-4 rounded-lg border border-slate-300",children:[p.jsx("h3",{className:"text-lg font-medium text-orange-700 mb-2",children:"Ejemplos"}),p.jsxs("div",{className:"mb-6",children:[p.jsx("p",{className:"text-gray-600 mb-3",children:p.jsx("strong",{className:"text-blue-600",children:"Ejemplo R1:"})}),p.jsx("div",{className:"flex justify-center mb-4",children:p.jsx("img",{src:"/assets/images/razonamiento/R1.png",alt:"Ejemplo R1",className:"max-w-full h-auto"})}),p.jsx("p",{className:"text-gray-600 mt-3",children:"En este ejemplo se presenta una figura que va girando 90 grados hacia la derecha de una casilla a otra. ¿Cuál debería ser la próxima figura de la serie?"}),p.jsxs("p",{className:"text-gray-600 mt-3",children:["La respuesta correcta es la ",p.jsx("strong",{children:"D"}),"."]})]}),p.jsxs("div",{children:[p.jsx("p",{className:"text-gray-600 mb-3",children:p.jsx("strong",{className:"text-blue-600",children:"Ejemplo R2:"})}),p.jsx("div",{className:"flex justify-center mb-4",children:p.jsx("img",{src:"/assets/images/razonamiento/R2.png",alt:"Ejemplo R2",className:"max-w-full h-auto"})})]})]}),p.jsxs("div",{children:[p.jsx("h3",{className:"text-lg font-medium text-gray-700 mb-2",children:"Detalles del Test"}),p.jsxs("ul",{className:"list-disc pl-5 space-y-2 text-gray-600",children:[p.jsx("li",{children:"El test consta de 32 preguntas de series lógicas."}),p.jsxs("li",{children:["Dispondrás de ",p.jsx("span",{className:"font-medium",children:"20 minutos"})," para completar todas las preguntas."]}),p.jsx("li",{children:"Puedes navegar libremente entre las preguntas y modificar tus respuestas durante el tiempo disponible."}),p.jsx("li",{children:'Al finalizar el tiempo o al presionar "Finalizar Test", se enviará automáticamente y no podrás realizar más cambios.'}),p.jsx("li",{children:"Cada pregunta tiene el mismo valor, por lo que te recomendamos responder a todas."}),p.jsx("li",{children:"No se penalizan las respuestas incorrectas, así que intenta responder todas las preguntas."}),p.jsx("li",{children:"Si terminas antes del tiempo concedido, repasa tus respuestas."})]})]}),p.jsxs("div",{className:"bg-yellow-50 border-l-4 border-yellow-500 p-4",children:[p.jsx("h3",{className:"text-lg font-medium text-yellow-800 mb-1",children:"Importante"}),p.jsx("p",{className:"text-yellow-700",children:"Una vez iniciado el test, el temporizador no se detendrá. Asegúrate de disponer del tiempo necesario para completarlo sin interrupciones. Encuentra un lugar tranquilo y asegúrate de tener una buena conexión a internet."})]})]})}),p.jsx(B,{className:"flex justify-end",children:p.jsx(L,{variant:"primary",onClick:()=>{g(!0),k.info("Test iniciado. ¡Buena suerte!")},className:"px-6 py-2 bg-amber-600 hover:bg-amber-700",children:"Comenzar Test"})})]})]});var C},ys=Object.freeze(Object.defineProperty({__proto__:null,default:bs},Symbol.toStringTag,{value:"Module"})),js=()=>{var e;const t=w(),a=C(),[r,s]=N.useState(!0),[n,i]=N.useState([]),[l,d]=N.useState(0),[m,x]=N.useState({}),[h,g]=N.useState(480),[f,b]=N.useState(!1),y=null==(e=a.state)?void 0:e.patientId,j=10,v={1:3,2:3,3:2,4:1,5:2,6:3,7:2,8:2,9:4,10:2,11:4,12:1,13:4,14:2,15:4,16:2,17:2,18:3,19:2,20:3,21:4,22:2,23:3,24:2,25:3,26:3,27:1,28:2,29:1,30:2,31:3,32:3,33:4,34:1,35:4,36:3,37:1,38:2,39:4,40:1,41:1,42:4,43:2,44:3,45:2,46:1,47:2,48:3,49:1,50:3,51:1,52:4,53:1,54:1,55:1,56:3,57:3,58:2,59:1,60:4,61:4,62:3,63:2,64:3,65:2,66:4,67:3,68:1,69:2,70:4,71:3,72:3,73:3,74:1,75:1,76:2,77:2,78:4,79:1,80:1};N.useEffect(()=>{u(void 0,null,function*(){try{yield new Promise(e=>setTimeout(e,800));const e=Array.from({length:80},(e,t)=>({id:t+1,type:"attention",imageUrl:fs("atencion",`Atencion${t+1}.png`),options:[{id:"0",text:"0 veces"},{id:"1",text:"1 vez"},{id:"2",text:"2 veces"},{id:"3",text:"3 veces"},{id:"4",text:"4 veces"}],correctAnswer:v[t+1]?v[t+1].toString():"0"}));i(e),s(!1)}catch(e){k.error("Error al cargar las preguntas del test"),s(!1)}})},[]),N.useEffect(()=>{if(!f||h<=0)return;const e=setInterval(()=>{g(t=>t<=1?(clearInterval(e),A(),0):t-1)},1e3);return()=>clearInterval(e)},[f,h]);const A=()=>u(void 0,null,function*(){try{const a=Object.keys(m).length,r=n.length,s=r-a;let i=0;Object.entries(m).forEach(([e,t])=>{const a=n.find(t=>t.id.toString()===e);a&&t===a.correctAnswer&&i++});const o=a-i,c=480-h,l={correctCount:i,incorrectCount:o,unansweredCount:s,timeUsed:c,totalQuestions:r,testType:"atencion"};if(y)try{yield os.saveTestResult({patientId:y,testType:"atencion",correctCount:i,incorrectCount:o,unansweredCount:s,timeUsed:c,totalQuestions:r,answers:m,errores:o})}catch(e){}k.success(`Test completado. Has respondido ${a} de ${r} preguntas. Respuestas correctas: ${i}`),t("/test/results/atencion",{state:l})}catch(e){k.error("Error al procesar los resultados del test")}}),E=e=>{const t=e%60;return`${Math.floor(e/60).toString().padStart(2,"0")}:${t.toString().padStart(2,"0")}`},_=Math.ceil(n.length/j);return p.jsxs("div",{className:"container mx-auto py-6 max-w-4xl",children:[p.jsxs("div",{className:"mb-6",children:[p.jsxs("div",{className:"text-center mb-4",children:[p.jsxs("h1",{className:"text-2xl font-bold text-gray-800 mb-2",children:[p.jsx("i",{className:"fas fa-eye mr-2 text-purple-600"}),"Test de Atención y Concentración"]}),p.jsx("p",{className:"text-gray-600",children:"Localización de símbolos específicos"})]}),f&&p.jsx("div",{className:"text-center",children:p.jsx("div",{className:"text-xl font-mono font-bold text-red-600",children:E(h)})})]}),r?p.jsx("div",{className:"py-16 text-center",children:p.jsxs("div",{className:"flex flex-col items-center justify-center",children:[p.jsx("div",{className:"animate-spin rounded-full h-12 w-12 border-b-2 border-blue-500 mb-4"}),p.jsx("p",{className:"text-gray-500",children:"Cargando test de atención..."})]})}):f?n.length>0?p.jsx(p.Fragment,{children:p.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-4 gap-6",children:[p.jsxs("div",{className:"md:col-span-3",children:[p.jsxs(R,{className:"mb-6",children:[p.jsxs(I,{className:"flex justify-between items-center",children:[p.jsxs("div",{children:[p.jsxs("h2",{className:"text-lg font-medium",children:["Página ",l+1," de ",_]}),p.jsxs("p",{className:"text-sm text-gray-500",children:["Preguntas ",l*j+1," - ",Math.min((l+1)*j,n.length)," de ",n.length]})]}),p.jsxs("div",{className:"text-sm text-gray-500",children:["Tiempo restante: ",E(h)]})]}),p.jsx(M,{children:p.jsx("div",{className:"space-y-8",children:(()=>{const e=l*j,t=e+j;return n.slice(e,t)})().map(e=>p.jsx("div",{className:"border-b pb-6 mb-6 last:border-b-0 last:pb-0 last:mb-0",children:p.jsxs("div",{className:"flex flex-col md:flex-row md:items-start gap-4",children:[p.jsxs("div",{className:"md:w-3/4",children:[p.jsxs("h3",{className:"text-md font-medium mb-3",children:["Pregunta ",e.id]}),p.jsx("div",{className:"mb-4",children:p.jsx("img",{src:e.imageUrl,alt:`Pregunta ${e.id}`,className:"w-full max-w-2xl h-auto border rounded shadow-sm",onError:e=>{e.target.onerror=null,e.target.src="https://via.placeholder.com/600x100?text=Imagen+no+disponible"}})}),p.jsx("p",{className:"text-gray-600 mb-3",children:"¿Cuántas veces aparece el símbolo modelo en esta fila?"})]}),p.jsx("div",{className:"md:w-1/4",children:p.jsx("div",{className:"space-y-2",children:e.options.map(t=>p.jsx("div",{className:"border rounded-lg p-3 cursor-pointer transition-colors "+(m[e.id]===t.id?"bg-indigo-50 border-indigo-500":"hover:bg-gray-50"),onClick:()=>{return a=e.id,r=t.id,void x(c(o({},m),{[a]:r}));var a,r},children:p.jsxs("div",{className:"flex items-center",children:[p.jsx("div",{className:"w-6 h-6 flex items-center justify-center rounded-full mr-3 "+(m[e.id]===t.id?"bg-indigo-500 text-white":"bg-gray-200 text-gray-700"),children:t.id}),p.jsx("div",{className:"text-gray-700",children:t.text})]})},t.id))})})]})},e.id))})}),p.jsxs(B,{className:"flex justify-between",children:[p.jsx(L,{variant:"outline",onClick:()=>{l>0&&(d(l-1),window.scrollTo(0,0))},disabled:0===l,children:"Página Anterior"}),p.jsxs("div",{className:"flex space-x-2",children:[Array.from({length:_},(e,t)=>p.jsx("button",{className:"w-8 h-8 rounded-full text-sm "+(l===t?"bg-indigo-500 text-white":"bg-gray-200 text-gray-700 hover:bg-gray-300"),onClick:()=>d(t),children:t+1},t)).slice(Math.max(0,l-2),Math.min(_,l+3)),l+3<_&&p.jsx("span",{className:"self-center",children:"..."})]}),l<_-1?p.jsx(L,{variant:"primary",onClick:()=>{(l+1)*j<n.length&&(d(l+1),window.scrollTo(0,0))},children:"Página Siguiente"}):p.jsx(L,{variant:"primary",onClick:A,children:"Finalizar Test"})]})]}),p.jsxs("div",{className:"flex justify-between items-center",children:[p.jsxs("div",{children:[p.jsxs("p",{className:"text-sm text-gray-600",children:["Has respondido ",Object.keys(m).length," de ",n.length," preguntas (",Math.round(Object.keys(m).length/n.length*100),"%)"]}),p.jsx("div",{className:"w-64 bg-gray-200 rounded-full h-2 mt-1",children:p.jsx("div",{className:"bg-green-500 h-2 rounded-full",style:{width:Object.keys(m).length/n.length*100+"%"}})})]}),p.jsx(L,{variant:"primary",onClick:A,children:"Finalizar Test"})]})]}),p.jsx("div",{children:p.jsxs(R,{className:"sticky top-6",children:[p.jsx(I,{children:p.jsx("h2",{className:"text-md font-medium",children:"Navegación"})}),p.jsxs(M,{children:[p.jsx("div",{className:"grid grid-cols-4 gap-2 max-h-64 overflow-y-auto p-1",children:n.map((e,t)=>{const a=Math.floor(t/j),r=a===l;return p.jsx("button",{className:"w-8 h-8 rounded-full font-medium text-sm "+(r?"bg-indigo-500 text-white":m[e.id]?"bg-green-100 text-green-800 border border-green-300":"bg-gray-100 text-gray-800 hover:bg-gray-200"),onClick:()=>{const e=Math.floor(t/j);d(e)},title:`Pregunta ${t+1} - Página ${a+1}`,children:t+1},e.id)})}),p.jsxs("div",{className:"mt-4 pt-4 border-t border-gray-200",children:[p.jsxs("div",{className:"text-sm text-gray-600 mb-2",children:["Progreso: ",Object.keys(m).length,"/",n.length]}),p.jsx("div",{className:"w-full bg-gray-200 rounded-full h-2 mb-3",children:p.jsx("div",{className:"bg-green-500 h-2 rounded-full transition-all duration-300",style:{width:Object.keys(m).length/n.length*100+"%"}})}),p.jsx(L,{variant:"primary",className:"w-full",onClick:A,children:"Finalizar Test"})]})]})]})})]})}):p.jsx(R,{children:p.jsx(M,{children:p.jsxs("div",{className:"py-8 text-center",children:[p.jsx("p",{className:"text-gray-500",children:"No se encontraron preguntas para este test."}),p.jsx(L,{variant:"outline",className:"mt-4",onClick:()=>t("/student/tests"),children:"Volver a Tests"})]})})}):p.jsxs(R,{children:[p.jsx(I,{children:p.jsx("h2",{className:"text-xl font-semibold text-gray-800",children:"Test de Atención: Instrucciones"})}),p.jsx(M,{children:p.jsxs("div",{className:"space-y-6",children:[p.jsxs("div",{children:[p.jsx("h3",{className:"text-lg font-medium text-gray-700 mb-2",children:"¿Qué es el Test de Atención?"}),p.jsx("p",{className:"text-gray-600 mb-2",children:"El test de atención evalúa tu capacidad para mantener la concentración y detectar estímulos específicos entre un conjunto de elementos similares. Esta habilidad es fundamental para el aprendizaje, el trabajo y muchas actividades cotidianas."}),p.jsx("p",{className:"text-gray-600",children:"Esta prueba mide específicamente tu atención selectiva, velocidad perceptiva, discriminación visual y capacidad de concentración sostenida."})]}),p.jsxs("div",{children:[p.jsx("h3",{className:"text-lg font-medium text-gray-700 mb-2",children:"Instrucciones del Test"}),p.jsx("p",{className:"text-gray-600 mb-3",children:"Esta prueba trata de evaluar tu rapidez y tu precisión trabajando con símbolos. En cada ejercicio aparece una fila con diferentes símbolos y tu tarea consistirá en localizar cuántas veces aparece uno determinado."}),p.jsx("p",{className:"text-gray-600 mb-3",children:"El símbolo que tienes que localizar es siempre el mismo y se presenta en la parte superior de la página; en cada ejercicio puede aparecer 0, 1, 2, 3 o 4 veces."}),p.jsx("p",{className:"text-gray-600",children:"Deberás seleccionar cuántas veces aparece el símbolo en cada fila (0, 1, 2, 3 o 4) asegurándote de que tu respuesta se corresponda con el número del ejercicio que estás contestando."})]}),p.jsxs("div",{className:"bg-slate-200 p-4 rounded-lg border border-slate-300",children:[p.jsx("h3",{className:"text-lg font-medium text-indigo-700 mb-2",children:"Ejemplos"}),p.jsxs("div",{className:"mb-6",children:[p.jsx("div",{className:"flex justify-center mb-3",children:p.jsx("img",{src:fs("atencion","Atencion.png"),alt:"Ejemplos de atención",className:"max-w-md h-auto border rounded shadow-sm",onError:e=>{e.target.onerror=null,e.target.src="https://via.placeholder.com/300x150?text=Imagen+no+disponible"}})}),p.jsxs("p",{className:"text-gray-600 mt-3",children:[p.jsx("strong",{className:"text-indigo-600",children:"Ejemplo A1:"})," El símbolo del óvalo aparece una única vez, y es el tercer símbolo de la fila. Por eso la respuesta correcta es ",p.jsx("strong",{children:"1"}),"."]}),p.jsxs("p",{className:"text-gray-600 mt-2",children:[p.jsx("strong",{className:"text-indigo-600",children:"Ejemplo A2:"})," En esta ocasión no hay ningún símbolo que coincida exactamente con el modelo; por tanto la respuesta correcta es ",p.jsx("strong",{children:"0"}),"."]}),p.jsxs("p",{className:"text-gray-600 mt-2",children:[p.jsx("strong",{className:"text-indigo-600",children:"Ejemplo A3:"})," El símbolo del óvalo aparece en dos ocasiones, en primera y quinta posición. Por eso, la respuesta correcta es ",p.jsx("strong",{children:"2"}),"."]})]})]}),p.jsxs("div",{children:[p.jsx("h3",{className:"text-lg font-medium text-gray-700 mb-2",children:"Detalles del Test"}),p.jsxs("ul",{className:"list-disc pl-5 space-y-2 text-gray-600",children:[p.jsx("li",{children:"El test consta de 80 preguntas de atención."}),p.jsxs("li",{children:["Dispondrás de ",p.jsx("span",{className:"font-medium",children:"8 minutos"})," para completar todas las preguntas."]}),p.jsx("li",{children:"Las preguntas se presentan en páginas de 10 preguntas cada una."}),p.jsx("li",{children:"Puedes navegar libremente entre las páginas y modificar tus respuestas durante el tiempo disponible."}),p.jsx("li",{children:'Al finalizar el tiempo o al presionar "Finalizar Test", se enviará automáticamente y no podrás realizar más cambios.'}),p.jsx("li",{children:"Cada pregunta tiene el mismo valor, por lo que te recomendamos responder a todas."}),p.jsx("li",{children:"No se penalizan las respuestas incorrectas, así que intenta responder todas las preguntas."})]})]}),p.jsxs("div",{className:"bg-yellow-50 border-l-4 border-yellow-500 p-4",children:[p.jsx("h3",{className:"text-lg font-medium text-yellow-800 mb-1",children:"Importante"}),p.jsx("p",{className:"text-yellow-700",children:"Una vez iniciado el test, el temporizador no se detendrá. Asegúrate de disponer del tiempo necesario para completarlo sin interrupciones. Encuentra un lugar tranquilo y asegúrate de tener una buena conexión a internet."})]})]})}),p.jsx(B,{className:"flex justify-end",children:p.jsx(L,{variant:"primary",onClick:()=>{b(!0),k.info("Test iniciado. ¡Buena suerte!")},className:"px-6 py-2",children:"Comenzar Test"})})]})]})},vs=Object.freeze(Object.defineProperty({__proto__:null,default:js},Symbol.toStringTag,{value:"Module"})),Ns=()=>{const e=w(),[t,a]=N.useState(!0),[r,s]=N.useState([]),[n,i]=N.useState(0),[l,d]=N.useState({}),[m,x]=N.useState(900),[h,g]=N.useState(!1),f={1:"C",2:"D",3:"B",4:"A",5:"A",6:"A",7:"D",8:"B",9:"D",10:"D",11:"C",12:"A",13:"D",14:"A",15:"A",16:"B",17:"C",18:"A",19:"C",20:"D",21:"D",22:"C",23:"D",24:"B",25:"C",26:"C",27:"D",28:"C"};N.useEffect(()=>{u(void 0,null,function*(){try{yield new Promise(e=>setTimeout(e,800));const e=Array.from({length:28},(e,t)=>({id:t+1,type:"spatial",imageUrl:fs("espacial",`Espacial${t+1}.png`),options:[{id:"A",text:"Opción A"},{id:"B",text:"Opción B"},{id:"C",text:"Opción C"},{id:"D",text:"Opción D"}],correctAnswer:f[t+1]}));s(e),a(!1)}catch(e){k.error("Error al cargar las preguntas del test"),a(!1)}})},[]),N.useEffect(()=>{if(!h||m<=0)return;const e=setInterval(()=>{x(t=>t<=1?(clearInterval(e),b(),0):t-1)},1e3);return()=>clearInterval(e)},[h,m]);const b=()=>{const t=Object.keys(l).length,a=r.length,s=a-t;let n=0;Object.entries(l).forEach(([e,t])=>{const a=r.find(t=>t.id.toString()===e);a&&t===a.correctAnswer&&n++});const i=t-n,o=900-m;k.success(`Test completado. Has respondido ${t} de ${a} preguntas. Respuestas correctas: ${n}`);e("/test/results/espacial",{state:{correctCount:n,incorrectCount:i,unansweredCount:s,timeUsed:o,totalQuestions:a,testType:"espacial"}})},y=e=>{const t=e%60;return`${Math.floor(e/60).toString().padStart(2,"0")}:${t.toString().padStart(2,"0")}`},j=r[n],v=!!j&&l[j.id];return p.jsxs("div",{className:"container mx-auto py-6 max-w-4xl",children:[p.jsxs("div",{className:"mb-6",children:[p.jsxs("div",{className:"text-center mb-4",children:[p.jsxs("h1",{className:"text-2xl font-bold text-gray-800 mb-2",children:[p.jsx("i",{className:"fas fa-cube mr-2 text-indigo-600"}),"Test de Aptitud Espacial"]}),p.jsx("p",{className:"text-gray-600",children:"Razonamiento espacial con cubos y redes"})]}),h&&p.jsx("div",{className:"text-center",children:p.jsx("div",{className:"text-xl font-mono font-bold text-red-600",children:y(m)})})]}),t?p.jsx("div",{className:"py-16 text-center",children:p.jsxs("div",{className:"flex flex-col items-center justify-center",children:[p.jsx("div",{className:"animate-spin rounded-full h-12 w-12 border-b-2 border-blue-500 mb-4"}),p.jsx("p",{className:"text-gray-500",children:"Cargando test de razonamiento espacial..."})]})}):h?r.length>0?p.jsx(p.Fragment,{children:p.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-4 gap-6",children:[p.jsx("div",{className:"md:col-span-3",children:p.jsxs(R,{className:"mb-6",children:[p.jsxs(I,{className:"flex justify-between items-center",children:[p.jsxs("div",{children:[p.jsxs("h2",{className:"text-lg font-medium",children:["Pregunta ",n+1," de ",r.length]}),p.jsx("p",{className:"text-sm text-gray-500",children:j?(A=j.type,"spatial"===A?"Razonamiento Espacial":A):""})]}),p.jsx("div",{className:"text-sm text-gray-500",children:v?"Respondida":"Sin responder"})]}),p.jsx(M,{children:j&&p.jsxs(p.Fragment,{children:[p.jsx("div",{className:"flex justify-center mb-6",children:p.jsx("img",{src:j.imageUrl,alt:`Pregunta ${n+1}`,className:"max-w-full h-auto border rounded shadow-sm",onError:e=>{e.target.onerror=null,e.target.src="https://via.placeholder.com/600x300?text=Imagen+no+disponible"}})}),p.jsx("div",{className:"space-y-3",children:j.options.map(e=>p.jsx("div",{className:"border rounded-lg p-3 cursor-pointer transition-colors "+(l[j.id]===e.id?"bg-indigo-50 border-indigo-500":"hover:bg-gray-50"),onClick:()=>{return t=j.id,a=e.id,void d(c(o({},l),{[t]:a}));var t,a},children:p.jsxs("div",{className:"flex items-center",children:[p.jsx("div",{className:"w-6 h-6 flex items-center justify-center rounded-full mr-3 "+(l[j.id]===e.id?"bg-indigo-500 text-white":"bg-gray-200 text-gray-700"),children:e.id.toUpperCase()}),p.jsx("div",{className:"text-gray-700",children:e.text})]})},e.id))})]})}),p.jsxs(B,{className:"flex justify-between",children:[p.jsx(L,{variant:"outline",onClick:()=>{n>0&&i(n-1)},disabled:0===n,children:"Anterior"}),n<r.length-1?p.jsx(L,{variant:"primary",onClick:()=>{n<r.length-1&&i(n+1)},children:"Siguiente"}):p.jsx(L,{variant:"primary",onClick:b,children:"Finalizar Test"})]})]})}),p.jsx("div",{children:p.jsxs(R,{className:"sticky top-6",children:[p.jsx(I,{children:p.jsx("h2",{className:"text-md font-medium",children:"Navegación"})}),p.jsxs(M,{children:[p.jsx("div",{className:"grid grid-cols-4 gap-2 max-h-64 overflow-y-auto p-1",children:r.map((e,t)=>p.jsx("button",{className:"w-8 h-8 rounded-full font-medium text-sm "+(n===t?"bg-indigo-500 text-white":l[e.id]?"bg-green-100 text-green-800 border border-green-300":"bg-gray-100 text-gray-800 hover:bg-gray-200"),onClick:()=>i(t),title:`Pregunta ${t+1}`,children:t+1},e.id))}),p.jsxs("div",{className:"mt-6",children:[p.jsxs("div",{className:"flex items-center justify-between mb-2 text-sm",children:[p.jsx("span",{children:"Progreso"}),p.jsxs("span",{children:[Object.keys(l).length," de ",r.length]})]}),p.jsx("div",{className:"w-full bg-gray-200 rounded-full h-2",children:p.jsx("div",{className:"bg-green-500 h-2 rounded-full",style:{width:Object.keys(l).length/r.length*100+"%"}})})]}),p.jsx("div",{className:"mt-6",children:p.jsxs("div",{className:"bg-indigo-50 p-3 rounded-lg border border-indigo-100 mb-4",children:[p.jsx("h3",{className:"text-sm font-medium text-indigo-700 mb-1",children:"Información"}),p.jsxs("p",{className:"text-xs text-gray-600 mb-2",children:["Tiempo restante: ",p.jsx("span",{className:"font-medium",children:y(m)})]}),p.jsx("p",{className:"text-xs text-gray-600",children:"Recuerda que al responder una pregunta, puedes cambiar tu respuesta antes de finalizar el test."})]})}),p.jsx(L,{variant:"primary",className:"w-full mt-2",onClick:b,children:"Finalizar Test"})]})]})})]})}):p.jsx(R,{children:p.jsx(M,{children:p.jsxs("div",{className:"py-8 text-center",children:[p.jsx("p",{className:"text-gray-500",children:"No se encontraron preguntas para este test."}),p.jsx(L,{variant:"outline",className:"mt-4",onClick:()=>e("/student/tests"),children:"Volver a Tests"})]})})}):p.jsxs(R,{children:[p.jsx(I,{children:p.jsx("h2",{className:"text-xl font-semibold text-gray-800",children:"Razonamiento Espacial: Instrucciones"})}),p.jsx(M,{children:p.jsxs("div",{className:"space-y-6",children:[p.jsxs("div",{children:[p.jsx("h3",{className:"text-lg font-medium text-gray-700 mb-2",children:"¿Qué es el Razonamiento Espacial?"}),p.jsx("p",{className:"text-gray-600 mb-2",children:"El razonamiento espacial es la capacidad para visualizar y manipular objetos mentalmente en el espacio tridimensional. Implica entender cómo se relacionan las formas y los objetos entre sí, y cómo se transforman cuando cambian de posición o perspectiva."}),p.jsx("p",{className:"text-gray-600",children:"Esta habilidad es fundamental en campos como la arquitectura, ingeniería, diseño, matemáticas y ciencias, siendo especialmente relevante para carreras que requieren visualización y manipulación de objetos en el espacio."})]}),p.jsxs("div",{children:[p.jsx("h3",{className:"text-lg font-medium text-gray-700 mb-2",children:"Instrucciones del Test"}),p.jsx("p",{className:"text-gray-600 mb-3",children:"En este test encontrarás un cubo junto con un modelo desplegado del mismo cubo. En el modelo desplegado falta una cara, marcada con un signo de interrogación (?)."}),p.jsx("p",{className:"text-gray-600 mb-3",children:"Tu tarea consistirá en averiguar qué opción (A, B, C o D) debería aparecer en lugar del interrogante para que el modelo desplegado corresponda al cubo cuando se pliega."}),p.jsx("p",{className:"text-gray-600",children:"Para facilitar tu tarea, en el cubo se han representado en color gris los números o letras que se encuentran en las caras de atrás (las que no se ven directamente)."})]}),p.jsxs("div",{className:"bg-slate-200 p-4 rounded-lg border border-slate-300",children:[p.jsx("h3",{className:"text-lg font-medium text-indigo-700 mb-2",children:"Ejemplos"}),p.jsxs("div",{className:"mb-6",children:[p.jsx("p",{className:"text-gray-600 mb-3",children:p.jsx("strong",{className:"text-indigo-600",children:"Ejemplo 1:"})}),p.jsx("div",{className:"flex justify-center mb-3",children:p.jsx("img",{src:fs("espacial","Modelo Espacial.png"),alt:"Ejemplo 1",className:"max-w-full h-auto border rounded shadow-sm",onError:e=>{e.target.onerror=null,e.target.src="https://via.placeholder.com/400x200?text=Ejemplo+1"}})}),p.jsxs("p",{className:"text-gray-600 mt-3",children:["La respuesta correcta es ",p.jsx("strong",{children:"B"}),". Si se sustituye el interrogante por la letra «h» y se pliegan las caras del modelo hasta formar el cubo, este se corresponde con el que aparece a la izquierda."]})]}),p.jsxs("div",{children:[p.jsx("p",{className:"text-gray-600 mb-3",children:p.jsx("strong",{className:"text-indigo-600",children:"Ejemplo 2:"})}),p.jsx("div",{className:"flex justify-center mb-3",children:p.jsx("img",{src:fs("espacial","Espacial1.png"),alt:"Ejemplo 2",className:"max-w-full h-auto border rounded shadow-sm",onError:e=>{e.target.onerror=null,e.target.src="https://via.placeholder.com/400x200?text=Ejemplo+2"}})}),p.jsxs("p",{className:"text-gray-600 mt-3",children:["La respuesta correcta es ",p.jsx("strong",{children:"A"}),"."]})]})]}),p.jsxs("div",{children:[p.jsx("h3",{className:"text-lg font-medium text-gray-700 mb-2",children:"Detalles del Test"}),p.jsxs("ul",{className:"list-disc pl-5 space-y-2 text-gray-600",children:[p.jsx("li",{children:"El test consta de 28 preguntas de razonamiento espacial."}),p.jsxs("li",{children:["Dispondrás de ",p.jsx("span",{className:"font-medium",children:"15 minutos"})," para completar todas las preguntas."]}),p.jsx("li",{children:"Puedes navegar libremente entre las preguntas y modificar tus respuestas durante el tiempo disponible."}),p.jsx("li",{children:'Al finalizar el tiempo o al presionar "Finalizar Test", se enviará automáticamente y no podrás realizar más cambios.'}),p.jsx("li",{children:"Cada pregunta tiene el mismo valor, por lo que te recomendamos responder a todas."}),p.jsx("li",{children:"No se penalizan las respuestas incorrectas, así que intenta responder todas las preguntas."}),p.jsx("li",{children:"Si terminas antes del tiempo concedido, repasa tus respuestas."})]})]}),p.jsxs("div",{className:"bg-yellow-50 border-l-4 border-yellow-500 p-4",children:[p.jsx("h3",{className:"text-lg font-medium text-yellow-800 mb-1",children:"Importante"}),p.jsx("p",{className:"text-yellow-700",children:"Una vez iniciado el test, el temporizador no se detendrá. Asegúrate de disponer del tiempo necesario para completarlo sin interrupciones. Encuentra un lugar tranquilo y asegúrate de tener una buena conexión a internet."})]})]})}),p.jsx(B,{className:"flex justify-end",children:p.jsx(L,{variant:"primary",onClick:()=>{g(!0),k.info("Test iniciado. ¡Buena suerte!")},className:"px-6 py-2",children:"Comenzar Test"})})]})]});var A},ws=Object.freeze(Object.defineProperty({__proto__:null,default:Ns},Symbol.toStringTag,{value:"Module"})),As=()=>{const e=w(),[t,a]=N.useState(!1),[r,s]=N.useState(0),[n,i]=N.useState({}),[l,d]=N.useState(720),[u,m]=N.useState(!1),x=[{id:1,question:"¿Qué tipo de polea podrá subir MÁS peso sin vencerse?",subtitle:"(Si no hay diferencia, marca D).",image:fs("mecanico","mecanico1.png"),options:["A","B","C","D"],correctAnswer:0},{id:2,question:"¿Qué estante es MENOS resistente?",subtitle:"(Si no hay diferencia, marca D).",image:fs("mecanico","mecanico2.png"),options:["A","B","C","D"],correctAnswer:2},{id:3,question:"¿Qué tipo de listones permite mover la carga con MENOS esfuerzo?",subtitle:"(Si no hay diferencia, marca D).",image:fs("mecanico","mecanico3.png"),options:["A","B","C","D"],correctAnswer:0},{id:4,question:"Si el viento sopla en la dirección indicada, ¿hacia dónde tendríamos que golpear la bola para acercarla MÁS al hoyo?",subtitle:"",image:fs("mecanico","mecanico4.png"),options:["A","B","C","D"],correctAnswer:1},{id:5,question:"¿En qué zona (A, B o C) es MÁS probable que se rompan las cuerdas al colocar la carga?",subtitle:"(Si no hay diferencia, marca D).",image:fs("mecanico","mecanico5.png"),options:["A","B","C","D"],correctAnswer:1},{id:6,question:"¿De qué recipiente saldrá el líquido con MÁS fuerza?",subtitle:"",image:fs("mecanico","mecanico6.png"),options:["A","B","C","D"],correctAnswer:2},{id:7,question:"¿Cuál de estos tres recipientes llenos de agua pesa MENOS?",subtitle:"(Si no hay diferencia, marca D).",image:fs("mecanico","mecanico7.png"),options:["A","B","C","D"],correctAnswer:3},{id:8,question:"¿Qué torno deberá dar MÁS vueltas para enrollar los mismos metros de cuerda?",subtitle:"",image:fs("mecanico","mecanico8.png"),options:["A","B","C","D"],correctAnswer:3},{id:9,question:"¿Hacia qué dirección (A, B, C o D) está soplando el viento?",subtitle:"",image:fs("mecanico","mecanico9.png"),options:["A","B","C","D"],correctAnswer:1},{id:10,question:"¿Cuál de estos tres tejados es MÁS probable que se rompa en caso de nevada?",subtitle:"(Si no hay diferencia, marca D).",image:fs("mecanico","mecanico10.png"),options:["A","B","C","D"],correctAnswer:0},{id:11,question:"¿A cuál de estas personas le costará MÁS esfuerzo trasladar la carga?",subtitle:"(Si no hay diferencia, marca D).",image:fs("mecanico","mecanico11.png"),options:["A","B","C","D"],correctAnswer:2},{id:12,question:"¿Con qué bomba se inflará MÁS lentamente un colchón flotador?",subtitle:"(Si no hay diferencia, marca D).",image:fs("mecanico","mecanico12.png"),options:["A","B","C","D"],correctAnswer:2},{id:13,question:"¿En qué caso se debe ejercer MENOS fuerza en el punto indicado por la flecha para sujetar el mismo peso?",subtitle:"(Si no hay diferencia, marca D).",image:fs("mecanico","mecanico13.png"),options:["A","B","C","D"],correctAnswer:0},{id:14,question:"Si al frenar la bicicleta solo se usan los frenos delanteros, ¿hacia qué dirección será impulsado el ciclista?",subtitle:"",image:fs("mecanico","mecanico14.png"),options:["A","B","C","D"],correctAnswer:3},{id:15,question:"¿Cuál de estos tres pesos (A, B o C) pesa MENOS?",subtitle:"(Si no hay diferencia, marca D).",image:fs("mecanico","mecanico15.png"),options:["A","B","C","D"],correctAnswer:3},{id:16,question:"¿Qué columna será MÁS resistente en caso de terremoto?",subtitle:"",image:fs("mecanico","mecanico16.png"),options:["A","B","C","D"],correctAnswer:1},{id:17,question:"¿Qué micrófono tiene MENOS probabilidad de caerse ante un golpe?",subtitle:"(Si no hay diferencia, marca D).",image:fs("mecanico","mecanico17.png"),options:["A","B","C","D"],correctAnswer:0},{id:18,question:"¿Qué trayectoria (A, B o C) debe seguir el nadador para cruzar el río con MENOS esfuerzo?",subtitle:"(Si no hay diferencia, marca D).",image:fs("mecanico","mecanico18.png"),options:["A","B","C","D"],correctAnswer:1},{id:19,question:"¿En qué punto es necesario ejercer MÁS fuerza para cerrar la puerta?",subtitle:"",image:fs("mecanico","mecanico19.png"),options:["A","B","C","D"],correctAnswer:0},{id:20,question:"¿En qué caso habrá que ejercer MENOS fuerza para levantar las ruedas delanteras del carro?",subtitle:"(Si no hay diferencia, marca D).",image:fs("mecanico","mecanico20.png"),options:["A","B","C","D"],correctAnswer:2},{id:21,question:"¿Qué coche ofrece MENOS resistencia al aire?",subtitle:"(Si no hay diferencia, marca D).",image:fs("mecanico","mecanico21.png"),options:["A","B","C","D"],correctAnswer:2},{id:22,question:"¿Cómo debe agarrarse la persona a la roca para que no la arrastre la corriente?",subtitle:"(Si no hay diferencia, marca D).",image:fs("mecanico","mecanico22.png"),options:["A","B","C","D"],correctAnswer:2},{id:23,question:"Si tenemos estas tres linternas, ¿cuál iluminará un área MAYOR?",subtitle:"(Si no hay diferencia, marca D).",image:fs("mecanico","mecanico23.png"),options:["A","B","C","D"],correctAnswer:2},{id:24,question:"¿Qué coche es MENOS probable que vuelque?",subtitle:"(Si no hay diferencia, marca D).",image:fs("mecanico","mecanico24.png"),options:["A","B","C","D"],correctAnswer:2},{id:25,question:"¿En qué punto alcanzará MÁS velocidad el paracaidista?",subtitle:"(Si no hay diferencia, marca D).",image:fs("mecanico","mecanico25.png"),options:["A","B","C","D"],correctAnswer:2},{id:26,question:"Si dejáramos tan solo UNO de los bloques (A, B, C o D), ¿con cuál se mantendría la estructura en equilibrio?",subtitle:"",image:fs("mecanico","mecanico26.png"),options:["A","B","C","D"],correctAnswer:2},{id:27,question:"¿Hacia qué zona de la cápsula será impulsado el astronauta cuando la máquina gire en el sentido indicado por la flecha?",subtitle:"",image:fs("mecanico","mecanico27.png"),options:["A","B","C","D"],correctAnswer:1},{id:28,question:"Si colgamos el peso de esta forma, ¿por cuál de los puntos (A, B o C) es MENOS probable que se rompa la madera?",subtitle:"(Si no hay diferencia, marca D).",image:fs("mecanico","mecanico28.png"),options:["A","B","C","D"],correctAnswer:2}];N.useEffect(()=>{let e;return t&&!u&&l>0&&(e=setInterval(()=>{d(e=>e<=1?(f(),0):e-1)},1e3)),()=>clearInterval(e)},[t,u,l]);const h=e=>`${Math.floor(e/60)}:${(e%60).toString().padStart(2,"0")}`,g=e=>{s(e)},f=()=>{m(!0);const t=Object.keys(n).length,a=x.length,r=a-t;let s=0;x.forEach(e=>{n[e.id]===e.correctAnswer&&s++});const i=t-s,o=720-l;k.success(`Test completado. Has respondido ${t} de ${a} preguntas. Respuestas correctas: ${s}`);e("/test/results/mecanico",{state:{correctCount:s,incorrectCount:i,unansweredCount:r,timeUsed:o,totalQuestions:a,testType:"mecanico"}})},b=x[r];return p.jsxs("div",{className:"container mx-auto py-6 max-w-4xl",children:[p.jsxs("div",{className:"mb-6",children:[p.jsxs("div",{className:"text-center mb-4",children:[p.jsxs("h1",{className:"text-2xl font-bold text-gray-800 mb-2",children:[p.jsx("i",{className:"fas fa-cogs mr-2 text-orange-600"}),"Test de Aptitud Mecánica"]}),p.jsx("p",{className:"text-gray-600",children:"Evalúa tu comprensión de principios mecánicos y físicos básicos"})]}),t&&p.jsx("div",{className:"text-center",children:p.jsx("div",{className:"text-xl font-mono font-bold text-red-600",children:h(l)})})]}),t?p.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-4 gap-6",children:[p.jsx("div",{className:"md:col-span-3",children:p.jsxs(R,{className:"mb-6",children:[p.jsxs(I,{className:"flex justify-between items-center",children:[p.jsxs("div",{children:[p.jsxs("h2",{className:"text-lg font-medium",children:["Pregunta ",r+1," de ",x.length]}),p.jsx("p",{className:"text-sm text-gray-500",children:"Aptitud Mecánica"})]}),p.jsx("div",{className:"text-sm text-gray-500",children:void 0!==n[b.id]?"Respondida":"Sin responder"})]}),p.jsxs(M,{children:[p.jsxs("div",{className:"mb-6",children:[p.jsx("h4",{className:"text-lg font-medium text-gray-800 mb-2",children:b.question}),b.subtitle&&p.jsx("p",{className:"text-sm text-gray-600 mb-4",children:b.subtitle}),p.jsxs("div",{className:"bg-gray-50 border border-gray-200 rounded-lg p-4 mb-6 text-center",children:[p.jsx("img",{src:b.image,alt:`Pregunta ${b.id}`,className:"max-w-full h-auto mx-auto",style:{maxHeight:"400px"},onError:e=>{e.target.style.display="none",e.target.nextSibling.style.display="block"}}),p.jsxs("div",{className:"text-gray-500 text-sm",style:{display:"none"},children:["[Imagen no disponible: ",b.image,"]"]})]})]}),p.jsx("div",{className:"grid grid-cols-2 md:grid-cols-4 gap-3",children:b.options.map((e,t)=>p.jsxs("div",{className:"border rounded-lg p-4 cursor-pointer transition-colors text-center "+(n[b.id]===t?"bg-blue-50 border-blue-500":"hover:bg-gray-50"),onClick:()=>{return e=b.id,a=t,void i(t=>c(o({},t),{[e]:a}));var e,a},children:[p.jsx("div",{className:"w-8 h-8 flex items-center justify-center rounded-full mx-auto mb-2 "+(n[b.id]===t?"bg-blue-500 text-white":"bg-gray-200 text-gray-700"),children:e}),p.jsxs("div",{className:"text-sm text-gray-600",children:["Opción ",e]})]},t))})]}),p.jsxs(B,{className:"flex justify-between",children:[p.jsx(L,{variant:"outline",onClick:()=>g(Math.max(0,r-1)),disabled:0===r,children:"Anterior"}),r<x.length-1?p.jsx(L,{variant:"primary",onClick:()=>g(Math.min(x.length-1,r+1)),children:"Siguiente"}):p.jsx(L,{variant:"primary",onClick:f,children:"Finalizar Test"})]})]})}),p.jsx("div",{children:p.jsxs(R,{className:"sticky top-6",children:[p.jsx(I,{children:p.jsx("h2",{className:"text-md font-medium",children:"Navegación"})}),p.jsxs(M,{children:[p.jsx("div",{className:"grid grid-cols-4 gap-2",children:x.map((e,t)=>p.jsx("button",{className:"w-8 h-8 rounded-full font-medium text-sm "+(r===t?"bg-blue-500 text-white":void 0!==n[x[t].id]?"bg-green-100 text-green-800 border border-green-300":"bg-gray-100 text-gray-800 hover:bg-gray-200"),onClick:()=>g(t),title:`Pregunta ${t+1}`,children:t+1},t))}),p.jsxs("div",{className:"mt-6",children:[p.jsxs("div",{className:"flex items-center justify-between mb-2 text-sm",children:[p.jsx("span",{children:"Progreso"}),p.jsxs("span",{children:[Object.keys(n).length," de ",x.length]})]}),p.jsx("div",{className:"w-full bg-gray-200 rounded-full h-2",children:p.jsx("div",{className:"bg-green-500 h-2 rounded-full",style:{width:Object.keys(n).length/x.length*100+"%"}})})]}),p.jsx("div",{className:"mt-6",children:p.jsxs("div",{className:"bg-blue-50 p-3 rounded-lg border border-blue-100 mb-4",children:[p.jsx("h3",{className:"text-sm font-medium text-blue-700 mb-1",children:"Información"}),p.jsxs("p",{className:"text-xs text-gray-600 mb-2",children:["Tiempo restante: ",p.jsx("span",{className:"font-medium",children:h(l)})]}),p.jsx("p",{className:"text-xs text-gray-600",children:"Observa cuidadosamente cada imagen antes de responder. Puedes cambiar tu respuesta antes de finalizar el test."})]})}),p.jsx(L,{variant:"primary",className:"w-full mt-2",onClick:f,children:"Finalizar Test"})]})]})})]}):p.jsxs(R,{children:[p.jsx(I,{children:p.jsx("h2",{className:"text-xl font-semibold text-gray-800",children:"Aptitud Mecánica: Instrucciones"})}),p.jsx(M,{children:p.jsxs("div",{className:"space-y-6",children:[p.jsxs("div",{className:"bg-blue-50 border border-blue-200 rounded-lg p-4",children:[p.jsx("h3",{className:"text-lg font-medium text-blue-800 mb-3",children:"Confirmación"}),p.jsx("p",{className:"text-gray-700 mb-4",children:"He leído y acepto las instrucciones. Entiendo que una vez iniciado el test no podré pausarlo y deberé completarlo en su totalidad."})]}),p.jsxs("div",{children:[p.jsx("h3",{className:"text-lg font-medium text-gray-700 mb-3",children:"Instrucciones"}),p.jsx("p",{className:"text-gray-600 mb-4",children:"En esta prueba aparecen varios tipos de situaciones sobre las cuales se te harán algunas preguntas."}),p.jsx("p",{className:"text-gray-600 mb-4",children:"Lee atentamente cada pregunta, observa el dibujo y elige cuál de las opciones es la más adecuada."}),p.jsxs("p",{className:"text-gray-600 mb-4",children:["Recuerda que solo existe ",p.jsx("strong",{children:"UNA opción correcta"}),". Cuando hayas decidido qué opción es, marca la letra correspondiente (A, B, C o D), asegurándote de que coincida con el número del ejercicio que estás contestando."]})]}),p.jsxs("div",{className:"bg-blue-50 border border-blue-200 rounded-lg p-4",children:[p.jsx("h4",{className:"text-md font-medium text-blue-800 mb-3",children:"Ejemplo M1"}),p.jsxs("p",{className:"text-gray-700 mb-4 font-medium",children:["¿Cuál de las tres botellas podría quitarse sin que se cayera la bandeja?",p.jsx("br",{}),p.jsx("span",{className:"text-sm text-gray-600",children:"(Si no hay diferencia, marca D)."})]}),p.jsxs("div",{className:"bg-white border border-gray-300 rounded p-4 mb-3",children:[p.jsxs("div",{className:"bg-gray-50 border border-gray-200 rounded-lg p-4 mb-3 text-center",children:[p.jsx("img",{src:fs("mecanico","m1.png"),alt:"Ejemplo M1 - Bandeja en equilibrio",className:"max-w-full h-auto mx-auto",style:{maxHeight:"300px"},onError:e=>{e.target.style.display="none",e.target.nextSibling.style.display="block"}}),p.jsx("div",{className:"text-gray-500 text-sm",style:{display:"none"},children:"[Imagen no disponible: Bandeja en equilibrio con 3 botellas]"})]}),p.jsxs("div",{className:"grid grid-cols-4 gap-2 mb-3",children:[p.jsx("div",{className:"bg-gray-100 p-2 rounded text-center",children:"A"}),p.jsx("div",{className:"bg-gray-100 p-2 rounded text-center",children:"B"}),p.jsx("div",{className:"bg-green-100 border-2 border-green-500 p-2 rounded text-center font-medium",children:"C"}),p.jsx("div",{className:"bg-gray-100 p-2 rounded text-center",children:"D"})]}),p.jsxs("p",{className:"text-sm text-gray-600",children:[p.jsx("strong",{children:"Solución:"})," En este ejemplo se presenta una bandeja en equilibrio sobre una mesa y encima de ella 3 botellas. Si se quitase la botella A o la botella B, la bandeja perdería el equilibrio y caería al suelo; si quitáramos la botella C la bandeja se mantendría en equilibrio. Por lo tanto, la solución al ejemplo M1 es ",p.jsx("strong",{children:"C"}),"."]})]})]}),p.jsxs("div",{className:"bg-green-50 border border-green-200 rounded-lg p-4",children:[p.jsx("h4",{className:"text-md font-medium text-green-800 mb-3",children:"Ejemplo M2"}),p.jsxs("p",{className:"text-gray-700 mb-4 font-medium",children:["Si los tres vehículos se están desplazando a 70 km/h, ¿cuál va MÁS rápido?",p.jsx("br",{}),p.jsx("span",{className:"text-sm text-gray-600",children:"(Si no hay diferencia, marca D)."})]}),p.jsxs("div",{className:"bg-white border border-gray-300 rounded p-4 mb-3",children:[p.jsxs("div",{className:"bg-gray-50 border border-gray-200 rounded-lg p-4 mb-3 text-center",children:[p.jsx("img",{src:fs("mecanico","m2.png"),alt:"Ejemplo M2 - Tres vehículos a 70 km/h",className:"max-w-full h-auto mx-auto",style:{maxHeight:"300px"},onError:e=>{e.target.style.display="none",e.target.nextSibling.style.display="block"}}),p.jsx("div",{className:"text-gray-500 text-sm",style:{display:"none"},children:"[Imagen no disponible: Tres vehículos a 70 km/h]"})]}),p.jsxs("div",{className:"grid grid-cols-4 gap-2 mb-3",children:[p.jsx("div",{className:"bg-gray-100 p-2 rounded text-center",children:"A"}),p.jsx("div",{className:"bg-gray-100 p-2 rounded text-center",children:"B"}),p.jsx("div",{className:"bg-gray-100 p-2 rounded text-center",children:"C"}),p.jsx("div",{className:"bg-green-100 border-2 border-green-500 p-2 rounded text-center font-medium",children:"D"})]}),p.jsxs("p",{className:"text-sm text-gray-600",children:[p.jsx("strong",{children:"Solución:"})," Al desplazarse los tres vehículos a la misma velocidad (70 km/h), los tres van igual de rápido. Por lo tanto, la solución a este ejemplo es la opción ",p.jsx("strong",{children:"D"})," (no hay diferencia)."]})]})]}),p.jsxs("div",{className:"bg-gray-50 border border-gray-200 rounded-lg p-4",children:[p.jsx("h4",{className:"text-md font-medium text-gray-800 mb-3",children:"Instrucciones para el Test"}),p.jsxs("p",{className:"text-gray-700 mb-4",children:["El tiempo máximo para la realización de esta prueba es de ",p.jsx("strong",{children:"12 minutos"}),", por lo que deberás trabajar rápidamente, esforzándote al máximo en encontrar la respuesta correcta."]}),p.jsxs("ul",{className:"list-disc pl-5 space-y-2 text-gray-700 mb-4",children:[p.jsx("li",{children:"Si en algún ejercicio no estás completamente seguro de cuál puede ser, elige la opción que creas que es más correcta de las que aparecen."}),p.jsxs("li",{children:[p.jsx("strong",{children:"No se penalizará el error"}),", así que intenta responder todas las preguntas."]}),p.jsx("li",{children:"Si terminas antes del tiempo concedido, repasa tus respuestas, pero NO continúes con las demás pruebas."}),p.jsx("li",{children:"Puedes navegar libremente entre las preguntas durante el tiempo disponible."})]})]}),p.jsxs("div",{className:"bg-yellow-50 border-l-4 border-yellow-500 p-4",children:[p.jsx("h3",{className:"text-lg font-medium text-yellow-800 mb-1",children:"Importante"}),p.jsx("p",{className:"text-yellow-700",children:"Una vez iniciado el test, el temporizador no se detendrá. Asegúrate de disponer del tiempo necesario para completarlo sin interrupciones. Encuentra un lugar tranquilo y asegúrate de tener una buena conexión a internet."})]})]})}),p.jsx(B,{className:"flex justify-end",children:p.jsx(L,{variant:"primary",onClick:()=>{a(!0)},className:"px-6 py-2",children:"Comenzar Test"})})]})]})},Es=Object.freeze(Object.defineProperty({__proto__:null,default:As},Symbol.toStringTag,{value:"Module"})),Cs=[{id:1,type:"equality",question:"6 + 22 = 30 - ?",options:["2","8","10","12","28"],correct:0},{id:2,type:"equality",question:"18 - 6 = 16 - ?",options:["2","3","4","6","10"],correct:2},{id:3,type:"equality",question:"7² - 9 = ? x 2",options:["2","7","10","20","40"],correct:3},{id:4,type:"equality",question:"(6 + 8) x ? = 4 x 7",options:["2","3","4","7","10"],correct:0},{id:5,type:"equality",question:"(3 + 9) x 3 = (? x 2) x 6",options:["1","2","3","4","6"],correct:2},{id:6,type:"series",question:"23 • 18 • 14 • 11 • ?",options:["5","6","7","8","9"],correct:4},{id:7,type:"series",question:"9 • 11 • 10 • 12 • 11 • 13 • ?",options:["11","12","13","14","15"],correct:1},{id:8,type:"series",question:"2 • 6 • 11 • 17 • 24 • 32 • ?",options:["36","37","40","41","42"],correct:3},{id:9,type:"series",question:"21 • 23 • 20 • 24 • 19 • 25 • 18 • ?",options:["16","20","21","22","26"],correct:4},{id:10,type:"series",question:"16 • 8 • 16 • 20 • 10 • 20 • 24 • 12 • ?",options:["4","6","14","24","25"],correct:3},{id:11,type:"table",question:'Tabla: "Puntos obtenidos en la compra"',tableData:{headers:["Artículo","Unidades","Puntos/Unidad","Total puntos"],rows:[["Café","55","15","825"],["Galletas","?","6","240"],["Sal","20","5","100"],["","","","1.165"]]},questionText:"El interrogante (?) está en las Unidades de Galletas.",options:["4","40","60","75","234"],correct:1},{id:12,type:"table",question:'Tabla: "Venta de productos por meses"',tableData:{headers:["Meses","Televisión","Altavoces","Auriculares","Total"],rows:[["Enero","?","(dato borrado)","35","85"],["Febrero","45","(dato borrado)","80","175"],["Marzo","60","45","(dato borrado)","(dato borrado)"],["Total","125","(dato borrado)","155","(dato borrado)"]]},questionText:"El interrogante (?) está en Televisión de Enero.",options:["10","20","25","30","50"],correct:1},{id:13,type:"table",question:'Tabla: "Venta de productos por meses"',tableData:{headers:["Meses","Secadoras","Lavadoras","Frigoríficos","Total"],rows:[["Enero","(dato borrado)","(dato borrado)","30","90"],["Febrero","5","40","25","70"],["Marzo","(dato borrado)","30","35","105"],["Abril","50","45","?","(dato borrado)"],["Total","(dato borrado)","155","145","(dato borrado)"]]},questionText:"El interrogante (?) está en Frigoríficos de Abril.",options:["30","45","55","65","90"],correct:2},{id:14,type:"table",question:'Tabla: "Venta de productos por meses"',tableData:{headers:["Meses","Televisión","Altavoces","Auriculares","Total"],rows:[["Abril","5","8","(dato borrado)","33"],["Mayo","(dato borrado)","15","5","30"],["Junio","10","(dato borrado)","(dato borrado)","(dato borrado)"],["Total","?","38","32","(dato borrado)"]]},questionText:"El interrogante (?) está en el Total de Televisión.",options:["10","15","20","25","30"],correct:3},{id:15,type:"table",question:'Tabla: "Venta de productos por meses"',tableData:{headers:["Meses","Televisión","Altavoces","Auriculares","Total"],rows:[["Enero","20","(dato borrado)","15","(dato borrado)"],["Febrero","?","(dato borrado)","30","70"],["Marzo","20","(dato borrado)","30","(dato borrado)"],["Abril","(dato borrado)","15","10","55"],["Total","85","(dato borrado)","80","(dato borrado)"]]},questionText:"El interrogante (?) está en Televisión de Febrero.",options:["10","15","25","40","45"],correct:1},{id:16,type:"equality",question:"(30 : 5) : (14 : 7) = [(? x 5) + 3] : 11",options:["1","2","3","4","6"],correct:4},{id:17,type:"equality",question:"[(23 - 9) - 4] x 2 = [(? : 6) - 3] x 5",options:["7","20","24","30","42"],correct:4},{id:18,type:"equality",question:"20 + 35 - 14 = (? x 2) - 19",options:["11","25","30","35","60"],correct:2},{id:19,type:"equality",question:"(9 x 7) : (? - 2) = 9 + 7 + 5",options:["3","4","5","6","12"],correct:2},{id:20,type:"equality",question:"[(? : 7) - 3] : 2 = 21 : 7",options:["2","9","42","49","63"],correct:4},{id:21,type:"series",question:"14 • 11 • 15 • 12 • 17 • 14 • 20 • 17 • 24 • 21 • ?",options:["18","25","26","27","29"],correct:4},{id:22,type:"series",question:"2 • 8 • 4 • 16 • 8 • ?",options:["4","14","24","26","32"],correct:4},{id:23,type:"series",question:"5 • 6 • 8 • 7 • 10 • 14 • 13 • 18 • 24 • 23 • ?",options:["22","24","26","28","30"],correct:4},{id:24,type:"series",question:"11 • 13 • 16 • 15 • 19 • 24 • 22 • ?",options:["23","24","25","26","28"],correct:4},{id:25,type:"series",question:"3 • 6 • 4 • 8 • 6 • ?",options:["4","9","10","11","12"],correct:4},{id:26,type:"series",question:"3 • 2 • 6 • 4 • 12 • 8 • 24 • 16 • 48 • 32 • 96 • ?",options:["64","80","89","92","95"],correct:0},{id:27,type:"table",question:'Tabla: "Venta de productos por meses"',tableData:{headers:["Meses","Plancha","Depiladora","Afeitadora","Total"],rows:[["Mayo","20","5","(dato borrado)","40"],["Junio","(dato borrado)","(dato borrado)","10","(dato borrado)"],["Abril","(dato borrado)","5","(dato borrado)","25"],["Total","40","20","?","(dato borrado)"]]},questionText:"El interrogante (?) está en el Total de Afeitadora.",options:["60","65","75","90","95"],correct:3},{id:28,type:"table",question:'Tabla: "Venta de productos por meses"',tableData:{headers:["Meses","Hornos","Microondas","Vitrocerámica","Total"],rows:[["Septiembre","25","40","5","70"],["Octubre","(dato borrado)","45","50","(dato borrado)"],["Noviembre","30","(dato borrado)","?","90"],["Diciembre","35","30","(dato borrado)","105"],["Total","145","155","(dato borrado)","(dato borrado)"]]},questionText:"El interrogante (?) está en Vitrocerámica de Noviembre.",options:["10","15","20","30","60"],correct:2},{id:29,type:"table",question:'Tabla: "Venta de productos por meses"',tableData:{headers:["Meses","Cafetera","Tostadora","Freidora","Total"],rows:[["Enero","(dato borrado)","5","20","35"],["Febrero","(dato borrado)","(dato borrado)","5","30"],["Marzo","15","30","?","(dato borrado)"],["Total","(dato borrado)","55","40","(dato borrado)"]]},questionText:"El interrogante (?) está en Freidora de Marzo.",options:["5","10","15","20","25"],correct:2},{id:30,type:"table",question:'Tabla: "Puntos obtenidos en la compra"',tableData:{headers:["Artículo","Unidades","Puntos/Unidad","Total puntos"],rows:[["Chocolate","5","225","1.125"],["Harina","6","?","(dato borrado)"],["Nueces","8","140","(dato borrado)"],["","","","3.925"]]},questionText:"El interrogante (?) está en Puntos/Unidad de Harina.",options:["26","265","270","280","1.680"],correct:3},{id:31,type:"table",question:'Tabla: "Venta de productos por meses"',tableData:{headers:["Meses","Hornos","Microondas","Vitrocerámica","Total"],rows:[["Mayo","(dato borrado)","15","20","45"],["Junio","15","10","(dato borrado)","55"],["Julio","(dato borrado)","5","20","(dato borrado)"],["Agosto","10","(dato borrado)","10","25"],["Total","?","(dato borrado)","80","155"]]},questionText:"El interrogante (?) está en el Total de Hornos.",options:["25","35","40","45","50"],correct:2},{id:32,type:"table",question:'Tabla: "Puntos obtenidos en la compra"',tableData:{headers:["Artículo","Unidades","Puntos/Unidad","Total puntos"],rows:[["Grapa","2.500","0,05","125"],["Chincheta","3.000","?","(dato borrado)"],["Tornillo","1.200","0,1","(dato borrado)"],["","","","845"]]},questionText:"El interrogante (?) está en Puntos/Unidad de Chincheta.",options:["0,03","0,1","0,2","0,5","5"],correct:2}],_s=()=>{var e;const t=w(),a=C(),[r,s]=N.useState(0),[n,i]=N.useState({}),[l,d]=N.useState(1200),[m,x]=N.useState(!1),[h,g]=N.useState(!1),f=null==(e=a.state)?void 0:e.patientId,b=Cs,y=N.useCallback(()=>u(void 0,null,function*(){try{const a=Object.keys(n).length,r=b.length,s=r-a;let i=0;Object.entries(n).forEach(([e,t])=>{const a=b.find(t=>t.id.toString()===e);a&&parseInt(t)===a.correct&&i++});const o=a-i,c=1200-l,d={correctCount:i,incorrectCount:o,unansweredCount:s,timeUsed:c,totalQuestions:r,testType:"numerico"};if(f)try{yield os.saveTestResult({patientId:f,testType:"numerico",correctCount:i,incorrectCount:o,unansweredCount:s,timeUsed:c,totalQuestions:r,answers:n,errores:o})}catch(e){}k.success(`Test completado. Has respondido ${a} de ${r} preguntas. Respuestas correctas: ${i}`),t("/test/results/numerico",{state:d})}catch(e){k.error("Error al procesar los resultados del test")}}),[n,b,l,t,f]);N.useEffect(()=>{if(h&&l>0&&!m){const e=setTimeout(()=>d(l-1),1e3);return()=>clearTimeout(e)}0===l&&setTimeout(()=>y(),0)},[l,m,h,y]);const j=e=>`${Math.floor(e/60)}:${(e%60).toString().padStart(2,"0")}`,v=e=>{s(e)},A=b[r];return p.jsxs("div",{className:"container mx-auto py-6 max-w-4xl",children:[p.jsxs("div",{className:"mb-6",children:[p.jsxs("div",{className:"text-center mb-4",children:[p.jsxs("h1",{className:"text-2xl font-bold text-gray-800 mb-2",children:[p.jsx("i",{className:"fas fa-calculator mr-2 text-teal-600"}),"Test de Aptitud Numérica"]}),p.jsx("p",{className:"text-gray-600",children:"Resolución de igualdades, series numéricas y análisis de tablas de datos"})]}),h&&p.jsx("div",{className:"text-center",children:p.jsx("div",{className:"text-xl font-mono font-bold text-red-600",children:j(l)})})]}),h?p.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-4 gap-6",children:[p.jsx("div",{className:"md:col-span-3",children:p.jsxs(R,{className:"mb-6",children:[p.jsxs(I,{className:"flex justify-between items-center",children:[p.jsxs("div",{children:[p.jsxs("h2",{className:"text-lg font-medium",children:["Pregunta ",r+1," de ",b.length]}),p.jsx("p",{className:"text-sm text-gray-500",children:"equality"===A.type?"Igualdades Numéricas":"series"===A.type?"Series Numéricas":"Tablas de Datos"})]}),p.jsx("div",{className:"text-sm text-gray-500",children:void 0!==n[A.id]?"Respondida":"Sin responder"})]}),p.jsxs(M,{children:[p.jsxs("div",{className:"mb-6",children:[p.jsxs("h4",{className:"text-lg font-medium text-gray-800 mb-4",children:["equality"===A.type&&"¿Qué número debe aparecer en lugar del interrogante (?) para que se cumpla la igualdad?","series"===A.type&&"¿Qué número debe aparecer en lugar del interrogante (?) de modo que continúe la serie?","table"===A.type&&"¿Qué número debe aparecer en lugar del interrogante (?) a partir de los datos de la tabla?"]}),"table"===A.type?p.jsxs("div",{children:[p.jsx("h5",{className:"font-medium mb-3",children:A.question}),p.jsx("div",{className:"overflow-x-auto mb-4",children:p.jsxs("table",{className:"min-w-full border border-gray-300",children:[p.jsx("thead",{children:p.jsx("tr",{className:"bg-gray-50",children:A.tableData.headers.map((e,t)=>p.jsx("th",{className:"border border-gray-300 px-3 py-2 text-left font-medium",children:e},t))})}),p.jsx("tbody",{children:A.tableData.rows.map((e,t)=>p.jsx("tr",{className:t%2==0?"bg-white":"bg-gray-50",children:e.map((e,t)=>p.jsx("td",{className:"border border-gray-300 px-3 py-2",children:e},t))},t))})]})}),p.jsx("p",{className:"text-gray-700 mb-4",children:A.questionText})]}):p.jsx("div",{className:"bg-gray-50 p-4 rounded-lg text-center",children:p.jsx("span",{className:"text-xl font-mono",children:A.question})})]}),p.jsx("div",{className:"space-y-3",children:A.options.map((e,t)=>p.jsx("div",{className:"border rounded-lg p-3 cursor-pointer transition-colors "+(n[A.id]===t?"bg-blue-50 border-blue-500":"hover:bg-gray-50"),onClick:()=>{return e=A.id,a=t,void i(t=>c(o({},t),{[e]:a}));var e,a},children:p.jsxs("div",{className:"flex items-center",children:[p.jsx("div",{className:"w-6 h-6 flex items-center justify-center rounded-full mr-3 "+(n[A.id]===t?"bg-blue-500 text-white":"bg-gray-200 text-gray-700"),children:String.fromCharCode(65+t)}),p.jsx("div",{className:"text-gray-700",children:e})]})},t))})]}),p.jsxs(B,{className:"flex justify-between",children:[p.jsx(L,{variant:"outline",onClick:()=>v(Math.max(0,r-1)),disabled:0===r,children:"Anterior"}),r<b.length-1?p.jsx(L,{variant:"primary",onClick:()=>v(Math.min(b.length-1,r+1)),children:"Siguiente"}):p.jsx(L,{variant:"primary",onClick:y,children:"Finalizar Test"})]})]})}),p.jsx("div",{children:p.jsxs(R,{className:"sticky top-6",children:[p.jsx(I,{children:p.jsx("h2",{className:"text-md font-medium",children:"Navegación"})}),p.jsxs(M,{children:[p.jsx("div",{className:"grid grid-cols-4 gap-2",children:b.map((e,t)=>p.jsx("button",{className:"w-8 h-8 rounded-full font-medium text-sm "+(r===t?"bg-blue-500 text-white":void 0!==n[b[t].id]?"bg-green-100 text-green-800 border border-green-300":"bg-gray-100 text-gray-800 hover:bg-gray-200"),onClick:()=>v(t),title:`Pregunta ${t+1}`,children:t+1},t))}),p.jsxs("div",{className:"mt-6",children:[p.jsxs("div",{className:"flex items-center justify-between mb-2 text-sm",children:[p.jsx("span",{children:"Progreso"}),p.jsxs("span",{children:[Object.keys(n).length," de ",b.length]})]}),p.jsx("div",{className:"w-full bg-gray-200 rounded-full h-2",children:p.jsx("div",{className:"bg-green-500 h-2 rounded-full",style:{width:Object.keys(n).length/b.length*100+"%"}})})]}),p.jsx("div",{className:"mt-6",children:p.jsxs("div",{className:"bg-blue-50 p-3 rounded-lg border border-blue-100 mb-4",children:[p.jsx("h3",{className:"text-sm font-medium text-blue-700 mb-1",children:"Información"}),p.jsxs("p",{className:"text-xs text-gray-600 mb-2",children:["Tiempo restante: ",p.jsx("span",{className:"font-medium",children:j(l)})]}),p.jsx("p",{className:"text-xs text-gray-600",children:"Recuerda que al responder una pregunta, puedes cambiar tu respuesta antes de finalizar el test."})]})}),p.jsx(L,{variant:"primary",className:"w-full mt-2",onClick:y,children:"Finalizar Test"})]})]})})]}):p.jsxs(R,{children:[p.jsx(I,{children:p.jsx("h2",{className:"text-xl font-semibold text-gray-800",children:"Aptitud Numérica: Instrucciones"})}),p.jsx(M,{children:p.jsxs("div",{className:"space-y-6",children:[p.jsxs("div",{children:[p.jsx("h3",{className:"text-lg font-medium text-gray-700 mb-3",children:"Instrucciones"}),p.jsx("p",{className:"text-gray-600 mb-4",children:"En esta prueba encontrarás distintos ejercicios numéricos que tendrás que resolver. Para ello tendrás que analizar la información que se presenta y determinar qué debe aparecer en lugar del interrogante. Cuando lo hayas decidido, deberás marcar la letra de la opción correspondiente (A, B, C, D o E), asegurándote de que coincida con el ejercicio que estás contestando. Ten en cuenta que en este ejercicio hay 5 posibles opciones de respuesta."})]}),p.jsxs("div",{className:"bg-blue-50 border border-blue-200 rounded-lg p-4",children:[p.jsx("h4",{className:"text-md font-medium text-blue-800 mb-3",children:"Tipo 1: Igualdades Numéricas"}),p.jsx("p",{className:"text-gray-700 mb-4",children:"En un primer tipo de ejercicios aparecerá una igualdad numérica en la que se ha sustituido uno de los elementos por un interrogante (?). Tu tarea consistirá en averiguar qué valor numérico debe aparecer en lugar del interrogante para que se cumpla la igualdad."}),p.jsxs("div",{className:"bg-white border border-gray-300 rounded p-4 mb-3",children:[p.jsx("h5",{className:"font-medium text-gray-800 mb-2",children:"Ejemplo N1: ¿Qué número debe aparecer en lugar del interrogante (?) para que se cumpla la igualdad?"}),p.jsx("div",{className:"bg-gray-100 p-3 rounded text-center mb-3",children:p.jsx("span",{className:"text-xl font-mono",children:"16 - 4 = ? + 2"})}),p.jsxs("div",{className:"grid grid-cols-5 gap-2 mb-3",children:[p.jsx("div",{className:"bg-gray-100 p-2 rounded text-center",children:"A. 8"}),p.jsx("div",{className:"bg-green-100 border-2 border-green-500 p-2 rounded text-center font-medium",children:"B. 10"}),p.jsx("div",{className:"bg-gray-100 p-2 rounded text-center",children:"C. 12"}),p.jsx("div",{className:"bg-gray-100 p-2 rounded text-center",children:"D. 14"}),p.jsx("div",{className:"bg-gray-100 p-2 rounded text-center",children:"E. 16"})]}),p.jsxs("p",{className:"text-sm text-gray-600",children:[p.jsx("strong",{children:"Solución:"})," La primera parte de la igualdad, 16 – 4, da lugar a 12. Para que en la segunda parte se obtenga el mismo resultado sería necesario sustituir el interrogante por 10, quedando la igualdad como 16 – 4 = 10 + 2. Por tanto, la respuesta correcta es ",p.jsx("strong",{children:"B"}),"."]})]})]}),p.jsxs("div",{className:"bg-green-50 border border-green-200 rounded-lg p-4",children:[p.jsx("h4",{className:"text-md font-medium text-green-800 mb-3",children:"Tipo 2: Series Numéricas"}),p.jsx("p",{className:"text-gray-700 mb-4",children:"En otros ejercicios tendrás que observar una serie de números ordenados de acuerdo con una ley y determinar cuál debe continuar la serie ocupando el lugar del interrogante."}),p.jsxs("div",{className:"bg-white border border-gray-300 rounded p-4 mb-3",children:[p.jsx("h5",{className:"font-medium text-gray-800 mb-2",children:"Ejemplo N2: ¿Qué número debe aparecer en lugar del interrogante (?) de modo que continúe la serie?"}),p.jsx("div",{className:"bg-gray-100 p-3 rounded text-center mb-3",children:p.jsx("span",{className:"text-xl font-mono",children:"3 • 5 • 6 • 8 • 9 • 11 • 12 • 14 • ?"})}),p.jsxs("div",{className:"grid grid-cols-5 gap-2 mb-3",children:[p.jsx("div",{className:"bg-gray-100 p-2 rounded text-center",children:"A. 13"}),p.jsx("div",{className:"bg-green-100 border-2 border-green-500 p-2 rounded text-center font-medium",children:"B. 15"}),p.jsx("div",{className:"bg-gray-100 p-2 rounded text-center",children:"C. 16"}),p.jsx("div",{className:"bg-gray-100 p-2 rounded text-center",children:"D. 18"}),p.jsx("div",{className:"bg-gray-100 p-2 rounded text-center",children:"E. 20"})]}),p.jsx("div",{className:"bg-gray-50 p-3 rounded mb-3",children:p.jsxs("div",{className:"text-center text-sm font-mono",children:["3 → 5 → 6 → 8 → 9 → 11 → 12 → 14 → ?",p.jsx("br",{}),p.jsx("span",{className:"text-blue-600",children:"+2 +1 +2 +1 +2 +1 +2 +1"})]})}),p.jsxs("p",{className:"text-sm text-gray-600",children:[p.jsx("strong",{children:"Solución:"})," En este ejemplo la serie combina aumentos de 2 unidades y de 1 unidad (+2, +1, +2, +1...). Como puede observarse, en el lugar del interrogante debe aumentarse 1 unidad con respecto al número anterior, por lo que el número que continuaría la serie sería el 15. Por tanto, la respuesta correcta es ",p.jsx("strong",{children:"B"}),"."]})]})]}),p.jsxs("div",{className:"bg-purple-50 border border-purple-200 rounded-lg p-4",children:[p.jsx("h4",{className:"text-md font-medium text-purple-800 mb-3",children:"Tipo 3: Tablas de Datos"}),p.jsx("p",{className:"text-gray-700 mb-4",children:"Finalmente, en un tercer tipo de ejercicios, aparecen tablas en las que un valor se ha sustituido intencionadamente por un interrogante (?) y otros valores han sido borrados (<<>>). Tu tarea consistirá en averiguar el número que debería aparecer en lugar del interrogante."}),p.jsxs("div",{className:"bg-white border border-gray-300 rounded p-4 mb-3",children:[p.jsx("h5",{className:"font-medium text-gray-800 mb-2",children:"Ejemplo N3: De acuerdo con los datos de la tabla, ¿qué número debe aparecer en lugar del interrogante (?)?"}),p.jsxs("div",{className:"mb-4",children:[p.jsx("h6",{className:"text-center font-medium mb-3",children:"Puntos obtenidos en la compra"}),p.jsx("div",{className:"overflow-x-auto",children:p.jsxs("table",{className:"w-full border border-gray-300",children:[p.jsx("thead",{children:p.jsxs("tr",{className:"bg-gray-100",children:[p.jsx("th",{className:"border border-gray-300 px-3 py-2 text-left",children:"Artículo"}),p.jsx("th",{className:"border border-gray-300 px-3 py-2 text-center",children:"Unidades"}),p.jsx("th",{className:"border border-gray-300 px-3 py-2 text-center",children:"Puntos/Unidad"}),p.jsx("th",{className:"border border-gray-300 px-3 py-2 text-center",children:"Total puntos"})]})}),p.jsxs("tbody",{children:[p.jsxs("tr",{children:[p.jsx("td",{className:"border border-gray-300 px-3 py-2",children:"Jabón"}),p.jsx("td",{className:"border border-gray-300 px-3 py-2 text-center",children:"10"}),p.jsx("td",{className:"border border-gray-300 px-3 py-2 text-center font-bold text-red-600",children:"?"}),p.jsx("td",{className:"border border-gray-300 px-3 py-2 text-center",children:"30"})]}),p.jsxs("tr",{className:"bg-gray-50",children:[p.jsx("td",{className:"border border-gray-300 px-3 py-2",children:"Aceite"}),p.jsx("td",{className:"border border-gray-300 px-3 py-2 text-center",children:"20"}),p.jsx("td",{className:"border border-gray-300 px-3 py-2 text-center",children:"2"}),p.jsx("td",{className:"border border-gray-300 px-3 py-2 text-center",children:p.jsx("span",{className:"line-through",children:"40"})})]}),p.jsxs("tr",{children:[p.jsx("td",{className:"border border-gray-300 px-3 py-2 font-bold",colSpan:"3",children:"Total"}),p.jsx("td",{className:"border border-gray-300 px-3 py-2 text-center font-bold",children:"70"})]})]})]})})]}),p.jsxs("div",{className:"grid grid-cols-5 gap-2 mb-3",children:[p.jsx("div",{className:"bg-green-100 border-2 border-green-500 p-2 rounded text-center font-medium",children:"A. 3"}),p.jsx("div",{className:"bg-gray-100 p-2 rounded text-center",children:"B. 5"}),p.jsx("div",{className:"bg-gray-100 p-2 rounded text-center",children:"C. 10"}),p.jsx("div",{className:"bg-gray-100 p-2 rounded text-center",children:"D. 40"}),p.jsx("div",{className:"bg-gray-100 p-2 rounded text-center",children:"E. 60"})]}),p.jsxs("p",{className:"text-sm text-gray-600",children:[p.jsx("strong",{children:"Solución:"})," A partir de los datos de la tabla sabemos que se han comprado 10 unidades de jabón y que se han obtenido 30 puntos, por lo que se puede deducir que el valor del interrogante es igual a 3 (10 unidades × 3 puntos/unidad = 30 puntos). Por tanto, la respuesta correcta es ",p.jsx("strong",{children:"A"}),". Fíjate que en este ejemplo no es necesario calcular el valor que ha sido borrado para obtener el valor del interrogante, pero en otros ejercicios sí será necesario calcular todos o algunos de estos valores para alcanzar la solución."]})]})]}),p.jsxs("div",{className:"bg-gray-50 border border-gray-200 rounded-lg p-4",children:[p.jsx("h4",{className:"text-md font-medium text-gray-800 mb-3",children:"Instrucciones para el Test"}),p.jsxs("p",{className:"text-gray-700 mb-4",children:["Cuando comience la prueba encontrarás más ejercicios como estos. El tiempo máximo para su realización es de ",p.jsx("strong",{children:"20 minutos"}),", por lo que deberás trabajar rápidamente, esforzándote al máximo en encontrar la respuesta correcta."]}),p.jsxs("ul",{className:"list-disc pl-5 space-y-2 text-gray-700 mb-4",children:[p.jsx("li",{children:"Si en algún ejercicio no estás completamente seguro de cuál puede ser, elige la opción que creas que es más correcta de las cinco que aparecen."}),p.jsxs("li",{children:[p.jsx("strong",{children:"No se penalizará el error"}),", así que intenta responder todas las preguntas."]}),p.jsx("li",{children:"Si terminas antes del tiempo concedido, repasa tus respuestas, pero NO continúes con las demás pruebas."}),p.jsx("li",{children:"Puedes navegar libremente entre las preguntas durante el tiempo disponible."})]})]}),p.jsxs("div",{className:"bg-yellow-50 border-l-4 border-yellow-500 p-4",children:[p.jsx("h3",{className:"text-lg font-medium text-yellow-800 mb-1",children:"Importante"}),p.jsx("p",{className:"text-yellow-700",children:"Una vez iniciado el test, el temporizador no se detendrá. Asegúrate de disponer del tiempo necesario para completarlo sin interrupciones. Encuentra un lugar tranquilo y asegúrate de tener una buena conexión a internet."})]})]})}),p.jsx(B,{className:"flex justify-end",children:p.jsx(L,{variant:"primary",onClick:()=>{g(!0)},className:"px-6 py-2",children:"Comenzar Test"})})]})]})},Ss=Object.freeze(Object.defineProperty({__proto__:null,default:_s},Symbol.toStringTag,{value:"Module"})),Ts=({data:e})=>{var t;const a=e.reduce((e,t)=>e+t.value,0);if(!e.length||0===a)return p.jsx("div",{className:"flex items-center justify-center h-full",children:p.jsx("p",{className:"text-gray-500",children:"No hay datos disponibles"})});let r=0;const s=e.map(e=>{const t=e.value/a*100,s=t/100*360,n=c(o({},e),{percentage:t,startAngle:r,endAngle:r+s});return r+=s,n}),n=(e,t=50)=>{const a=(e-90)*Math.PI/180;return{x:50+t*Math.cos(a),y:50+t*Math.sin(a)}},i=e=>{const t=n(e.startAngle),a=n(e.endAngle),r=e.endAngle-e.startAngle<=180?"0":"1";return`M 50 50 L ${t.x} ${t.y} A 50 50 0 ${r} 1 ${a.x} ${a.y} Z`},l=1===s.length||s.some(e=>100===e.percentage);return p.jsxs("div",{className:"flex flex-col items-center h-full",children:[p.jsx("div",{className:"w-full max-w-xs",children:p.jsx("svg",{viewBox:"0 0 100 100",className:"w-48 h-48 mx-auto mb-4",children:l?p.jsx("circle",{cx:"50",cy:"50",r:"50",fill:(null==(t=s.find(e=>100===e.percentage))?void 0:t.color)||s[0].color}):s.map((e,t)=>p.jsx("path",{d:i(e),fill:e.color,stroke:"#fff",strokeWidth:"0.5"},t))})}),p.jsx("div",{className:"flex flex-col items-center w-full",children:s.map((e,t)=>p.jsxs("div",{className:"flex items-center mb-2 w-full justify-between max-w-xs",children:[p.jsxs("div",{className:"flex items-center",children:[p.jsx("div",{className:"w-4 h-4 mr-2",style:{backgroundColor:e.color}}),p.jsx("span",{className:"text-sm text-gray-700",children:e.name})]}),p.jsxs("div",{className:"flex items-center",children:[p.jsx("span",{className:"font-medium text-sm mr-2",children:e.value}),p.jsxs("span",{className:"text-xs text-gray-500",children:["(",e.percentage.toFixed(1),"%)"]})]})]},t))})]})},zs=()=>{const e=C().state||{correctCount:0,incorrectCount:0,unansweredCount:0,timeUsed:0,totalQuestions:32,testType:"unknown"},{correctCount:t,incorrectCount:a,unansweredCount:r,timeUsed:s,totalQuestions:n,testType:i}=e,o=[{name:"Correctas",value:t,color:"#10B981"},{name:"Incorrectas",value:a,color:"#EF4444"},{name:"Sin responder",value:r,color:"#9CA3AF"}].filter(e=>e.value>0);0===o.length&&o.push({name:"Correctas",value:t,color:"#10B981"});const c=Math.round(t/n*100),l=((e,t)=>{let a=[];return a="ortografia"===e?t<60?["Repasa las reglas básicas de ortografía, especialmente las reglas de acentuación","Practica la identificación de palabras correctas e incorrectas con ejercicios diarios","Presta especial atención a las letras que suelen causar confusión (b/v, g/j, h)"]:t<80?["Refuerza tu conocimiento en acentuación, especialmente en palabras agudas, llanas y esdrújulas","Practica con palabras que contengan h, b/v, g/j para mejorar tu precisión","Dedica tiempo a la lectura para familiarizarte con la escritura correcta de las palabras"]:["Continúa practicando con palabras poco comunes para expandir tu dominio ortográfico","Profundiza en las excepciones a las reglas de acentuación","Mantén el hábito de lectura para reforzar tu ortografía"]:"espacial"===e?100===t?["¡Felicidades! Has demostrado una capacidad excepcional de razonamiento espacial","Considera explorar campos profesionales como arquitectura, ingeniería, diseño 3D o ciencias que requieran esta habilidad","Tu capacidad para visualizar y manipular objetos mentalmente es extraordinaria","Podrías compartir tus técnicas y estrategias con otros para ayudarles a mejorar sus habilidades espaciales"]:t<60?["Practica con rompecabezas tridimensionales y juegos de construcción para mejorar tu visualización espacial","Realiza ejercicios de rotación mental, como imaginar objetos desde diferentes ángulos","Intenta dibujar objetos tridimensionales desde diferentes perspectivas","Utiliza aplicaciones o juegos que ejerciten el razonamiento espacial"]:t<80?["Continúa practicando con ejercicios de visualización espacial más complejos","Intenta resolver problemas de plegado de papel (origami) para mejorar tu comprensión de transformaciones espaciales","Practica con juegos de construcción y ensamblaje que requieran visualización tridimensional","Analiza las preguntas que te resultaron más difíciles para identificar patrones específicos"]:["Desafíate con problemas de visualización espacial más avanzados","Explora campos como la geometría tridimensional, el diseño 3D o la arquitectura","Comparte tus conocimientos y estrategias con otros para reforzar tu comprensión","Considera carreras o actividades que aprovechen tu excelente capacidad de razonamiento espacial"]:"mecanico"===e?100===t?["¡Excelente! Has demostrado una comprensión excepcional de principios mecánicos y físicos","Considera carreras en ingeniería mecánica, física aplicada, o diseño industrial","Tu capacidad para analizar sistemas mecánicos y predecir comportamientos es sobresaliente","Podrías explorar campos como robótica, automatización o diseño de maquinaria"]:t<60?["Repasa los principios básicos de física: fuerzas, palancas, poleas y equilibrio","Practica con ejercicios de mecánica básica y análisis de sistemas simples","Observa cómo funcionan las máquinas simples en la vida cotidiana","Dedica tiempo a entender conceptos como centro de gravedad, resistencia y fricción"]:t<80?["Profundiza en el estudio de máquinas simples y compuestas","Practica con problemas de equilibrio de fuerzas y análisis de estructuras","Estudia casos prácticos de aplicaciones mecánicas en la industria","Refuerza tu comprensión de principios como ventaja mecánica y eficiencia"]:["Explora conceptos avanzados de mecánica y termodinámica","Considera estudiar ingeniería mecánica o campos relacionados","Practica con simulaciones y modelado de sistemas mecánicos complejos","Mantén tu conocimiento actualizado con las últimas tecnologías mecánicas"]:"numerico"===e?100===t?["¡Excelente! Has demostrado una capacidad excepcional en razonamiento numérico","Considera carreras en matemáticas, estadística, ingeniería, economía o ciencias actuariales","Tu habilidad para resolver problemas numéricos complejos es sobresaliente","Podrías explorar campos como análisis de datos, investigación operativa o finanzas cuantitativas"]:t<60?["Repasa las operaciones básicas: suma, resta, multiplicación y división","Practica con ejercicios de igualdades numéricas y resolución de ecuaciones simples","Dedica tiempo a entender patrones en series numéricas","Refuerza tu comprensión de fracciones, decimales y porcentajes"]:t<80?["Practica con series numéricas más complejas para identificar patrones","Mejora tu velocidad en cálculo mental y operaciones aritméticas","Estudia problemas de proporcionalidad y regla de tres","Analiza tablas de datos y practica la interpretación de información numérica"]:["Desafíate con problemas de matemáticas más avanzados","Explora áreas como álgebra, estadística y análisis de datos","Considera estudiar carreras que requieran fuerte razonamiento cuantitativo","Mantén tu agilidad mental practicando regularmente con ejercicios numéricos"]:["Continúa practicando ejercicios similares para mejorar tu desempeño","Revisa los conceptos básicos relacionados con este tipo de prueba","Analiza las preguntas que te resultaron más difíciles para identificar áreas de mejora"],a})(i,c);return p.jsxs("div",{className:"container mx-auto px-4 py-8 max-w-4xl",children:[p.jsxs("div",{className:"mb-6",children:[p.jsx("h1",{className:"text-2xl font-bold text-gray-900 mb-2",children:(e=>{switch(e){case"verbal":return"Test de Aptitud Verbal";case"ortografia":return"Test de Ortografía";case"razonamiento":return"Test de Razonamiento";case"atencion":return"Test de Atención";case"espacial":return"Test de Visualización Espacial";case"mecanico":return"Test de Razonamiento Mecánico";case"numerico":return"Test de Razonamiento Numérico";default:return"Resultados del Test"}})(i)}),p.jsx("p",{className:"text-gray-600",children:"Resumen de tu desempeño en el test."})]}),p.jsxs("div",{className:"grid grid-cols-1 lg:grid-cols-2 gap-6 mb-8",children:[p.jsxs("div",{className:"bg-white shadow-md rounded-lg p-6",children:[p.jsx("h2",{className:"text-lg font-semibold text-gray-800 mb-4",children:"Resultados"}),p.jsx("div",{className:"h-64",children:p.jsx(Ts,{data:o})})]}),p.jsxs("div",{className:"bg-white shadow-md rounded-lg p-6",children:[p.jsx("h2",{className:"text-lg font-semibold text-gray-800 mb-4",children:"Estadísticas"}),p.jsxs("div",{className:"mb-6",children:[p.jsxs("div",{className:"text-3xl font-bold mb-1 "+(d=c,100===d?"text-green-600 animate-pulse":d>=90?"text-green-600":d>=75?"text-green-500":d>=60?"text-blue-500":d>=50?"text-yellow-500":"text-red-500"),children:[c,"%"]}),p.jsx("p",{className:"text-gray-700 font-medium",children:(e=>100===e||e>=90?"Excelente desempeño":e>=75?"Muy buen desempeño":e>=60?"Buen desempeño":e>=50?"Desempeño aceptable":"Necesita mejorar")(c)})]}),p.jsxs("div",{className:"space-y-3",children:[p.jsxs("div",{className:"flex justify-between py-2 border-b border-gray-100",children:[p.jsx("span",{className:"text-gray-600",children:"Respuestas correctas:"}),p.jsxs("span",{className:"font-medium text-gray-800",children:[t," de ",n]})]}),p.jsxs("div",{className:"flex justify-between py-2 border-b border-gray-100",children:[p.jsx("span",{className:"text-gray-600",children:"Respuestas incorrectas:"}),p.jsx("span",{className:"font-medium text-gray-800",children:a})]}),p.jsxs("div",{className:"flex justify-between py-2 border-b border-gray-100",children:[p.jsx("span",{className:"text-gray-600",children:"Sin responder:"}),p.jsx("span",{className:"font-medium text-gray-800",children:r})]}),p.jsxs("div",{className:"flex justify-between py-2",children:[p.jsx("span",{className:"text-gray-600",children:"Tiempo utilizado:"}),p.jsx("span",{className:"font-medium text-gray-800",children:(e=>{const t=e%60;return`${Math.floor(e/60)}:${t<10?"0":""}${t}`})(s)})]})]})]})]}),p.jsxs("div",{className:"bg-white shadow-md rounded-lg p-6 mb-6",children:[p.jsx("h2",{className:"text-lg font-semibold text-gray-800 mb-4",children:"Recomendaciones"}),p.jsx("ul",{className:"space-y-2",children:l.map((e,t)=>p.jsxs("li",{className:"flex items-start",children:[p.jsx("div",{className:"flex-shrink-0 h-5 w-5 mt-0.5",children:p.jsx("svg",{className:"h-5 w-5 text-green-500",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",xmlns:"http://www.w3.org/2000/svg",children:p.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:"2",d:"M5 13l4 4L19 7"})})}),p.jsx("span",{className:"ml-2 text-gray-700",children:e})]},t))})]}),p.jsxs("div",{className:"flex flex-col sm:flex-row gap-4",children:[p.jsx(A,{to:`/test/instructions/${i}`,className:"flex-1 bg-blue-600 text-white text-center py-3 px-4 rounded-md hover:bg-blue-700 transition-colors",children:"Realizar el Test Nuevamente"}),p.jsx(A,{to:"/student/tests",className:"flex-1 bg-gray-100 text-gray-800 text-center py-3 px-4 rounded-md hover:bg-gray-200 transition-colors",children:"Volver a la Lista de Tests"})]})]});var d},qs=Object.freeze(Object.defineProperty({__proto__:null,default:zs},Symbol.toStringTag,{value:"Module"})),Os=N.createContext(),Ps=()=>{const e=w(),{resultados:t,aplicacionId:a,testCompletado:r,cargarResultados:s,cargando:n}=N.useContext(Os),[i,o]=N.useState(!0),[c,l]=N.useState(null),[d,m]=N.useState({fortalezas:[],areas_mejora:[],recomendaciones:[]});N.useEffect(()=>{u(void 0,null,function*(){try{if(o(!0),!t&&a&&(yield s(a)),a){const e={fecha_evaluacion:"2025-06-12T10:00:00Z",candidatos:{id_candidato:"367894512",nombre:"Camila",apellido:"Vargas Vargas",sexo:"Femenino"}};e&&e.candidatos&&l({nombreCompleto:`${e.candidatos.nombre} ${e.candidatos.apellido}`,id_paciente:e.candidatos.id_candidato,sexo:e.candidatos.sexo,fecha_evaluacion:new Date(e.fecha_evaluacion).toLocaleDateString("es-ES",{day:"numeric",month:"long",year:"numeric"})})}t&&x(t)}catch(e){}finally{o(!1)}})},[a,t,s]);const x=e=>{const t=[],a=[],r=[];Object.entries(e).forEach(([e,r])=>{const{codigo:s,nombre:n,puntuacionCentil:i,interpretacion:o}=r;i>=70?t.push({codigo:s,nombre:n,interpretacion:`${o} (PC: ${i})`,descripcion:b(s,!0)}):i<=30&&a.push({codigo:s,nombre:n,interpretacion:`${o} (PC: ${i})`,descripcion:b(s,!1)})}),a.forEach(e=>{r.push({codigo:e.codigo,recomendacion:y(e.codigo)})}),m({fortalezas:t,areas_mejora:a,recomendaciones:r})},b=(e,t)=>{const a={V:{fortaleza:"Alta capacidad para comprender, utilizar y analizar el lenguaje escrito y hablado.",debilidad:"Dificultades para comprender conceptos expresados a través de palabras."},E:{fortaleza:"Excelente capacidad para visualizar y manipular mentalmente formas y patrones espaciales.",debilidad:"Dificultades para comprender relaciones espaciales y visualizar objetos en diferentes dimensiones."},A:{fortaleza:"Gran capacidad para mantener el foco en tareas específicas, detectando detalles con precisión.",debilidad:"Dificultad para mantener la concentración y detectar detalles específicos en tareas que requieren atención sostenida."},R:{fortaleza:"Destacada habilidad para identificar patrones lógicos y resolver problemas mediante el razonamiento.",debilidad:"Dificultades para identificar reglas lógicas y establecer inferencias en situaciones nuevas."},N:{fortaleza:"Excelente capacidad para comprender y manipular conceptos numéricos y resolver problemas matemáticos.",debilidad:"Dificultades en el manejo de conceptos numéricos y operaciones matemáticas básicas."},M:{fortaleza:"Buena comprensión de principios físicos y mecánicos básicos aplicados a situaciones cotidianas.",debilidad:"Dificultades para comprender el funcionamiento de dispositivos mecánicos y principios físicos básicos."},O:{fortaleza:"Excelente dominio de las reglas ortográficas y alta precisión en la escritura.",debilidad:"Dificultades con las reglas ortográficas y tendencia a cometer errores en la escritura."}};return a[e]?t?a[e].fortaleza:a[e].debilidad:"No hay descripción disponible."},y=e=>({V:"Fomentar la lectura diaria y realizar actividades que enriquezcan el vocabulario como juegos de palabras, debates y redacción.",E:"Practicar con rompecabezas, ejercicios de rotación mental, dibujo técnico y actividades que involucren navegación espacial.",A:"Realizar ejercicios de mindfulness, practicar tareas que requieran concentración por períodos cortos e ir aumentando gradualmente el tiempo.",R:"Resolver acertijos lógicos, participar en juegos de estrategia y analizar problemas complejos dividiéndolos en partes más sencillas.",N:"Practicar operaciones matemáticas diariamente, resolver problemas aplicados a la vida real y utilizar juegos que involucren cálculos.",M:"Construir modelos, experimentar con el funcionamiento de objetos cotidianos y estudiar los principios básicos de la física.",O:"Realizar ejercicios de dictado, revisión de textos y practicar la escritura consciente prestando atención a las reglas ortográficas."}[e]||"No hay recomendaciones específicas disponibles.");return i||n?p.jsx("div",{className:"flex items-center justify-center h-screen",children:p.jsx(Yr,{fullScreen:!0,message:"Cargando resultados..."})}):t&&0!==Object.keys(t).length?p.jsx("div",{className:"min-h-screen bg-gray-100 py-8 px-4 sm:px-6 lg:px-8",children:p.jsxs("div",{className:"max-w-5xl mx-auto",children:[p.jsx("h1",{className:"text-3xl font-bold text-gray-800 mb-6",children:"Resultados Detallados"}),c&&p.jsxs("div",{className:"bg-white shadow-lg rounded-lg p-6 mb-8 flex items-center justify-between",children:[p.jsxs("div",{className:"flex items-center",children:[p.jsx("div",{className:"mr-4",children:"Femenino"===c.sexo?p.jsx(h,{className:"text-pink-500 text-5xl"}):"Masculino"===c.sexo?p.jsx(g,{className:"text-blue-500 text-5xl"}):p.jsx(f,{className:"text-gray-500 text-5xl"})}),p.jsxs("div",{children:[p.jsx("h2",{className:"text-2xl font-bold text-gray-900",children:c.nombreCompleto}),p.jsxs("p",{className:"text-sm text-gray-600",children:["ID: ",c.id_paciente]})]})]}),p.jsxs("div",{className:"text-right",children:[p.jsx("p",{className:"text-sm text-gray-700",children:"Fecha de Evaluación:"}),p.jsx("p",{className:"text-md font-semibold text-gray-800",children:c.fecha_evaluacion})]})]}),t&&Object.keys(t).length>0?p.jsxs("div",{className:"bg-white shadow-lg rounded-lg overflow-hidden mb-8",children:[p.jsx("div",{className:"overflow-x-auto",children:p.jsxs("table",{className:"min-w-full divide-y divide-gray-200",children:[p.jsx("thead",{className:"bg-gray-50",children:p.jsxs("tr",{children:[p.jsx("th",{scope:"col",className:"px-6 py-4 text-center text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Test"}),p.jsx("th",{scope:"col",className:"px-6 py-4 text-center text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Puntaje PD"}),p.jsx("th",{scope:"col",className:"px-6 py-4 text-center text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Puntuación T"}),p.jsx("th",{scope:"col",className:"px-6 py-4 text-center text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Errores"}),p.jsx("th",{scope:"col",className:"px-6 py-4 text-center text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Tiempo"})]})}),p.jsx("tbody",{className:"bg-white divide-y divide-gray-200",children:Object.entries(t).map(([e,t])=>p.jsxs("tr",{children:[p.jsx("td",{className:"px-6 py-4 whitespace-nowrap text-center",children:p.jsx("div",{className:"text-sm font-medium text-gray-900",children:t.nombre||"N/A"})}),p.jsx("td",{className:"px-6 py-4 whitespace-nowrap text-center",children:p.jsx("div",{className:"text-sm text-gray-900",children:void 0!==t.puntuacionDirecta?t.puntuacionDirecta:"N/A"})}),p.jsx("td",{className:"px-6 py-4 whitespace-nowrap text-center",children:p.jsx("div",{className:"text-sm text-gray-900",children:void 0!==t.puntuacionCentil?t.puntuacionCentil:"N/A"})}),p.jsx("td",{className:"px-6 py-4 whitespace-nowrap text-center",children:p.jsx("div",{className:"text-sm text-gray-900",children:void 0!==t.errores?t.errores:"N/A"})}),p.jsx("td",{className:"px-6 py-4 whitespace-nowrap text-center",children:p.jsx("div",{className:"text-sm text-gray-900",children:t.tiempo||"N/A"})})]},e))})]})}),p.jsxs("div",{className:"p-4 text-xs text-gray-500 bg-gray-50 border-t",children:[p.jsxs("p",{children:[p.jsx("span",{className:"font-medium",children:"Puntaje PD:"})," Puntuación Directa - Número de respuestas correctas."]}),p.jsxs("p",{children:[p.jsx("span",{className:"font-medium",children:"Puntuación T:"})," Puntuación Transformada (ej. Percentil) - Posición relativa respecto a la población de referencia."]})]})]}):p.jsx("div",{className:"bg-white shadow-lg rounded-lg p-6 mb-8 text-center",children:p.jsx("p",{className:"text-gray-600",children:"No hay resultados de pruebas para mostrar para este paciente."})}),(d.fortalezas.length>0||d.areas_mejora.length>0||d.recomendaciones.length>0)&&p.jsxs("div",{className:"bg-white shadow-lg rounded-lg mb-8",children:[p.jsx("h2",{className:"text-xl font-semibold p-4 bg-gray-50 border-b text-gray-800",children:"Informe Cualitativo"}),p.jsxs("div",{className:"p-6",children:[d.fortalezas.length>0&&p.jsxs("div",{className:"mb-6",children:[p.jsx("h3",{className:"text-lg font-medium text-green-700 mb-2",children:"Fortalezas"}),p.jsx("div",{className:"space-y-3",children:d.fortalezas.map((e,t)=>p.jsxs("div",{className:"bg-green-50 p-3 rounded-md",children:[p.jsxs("div",{className:"font-semibold text-green-800",children:[e.nombre,": ",e.interpretacion]}),p.jsx("p",{className:"text-sm text-green-700 mt-1",children:e.descripcion})]},t))})]}),d.areas_mejora.length>0&&p.jsxs("div",{className:"mb-6",children:[p.jsx("h3",{className:"text-lg font-medium text-red-700 mb-2",children:"Áreas de Mejora"}),p.jsx("div",{className:"space-y-3",children:d.areas_mejora.map((e,t)=>p.jsxs("div",{className:"bg-red-50 p-3 rounded-md",children:[p.jsxs("div",{className:"font-semibold text-red-800",children:[e.nombre,": ",e.interpretacion]}),p.jsx("p",{className:"text-sm text-red-700 mt-1",children:e.descripcion})]},t))})]}),d.recomendaciones.length>0&&p.jsxs("div",{children:[p.jsx("h3",{className:"text-lg font-medium text-blue-700 mb-2",children:"Recomendaciones"}),p.jsx("div",{className:"space-y-3",children:d.recomendaciones.map((e,a)=>{var r;return p.jsxs("div",{className:"bg-blue-50 p-3 rounded-md",children:[p.jsxs("div",{className:"font-semibold text-blue-800",children:[e.codigo," - ",null==(r=t[Object.keys(t).find(a=>t[a].codigo===e.codigo)])?void 0:r.nombre]}),p.jsx("p",{className:"text-sm text-blue-700 mt-1",children:e.recomendacion})]},a)})})]})]})]}),p.jsxs("div",{className:"flex flex-col sm:flex-row justify-end space-y-3 sm:space-y-0 sm:space-x-3 mt-8",children:[p.jsx("button",{className:"px-6 py-3 bg-white border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50 transition duration-150",onClick:()=>e("/test"),children:"Volver"}),p.jsxs("div",{className:"flex space-x-3",children:[p.jsx("button",{className:"px-6 py-3 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition duration-150",onClick:()=>window.print(),children:"Imprimir Resultados"}),p.jsx("button",{className:"px-6 py-3 bg-green-600 text-white rounded-md hover:bg-green-700 transition duration-150",onClick:()=>alert("Función de exportar a PDF en desarrollo."),children:"Exportar a PDF"})]})]})]})}):p.jsx("div",{className:"flex items-center justify-center h-screen bg-blue-50",children:p.jsx("div",{className:"bg-white p-8 rounded-lg shadow-md max-w-md",children:p.jsxs("div",{className:"text-center",children:[p.jsx("svg",{className:"mx-auto h-16 w-16 text-blue-400",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",xmlns:"http://www.w3.org/2000/svg",children:p.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:"2",d:"M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"})}),p.jsx("h2",{className:"mt-4 text-xl font-bold text-gray-800",children:"No hay resultados disponibles"}),p.jsx("p",{className:"mt-2 text-gray-600",children:"No se han encontrado resultados para mostrar. Es posible que aún no hayas completado el test o que haya ocurrido un error."}),p.jsx("div",{className:"mt-6",children:p.jsx("button",{className:"w-full px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700",onClick:()=>e("/test"),children:"Volver a Tests"})})]})})})},Ds=()=>p.jsxs(T,{children:[p.jsx(z,{path:"/instructions/:testId",element:p.jsx(as,{})}),p.jsx(z,{path:"/verbal",element:p.jsx(cs,{})}),p.jsx(z,{path:"/ortografia",element:p.jsx(xs,{})}),p.jsx(z,{path:"/razonamiento",element:p.jsx(bs,{})}),p.jsx(z,{path:"/atencion",element:p.jsx(js,{})}),p.jsx(z,{path:"/espacial",element:p.jsx(Ns,{})}),p.jsx(z,{path:"/mecanico",element:p.jsx(As,{})}),p.jsx(z,{path:"/numerico",element:p.jsx(_s,{})}),p.jsx(z,{path:"/results/:resultId",element:p.jsx(zs,{})}),p.jsx(z,{path:"/resultados/:resultId",element:p.jsx(Ps,{})}),p.jsx(z,{path:"/",element:p.jsx(q,{to:"/student/tests",replace:!0})})]}),ks=()=>p.jsx("div",{className:"flex justify-center items-center min-h-screen",children:p.jsxs("div",{className:"text-center",children:[p.jsx(y,{className:"animate-spin text-blue-600 mx-auto mb-4 text-4xl"}),p.jsx("p",{className:"text-gray-600 font-medium",children:"Cargando..."})]})}),Rs=e=>t=>p.jsx(Zr,{children:p.jsx(e,o({},t))}),Is=e=>t=>p.jsx(Zr,{children:p.jsx(N.Suspense,{fallback:p.jsx(ks,{}),children:p.jsx(e,o({},t))})}),Ms=Is(N.lazy(()=>b(()=>import("./Dashboard-0f28ccc7.js"),["assets/Dashboard-0f28ccc7.js","assets/auth-3ab59eff.js","assets/ui-vendor-9705a4a1.js","assets/react-vendor-99be060c.js","assets/utils-vendor-4d1206d7.js","assets/enhancedSupabaseService-a93ecdc2.js"]))),Bs=Is(N.lazy(()=>b(()=>import("./Profile-d661f09a.js"),["assets/Profile-d661f09a.js","assets/auth-3ab59eff.js","assets/ui-vendor-9705a4a1.js","assets/react-vendor-99be060c.js","assets/utils-vendor-4d1206d7.js"]))),Ls=Is(N.lazy(()=>b(()=>import("./Settings-930052d3.js"),["assets/Settings-930052d3.js","assets/auth-3ab59eff.js","assets/ui-vendor-9705a4a1.js","assets/react-vendor-99be060c.js","assets/utils-vendor-4d1206d7.js"]))),$s=Is(N.lazy(()=>b(()=>import("./Home-e4d5a76e.js"),["assets/Home-e4d5a76e.js","assets/auth-3ab59eff.js","assets/ui-vendor-9705a4a1.js","assets/react-vendor-99be060c.js","assets/utils-vendor-4d1206d7.js","assets/admin-168d579d.js","assets/admin-93d8b085.css"]))),Fs=Is(N.lazy(()=>b(()=>import("./Help-4c3906eb.js"),["assets/Help-4c3906eb.js","assets/auth-3ab59eff.js","assets/ui-vendor-9705a4a1.js","assets/react-vendor-99be060c.js","assets/utils-vendor-4d1206d7.js","assets/admin-168d579d.js","assets/admin-93d8b085.css"]))),Vs=Is(N.lazy(()=>b(()=>import("./Configuracion-e64057c3.js"),["assets/Configuracion-e64057c3.js","assets/auth-3ab59eff.js","assets/ui-vendor-9705a4a1.js","assets/react-vendor-99be060c.js","assets/utils-vendor-4d1206d7.js","assets/admin-168d579d.js","assets/admin-93d8b085.css"]))),Us=Rs(N.lazy(()=>b(()=>import("./Candidates-b6805f00.js"),["assets/Candidates-b6805f00.js","assets/auth-3ab59eff.js","assets/ui-vendor-9705a4a1.js","assets/react-vendor-99be060c.js","assets/utils-vendor-4d1206d7.js","assets/admin-168d579d.js","assets/admin-93d8b085.css","assets/useToast-f53e3f80.js"]))),Qs=Rs(N.lazy(()=>b(()=>import("./VerbalInfo-4f7ecc75.js"),["assets/VerbalInfo-4f7ecc75.js","assets/auth-3ab59eff.js","assets/ui-vendor-9705a4a1.js","assets/react-vendor-99be060c.js","assets/utils-vendor-4d1206d7.js","assets/admin-168d579d.js","assets/admin-93d8b085.css"]))),Ws=Is(N.lazy(()=>b(()=>import("./Users-99579183.js"),["assets/Users-99579183.js","assets/auth-3ab59eff.js","assets/ui-vendor-9705a4a1.js","assets/react-vendor-99be060c.js","assets/utils-vendor-4d1206d7.js","assets/admin-168d579d.js","assets/admin-93d8b085.css"]))),Hs=Is(N.lazy(()=>b(()=>import("./admin-168d579d.js").then(e=>e.I),["assets/admin-168d579d.js","assets/auth-3ab59eff.js","assets/ui-vendor-9705a4a1.js","assets/react-vendor-99be060c.js","assets/utils-vendor-4d1206d7.js","assets/admin-93d8b085.css"]))),Gs=Is(N.lazy(()=>b(()=>import("./Reports-b817b864.js"),["assets/Reports-b817b864.js","assets/auth-3ab59eff.js","assets/ui-vendor-9705a4a1.js","assets/react-vendor-99be060c.js","assets/utils-vendor-4d1206d7.js","assets/admin-168d579d.js","assets/admin-93d8b085.css"]))),Xs=Is(N.lazy(()=>b(()=>import("./Patients-82d3bb9d.js"),["assets/Patients-82d3bb9d.js","assets/auth-3ab59eff.js","assets/ui-vendor-9705a4a1.js","assets/react-vendor-99be060c.js","assets/utils-vendor-4d1206d7.js","assets/admin-168d579d.js","assets/admin-93d8b085.css"]))),Ks=Is(N.lazy(()=>b(()=>import("./admin-168d579d.js").then(e=>e.A),["assets/admin-168d579d.js","assets/auth-3ab59eff.js","assets/ui-vendor-9705a4a1.js","assets/react-vendor-99be060c.js","assets/utils-vendor-4d1206d7.js","assets/admin-93d8b085.css"]))),Js=Is(N.lazy(()=>b(()=>import("./TestPage-767adbcf.js"),["assets/TestPage-767adbcf.js","assets/auth-3ab59eff.js","assets/ui-vendor-9705a4a1.js","assets/react-vendor-99be060c.js","assets/utils-vendor-4d1206d7.js","assets/admin-168d579d.js","assets/admin-93d8b085.css","assets/enhancedSupabaseService-a93ecdc2.js"]))),Ys=Is(N.lazy(()=>b(()=>import("./CompleteReport-d51cdf38.js"),["assets/CompleteReport-d51cdf38.js","assets/auth-3ab59eff.js","assets/ui-vendor-9705a4a1.js","assets/react-vendor-99be060c.js","assets/utils-vendor-4d1206d7.js","assets/admin-168d579d.js","assets/admin-93d8b085.css","assets/interpretacionesAptitudes-bd504cf8.js"]))),Zs=Is(N.lazy(()=>b(()=>import("./SavedReports-890ca0ce.js"),["assets/SavedReports-890ca0ce.js","assets/auth-3ab59eff.js","assets/ui-vendor-9705a4a1.js","assets/react-vendor-99be060c.js","assets/utils-vendor-4d1206d7.js","assets/admin-168d579d.js","assets/admin-93d8b085.css"]))),en=Is(N.lazy(()=>b(()=>import("./ViewSavedReport-da035186.js"),["assets/ViewSavedReport-da035186.js","assets/auth-3ab59eff.js","assets/ui-vendor-9705a4a1.js","assets/react-vendor-99be060c.js","assets/utils-vendor-4d1206d7.js","assets/admin-168d579d.js","assets/admin-93d8b085.css","assets/interpretacionesAptitudes-bd504cf8.js"]))),tn=Is(N.lazy(()=>b(()=>import("./Students-9126dfbb.js"),["assets/Students-9126dfbb.js","assets/auth-3ab59eff.js","assets/ui-vendor-9705a4a1.js","assets/react-vendor-99be060c.js","assets/utils-vendor-4d1206d7.js","assets/admin-168d579d.js","assets/admin-93d8b085.css"]))),an=Is(N.lazy(()=>b(()=>import("./Tests-55cbb82b.js"),["assets/Tests-55cbb82b.js","assets/auth-3ab59eff.js","assets/ui-vendor-9705a4a1.js","assets/react-vendor-99be060c.js","assets/utils-vendor-4d1206d7.js","assets/admin-168d579d.js","assets/admin-93d8b085.css"]))),rn=Is(N.lazy(()=>b(()=>import("./Reports-0fd019bb.js"),["assets/Reports-0fd019bb.js","assets/auth-3ab59eff.js","assets/ui-vendor-9705a4a1.js","assets/react-vendor-99be060c.js","assets/utils-vendor-4d1206d7.js","assets/admin-168d579d.js","assets/admin-93d8b085.css"]))),sn=Is(N.lazy(()=>b(()=>import("./Patients-4ed993c7.js"),[]))),nn=Is(N.lazy(()=>b(()=>import("./Tests-9b74fcc5.js"),["assets/Tests-9b74fcc5.js","assets/auth-3ab59eff.js","assets/ui-vendor-9705a4a1.js","assets/react-vendor-99be060c.js","assets/utils-vendor-4d1206d7.js","assets/TestCard-4e5c662a.js","assets/TestCard-23675be1.css","assets/admin-168d579d.js","assets/admin-93d8b085.css"]))),on=Is(N.lazy(()=>b(()=>import("./Results-d5725f5a.js"),["assets/Results-d5725f5a.js","assets/auth-3ab59eff.js","assets/ui-vendor-9705a4a1.js","assets/react-vendor-99be060c.js","assets/utils-vendor-4d1206d7.js","assets/admin-168d579d.js","assets/admin-93d8b085.css","assets/useToast-f53e3f80.js"]))),cn=Is(N.lazy(()=>b(()=>import("./Patients-04aa2eee.js"),["assets/Patients-04aa2eee.js","assets/auth-3ab59eff.js","assets/ui-vendor-9705a4a1.js","assets/react-vendor-99be060c.js","assets/utils-vendor-4d1206d7.js","assets/admin-168d579d.js","assets/admin-93d8b085.css","assets/Patients-3edbc5b5.css"]))),ln=Is(N.lazy(()=>b(()=>import("./Questionnaire-bb09f69e.js"),["assets/Questionnaire-bb09f69e.js","assets/auth-3ab59eff.js","assets/ui-vendor-9705a4a1.js","assets/react-vendor-99be060c.js","assets/utils-vendor-4d1206d7.js","assets/TestCard-4e5c662a.js","assets/TestCard-23675be1.css","assets/admin-168d579d.js","assets/admin-93d8b085.css"]))),dn=Is(N.lazy(()=>b(()=>import("./InformePaciente-e6396bbf.js"),["assets/InformePaciente-e6396bbf.js","assets/auth-3ab59eff.js","assets/ui-vendor-9705a4a1.js","assets/react-vendor-99be060c.js","assets/utils-vendor-4d1206d7.js","assets/admin-168d579d.js","assets/admin-93d8b085.css"]))),un=Is(N.lazy(()=>b(()=>Promise.resolve().then(()=>rs),void 0))),pn=Is(N.lazy(()=>b(()=>Promise.resolve().then(()=>ls),void 0))),mn=Is(N.lazy(()=>b(()=>Promise.resolve().then(()=>ws),void 0))),xn=Is(N.lazy(()=>b(()=>Promise.resolve().then(()=>vs),void 0))),hn=Is(N.lazy(()=>b(()=>Promise.resolve().then(()=>ys),void 0))),gn=Is(N.lazy(()=>b(()=>Promise.resolve().then(()=>Ss),void 0))),fn=Is(N.lazy(()=>b(()=>Promise.resolve().then(()=>Es),void 0))),bn=Is(N.lazy(()=>b(()=>Promise.resolve().then(()=>hs),void 0))),yn=Is(N.lazy(()=>b(()=>Promise.resolve().then(()=>qs),void 0))),jn=Is(N.lazy(()=>b(()=>import("./BasicLogin-3ef0cafe.js"),["assets/BasicLogin-3ef0cafe.js","assets/auth-3ab59eff.js","assets/ui-vendor-9705a4a1.js","assets/react-vendor-99be060c.js","assets/utils-vendor-4d1206d7.js"]))),vn=()=>{const e=C();return N.useEffect(()=>{},[e.pathname]),p.jsx(Zr,{children:p.jsx(N.Suspense,{fallback:p.jsx(ks,{}),children:p.jsxs(T,{children:[p.jsx(z,{path:"/",element:p.jsx(q,{to:"/home",replace:!0})}),p.jsx(z,{path:"/login",element:p.jsx(jn,{})}),p.jsx(z,{path:"/register",element:p.jsx(q,{to:"/login",replace:!0})}),p.jsx(z,{path:"/auth/troubleshooting",element:p.jsx(q,{to:"/login",replace:!0})}),p.jsx(z,{path:"/auth",element:p.jsx(q,{to:"/login",replace:!0})}),p.jsx(z,{path:"/force-admin",element:p.jsx(q,{to:"/admin/administration",replace:!0})}),p.jsx(z,{path:"/info/verbal",element:p.jsx(Qs,{})}),p.jsx(z,{path:"/test/instructions/:testId",element:p.jsx(un,{})}),p.jsx(z,{path:"/test/verbal",element:p.jsx(pn,{})}),p.jsx(z,{path:"/test/espacial",element:p.jsx(mn,{})}),p.jsx(z,{path:"/test/atencion",element:p.jsx(xn,{})}),p.jsx(z,{path:"/test/razonamiento",element:p.jsx(hn,{})}),p.jsx(z,{path:"/test/numerico",element:p.jsx(gn,{})}),p.jsx(z,{path:"/test/mecanico",element:p.jsx(fn,{})}),p.jsx(z,{path:"/test/ortografia",element:p.jsx(bn,{})}),p.jsx(z,{path:"/test/results/:applicationId",element:p.jsx(yn,{})}),p.jsx(z,{path:"/test/*",element:p.jsx(Ds,{})}),p.jsxs(z,{element:p.jsx(Jr,{}),children:[p.jsx(z,{path:"/dashboard",element:p.jsx(q,{to:"/home",replace:!0})}),p.jsx(z,{path:"/profile",element:p.jsx(Bs,{})}),p.jsx(z,{path:"/settings",element:p.jsx(Ls,{})}),p.jsx(z,{path:"/home",element:p.jsx($s,{})}),p.jsx(z,{path:"/help",element:p.jsx(Fs,{})}),p.jsx(z,{path:"/configuracion",element:p.jsx(Vs,{})}),p.jsxs(z,{path:"/admin",children:[p.jsx(z,{index:!0,element:p.jsx(Ks,{})}),p.jsx(z,{path:"dashboard",element:p.jsx(Ms,{})}),p.jsx(z,{path:"users",element:p.jsx(Ws,{})}),p.jsx(z,{path:"institutions",element:p.jsx(Hs,{})}),p.jsx(z,{path:"reports",element:p.jsx(Gs,{})}),p.jsx(z,{path:"patients",element:p.jsx(Xs,{})}),p.jsx(z,{path:"administration",element:p.jsx(Ks,{})}),p.jsx(z,{path:"configuracion",element:p.jsx(Vs,{})}),p.jsx(z,{path:"tests",element:p.jsx(Js,{})}),p.jsx(z,{path:"informe-completo/:patientId",element:p.jsx(Ys,{})}),p.jsx(z,{path:"informes-guardados",element:p.jsx(Zs,{})}),p.jsx(z,{path:"informe-guardado/:reportId",element:p.jsx(en,{})})]}),p.jsxs(z,{path:"/professional",children:[p.jsx(z,{index:!0,element:p.jsx(Ms,{})}),p.jsx(z,{path:"dashboard",element:p.jsx(Ms,{})}),p.jsx(z,{path:"students",element:p.jsx(tn,{})}),p.jsx(z,{path:"tests",element:p.jsx(an,{})}),p.jsx(z,{path:"reports",element:p.jsx(rn,{})}),p.jsx(z,{path:"candidates",element:p.jsx(Us,{})}),p.jsx(z,{path:"patients",element:p.jsx(sn,{})})]}),p.jsxs(z,{path:"/student",children:[p.jsx(z,{index:!0,element:p.jsx(Ms,{})}),p.jsx(z,{path:"dashboard",element:p.jsx(Ms,{})}),p.jsx(z,{path:"tests",element:p.jsx(nn,{})}),p.jsx(z,{path:"questionnaire",element:p.jsx(ln,{})}),p.jsx(z,{path:"results",element:p.jsx(on,{})}),p.jsx(z,{path:"informe/:pacienteId",element:p.jsx(dn,{})}),p.jsx(z,{path:"patients",element:p.jsx(cn,{})})]})]}),p.jsx(z,{path:"*",element:p.jsx(q,{to:"/home",replace:!0})})]})})})},Nn=()=>p.jsx(O,{children:p.jsx(vn,{})});function wn(){return N.useEffect(()=>{const e=localStorage.getItem("userTheme");("dark"===e||"system"===e&&window.matchMedia("(prefers-color-scheme: dark)").matches)&&document.body.classList.add("dark-mode");const t=window.matchMedia("(prefers-color-scheme: dark)"),a=e=>{"system"===localStorage.getItem("userTheme")&&(e.matches?document.body.classList.add("dark-mode"):document.body.classList.remove("dark-mode"))};return t.addEventListener("change",a),()=>{t.removeEventListener("change",a)}},[]),p.jsxs("div",{className:"app",children:[p.jsx(D,{position:"top-right",autoClose:5e3,hideProgressBar:!1,newestOnTop:!0,closeOnClick:!0,rtl:!1,pauseOnFocusLoss:!0,draggable:!0,pauseOnHover:!0}),p.jsx(Nn,{})]})}F.createRoot(document.getElementById("root")).render(p.jsx(P.StrictMode,{children:p.jsx(Z,{store:Qr,children:p.jsx(j,{children:p.jsx(wn,{})})})}));export{is as B,ms as P,ns as c,gs as g};
