var e=Object.defineProperty,t=Object.defineProperties,n=Object.getOwnPropertyDescriptors,o=Object.getOwnPropertySymbols,s=Object.prototype.hasOwnProperty,a=Object.prototype.propertyIsEnumerable,r=(t,n,o)=>n in t?e(t,n,{enumerable:!0,configurable:!0,writable:!0,value:o}):t[n]=o,i=(e,t)=>{for(var n in t||(t={}))s.call(t,n)&&r(e,n,t[n]);if(o)for(var n of o(t))a.call(t,n)&&r(e,n,t[n]);return e},l=(e,o)=>t(e,n(o)),c=(e,t)=>{var n={};for(var r in e)s.call(e,r)&&t.indexOf(r)<0&&(n[r]=e[r]);if(null!=e&&o)for(var r of o(e))t.indexOf(r)<0&&a.call(e,r)&&(n[r]=e[r]);return n};import{r as u,R as d}from"./react-vendor-99be060c.js";function f(e){var t,n,o="";if("string"==typeof e||"number"==typeof e)o+=e;else if("object"==typeof e)if(Array.isArray(e))for(t=0;t<e.length;t++)e[t]&&(n=f(e[t]))&&(o&&(o+=" "),o+=n);else for(t in e)e[t]&&(o&&(o+=" "),o+=t);return o}function p(){for(var e,t,n=0,o="";n<arguments.length;)(e=arguments[n++])&&(t=f(e))&&(o&&(o+=" "),o+=t);return o}const m=e=>"number"==typeof e&&!isNaN(e),g=e=>"string"==typeof e,y=e=>"function"==typeof e,h=e=>g(e)||y(e)?e:null,v=e=>u.isValidElement(e)||g(e)||y(e)||m(e);function E(e){let{enter:t,exit:n,appendPosition:o=!1,collapse:s=!0,collapseDuration:a=300}=e;return function(e){let{children:r,position:i,preventExitTransition:l,done:c,nodeRef:f,isIn:p}=e;const m=o?`${t}--${i}`:t,g=o?`${n}--${i}`:n,y=u.useRef(0);return u.useLayoutEffect(()=>{const e=f.current,t=m.split(" "),n=o=>{o.target===f.current&&(e.dispatchEvent(new Event("d")),e.removeEventListener("animationend",n),e.removeEventListener("animationcancel",n),0===y.current&&"animationcancel"!==o.type&&e.classList.remove(...t))};e.classList.add(...t),e.addEventListener("animationend",n),e.addEventListener("animationcancel",n)},[]),u.useEffect(()=>{const e=f.current,t=()=>{e.removeEventListener("animationend",t),s?function(e,t,n){void 0===n&&(n=300);const{scrollHeight:o,style:s}=e;requestAnimationFrame(()=>{s.minHeight="initial",s.height=o+"px",s.transition=`all ${n}ms`,requestAnimationFrame(()=>{s.height="0",s.padding="0",s.margin="0",setTimeout(t,n)})})}(e,c,a):c()};p||(l?t():(y.current=1,e.className+=` ${g}`,e.addEventListener("animationend",t)))},[p]),d.createElement(d.Fragment,null,r)}}function T(e,t){return null!=e?{content:e.content,containerId:e.props.containerId,id:e.props.toastId,theme:e.props.theme,type:e.props.type,data:e.props.data||{},isLoading:e.props.isLoading,icon:e.props.icon,status:t}:{}}const b={list:new Map,emitQueue:new Map,on(e,t){return this.list.has(e)||this.list.set(e,[]),this.list.get(e).push(t),this},off(e,t){if(t){const n=this.list.get(e).filter(e=>e!==t);return this.list.set(e,n),this}return this.list.delete(e),this},cancelEmit(e){const t=this.emitQueue.get(e);return t&&(t.forEach(clearTimeout),this.emitQueue.delete(e)),this},emit(e){this.list.has(e)&&this.list.get(e).forEach(t=>{const n=setTimeout(()=>{t(...[].slice.call(arguments,1))},0);this.emitQueue.has(e)||this.emitQueue.set(e,[]),this.emitQueue.get(e).push(n)})}},O=e=>{let t=e,{theme:n,type:o}=t,s=c(t,["theme","type"]);return d.createElement("svg",i({viewBox:"0 0 24 24",width:"100%",height:"100%",fill:"colored"===n?"currentColor":`var(--toastify-icon-color-${o})`},s))},C={info:function(e){return d.createElement(O,i({},e),d.createElement("path",{d:"M12 0a12 12 0 1012 12A12.013 12.013 0 0012 0zm.25 5a1.5 1.5 0 11-1.5 1.5 1.5 1.5 0 011.5-1.5zm2.25 13.5h-4a1 1 0 010-2h.75a.25.25 0 00.25-.25v-4.5a.25.25 0 00-.25-.25h-.75a1 1 0 010-2h1a2 2 0 012 2v4.75a.25.25 0 00.25.25h.75a1 1 0 110 2z"}))},warning:function(e){return d.createElement(O,i({},e),d.createElement("path",{d:"M23.32 17.191L15.438 2.184C14.728.833 13.416 0 11.996 0c-1.42 0-2.733.833-3.443 2.184L.533 17.448a4.744 4.744 0 000 4.368C1.243 23.167 2.555 24 3.975 24h16.05C22.22 24 24 22.044 24 19.632c0-.904-.251-1.746-.68-2.44zm-9.622 1.46c0 1.033-.724 1.823-1.698 1.823s-1.698-.79-1.698-1.822v-.043c0-1.028.724-1.822 1.698-1.822s1.698.79 1.698 1.822v.043zm.039-12.285l-.84 8.06c-.057.581-.408.943-.897.943-.49 0-.84-.367-.896-.942l-.84-8.065c-.057-.624.25-1.095.779-1.095h1.91c.528.005.84.476.784 1.1z"}))},success:function(e){return d.createElement(O,i({},e),d.createElement("path",{d:"M12 0a12 12 0 1012 12A12.014 12.014 0 0012 0zm6.927 8.2l-6.845 9.289a1.011 1.011 0 01-1.43.188l-4.888-3.908a1 1 0 111.25-1.562l4.076 3.261 6.227-8.451a1 1 0 111.61 1.183z"}))},error:function(e){return d.createElement(O,i({},e),d.createElement("path",{d:"M11.983 0a12.206 12.206 0 00-8.51 3.653A11.8 11.8 0 000 12.207 11.779 11.779 0 0011.8 24h.214A12.111 12.111 0 0024 11.791 11.766 11.766 0 0011.983 0zM10.5 16.542a1.476 1.476 0 011.449-1.53h.027a1.527 1.527 0 011.523 1.47 1.475 1.475 0 01-1.449 1.53h-.027a1.529 1.529 0 01-1.523-1.47zM11 12.5v-6a1 1 0 012 0v6a1 1 0 11-2 0z"}))},spinner:function(){return d.createElement("div",{className:"Toastify__spinner"})}};function I(e){return e.targetTouches&&e.targetTouches.length>=1?e.targetTouches[0].clientX:e.clientX}function _(e){return e.targetTouches&&e.targetTouches.length>=1?e.targetTouches[0].clientY:e.clientY}function L(e){let{closeToast:t,theme:n,ariaLabel:o="close"}=e;return d.createElement("button",{className:`Toastify__close-button Toastify__close-button--${n}`,type:"button",onClick:e=>{e.stopPropagation(),t(e)},"aria-label":o},d.createElement("svg",{"aria-hidden":"true",viewBox:"0 0 14 16"},d.createElement("path",{fillRule:"evenodd",d:"M7.71 8.23l3.75 3.75-1.48 1.48-3.75-3.75-3.75 3.75L1 11.98l3.75-3.75L1 4.48 2.48 3l3.75 3.75L9.98 3l1.48 1.48-3.75 3.75z"})))}function N(e){let{delay:t,isRunning:n,closeToast:o,type:s="default",hide:a,className:r,style:c,controlledProgress:u,progress:f,rtl:m,isIn:g,theme:h}=e;const v=a||u&&0===f,E=l(i({},c),{animationDuration:`${t}ms`,animationPlayState:n?"running":"paused",opacity:v?0:1});u&&(E.transform=`scaleX(${f})`);const T=p("Toastify__progress-bar",u?"Toastify__progress-bar--controlled":"Toastify__progress-bar--animated",`Toastify__progress-bar-theme--${h}`,`Toastify__progress-bar--${s}`,{"Toastify__progress-bar--rtl":m}),b=y(r)?r({rtl:m,type:s,defaultClassName:T}):p(T,r);return d.createElement("div",{role:"progressbar","aria-hidden":v?"true":"false","aria-label":"notification timer",className:b,style:E,[u&&f>=1?"onTransitionEnd":"onAnimationEnd"]:u&&f<1?null:()=>{g&&o()}})}const w=e=>{const{isRunning:t,preventExitTransition:n,toastRef:o,eventHandlers:s}=function(e){const[t,n]=u.useState(!1),[o,s]=u.useState(!1),a=u.useRef(null),r=u.useRef({start:0,x:0,y:0,delta:0,removalDistance:0,canCloseOnClick:!0,canDrag:!1,boundingRect:null,didMove:!1}).current,i=u.useRef(e),{autoClose:l,pauseOnHover:c,closeToast:d,onClick:f,closeOnClick:p}=e;function m(t){if(e.draggable){"touchstart"===t.nativeEvent.type&&t.nativeEvent.preventDefault(),r.didMove=!1,document.addEventListener("mousemove",E),document.addEventListener("mouseup",T),document.addEventListener("touchmove",E),document.addEventListener("touchend",T);const n=a.current;r.canCloseOnClick=!0,r.canDrag=!0,r.boundingRect=n.getBoundingClientRect(),n.style.transition="",r.x=I(t.nativeEvent),r.y=_(t.nativeEvent),"x"===e.draggableDirection?(r.start=r.x,r.removalDistance=n.offsetWidth*(e.draggablePercent/100)):(r.start=r.y,r.removalDistance=n.offsetHeight*(80===e.draggablePercent?1.5*e.draggablePercent:e.draggablePercent/100))}}function g(t){if(r.boundingRect){const{top:n,bottom:o,left:s,right:a}=r.boundingRect;"touchend"!==t.nativeEvent.type&&e.pauseOnHover&&r.x>=s&&r.x<=a&&r.y>=n&&r.y<=o?v():h()}}function h(){n(!0)}function v(){n(!1)}function E(n){const o=a.current;r.canDrag&&o&&(r.didMove=!0,t&&v(),r.x=I(n),r.y=_(n),r.delta="x"===e.draggableDirection?r.x-r.start:r.y-r.start,r.start!==r.x&&(r.canCloseOnClick=!1),o.style.transform=`translate${e.draggableDirection}(${r.delta}px)`,o.style.opacity=""+(1-Math.abs(r.delta/r.removalDistance)))}function T(){document.removeEventListener("mousemove",E),document.removeEventListener("mouseup",T),document.removeEventListener("touchmove",E),document.removeEventListener("touchend",T);const t=a.current;if(r.canDrag&&r.didMove&&t){if(r.canDrag=!1,Math.abs(r.delta)>r.removalDistance)return s(!0),void e.closeToast();t.style.transition="transform 0.2s, opacity 0.2s",t.style.transform=`translate${e.draggableDirection}(0)`,t.style.opacity="1"}}u.useEffect(()=>{i.current=e}),u.useEffect(()=>(a.current&&a.current.addEventListener("d",h,{once:!0}),y(e.onOpen)&&e.onOpen(u.isValidElement(e.children)&&e.children.props),()=>{const e=i.current;y(e.onClose)&&e.onClose(u.isValidElement(e.children)&&e.children.props)}),[]),u.useEffect(()=>(e.pauseOnFocusLoss&&(document.hasFocus()||v(),window.addEventListener("focus",h),window.addEventListener("blur",v)),()=>{e.pauseOnFocusLoss&&(window.removeEventListener("focus",h),window.removeEventListener("blur",v))}),[e.pauseOnFocusLoss]);const b={onMouseDown:m,onTouchStart:m,onMouseUp:g,onTouchEnd:g};return l&&c&&(b.onMouseEnter=v,b.onMouseLeave=h),p&&(b.onClick=e=>{f&&f(e),r.canCloseOnClick&&d()}),{playToast:h,pauseToast:v,isRunning:t,preventExitTransition:o,toastRef:a,eventHandlers:b}}(e),{closeButton:a,children:r,autoClose:c,onClick:f,type:m,hideProgressBar:g,closeToast:h,transition:v,position:E,className:T,style:b,bodyClassName:O,bodyStyle:C,progressClassName:w,progressStyle:R,updateId:x,role:P,progress:M,rtl:k,toastId:$,deleteToast:z,isIn:B,isLoading:D,iconOut:A,closeOnClick:j,theme:S}=e,F=p("Toastify__toast",`Toastify__toast-theme--${S}`,`Toastify__toast--${m}`,{"Toastify__toast--rtl":k},{"Toastify__toast--close-on-click":j}),H=y(T)?T({rtl:k,position:E,type:m,defaultClassName:F}):p(F,T),q=!!M||!c,Q={closeToast:h,type:m,theme:S};let V=null;return!1===a||(V=y(a)?a(Q):u.isValidElement(a)?u.cloneElement(a,Q):L(Q)),d.createElement(v,{isIn:B,done:z,position:E,preventExitTransition:n,nodeRef:o},d.createElement("div",l(i({id:$,onClick:f,className:H},s),{style:b,ref:o}),d.createElement("div",l(i({},B&&{role:P}),{className:y(O)?O({type:m}):p("Toastify__toast-body",O),style:C}),null!=A&&d.createElement("div",{className:p("Toastify__toast-icon",{"Toastify--animate-icon Toastify__zoom-enter":!D})},A),d.createElement("div",null,r)),V,d.createElement(N,l(i({},x&&!q?{key:`pb-${x}`}:{}),{rtl:k,theme:S,delay:c,isRunning:t,isIn:B,closeToast:h,hide:g,type:m,style:R,className:w,controlledProgress:q,progress:M||0}))))},R=function(e,t){return void 0===t&&(t=!1),{enter:`Toastify--animate Toastify__${e}-enter`,exit:`Toastify--animate Toastify__${e}-exit`,appendPosition:t}},x=E(R("bounce",!0));E(R("slide",!0)),E(R("zoom")),E(R("flip"));const P=u.forwardRef((e,t)=>{const{getToastToRender:n,containerRef:o,isToastActive:s}=function(e){const[,t]=u.useReducer(e=>e+1,0),[n,o]=u.useState([]),s=u.useRef(null),a=u.useRef(new Map).current,r=e=>-1!==n.indexOf(e),d=u.useRef({toastKey:1,displayedToast:0,count:0,queue:[],props:e,containerId:null,isToastActive:r,getToast:e=>a.get(e)}).current;function f(e){let{containerId:t}=e;const{limit:n}=d.props;!n||t&&d.containerId!==t||(d.count-=d.queue.length,d.queue=[])}function p(e){o(t=>null==e?[]:t.filter(t=>t!==e))}function E(){const{toastContent:e,toastProps:t,staleId:n}=d.queue.shift();I(e,t,n)}function O(e,n){let o=n,{delay:r,staleId:f}=o,O=c(o,["delay","staleId"]);if(!v(e)||(_=O,!s.current||d.props.enableMultiContainer&&_.containerId!==d.props.containerId||a.has(_.toastId)&&null==_.updateId))return;var _;const{toastId:L,updateId:N,data:w}=O,{props:R}=d,x=()=>p(L),P=null==N;P&&d.count++;const M=l(i(l(i({},R),{style:R.toastStyle,key:d.toastKey++}),Object.fromEntries(Object.entries(O).filter(e=>{let[t,n]=e;return null!=n}))),{toastId:L,updateId:N,data:w,closeToast:x,isIn:!1,className:h(O.className||R.toastClassName),bodyClassName:h(O.bodyClassName||R.bodyClassName),progressClassName:h(O.progressClassName||R.progressClassName),autoClose:!O.isLoading&&(k=O.autoClose,$=R.autoClose,!1===k||m(k)&&k>0?k:$),deleteToast(){const e=T(a.get(L),"removed");a.delete(L),b.emit(4,e);const n=d.queue.length;if(d.count=null==L?d.count-d.displayedToast:d.count-1,d.count<0&&(d.count=0),n>0){const e=null==L?d.props.limit:1;if(1===n||1===e)d.displayedToast++,E();else{const t=e>n?n:e;d.displayedToast=t;for(let e=0;e<t;e++)E()}}else t()}});var k,$;M.iconOut=function(e){let{theme:t,type:n,isLoading:o,icon:s}=e,a=null;const r={theme:t,type:n};return!1===s||(y(s)?a=s(r):u.isValidElement(s)?a=u.cloneElement(s,r):g(s)||m(s)?a=s:o?a=C.spinner():n in C&&(a=C[n](r))),a}(M),y(O.onOpen)&&(M.onOpen=O.onOpen),y(O.onClose)&&(M.onClose=O.onClose),M.closeButton=R.closeButton,!1===O.closeButton||v(O.closeButton)?M.closeButton=O.closeButton:!0===O.closeButton&&(M.closeButton=!v(R.closeButton)||R.closeButton);let z=e;u.isValidElement(e)&&!g(e.type)?z=u.cloneElement(e,{closeToast:x,toastProps:M,data:w}):y(e)&&(z=e({closeToast:x,toastProps:M,data:w})),R.limit&&R.limit>0&&d.count>R.limit&&P?d.queue.push({toastContent:z,toastProps:M,staleId:f}):m(r)?setTimeout(()=>{I(z,M,f)},r):I(z,M,f)}function I(e,t,n){const{toastId:s}=t;n&&a.delete(n);const r={content:e,props:t};a.set(s,r),o(e=>[...e,s].filter(e=>e!==n)),b.emit(4,T(r,null==r.props.updateId?"added":"updated"))}return u.useEffect(()=>(d.containerId=e.containerId,b.cancelEmit(3).on(0,O).on(1,e=>s.current&&p(e)).on(5,f).emit(2,d),()=>{a.clear(),b.emit(3,d)}),[]),u.useEffect(()=>{d.props=e,d.isToastActive=r,d.displayedToast=n.length}),{getToastToRender:function(t){const n=new Map,o=Array.from(a.values());return e.newestOnTop&&o.reverse(),o.forEach(e=>{const{position:t}=e.props;n.has(t)||n.set(t,[]),n.get(t).push(e)}),Array.from(n,e=>t(e[0],e[1]))},containerRef:s,isToastActive:r}}(e),{className:a,style:r,rtl:f,containerId:E}=e;function O(e){const t=p("Toastify__toast-container",`Toastify__toast-container--${e}`,{"Toastify__toast-container--rtl":f});return y(a)?a({position:e,rtl:f,defaultClassName:t}):p(t,h(a))}return u.useEffect(()=>{t&&(t.current=o.current)},[]),d.createElement("div",{ref:o,className:"Toastify",id:E},n((e,t)=>{const n=t.length?i({},r):l(i({},r),{pointerEvents:"none"});return d.createElement("div",{className:O(e),style:n,key:`container-${e}`},t.map((e,n)=>{let{content:o,props:a}=e;return d.createElement(w,l(i({},a),{isIn:s(a.toastId),style:l(i({},a.style),{"--nth":n+1,"--len":t.length}),key:`toast-${a.key}`}),o)}))}))});P.displayName="ToastContainer",P.defaultProps={position:"top-right",transition:x,autoClose:5e3,closeButton:L,pauseOnHover:!0,pauseOnFocusLoss:!0,closeOnClick:!0,draggable:!0,draggablePercent:80,draggableDirection:"x",role:"alert",theme:"light"};let M,k=new Map,$=[],z=1;function B(){return""+z++}function D(e){return e&&(g(e.toastId)||m(e.toastId))?e.toastId:B()}function A(e,t){return k.size>0?b.emit(0,e,t):$.push({content:e,options:t}),t.toastId}function j(e,t){return l(i({},t),{type:t&&t.type||e,toastId:D(t)})}function S(e){return(t,n)=>A(t,j(e,n))}function F(e,t){return A(e,j("default",t))}F.loading=(e,t)=>A(e,j("default",i({isLoading:!0,autoClose:!1,closeOnClick:!1,closeButton:!1,draggable:!1},t))),F.promise=function(e,t,n){let o,{pending:s,error:a,success:r}=t;s&&(o=g(s)?F.loading(s,n):F.loading(s.render,i(i({},n),s)));const c={isLoading:null,autoClose:null,closeOnClick:null,closeButton:null,draggable:null},u=(e,t,s)=>{if(null==t)return void F.dismiss(o);const a=l(i(i({type:e},c),n),{data:s}),r=g(t)?{render:t}:t;return o?F.update(o,i(i({},a),r)):F(r.render,i(i({},a),r)),s},d=y(e)?e():e;return d.then(e=>u("success",r,e)).catch(e=>u("error",a,e)),d},F.success=S("success"),F.info=S("info"),F.error=S("error"),F.warning=S("warning"),F.warn=F.warning,F.dark=(e,t)=>A(e,j("default",i({theme:"dark"},t))),F.dismiss=e=>{k.size>0?b.emit(1,e):$=$.filter(t=>null!=e&&t.options.toastId!==e)},F.clearWaitingQueue=function(e){return void 0===e&&(e={}),b.emit(5,e)},F.isActive=e=>{let t=!1;return k.forEach(n=>{n.isToastActive&&n.isToastActive(e)&&(t=!0)}),t},F.update=function(e,t){void 0===t&&(t={}),setTimeout(()=>{const n=function(e,t){let{containerId:n}=t;const o=k.get(n||M);return o&&o.getToast(e)}(e,t);if(n){const{props:o,content:s}=n,a=l(i(i({delay:100},o),t),{toastId:t.toastId||e,updateId:B()});a.toastId!==e&&(a.staleId=e);const r=a.render||s;delete a.render,A(r,a)}},0)},F.done=e=>{F.update(e,{progress:1})},F.onChange=e=>(b.on(4,e),()=>{b.off(4,e)}),F.POSITION={TOP_LEFT:"top-left",TOP_RIGHT:"top-right",TOP_CENTER:"top-center",BOTTOM_LEFT:"bottom-left",BOTTOM_RIGHT:"bottom-right",BOTTOM_CENTER:"bottom-center"},F.TYPE={INFO:"info",SUCCESS:"success",WARNING:"warning",ERROR:"error",DEFAULT:"default"},b.on(2,e=>{M=e.containerId||e,k.set(M,e),$.forEach(e=>{b.emit(0,e.content,e.options)}),$=[]}).on(3,e=>{k.delete(e.containerId||e),0===k.size&&b.off(0).off(1).off(5)});var H={color:void 0,size:void 0,className:void 0,style:void 0,attr:void 0},q=d.createContext&&d.createContext(H),Q=globalThis&&globalThis.__assign||function(){return Q=Object.assign||function(e){for(var t,n=1,o=arguments.length;n<o;n++)for(var s in t=arguments[n])Object.prototype.hasOwnProperty.call(t,s)&&(e[s]=t[s]);return e},Q.apply(this,arguments)},V=globalThis&&globalThis.__rest||function(e,t){var n={};for(var o in e)Object.prototype.hasOwnProperty.call(e,o)&&t.indexOf(o)<0&&(n[o]=e[o]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols){var s=0;for(o=Object.getOwnPropertySymbols(e);s<o.length;s++)t.indexOf(o[s])<0&&Object.prototype.propertyIsEnumerable.call(e,o[s])&&(n[o[s]]=e[o[s]])}return n};function G(e){return e&&e.map(function(e,t){return d.createElement(e.tag,Q({key:t},e.attr),G(e.child))})}function W(e){return function(t){return d.createElement(U,Q({attr:Q({},e.attr)},t),G(e.child))}}function U(e){var t=function(t){var n,o=e.attr,s=e.size,a=e.title,r=V(e,["attr","size","title"]),i=s||t.size||"1em";return t.className&&(n=t.className),e.className&&(n=(n?n+" ":"")+e.className),d.createElement("svg",Q({stroke:"currentColor",fill:"currentColor",strokeWidth:"0"},t.attr,o,r,{className:n,style:Q(Q({color:e.color||t.color},t.style),e.style),height:i,width:i,xmlns:"http://www.w3.org/2000/svg"}),a&&d.createElement("title",null,a),e.children)};return void 0!==q?d.createElement(q.Consumer,null,function(e){return t(e)}):t(H)}export{W as G,F as Q,P as k};
