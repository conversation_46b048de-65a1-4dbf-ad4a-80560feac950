/**
 * Dynamic wrapper for ExportService to avoid 500 errors during import
 */
const DynamicExportService = {
  /**
   * Export data to PDF
   * @param {Object} data - Data to export
   * @param {Object} options - Export options
   */
  async exportToPDF(data, options = {}) {
    try {
      // Dynamically import the service
      const module = await import('./src/services/exportService.js');
      return module.default.exportToPDF(data, options);
    } catch (error) {
      console.error('Error loading ExportService for PDF export:', error);
      // Fallback behavior
      console.log('Would export data to PDF:', data);
      alert('PDF export is currently unavailable. Please try again later.');
      return false;
    }
  },

  /**
   * Export data to Excel
   * @param {Object} data - Data to export
   * @param {Object} options - Export options
   */
  async exportToExcel(data, options = {}) {
    try {
      // Dynamically import the service
      const module = await import('./src/services/exportService.js');
      return module.default.exportToExcel(data, options);
    } catch (error) {
      console.error('Error loading ExportService for Excel export:', error);
      // Fallback behavior
      console.log('Would export data to Excel:', data);
      alert('Excel export is currently unavailable. Please try again later.');
      return false;
    }
  },

  /**
   * Export data to PowerPoint (simulated)
   * @param {Object} data - Data to export
   * @param {Object} options - Export options
   */
  async exportToPowerPoint(data, options = {}) {
    try {
      // Dynamically import the service
      const module = await import('./src/services/exportService.js');
      return module.default.exportToPowerPoint(data, options);
    } catch (error) {
      console.error('Error loading ExportService for PowerPoint export:', error);
      // Fallback behavior
      console.log('Would export data to PowerPoint:', data);
      alert('PowerPoint export is currently unavailable. Please try again later.');
      return false;
    }
  },

  /**
   * Send report by email (simulated)
   * @param {Object} data - Data to send
   * @param {Object} options - Email options
   */
  async sendByEmail(data, options = {}) {
    try {
      // Dynamically import the service
      const module = await import('./src/services/exportService.js');
      return module.default.sendByEmail(data, options);
    } catch (error) {
      console.error('Error loading ExportService for email:', error);
      // Fallback behavior
      console.log('Would send data by email:', data);
      alert('Email functionality is currently unavailable. Please try again later.');
      return false;
    }
  }
};

export default DynamicExportService;