{"name": "sistema-gestion-psicologica", "private": true, "version": "0.1.0", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "preview": "vite preview", "test": "vitest", "test:ui": "vitest --ui", "test:run": "vitest run", "test:coverage": "vitest run --coverage", "test:watch": "vitest --watch", "cypress:open": "cypress open", "cypress:run": "cypress run", "cypress:run:chrome": "cypress run --browser chrome", "cypress:run:firefox": "cypress run --browser firefox", "e2e": "start-server-and-test dev http://localhost:5173 cypress:run", "e2e:open": "start-server-and-test dev http://localhost:5173 cypress:open", "test:all": "npm run test:run && npm run e2e"}, "dependencies": {"@reduxjs/toolkit": "^2.8.0", "@supabase/supabase-js": "^2.33.1", "chart.js": "^4.5.0", "crypto-js": "^4.2.0", "date-fns": "^4.1.0", "dotenv": "^17.2.0", "file-saver": "^2.0.5", "framer-motion": "^12.23.6", "html2canvas": "^1.4.1", "jspdf": "^3.0.1", "jspdf-autotable": "^5.0.2", "jspdf-html2canvas": "^1.5.2", "lodash": "^4.17.21", "motion": "^12.23.6", "pg": "^8.16.3", "prop-types": "^15.8.1", "qrcode": "^1.5.4", "react": "^18.2.0", "react-chartjs-2": "^5.3.0", "react-dom": "^18.2.0", "react-error-boundary": "^6.0.0", "react-icons": "^4.10.1", "react-redux": "^9.2.0", "react-router-dom": "^6.15.0", "react-toastify": "^9.1.3", "recharts": "^2.15.4", "xlsx": "^0.18.5", "yup": "^1.6.1"}, "devDependencies": {"@cypress/react": "^9.0.1", "@cypress/vite-dev-server": "^6.0.3", "@testing-library/jest-dom": "^6.6.3", "@testing-library/react": "^16.3.0", "@testing-library/user-event": "^14.6.1", "@types/file-saver": "^2.0.7", "@types/lodash": "^4.17.20", "@types/react": "^18.2.15", "@types/react-dom": "^18.2.7", "@vitejs/plugin-react": "^4.0.3", "@vitest/coverage-v8": "^3.2.4", "@vitest/ui": "^3.2.4", "autoprefixer": "^10.4.15", "cypress": "^14.5.1", "jsdom": "^26.1.0", "msw": "^2.10.4", "postcss": "^8.4.28", "start-server-and-test": "^2.0.12", "tailwindcss": "^3.3.3", "terser": "^5.43.1", "vite": "^4.4.5", "vitest": "^3.2.4"}}