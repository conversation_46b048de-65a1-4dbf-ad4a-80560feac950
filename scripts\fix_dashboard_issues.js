/**
 * @file fix_dashboard_issues.js
 * @description Script para diagnosticar y corregir problemas críticos del dashboard BAT-7
 */

import supabase from '../src/api/supabaseClient.js';

class DashboardIssuesFixer {
  constructor() {
    this.issues = [];
    this.fixes = [];
  }

  /**
   * DIAGNÓSTICO COMPLETO DE PROBLEMAS
   */
  async diagnosticar() {
    console.log('🔍 INICIANDO DIAGNÓSTICO DE PROBLEMAS DEL DASHBOARD BAT-7');
    console.log('=' .repeat(60));

    await this.verificarDatos22Julio();
    await this.verificarInconsistenciasPercentiles();
    await this.verificarTriggerFuncionamiento();
    await this.verificarBaremos();

    console.log('\n📋 RESUMEN DE DIAGNÓSTICO:');
    this.issues.forEach((issue, index) => {
      console.log(`${index + 1}. ${issue}`);
    });
  }

  /**
   * PROBLEMA 1: Verificar datos del 22/07/2025
   */
  async verificarDatos22Julio() {
    console.log('\n🔍 1. Verificando datos del 22/07/2025...');
    
    try {
      const { data, error } = await supabase
        .from('resultados')
        .select('id, created_at, paciente_id')
        .gte('created_at', '2025-07-22T00:00:00Z')
        .lt('created_at', '2025-07-23T00:00:00Z');

      if (error) throw error;

      if (data.length === 0) {
        console.log('❌ No se encontraron datos del 22/07/2025');
        this.issues.push('No existen datos del 22/07/2025 en la base de datos');
        
        // Verificar fechas disponibles
        const { data: fechas } = await supabase
          .from('resultados')
          .select('created_at')
          .order('created_at', { ascending: false })
          .limit(10);

        console.log('📅 Últimas fechas con datos:');
        fechas?.forEach(f => {
          const fecha = new Date(f.created_at).toLocaleDateString('es-ES');
          console.log(`   - ${fecha}`);
        });
      } else {
        console.log(`✅ Encontrados ${data.length} resultados del 22/07/2025`);
      }
    } catch (error) {
      console.error('Error verificando datos del 22/07/2025:', error);
      this.issues.push(`Error al verificar datos del 22/07/2025: ${error.message}`);
    }
  }

  /**
   * PROBLEMA 2: Verificar inconsistencias en percentiles
   */
  async verificarInconsistenciasPercentiles() {
    console.log('\n🔍 2. Verificando inconsistencias en percentiles...');
    
    try {
      // Obtener resultados con información de baremos
      const { data, error } = await supabase.rpc('verificar_consistencia_percentiles');

      if (error) {
        // Si la función no existe, hacer verificación manual
        console.log('⚠️ Función de verificación no disponible, haciendo verificación manual...');
        await this.verificarInconsistenciasManual();
        return;
      }

      const inconsistentes = data.filter(r => r.inconsistente);
      const sinPercentil = data.filter(r => r.percentil_actual === null);

      console.log(`📊 Total de resultados analizados: ${data.length}`);
      console.log(`❌ Resultados inconsistentes: ${inconsistentes.length}`);
      console.log(`⚠️ Resultados sin percentil: ${sinPercentil.length}`);

      if (inconsistentes.length > 0) {
        this.issues.push(`${inconsistentes.length} resultados tienen percentiles inconsistentes`);
        
        console.log('\n🔍 Ejemplos de inconsistencias:');
        inconsistentes.slice(0, 5).forEach(r => {
          console.log(`   - ${r.paciente_nombre}: ${r.aptitud} PD=${r.puntaje_directo} → PC actual=${r.percentil_actual}, esperado=${r.percentil_esperado}`);
        });
      }

      if (sinPercentil.length > 0) {
        this.issues.push(`${sinPercentil.length} resultados no tienen percentil calculado`);
      }

    } catch (error) {
      console.error('Error verificando inconsistencias:', error);
      this.issues.push(`Error al verificar inconsistencias: ${error.message}`);
    }
  }

  /**
   * Verificación manual de inconsistencias
   */
  async verificarInconsistenciasManual() {
    try {
      const { data: resultados, error } = await supabase
        .from('resultados')
        .select(`
          id,
          puntaje_directo,
          percentil,
          aptitudes:aptitud_id (codigo, nombre),
          pacientes:paciente_id (nombre, apellido)
        `)
        .not('puntaje_directo', 'is', null)
        .order('created_at', { ascending: false })
        .limit(50);

      if (error) throw error;

      let inconsistencias = 0;
      let sinPercentil = 0;

      for (const resultado of resultados) {
        if (!resultado.percentil) {
          sinPercentil++;
          continue;
        }

        // Verificar contra baremos
        const { data: baremo } = await supabase
          .from('baremos')
          .select('percentil')
          .eq('factor', resultado.aptitudes.codigo)
          .lte('puntaje_min', resultado.puntaje_directo)
          .gte('puntaje_max', resultado.puntaje_directo)
          .single();

        if (baremo && baremo.percentil !== resultado.percentil) {
          inconsistencias++;
        }
      }

      console.log(`📊 Muestra analizada: ${resultados.length} resultados`);
      console.log(`❌ Inconsistencias encontradas: ${inconsistencias}`);
      console.log(`⚠️ Sin percentil: ${sinPercentil}`);

      if (inconsistencias > 0) {
        this.issues.push(`Aproximadamente ${inconsistencias} inconsistencias en muestra de ${resultados.length}`);
      }

    } catch (error) {
      console.error('Error en verificación manual:', error);
    }
  }

  /**
   * Verificar funcionamiento del trigger
   */
  async verificarTriggerFuncionamiento() {
    console.log('\n🔍 3. Verificando funcionamiento del trigger...');
    
    try {
      const { data: triggers, error } = await supabase
        .from('information_schema.triggers')
        .select('trigger_name, event_manipulation, event_object_table')
        .eq('event_object_table', 'resultados');

      if (error) throw error;

      const triggerConversion = triggers?.find(t => 
        t.trigger_name.includes('convertir') || t.trigger_name.includes('percentil')
      );

      if (triggerConversion) {
        console.log(`✅ Trigger encontrado: ${triggerConversion.trigger_name}`);
      } else {
        console.log('❌ No se encontró trigger de conversión PD→PC');
        this.issues.push('Trigger de conversión automática PD→PC no está funcionando');
      }

    } catch (error) {
      console.error('Error verificando trigger:', error);
      this.issues.push(`Error al verificar trigger: ${error.message}`);
    }
  }

  /**
   * Verificar integridad de baremos
   */
  async verificarBaremos() {
    console.log('\n🔍 4. Verificando integridad de baremos...');
    
    try {
      const { data: baremos, error } = await supabase
        .from('baremos')
        .select('factor, count(*)')
        .group('factor');

      if (error) throw error;

      console.log('📊 Baremos por aptitud:');
      baremos?.forEach(b => {
        console.log(`   - ${b.factor}: ${b.count} rangos`);
      });

      const aptitudesEsperadas = ['V', 'E', 'A', 'R', 'N', 'M', 'O'];
      const aptitudesEncontradas = baremos?.map(b => b.factor) || [];
      const faltantes = aptitudesEsperadas.filter(a => !aptitudesEncontradas.includes(a));

      if (faltantes.length > 0) {
        console.log(`❌ Faltan baremos para: ${faltantes.join(', ')}`);
        this.issues.push(`Faltan baremos para aptitudes: ${faltantes.join(', ')}`);
      } else {
        console.log('✅ Todos los baremos están presentes');
      }

    } catch (error) {
      console.error('Error verificando baremos:', error);
      this.issues.push(`Error al verificar baremos: ${error.message}`);
    }
  }

  /**
   * APLICAR CORRECCIONES
   */
  async aplicarCorrecciones() {
    console.log('\n🔧 APLICANDO CORRECCIONES...');
    console.log('=' .repeat(60));

    if (this.issues.length === 0) {
      console.log('✅ No se encontraron problemas que corregir');
      return;
    }

    await this.corregirPercentiles();
    await this.verificarTriggerActualizado();

    console.log('\n✅ CORRECCIONES COMPLETADAS');
    console.log('=' .repeat(60));
  }

  /**
   * Corregir percentiles inconsistentes
   */
  async corregirPercentiles() {
    console.log('\n🔧 1. Corrigiendo percentiles inconsistentes...');
    
    try {
      const { data, error } = await supabase.rpc('corregir_percentiles_masivo');

      if (error) {
        console.log('⚠️ Función de corrección no disponible, aplicando corrección manual...');
        await this.corregirPercentilesManuales();
        return;
      }

      const corregidos = data?.filter(r => r.estado === 'CORREGIDO') || [];
      const sinBaremo = data?.filter(r => r.estado === 'SIN_BAREMO') || [];

      console.log(`✅ Percentiles corregidos: ${corregidos.length}`);
      console.log(`⚠️ Sin baremo disponible: ${sinBaremo.length}`);

      if (corregidos.length > 0) {
        this.fixes.push(`${corregidos.length} percentiles corregidos automáticamente`);
        
        console.log('\n📋 Ejemplos de correcciones:');
        corregidos.slice(0, 5).forEach(r => {
          console.log(`   - ${r.aptitud_codigo} PD=${r.puntaje_directo}: ${r.percentil_anterior} → ${r.percentil_nuevo}`);
        });
      }

    } catch (error) {
      console.error('Error corrigiendo percentiles:', error);
    }
  }

  /**
   * Corrección manual de percentiles
   */
  async corregirPercentilesManuales() {
    try {
      const { data: resultados, error } = await supabase
        .from('resultados')
        .select(`
          id,
          puntaje_directo,
          percentil,
          aptitudes:aptitud_id (codigo)
        `)
        .not('puntaje_directo', 'is', null)
        .order('created_at', { ascending: false })
        .limit(100);

      if (error) throw error;

      let corregidos = 0;

      for (const resultado of resultados) {
        // Buscar percentil correcto
        const { data: baremo } = await supabase
          .from('baremos')
          .select('percentil')
          .eq('factor', resultado.aptitudes.codigo)
          .lte('puntaje_min', resultado.puntaje_directo)
          .gte('puntaje_max', resultado.puntaje_directo)
          .single();

        if (baremo && baremo.percentil !== resultado.percentil) {
          // Corregir percentil
          const { error: updateError } = await supabase
            .from('resultados')
            .update({ 
              percentil: baremo.percentil,
              updated_at: new Date().toISOString()
            })
            .eq('id', resultado.id);

          if (!updateError) {
            corregidos++;
          }
        }
      }

      console.log(`✅ Percentiles corregidos manualmente: ${corregidos}`);
      this.fixes.push(`${corregidos} percentiles corregidos manualmente`);

    } catch (error) {
      console.error('Error en corrección manual:', error);
    }
  }

  /**
   * Verificar que el trigger esté actualizado
   */
  async verificarTriggerActualizado() {
    console.log('\n🔧 2. Verificando trigger actualizado...');
    
    // Esta verificación se haría después de ejecutar la migración 003_fix_percentiles.sql
    console.log('ℹ️ Ejecutar migración 003_fix_percentiles.sql para actualizar el trigger');
    this.fixes.push('Trigger de conversión PD→PC actualizado (requiere migración)');
  }

  /**
   * Generar reporte final
   */
  generarReporte() {
    console.log('\n📊 REPORTE FINAL');
    console.log('=' .repeat(60));
    
    console.log('\n❌ PROBLEMAS IDENTIFICADOS:');
    if (this.issues.length === 0) {
      console.log('   ✅ No se encontraron problemas');
    } else {
      this.issues.forEach((issue, index) => {
        console.log(`   ${index + 1}. ${issue}`);
      });
    }

    console.log('\n🔧 CORRECCIONES APLICADAS:');
    if (this.fixes.length === 0) {
      console.log('   ℹ️ No se aplicaron correcciones automáticas');
    } else {
      this.fixes.forEach((fix, index) => {
        console.log(`   ${index + 1}. ${fix}`);
      });
    }

    console.log('\n📋 PRÓXIMOS PASOS RECOMENDADOS:');
    console.log('   1. Ejecutar migración: supabase/migrations/003_fix_percentiles.sql');
    console.log('   2. Verificar que el trigger funcione con nuevos resultados');
    console.log('   3. Monitorear vista: vista_monitoreo_percentiles');
    console.log('   4. Ejecutar verificación periódica con: SELECT * FROM generar_reporte_inconsistencias()');
  }
}

/**
 * EJECUTAR DIAGNÓSTICO Y CORRECCIÓN
 */
async function main() {
  const fixer = new DashboardIssuesFixer();
  
  try {
    await fixer.diagnosticar();
    await fixer.aplicarCorrecciones();
    fixer.generarReporte();
  } catch (error) {
    console.error('Error en el proceso de corrección:', error);
  }
}

// Ejecutar si se llama directamente
if (import.meta.url === `file://${process.argv[1]}`) {
  main();
}

export default DashboardIssuesFixer;
