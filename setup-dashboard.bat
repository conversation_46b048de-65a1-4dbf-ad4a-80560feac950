@echo off
echo.
echo ========================================
echo   CONFIGURACION DASHBOARD BAT-7
echo ========================================
echo.

echo [1/4] Instalando dependencias...
call npm install chart.js react-chartjs-2 jspdf html2canvas crypto-js qrcode
if %errorlevel% neq 0 (
    echo ERROR: Fallo en la instalacion de dependencias
    pause
    exit /b 1
)
echo ✅ Dependencias instaladas correctamente
echo.

echo [2/4] Activando componentes de graficos...
node activate-dashboard.js
if %errorlevel% neq 0 (
    echo ERROR: Fallo activando componentes
    pause
    exit /b 1
)
echo ✅ Componentes activados correctamente
echo.

echo [3/4] Verificando estructura de archivos...
if not exist "src\hooks\useEnhancedDashboardData.js" (
    echo ⚠️  Hook mejorado no encontrado, usando version basica
) else (
    echo ✅ Hook mejorado disponible
)

if not exist "src\components\dashboard\views\EnhancedIndividualView.jsx" (
    echo ⚠️  Vista individual mejorada no encontrada, usando version basica
) else (
    echo ✅ Vista individual mejorada disponible
)
echo.

echo [4/4] Iniciando servidor de desarrollo...
echo.
echo ========================================
echo   DASHBOARD BAT-7 LISTO!
echo ========================================
echo.
echo 📊 Funcionalidades activadas:
echo   ✅ Graficos interactivos
echo   ✅ Exportacion PDF
echo   ✅ Firmas digitales
echo   ✅ Conexion de datos mejorada
echo.
echo 🌐 Abriendo en: http://localhost:3011/admin/dashboard
echo.
echo Presiona Ctrl+C para detener el servidor
echo.

start http://localhost:3011/admin/dashboard
call npm run dev
