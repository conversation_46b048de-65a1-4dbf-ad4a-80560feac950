import React, { Suspense } from 'react';
import { BrowserRouter as Router, Route, Routes, Navigate } from 'react-router-dom';
import NewLayout from './components/layout/NewLayout';
import AdminRoutes from './routes/AdminRoutes';

const Loading = () => <div>Loading...</div>;

function NewApp() {
  return (
    <Router>
      <Suspense fallback={<Loading />}>
        <Routes>
          <Route path="/admin" element={<NewLayout />}>
            <Route path="/*" element={<AdminRoutes />} />
          </Route>
          <Route path="*" element={<Navigate to="/admin/dashboard" replace />} />
        </Routes>
      </Suspense>
    </Router>
  );
}

export default NewApp;