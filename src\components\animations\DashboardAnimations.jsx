import React, { useState, useEffect } from 'react';
import { motion, AnimatePresence, useSpring, useTransform } from 'framer-motion';

/**
 * Variantes de animación para diferentes componentes
 */
export const animationVariants = {
  // Animaciones de entrada para vistas
  viewTransition: {
    initial: { opacity: 0, y: 20, scale: 0.95 },
    animate: { 
      opacity: 1, 
      y: 0, 
      scale: 1,
      transition: {
        duration: 0.4,
        ease: [0.25, 0.46, 0.45, 0.94]
      }
    },
    exit: { 
      opacity: 0, 
      y: -20, 
      scale: 0.95,
      transition: {
        duration: 0.3,
        ease: [0.25, 0.46, 0.45, 0.94]
      }
    }
  },

  // Animaciones para tarjetas
  cardHover: {
    rest: { scale: 1, y: 0 },
    hover: { 
      scale: 1.02, 
      y: -4,
      transition: {
        duration: 0.2,
        ease: "easeOut"
      }
    }
  },

  // Animaciones para gráficos
  chartEntry: {
    initial: { opacity: 0, scale: 0.8 },
    animate: { 
      opacity: 1, 
      scale: 1,
      transition: {
        duration: 0.6,
        ease: "easeOut",
        delay: 0.2
      }
    }
  },

  // Animaciones para listas
  listItem: {
    initial: { opacity: 0, x: -20 },
    animate: (index) => ({
      opacity: 1,
      x: 0,
      transition: {
        duration: 0.3,
        delay: index * 0.1,
        ease: "easeOut"
      }
    })
  },

  // Animaciones para modales
  modal: {
    initial: { opacity: 0, scale: 0.9 },
    animate: { 
      opacity: 1, 
      scale: 1,
      transition: {
        duration: 0.3,
        ease: "easeOut"
      }
    },
    exit: { 
      opacity: 0, 
      scale: 0.9,
      transition: {
        duration: 0.2,
        ease: "easeIn"
      }
    }
  },

  // Animaciones para notificaciones
  notification: {
    initial: { opacity: 0, y: -50, scale: 0.9 },
    animate: { 
      opacity: 1, 
      y: 0, 
      scale: 1,
      transition: {
        duration: 0.4,
        ease: [0.25, 0.46, 0.45, 0.94]
      }
    },
    exit: { 
      opacity: 0, 
      x: 300,
      transition: {
        duration: 0.3,
        ease: "easeIn"
      }
    }
  }
};

/**
 * Componente de vista animada
 */
export const AnimatedView = ({ children, className = "" }) => (
  <motion.div
    className={className}
    variants={animationVariants.viewTransition}
    initial="initial"
    animate="animate"
    exit="exit"
  >
    {children}
  </motion.div>
);

/**
 * Componente de tarjeta con hover animado
 */
export const AnimatedCard = ({ children, className = "", onClick }) => (
  <motion.div
    className={`cursor-pointer ${className}`}
    variants={animationVariants.cardHover}
    initial="rest"
    whileHover="hover"
    whileTap={{ scale: 0.98 }}
    onClick={onClick}
  >
    {children}
  </motion.div>
);

/**
 * Componente de gráfico con animación de entrada
 */
export const AnimatedChart = ({ children, className = "" }) => (
  <motion.div
    className={className}
    variants={animationVariants.chartEntry}
    initial="initial"
    animate="animate"
  >
    {children}
  </motion.div>
);

/**
 * Lista animada con stagger
 */
export const AnimatedList = ({ items, renderItem, className = "" }) => (
  <motion.div className={className}>
    {items.map((item, index) => (
      <motion.div
        key={item.id || index}
        variants={animationVariants.listItem}
        initial="initial"
        animate="animate"
        custom={index}
      >
        {renderItem(item, index)}
      </motion.div>
    ))}
  </motion.div>
);

/**
 * Modal animado
 */
export const AnimatedModal = ({ isOpen, onClose, children, className = "" }) => (
  <AnimatePresence>
    {isOpen && (
      <>
        {/* Backdrop */}
        <motion.div
          className="fixed inset-0 bg-black bg-opacity-50 z-40"
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          exit={{ opacity: 0 }}
          onClick={onClose}
        />
        
        {/* Modal content */}
        <motion.div
          className={`fixed inset-0 z-50 flex items-center justify-center p-4 ${className}`}
          variants={animationVariants.modal}
          initial="initial"
          animate="animate"
          exit="exit"
        >
          {children}
        </motion.div>
      </>
    )}
  </AnimatePresence>
);

/**
 * Contador animado
 */
export const AnimatedCounter = ({ 
  value, 
  duration = 1000, 
  className = "",
  prefix = "",
  suffix = "",
  decimals = 0
}) => {
  const [displayValue, setDisplayValue] = useState(0);

  useEffect(() => {
    let startTime;
    let animationFrame;

    const animate = (timestamp) => {
      if (!startTime) startTime = timestamp;
      const progress = Math.min((timestamp - startTime) / duration, 1);
      
      // Easing function (ease-out)
      const easeOut = 1 - Math.pow(1 - progress, 3);
      const currentValue = easeOut * value;
      
      setDisplayValue(currentValue);

      if (progress < 1) {
        animationFrame = requestAnimationFrame(animate);
      }
    };

    animationFrame = requestAnimationFrame(animate);

    return () => {
      if (animationFrame) {
        cancelAnimationFrame(animationFrame);
      }
    };
  }, [value, duration]);

  return (
    <span className={className}>
      {prefix}{displayValue.toFixed(decimals)}{suffix}
    </span>
  );
};

/**
 * Barra de progreso animada
 */
export const AnimatedProgressBar = ({ 
  progress, 
  className = "",
  barClassName = "",
  duration = 1000,
  showPercentage = true
}) => {
  return (
    <div className={`relative ${className}`}>
      <div className="w-full bg-gray-200 rounded-full h-2">
        <motion.div
          className={`h-2 rounded-full ${barClassName}`}
          initial={{ width: 0 }}
          animate={{ width: `${progress}%` }}
          transition={{ duration: duration / 1000, ease: "easeOut" }}
        />
      </div>
      {showPercentage && (
        <div className="absolute right-0 top-3 text-xs text-gray-600">
          <AnimatedCounter value={progress} suffix="%" />
        </div>
      )}
    </div>
  );
};

/**
 * Indicador de carga con pulso
 */
export const PulseLoader = ({ size = "md", color = "blue" }) => {
  const sizeClasses = {
    sm: "w-4 h-4",
    md: "w-8 h-8",
    lg: "w-12 h-12"
  };

  const colorClasses = {
    blue: "bg-blue-500",
    green: "bg-green-500",
    red: "bg-red-500",
    yellow: "bg-yellow-500"
  };

  return (
    <div className="flex space-x-1">
      {[0, 1, 2].map((index) => (
        <motion.div
          key={index}
          className={`${sizeClasses[size]} ${colorClasses[color]} rounded-full`}
          animate={{
            scale: [1, 1.2, 1],
            opacity: [1, 0.5, 1]
          }}
          transition={{
            duration: 1,
            repeat: Infinity,
            delay: index * 0.2
          }}
        />
      ))}
    </div>
  );
};

/**
 * Skeleton loader animado
 */
export const SkeletonLoader = ({ 
  lines = 3, 
  className = "",
  lineClassName = "h-4 bg-gray-200 rounded"
}) => (
  <div className={`animate-pulse ${className}`}>
    {Array.from({ length: lines }).map((_, index) => (
      <motion.div
        key={index}
        className={`${lineClassName} mb-2`}
        style={{ width: `${Math.random() * 40 + 60}%` }}
        animate={{
          opacity: [0.5, 1, 0.5]
        }}
        transition={{
          duration: 1.5,
          repeat: Infinity,
          delay: index * 0.1
        }}
      />
    ))}
  </div>
);

/**
 * Notificación toast animada
 */
export const AnimatedToast = ({ 
  message, 
  type = "info", 
  isVisible, 
  onClose,
  duration = 5000
}) => {
  useEffect(() => {
    if (isVisible && duration > 0) {
      const timer = setTimeout(onClose, duration);
      return () => clearTimeout(timer);
    }
  }, [isVisible, duration, onClose]);

  const typeStyles = {
    success: "bg-green-500 text-white",
    error: "bg-red-500 text-white",
    warning: "bg-yellow-500 text-black",
    info: "bg-blue-500 text-white"
  };

  return (
    <AnimatePresence>
      {isVisible && (
        <motion.div
          className={`fixed top-4 right-4 p-4 rounded-lg shadow-lg z-50 ${typeStyles[type]}`}
          variants={animationVariants.notification}
          initial="initial"
          animate="animate"
          exit="exit"
        >
          <div className="flex items-center justify-between">
            <span>{message}</span>
            <button
              onClick={onClose}
              className="ml-4 text-white hover:text-gray-200"
            >
              ×
            </button>
          </div>
        </motion.div>
      )}
    </AnimatePresence>
  );
};

/**
 * Hook para animaciones de scroll
 */
export const useScrollAnimation = (threshold = 0.1) => {
  const [isVisible, setIsVisible] = useState(false);
  const [ref, setRef] = useState(null);

  useEffect(() => {
    const observer = new IntersectionObserver(
      ([entry]) => {
        setIsVisible(entry.isIntersecting);
      },
      { threshold }
    );

    if (ref) {
      observer.observe(ref);
    }

    return () => {
      if (ref) {
        observer.unobserve(ref);
      }
    };
  }, [ref, threshold]);

  return [setRef, isVisible];
};

/**
 * Componente de fade in al hacer scroll
 */
export const ScrollFadeIn = ({ children, className = "" }) => {
  const [ref, isVisible] = useScrollAnimation();

  return (
    <motion.div
      ref={ref}
      className={className}
      initial={{ opacity: 0, y: 50 }}
      animate={isVisible ? { opacity: 1, y: 0 } : { opacity: 0, y: 50 }}
      transition={{ duration: 0.6, ease: "easeOut" }}
    >
      {children}
    </motion.div>
  );
};

export default {
  AnimatedView,
  AnimatedCard,
  AnimatedChart,
  AnimatedList,
  AnimatedModal,
  AnimatedCounter,
  AnimatedProgressBar,
  PulseLoader,
  SkeletonLoader,
  AnimatedToast,
  ScrollFadeIn,
  useScrollAnimation,
  animationVariants
};
