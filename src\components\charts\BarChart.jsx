import React from 'react';
import { <PERSON><PERSON><PERSON> as Re<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, Bar, XAxis, <PERSON>A<PERSON>s, CartesianGrid, <PERSON>lt<PERSON>, Legend, ResponsiveContainer } from 'recharts';

export const BarChart = ({ data, options = {} }) => {
  // Si no hay datos, mostrar mensaje
  if (!data || !data.datasets || !data.datasets.length) {
    return (
      <div className="flex items-center justify-center h-full">
        <p className="text-gray-500">No hay datos disponibles</p>
      </div>
    );
  }

  // Transformar datos de Chart.js a formato Recharts
  const transformedData = data.labels?.map((label, index) => {
    const item = { name: label };
    data.datasets.forEach((dataset, datasetIndex) => {
      item[dataset.label || `Dataset ${datasetIndex}`] = dataset.data[index];
    });
    return item;
  }) || [];

  // Colores por defecto
  const colors = ['#3B82F6', '#10B981', '#F59E0B', '#EF4444', '#8B5CF6', '#06B6D4', '#84CC16'];

  return (
    <div className="w-full h-full">
      <ResponsiveContainer width="100%" height="100%">
        <RechartsBarChart data={transformedData} margin={{ top: 20, right: 30, left: 20, bottom: 5 }}>
          <CartesianGrid strokeDasharray="3 3" />
          <XAxis dataKey="name" />
          <YAxis domain={[0, options.scales?.y?.max || 100]} />
          <Tooltip />
          <Legend />
          {data.datasets.map((dataset, index) => (
            <Bar
              key={index}
              dataKey={dataset.label || `Dataset ${index}`}
              fill={dataset.backgroundColor || colors[index % colors.length]}
            />
          ))}
        </RechartsBarChart>
      </ResponsiveContainer>
    </div>
  );
};
