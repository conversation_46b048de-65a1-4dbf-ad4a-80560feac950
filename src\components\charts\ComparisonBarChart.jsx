import React from 'react';
import PropTypes from 'prop-types';

/**
 * Un componente de gráfico de barras reutilizable para comparaciones.
 * @param {object} props
 * @param {Array<{name: string, value: number}>} props.data - Los datos a visualizar.
 * @param {string} [props.title] - El título del gráfico.
 * @param {number} [props.maxValue=120] - El valor máximo para la escala del gráfico.
 */
const ComparisonBarChart = ({ data, title, maxValue = 120 }) => {
  if (!data || data.length === 0) {
    return <div className="text-center text-gray-500 py-8">No hay datos para mostrar en el gráfico.</div>;
  }

  return (
    <div className="bg-white p-6 rounded-lg shadow">
      {title && <h3 className="font-semibold mb-4 text-lg text-gray-700">{title}</h3>}
      <div className="space-y-4">
        {data.map((item) => (
          <div key={item.name} className="flex items-center" title={`Puntaje: ${item.value.toFixed(1)}`}>
            <div className="w-32 text-sm font-medium text-gray-700 truncate pr-2">{item.name}</div>
            <div className="flex-grow bg-gray-200 rounded-full h-6">
              <div
                className="bg-gradient-to-r from-blue-400 to-indigo-500 h-6 rounded-full text-white text-xs flex items-center justify-end pr-2 transition-all duration-500 ease-out"
                style={{ width: `${(item.value / maxValue) * 100}%` }}
              >
                {item.value.toFixed(1)}
              </div>
            </div>
          </div>
        ))}
      </div>
    </div>
  );
};

ComparisonBarChart.propTypes = {
  data: PropTypes.arrayOf(PropTypes.shape({
    name: PropTypes.string.isRequired,
    value: PropTypes.number.isRequired,
  })).isRequired,
  title: PropTypes.string,
  maxValue: PropTypes.number,
};

export default ComparisonBarChart;