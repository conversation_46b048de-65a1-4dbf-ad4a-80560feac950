/**
 * @file ChartErrorBoundary.jsx
 * @description Error boundary specifically for chart components
 */

import React from 'react';
import PropTypes from 'prop-types';
import { ExclamationTriangleIcon } from '@heroicons/react/24/outline';

class ChartErrorBoundary extends React.Component {
  constructor(props) {
    super(props);
    this.state = { hasError: false, error: null };
  }

  static getDerivedStateFromError(error) {
    return { hasError: true, error };
  }

  componentDidCatch(error, errorInfo) {
    console.error('Chart Error:', error, errorInfo);
    
    // Log to external service in production
    if (process.env.NODE_ENV === 'production') {
      // logErrorToService(error, errorInfo);
    }
  }

  render() {
    if (this.state.hasError) {
      return (
        <div className="flex flex-col items-center justify-center h-64 bg-gray-50 rounded-lg border-2 border-dashed border-gray-300">
          <ExclamationTriangleIcon className="h-12 w-12 text-gray-400 mb-4" />
          <h3 className="text-lg font-medium text-gray-900 mb-2">
            Error al cargar el gráfico
          </h3>
          <p className="text-sm text-gray-500 text-center max-w-sm">
            {this.props.fallbackMessage || 'Ha ocurrido un error inesperado al renderizar el gráfico.'}
          </p>
          {process.env.NODE_ENV === 'development' && (
            <details className="mt-4 text-xs text-gray-400">
              <summary className="cursor-pointer">Detalles del error</summary>
              <pre className="mt-2 p-2 bg-gray-100 rounded text-left overflow-auto max-w-md">
                {this.state.error?.toString()}
              </pre>
            </details>
          )}
        </div>
      );
    }

    return this.props.children;
  }
}

ChartErrorBoundary.propTypes = {
  children: PropTypes.node.isRequired,
  fallbackMessage: PropTypes.string
};

export default ChartErrorBoundary;