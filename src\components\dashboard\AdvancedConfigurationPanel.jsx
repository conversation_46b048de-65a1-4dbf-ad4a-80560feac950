import React, { useState, useEffect } from 'react';
import { FaCog, FaSignature, FaFileAlt, FaDatabase, FaChartLine, FaSave, FaDownload, FaUpload } from 'react-icons/fa';
import { Card, CardHeader, CardBody } from '../ui/Card';
import { useRealDataConnection } from '../../hooks/useRealDataConnection';
import ReportTemplateService from '../../services/ReportTemplateService';
import DigitalSignatureService from '../../services/DigitalSignatureService';
import InterpretationCustomizationService from '../../services/InterpretationCustomizationService';

/**
 * Panel de Configuración Avanzada del Dashboard BAT-7
 * Gestiona todas las funcionalidades avanzadas implementadas
 */
const AdvancedConfigurationPanel = ({ isOpen, onClose }) => {
  const [activeSection, setActiveSection] = useState('database');
  const [configurations, setConfigurations] = useState({
    database: {},
    templates: {},
    signatures: {},
    interpretations: {},
    charts: {}
  });

  const { isRealDataAvailable, connectionStatus, recheckConnection } = useRealDataConnection();

  const sections = [
    { id: 'database', label: 'Base de Datos', icon: FaDatabase },
    { id: 'templates', label: 'Plantillas', icon: FaFileAlt },
    { id: 'signatures', label: 'Firmas Digitales', icon: FaSignature },
    { id: 'interpretations', label: 'Interpretaciones', icon: FaCog },
    { id: 'charts', label: 'Gráficos', icon: FaChartLine }
  ];

  useEffect(() => {
    loadConfigurations();
  }, []);

  const loadConfigurations = () => {
    // Cargar configuraciones desde los servicios
    setConfigurations({
      database: {
        connectionStatus,
        isRealDataAvailable,
        autoConnect: true,
        fallbackToSimulated: true
      },
      templates: {
        activeTemplate: 'standard',
        availableTemplates: ReportTemplateService.listTemplates(),
        customSections: 0
      },
      signatures: {
        certificatesCount: 2,
        signaturesCount: 0,
        validationEnabled: true
      },
      interpretations: {
        institutionProfiles: 1,
        ageGroupProfiles: 1,
        contextualModifiers: true
      },
      charts: {
        defaultChartType: 'line',
        enableInteractivity: true,
        showNorms: true,
        colorScheme: 'professional'
      }
    });
  };

  const handleSaveConfiguration = () => {
    console.log('💾 [AdvancedConfig] Guardando configuraciones...');
    // Aquí se implementaría el guardado real
    alert('Configuraciones guardadas exitosamente');
  };

  const handleExportConfiguration = () => {
    const exportData = {
      templates: ReportTemplateService.exportTemplate('standard'),
      signatures: DigitalSignatureService.exportSignatureData(),
      interpretations: InterpretationCustomizationService.exportCustomizationConfig(),
      exportedAt: new Date().toISOString(),
      version: '1.0'
    };

    const blob = new Blob([JSON.stringify(exportData, null, 2)], { type: 'application/json' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `bat7_config_${new Date().toISOString().split('T')[0]}.json`;
    a.click();
    URL.revokeObjectURL(url);
  };

  const handleImportConfiguration = (event) => {
    const file = event.target.files[0];
    if (!file) return;

    const reader = new FileReader();
    reader.onload = (e) => {
      try {
        const importData = JSON.parse(e.target.result);
        
        // Importar configuraciones
        if (importData.templates) {
          ReportTemplateService.importTemplate(importData.templates);
        }
        if (importData.signatures) {
          DigitalSignatureService.importSignatureData(importData.signatures);
        }
        if (importData.interpretations) {
          InterpretationCustomizationService.importCustomizationConfig(importData.interpretations);
        }

        loadConfigurations();
        alert('Configuraciones importadas exitosamente');
      } catch (error) {
        console.error('Error importando configuraciones:', error);
        alert('Error al importar configuraciones');
      }
    };
    reader.readAsText(file);
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-white rounded-lg shadow-xl w-full max-w-6xl h-5/6 flex flex-col">
        {/* Header */}
        <div className="flex items-center justify-between p-6 border-b">
          <div className="flex items-center">
            <FaCog className="text-blue-500 text-xl mr-3" />
            <h2 className="text-xl font-semibold text-gray-800">Configuración Avanzada BAT-7</h2>
          </div>
          <div className="flex items-center space-x-3">
            <button
              onClick={handleSaveConfiguration}
              className="flex items-center px-3 py-2 bg-green-500 text-white rounded-lg hover:bg-green-600 transition-colors"
            >
              <FaSave className="mr-2" />
              Guardar
            </button>
            <button
              onClick={handleExportConfiguration}
              className="flex items-center px-3 py-2 bg-blue-500 text-white rounded-lg hover:bg-blue-600 transition-colors"
            >
              <FaDownload className="mr-2" />
              Exportar
            </button>
            <label className="flex items-center px-3 py-2 bg-purple-500 text-white rounded-lg hover:bg-purple-600 transition-colors cursor-pointer">
              <FaUpload className="mr-2" />
              Importar
              <input
                type="file"
                accept=".json"
                onChange={handleImportConfiguration}
                className="hidden"
              />
            </label>
            <button
              onClick={onClose}
              className="text-gray-500 hover:text-gray-700 text-xl"
            >
              ×
            </button>
          </div>
        </div>

        <div className="flex flex-1 overflow-hidden">
          {/* Sidebar */}
          <div className="w-64 bg-gray-50 border-r overflow-y-auto">
            <nav className="p-4 space-y-2">
              {sections.map(section => {
                const Icon = section.icon;
                return (
                  <button
                    key={section.id}
                    onClick={() => setActiveSection(section.id)}
                    className={`w-full flex items-center px-3 py-2 rounded-lg text-left transition-colors ${
                      activeSection === section.id
                        ? 'bg-blue-500 text-white'
                        : 'text-gray-700 hover:bg-gray-200'
                    }`}
                  >
                    <Icon className="mr-3" />
                    {section.label}
                  </button>
                );
              })}
            </nav>
          </div>

          {/* Content */}
          <div className="flex-1 overflow-y-auto p-6">
            {activeSection === 'database' && (
              <div className="space-y-6">
                <Card>
                  <CardHeader>
                    <h3 className="text-lg font-semibold">Estado de Conexión a Base de Datos</h3>
                  </CardHeader>
                  <CardBody>
                    <div className="space-y-4">
                      <div className="flex items-center justify-between">
                        <span>Estado actual:</span>
                        <div className="flex items-center">
                          <div className={`w-3 h-3 rounded-full mr-2 ${
                            connectionStatus === 'connected' ? 'bg-green-500' :
                            connectionStatus === 'simulated' ? 'bg-yellow-500' :
                            'bg-red-500'
                          }`}></div>
                          <span className="capitalize">{connectionStatus}</span>
                        </div>
                      </div>
                      <div className="flex items-center justify-between">
                        <span>Datos reales disponibles:</span>
                        <span>{isRealDataAvailable ? 'Sí' : 'No'}</span>
                      </div>
                      <button
                        onClick={recheckConnection}
                        className="w-full py-2 bg-blue-500 text-white rounded-lg hover:bg-blue-600"
                      >
                        Verificar Conexión
                      </button>
                    </div>
                  </CardBody>
                </Card>

                <Card>
                  <CardHeader>
                    <h3 className="text-lg font-semibold">Configuración de Datos</h3>
                  </CardHeader>
                  <CardBody>
                    <div className="space-y-4">
                      <label className="flex items-center">
                        <input type="checkbox" defaultChecked className="mr-2" />
                        Conexión automática al iniciar
                      </label>
                      <label className="flex items-center">
                        <input type="checkbox" defaultChecked className="mr-2" />
                        Fallback a datos simulados
                      </label>
                      <label className="flex items-center">
                        <input type="checkbox" defaultChecked className="mr-2" />
                        Sincronización en tiempo real
                      </label>
                    </div>
                  </CardBody>
                </Card>
              </div>
            )}

            {activeSection === 'templates' && (
              <div className="space-y-6">
                <Card>
                  <CardHeader>
                    <h3 className="text-lg font-semibold">Gestión de Plantillas</h3>
                  </CardHeader>
                  <CardBody>
                    <div className="space-y-4">
                      <div>
                        <label className="block text-sm font-medium mb-2">Plantilla activa:</label>
                        <select className="w-full p-2 border rounded-lg">
                          <option value="standard">Informe Estándar BAT-7</option>
                          <option value="clinical">Informe Clínico Detallado</option>
                          <option value="educational">Informe Educativo</option>
                        </select>
                      </div>
                      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                        {configurations.templates.availableTemplates?.map(template => (
                          <div key={template.id} className="border rounded-lg p-3">
                            <h4 className="font-medium">{template.name}</h4>
                            <p className="text-sm text-gray-600 mt-1">{template.description}</p>
                            <div className="text-xs text-gray-500 mt-2">
                              {template.sectionsCount} secciones
                            </div>
                          </div>
                        ))}
                      </div>
                    </div>
                  </CardBody>
                </Card>
              </div>
            )}

            {activeSection === 'signatures' && (
              <div className="space-y-6">
                <Card>
                  <CardHeader>
                    <h3 className="text-lg font-semibold">Certificados Digitales</h3>
                  </CardHeader>
                  <CardBody>
                    <div className="space-y-4">
                      <div className="bg-green-50 border border-green-200 rounded-lg p-4">
                        <h4 className="font-medium text-green-800">Dr. Juan Pérez</h4>
                        <p className="text-sm text-green-700">Licencia: PSI-2024-001</p>
                        <p className="text-xs text-green-600">Válido hasta: 31/12/2026</p>
                      </div>
                      <div className="bg-green-50 border border-green-200 rounded-lg p-4">
                        <h4 className="font-medium text-green-800">Dra. María González</h4>
                        <p className="text-sm text-green-700">Licencia: PSI-2024-002</p>
                        <p className="text-xs text-green-600">Válido hasta: 31/12/2026</p>
                      </div>
                      <button className="w-full py-2 bg-blue-500 text-white rounded-lg hover:bg-blue-600">
                        Agregar Nuevo Certificado
                      </button>
                    </div>
                  </CardBody>
                </Card>
              </div>
            )}

            {activeSection === 'interpretations' && (
              <div className="space-y-6">
                <Card>
                  <CardHeader>
                    <h3 className="text-lg font-semibold">Personalización de Interpretaciones</h3>
                  </CardHeader>
                  <CardBody>
                    <div className="space-y-4">
                      <div>
                        <h4 className="font-medium mb-2">Perfiles Institucionales</h4>
                        <div className="bg-blue-50 border border-blue-200 rounded-lg p-3">
                          <span className="font-medium">Colegio San José</span>
                          <p className="text-sm text-gray-600">Enfoque educativo personalizado</p>
                        </div>
                      </div>
                      <div>
                        <h4 className="font-medium mb-2">Grupos de Edad</h4>
                        <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-3">
                          <span className="font-medium">Adolescentes (13-17 años)</span>
                          <p className="text-sm text-gray-600">Consideraciones específicas para desarrollo adolescente</p>
                        </div>
                      </div>
                      <button className="w-full py-2 bg-green-500 text-white rounded-lg hover:bg-green-600">
                        Crear Nueva Personalización
                      </button>
                    </div>
                  </CardBody>
                </Card>
              </div>
            )}

            {activeSection === 'charts' && (
              <div className="space-y-6">
                <Card>
                  <CardHeader>
                    <h3 className="text-lg font-semibold">Configuración de Gráficos</h3>
                  </CardHeader>
                  <CardBody>
                    <div className="space-y-4">
                      <div>
                        <label className="block text-sm font-medium mb-2">Tipo de gráfico predeterminado:</label>
                        <select className="w-full p-2 border rounded-lg">
                          <option value="line">Líneas</option>
                          <option value="radar">Radar</option>
                          <option value="bar">Barras</option>
                        </select>
                      </div>
                      <div>
                        <label className="block text-sm font-medium mb-2">Esquema de colores:</label>
                        <select className="w-full p-2 border rounded-lg">
                          <option value="professional">Profesional</option>
                          <option value="colorful">Colorido</option>
                          <option value="monochrome">Monocromático</option>
                        </select>
                      </div>
                      <div className="space-y-2">
                        <label className="flex items-center">
                          <input type="checkbox" defaultChecked className="mr-2" />
                          Mostrar líneas de norma poblacional
                        </label>
                        <label className="flex items-center">
                          <input type="checkbox" defaultChecked className="mr-2" />
                          Habilitar interactividad
                        </label>
                        <label className="flex items-center">
                          <input type="checkbox" defaultChecked className="mr-2" />
                          Incluir tooltips explicativos
                        </label>
                      </div>
                    </div>
                  </CardBody>
                </Card>
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  );
};

export default AdvancedConfigurationPanel;
