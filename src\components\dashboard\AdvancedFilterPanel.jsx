import React, { useState } from 'react';
import { 
  FaFilter, 
  FaTimes, 
  FaHistory, 
  FaSave, 
  FaUndo, 
  FaRedo, 
  FaRocket,
  FaCalendarAlt,
  FaUsers,
  FaGraduationCap,
  FaExclamationTriangle,
  FaCheckCircle,
  FaSpinner
} from 'react-icons/fa';
import { useAdvancedFilters } from '../../hooks/useAdvancedFilters';
import { useRealDataConnection } from '../../hooks/useRealDataConnection';

/**
 * Panel de filtros avanzado con validaciones, presets y historial
 */
const AdvancedFilterPanel = ({ isOpen, onClose, onFiltersChange, initialFilters = {} }) => {
  const [showPresets, setShowPresets] = useState(false);
  const [showSaveDialog, setShowSaveDialog] = useState(false);
  const [saveConfigName, setSaveConfigName] = useState('');
  
  const { patients, psychologists } = useRealDataConnection();
  
  const {
    filters,
    validationErrors,
    isApplying,
    filterStats,
    canGoBack,
    canGoForward,
    updateFilter,
    updateMultipleFilters,
    clearFilters,
    applyPreset,
    goBackInHistory,
    goForwardInHistory,
    saveFilterConfiguration,
    loadFilterConfiguration,
    getSavedConfigurations
  } = useAdvancedFilters(initialFilters, onFiltersChange);

  const savedConfigurations = getSavedConfigurations();

  // Presets predefinidos
  const filterPresets = [
    {
      id: 'ultimoMes',
      name: 'Último Mes',
      description: 'Evaluaciones del último mes',
      icon: FaCalendarAlt,
      color: 'blue'
    },
    {
      id: 'escolares',
      name: 'Población Escolar',
      description: 'Estudiantes de 6-18 años, Nivel E',
      icon: FaGraduationCap,
      color: 'green'
    },
    {
      id: 'adultos',
      name: 'Población Adulta',
      description: 'Adultos 18+, Nivel S',
      icon: FaUsers,
      color: 'purple'
    },
    {
      id: 'riesgo',
      name: 'Casos de Riesgo',
      description: 'Identificar casos que requieren atención',
      icon: FaExclamationTriangle,
      color: 'red'
    }
  ];

  const handleSaveConfiguration = () => {
    if (saveConfigName.trim()) {
      saveFilterConfiguration(saveConfigName.trim());
      setSaveConfigName('');
      setShowSaveDialog(false);
    }
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 z-50 flex items-center justify-center p-4">
      <div className="bg-white rounded-xl shadow-2xl max-w-4xl w-full max-h-[90vh] overflow-hidden">
        {/* Header */}
        <div className="bg-gradient-to-r from-blue-600 to-purple-600 text-white p-6">
          <div className="flex items-center justify-between">
            <div className="flex items-center">
              <FaFilter className="text-2xl mr-3" />
              <div>
                <h2 className="text-xl font-bold">Filtros Avanzados</h2>
                <p className="text-blue-100 text-sm">
                  {filterStats.totalActive} filtros activos
                  {isApplying && <span className="ml-2">• Aplicando...</span>}
                </p>
              </div>
            </div>
            
            {/* Controles del header */}
            <div className="flex items-center space-x-2">
              {/* Historial */}
              <button
                onClick={goBackInHistory}
                disabled={!canGoBack}
                className="p-2 bg-white bg-opacity-20 rounded-lg hover:bg-opacity-30 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
                title="Filtros anteriores"
              >
                <FaUndo />
              </button>
              
              <button
                onClick={goForwardInHistory}
                disabled={!canGoForward}
                className="p-2 bg-white bg-opacity-20 rounded-lg hover:bg-opacity-30 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
                title="Filtros siguientes"
              >
                <FaRedo />
              </button>
              
              {/* Guardar configuración */}
              <button
                onClick={() => setShowSaveDialog(true)}
                className="p-2 bg-white bg-opacity-20 rounded-lg hover:bg-opacity-30 transition-colors"
                title="Guardar configuración"
              >
                <FaSave />
              </button>
              
              {/* Cerrar */}
              <button
                onClick={onClose}
                className="p-2 bg-white bg-opacity-20 rounded-lg hover:bg-opacity-30 transition-colors"
              >
                <FaTimes />
              </button>
            </div>
          </div>
          
          {/* Indicador de carga */}
          {isApplying && (
            <div className="mt-3 flex items-center text-sm text-blue-100">
              <FaSpinner className="animate-spin mr-2" />
              Aplicando filtros...
            </div>
          )}
        </div>

        <div className="flex h-[calc(90vh-120px)]">
          {/* Sidebar con presets y configuraciones guardadas */}
          <div className="w-80 bg-gray-50 border-r border-gray-200 p-4 overflow-y-auto">
            {/* Presets rápidos */}
            <div className="mb-6">
              <h3 className="font-semibold text-gray-900 mb-3 flex items-center">
                <FaRocket className="mr-2 text-blue-500" />
                Filtros Rápidos
              </h3>
              <div className="space-y-2">
                {filterPresets.map(preset => {
                  const Icon = preset.icon;
                  return (
                    <button
                      key={preset.id}
                      onClick={() => applyPreset(preset.id)}
                      className={`w-full text-left p-3 rounded-lg border transition-colors hover:shadow-sm ${
                        preset.color === 'blue' ? 'border-blue-200 hover:bg-blue-50' :
                        preset.color === 'green' ? 'border-green-200 hover:bg-green-50' :
                        preset.color === 'purple' ? 'border-purple-200 hover:bg-purple-50' :
                        'border-red-200 hover:bg-red-50'
                      }`}
                    >
                      <div className="flex items-start">
                        <Icon className={`mt-1 mr-3 ${
                          preset.color === 'blue' ? 'text-blue-500' :
                          preset.color === 'green' ? 'text-green-500' :
                          preset.color === 'purple' ? 'text-purple-500' :
                          'text-red-500'
                        }`} />
                        <div>
                          <div className="font-medium text-gray-900">{preset.name}</div>
                          <div className="text-xs text-gray-600">{preset.description}</div>
                        </div>
                      </div>
                    </button>
                  );
                })}
              </div>
            </div>

            {/* Configuraciones guardadas */}
            {savedConfigurations.length > 0 && (
              <div className="mb-6">
                <h3 className="font-semibold text-gray-900 mb-3 flex items-center">
                  <FaHistory className="mr-2 text-green-500" />
                  Configuraciones Guardadas
                </h3>
                <div className="space-y-1">
                  {savedConfigurations.map(configName => (
                    <button
                      key={configName}
                      onClick={() => loadFilterConfiguration(configName)}
                      className="w-full text-left p-2 text-sm rounded hover:bg-gray-100 transition-colors"
                    >
                      {configName}
                    </button>
                  ))}
                </div>
              </div>
            )}

            {/* Estadísticas de filtros */}
            <div className="bg-white rounded-lg p-3 border border-gray-200">
              <h4 className="font-medium text-gray-900 mb-2">Estado Actual</h4>
              <div className="space-y-1 text-sm">
                <div className="flex justify-between">
                  <span>Filtros activos:</span>
                  <span className="font-medium">{filterStats.totalActive}</span>
                </div>
                {filterStats.hasDateRange && (
                  <div className="flex items-center text-blue-600">
                    <FaCheckCircle className="mr-1" />
                    <span>Rango de fechas</span>
                  </div>
                )}
                {filterStats.hasSpecificPatient && (
                  <div className="flex items-center text-green-600">
                    <FaCheckCircle className="mr-1" />
                    <span>Análisis individual</span>
                  </div>
                )}
              </div>
            </div>
          </div>

          {/* Panel principal de filtros */}
          <div className="flex-1 p-6 overflow-y-auto">
            {/* Filtros de fecha */}
            <div className="mb-6">
              <h3 className="font-semibold text-gray-900 mb-3 flex items-center">
                <FaCalendarAlt className="mr-2 text-blue-500" />
                Rango de Fechas
              </h3>
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Fecha de inicio
                  </label>
                  <input
                    type="date"
                    value={filters.fechaInicio || ''}
                    onChange={(e) => updateFilter('fechaInicio', e.target.value || null)}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                  />
                  {validationErrors.fechaInicio && (
                    <p className="text-red-500 text-xs mt-1">{validationErrors.fechaInicio}</p>
                  )}
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Fecha de fin
                  </label>
                  <input
                    type="date"
                    value={filters.fechaFin || ''}
                    onChange={(e) => updateFilter('fechaFin', e.target.value || null)}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                  />
                  {validationErrors.fechaFin && (
                    <p className="text-red-500 text-xs mt-1">{validationErrors.fechaFin}</p>
                  )}
                </div>
              </div>
            </div>

            {/* Resto de filtros... */}
            {/* (Continúa en la siguiente parte) */}
          </div>
        </div>

        {/* Footer con acciones */}
        <div className="bg-gray-50 px-6 py-4 border-t border-gray-200">
          <div className="flex items-center justify-between">
            <button
              onClick={clearFilters}
              className="px-4 py-2 text-gray-600 hover:text-gray-800 transition-colors"
            >
              Limpiar todos los filtros
            </button>
            
            <div className="flex space-x-3">
              <button
                onClick={onClose}
                className="px-4 py-2 border border-gray-300 rounded-lg hover:bg-gray-50 transition-colors"
              >
                Cancelar
              </button>
              <button
                onClick={onClose}
                disabled={isApplying}
                className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:opacity-50 transition-colors"
              >
                {isApplying ? 'Aplicando...' : 'Aplicar Filtros'}
              </button>
            </div>
          </div>
        </div>
      </div>

      {/* Dialog para guardar configuración */}
      {showSaveDialog && (
        <div className="fixed inset-0 bg-black bg-opacity-50 z-60 flex items-center justify-center p-4">
          <div className="bg-white rounded-lg p-6 max-w-md w-full">
            <h3 className="text-lg font-semibold mb-4">Guardar Configuración</h3>
            <input
              type="text"
              value={saveConfigName}
              onChange={(e) => setSaveConfigName(e.target.value)}
              placeholder="Nombre de la configuración"
              className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 mb-4"
            />
            <div className="flex space-x-3">
              <button
                onClick={() => setShowSaveDialog(false)}
                className="flex-1 px-4 py-2 border border-gray-300 rounded-lg hover:bg-gray-50 transition-colors"
              >
                Cancelar
              </button>
              <button
                onClick={handleSaveConfiguration}
                disabled={!saveConfigName.trim()}
                className="flex-1 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:opacity-50 transition-colors"
              >
                Guardar
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default AdvancedFilterPanel;
