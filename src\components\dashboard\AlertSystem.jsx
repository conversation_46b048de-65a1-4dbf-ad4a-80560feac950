import React, { useState, useEffect } from 'react';
import { FaExclamationTriangle, FaInfoCircle, FaCheckCircle, FaTimes } from 'react-icons/fa';

const AlertSystem = ({ alerts = [] }) => {
  const [visibleAlerts, setVisibleAlerts] = useState([]);
  const [dismissedAlerts, setDismissedAlerts] = useState(new Set());

  useEffect(() => {
    // Filtrar alertas no descartadas
    const filtered = alerts.filter(alert => !dismissedAlerts.has(alert.id));
    setVisibleAlerts(filtered);
  }, [alerts, dismissedAlerts]);

  const dismissAlert = (alertId) => {
    setDismissedAlerts(prev => new Set([...prev, alertId]));
  };

  const getAlertIcon = (type) => {
    switch (type) {
      case 'critical': return <FaExclamationTriangle className="h-5 w-5" />;
      case 'warning': return <FaInfoCircle className="h-5 w-5" />;
      case 'success': return <FaCheckCircle className="h-5 w-5" />;
      default: return <FaInfoCircle className="h-5 w-5" />;
    }
  };

  const getAlertStyles = (type) => {
    switch (type) {
      case 'critical':
        return 'bg-red-50 border-red-200 text-red-800';
      case 'warning':
        return 'bg-yellow-50 border-yellow-200 text-yellow-800';
      case 'success':
        return 'bg-green-50 border-green-200 text-green-800';
      default:
        return 'bg-blue-50 border-blue-200 text-blue-800';
    }
  };

  if (visibleAlerts.length === 0) return null;

  return (
    <div className="space-y-3 mb-6">
      <h3 className="text-lg font-semibold text-gray-800 flex items-center">
        <FaExclamationTriangle className="h-5 w-5 mr-2 text-orange-500" />
        Alertas del Sistema ({visibleAlerts.length})
      </h3>
      
      <div className="space-y-2">
        {visibleAlerts.map((alert) => (
          <div
            key={alert.id}
            className={`border rounded-lg p-4 ${getAlertStyles(alert.type)} transition-all duration-300`}
          >
            <div className="flex items-start justify-between">
              <div className="flex items-start">
                <div className="flex-shrink-0 mr-3">
                  {getAlertIcon(alert.type)}
                </div>
                <div className="flex-1">
                  <h4 className="font-semibold text-sm">{alert.title}</h4>
                  <p className="text-sm mt-1">{alert.message}</p>
                  {alert.action && (
                    <button className="mt-2 text-xs underline hover:no-underline">
                      {alert.action}
                    </button>
                  )}
                </div>
              </div>
              <button
                onClick={() => dismissAlert(alert.id)}
                className="flex-shrink-0 ml-4 text-gray-400 hover:text-gray-600 transition-colors"
                title="Descartar alerta"
              >
                <FaTimes className="h-4 w-4" />
              </button>
            </div>
          </div>
        ))}
      </div>
    </div>
  );
};

export default AlertSystem;