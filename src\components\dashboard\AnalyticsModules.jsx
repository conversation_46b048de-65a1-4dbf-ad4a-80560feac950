/**
 * @file AnalyticsModules.jsx
 * @description Componente principal que integra todos los módulos analíticos del dashboard
 */

import React, { useState, useEffect } from 'react';
import { 
  ChartBarIcon, 
  CogIcon, 
  DocumentChartBarIcon,
  PresentationChartLineIcon,
  ScaleIcon,
  ChartPieIcon,
  ArrowTrendingUpIcon,
  DocumentArrowDownIcon,
  UserIcon,
  ComputerDesktopIcon
} from '@heroicons/react/24/outline';

import DashboardService from '../../services/DashboardService.js';
import SyncTestModule from './modules/SyncTestModule.jsx';
import ExecutiveSummaryModule from './modules/ExecutiveSummaryModule.jsx';
import CriticalKPIModule from './modules/CriticalKPIModule.jsx';
import OverviewModule from './modules/OverviewModule.jsx';
import TrendAnalysisModule from './modules/TrendAnalysisModule.jsx';
import ComparativeAnalysisModule from './modules/ComparativeAnalysisModule.jsx';
import StatisticalAnalysisModule from './modules/StatisticalAnalysisModule.jsx';
import ExportModule from './modules/ExportModule.jsx';
import IndividualReportModule from './modules/IndividualReportModule.jsx';
import SystemStatusModule from './modules/SystemStatusModule.jsx';

const AnalyticsModules = () => {
  const [activeModule, setActiveModule] = useState('overview');
  const [isLoading, setIsLoading] = useState(false);
  const [syncStatus, setSyncStatus] = useState('synced');

  // Configuración de módulos disponibles
  const modules = [
    {
      id: 'sync-test',
      name: 'Pruebas Sync',
      icon: CogIcon,
      description: 'Verificar integridad y sincronización',
      component: SyncTestModule,
      color: 'bg-blue-500'
    },
    {
      id: 'executive',
      name: 'Resumen Ejecutivo',
      icon: DocumentChartBarIcon,
      description: 'Visión estratégica de alto nivel',
      component: ExecutiveSummaryModule,
      color: 'bg-purple-500'
    },
    {
      id: 'kpis',
      name: 'KPIs Críticos',
      icon: ChartBarIcon,
      description: 'Indicadores clave de rendimiento',
      component: CriticalKPIModule,
      color: 'bg-green-500'
    },
    {
      id: 'overview',
      name: 'Visión General',
      icon: ChartPieIcon,
      description: 'Estadísticas descriptivas globales',
      component: OverviewModule,
      color: 'bg-indigo-500'
    },
    {
      id: 'trends',
      name: 'Análisis de Tendencias',
      icon: ArrowTrendingUpIcon,
      description: 'Evolución temporal de métricas',
      component: TrendAnalysisModule,
      color: 'bg-orange-500'
    },
    {
      id: 'comparative',
      name: 'Análisis Comparativo',
      icon: ScaleIcon,
      description: 'Benchmarking entre segmentos',
      component: ComparativeAnalysisModule,
      color: 'bg-teal-500'
    },
    {
      id: 'statistical',
      name: 'Análisis Estadístico',
      icon: PresentationChartLineIcon,
      description: 'Medidas estadísticas avanzadas',
      component: StatisticalAnalysisModule,
      color: 'bg-red-500'
    },
    {
      id: 'export',
      name: 'Exportación',
      icon: DocumentArrowDownIcon,
      description: 'Generar reportes y descargas',
      component: ExportModule,
      color: 'bg-gray-500'
    },
    {
      id: 'individual',
      name: 'Informe Individual',
      icon: UserIcon,
      description: 'Análisis detallado por paciente',
      component: IndividualReportModule,
      color: 'bg-pink-500'
    },
    {
      id: 'system',
      name: 'Estado del Sistema',
      icon: ComputerDesktopIcon,
      description: 'Dashboard técnico administrativo',
      component: SystemStatusModule,
      color: 'bg-yellow-500'
    }
  ];

  // Sincronización automática
  const handleSync = async () => {
    setIsLoading(true);
    setSyncStatus('syncing');
    
    try {
      await DashboardService.syncDashboardData();
      setSyncStatus('synced');
    } catch (error) {
      console.error('Error en sincronización:', error);
      setSyncStatus('error');
    } finally {
      setIsLoading(false);
    }
  };

  // Componente activo
  const ActiveModuleComponent = modules.find(m => m.id === activeModule)?.component || OverviewModule;

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header con navegación de módulos */}
      <div className="bg-white shadow-sm border-b">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex items-center justify-between h-16">
            <div className="flex items-center space-x-4">
              <h1 className="text-xl font-semibold text-gray-900">
                Dashboard Analítico BAT-7
              </h1>
              
              {/* Indicador de estado de sincronización */}
              <div className="flex items-center space-x-2">
                <div className={`w-2 h-2 rounded-full ${
                  syncStatus === 'synced' ? 'bg-green-400' :
                  syncStatus === 'syncing' ? 'bg-yellow-400 animate-pulse' :
                  'bg-red-400'
                }`} />
                <span className="text-sm text-gray-500">
                  {syncStatus === 'synced' ? 'Sincronizado' :
                   syncStatus === 'syncing' ? 'Sincronizando...' :
                   'Error de sincronización'}
                </span>
              </div>
            </div>

            {/* Botón de sincronización */}
            <button
              onClick={handleSync}
              disabled={isLoading}
              className="inline-flex items-center px-3 py-2 border border-transparent text-sm leading-4 font-medium rounded-md text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 disabled:opacity-50"
            >
              <CogIcon className={`w-4 h-4 mr-2 ${isLoading ? 'animate-spin' : ''}`} />
              {isLoading ? 'Sincronizando...' : 'Sincronizar'}
            </button>
          </div>
        </div>
      </div>

      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6">
        <div className="flex flex-col lg:flex-row gap-6">
          {/* Sidebar de navegación de módulos */}
          <div className="lg:w-64 flex-shrink-0">
            <div className="bg-white rounded-lg shadow-sm border">
              <div className="p-4 border-b">
                <h2 className="text-lg font-medium text-gray-900">Módulos Analíticos</h2>
                <p className="text-sm text-gray-500 mt-1">
                  Selecciona un módulo para ver el análisis detallado
                </p>
              </div>
              
              <nav className="p-2">
                {modules.map((module) => {
                  const Icon = module.icon;
                  const isActive = activeModule === module.id;
                  
                  return (
                    <button
                      key={module.id}
                      onClick={() => setActiveModule(module.id)}
                      className={`w-full flex items-center px-3 py-2 text-sm font-medium rounded-md mb-1 transition-colors ${
                        isActive
                          ? 'bg-indigo-100 text-indigo-700 border-indigo-200'
                          : 'text-gray-600 hover:bg-gray-50 hover:text-gray-900'
                      }`}
                    >
                      <div className={`w-2 h-2 rounded-full mr-3 ${module.color}`} />
                      <Icon className="w-4 h-4 mr-2 flex-shrink-0" />
                      <div className="text-left flex-1 min-w-0">
                        <div className="truncate">{module.name}</div>
                        <div className="text-xs text-gray-500 truncate">
                          {module.description}
                        </div>
                      </div>
                    </button>
                  );
                })}
              </nav>
            </div>

            {/* Información del módulo activo */}
            <div className="mt-4 bg-white rounded-lg shadow-sm border p-4">
              <h3 className="text-sm font-medium text-gray-900 mb-2">
                {modules.find(m => m.id === activeModule)?.name}
              </h3>
              <p className="text-sm text-gray-600">
                {modules.find(m => m.id === activeModule)?.description}
              </p>
            </div>
          </div>

          {/* Contenido principal del módulo */}
          <div className="flex-1 min-w-0">
            <div className="bg-white rounded-lg shadow-sm border">
              <div className="p-6">
                <React.Suspense 
                  fallback={
                    <div className="flex items-center justify-center h-64">
                      <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-indigo-600"></div>
                      <span className="ml-2 text-gray-600">Cargando módulo...</span>
                    </div>
                  }
                >
                  <ActiveModuleComponent />
                </React.Suspense>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default AnalyticsModules;