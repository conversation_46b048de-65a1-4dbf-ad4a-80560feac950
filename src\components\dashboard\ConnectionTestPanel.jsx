import React, { useState, useEffect } from 'react';
import { FaDatabase, FaCheckCircle, FaExclamationTriangle, FaSpinner, FaRedo } from 'react-icons/fa';
import supabase from '../../api/supabaseClient';

/**
 * Panel de prueba de conexión para verificar el estado de la base de datos
 */
const ConnectionTestPanel = ({ isRealDataAvailable, connectionStatus }) => {
  const [testResults, setTestResults] = useState({});
  const [testing, setTesting] = useState(false);

  const runConnectionTests = async () => {
    setTesting(true);
    const results = {};

    try {
      // Test 1: Conexión básica a Supabase
      console.log('🔍 Probando conexión básica...');
      const { data: authData, error: authError } = await supabase.auth.getSession();
      results.basicConnection = {
        status: authError ? 'error' : 'success',
        message: authError ? authError.message : 'Conexión establecida',
        details: authData ? 'Sesión activa' : 'Sin sesión activa'
      };

      // Test 2: Verificar tabla pacientes
      console.log('🔍 Probando tabla pacientes...');
      const { data: patientsData, error: patientsError } = await supabase
        .from('pacientes')
        .select('id')
        .limit(1);
      
      results.patientsTable = {
        status: patientsError ? 'error' : 'success',
        message: patientsError ? patientsError.message : `Tabla accesible (${patientsData?.length || 0} registros encontrados)`,
        details: patientsError?.details || 'Estructura correcta'
      };

      // Test 3: Verificar tabla evaluaciones
      console.log('🔍 Probando tabla evaluaciones...');
      const { data: evaluationsData, error: evaluationsError } = await supabase
        .from('evaluaciones')
        .select('id')
        .limit(1);
      
      results.evaluationsTable = {
        status: evaluationsError ? 'error' : 'success',
        message: evaluationsError ? evaluationsError.message : `Tabla accesible (${evaluationsData?.length || 0} registros encontrados)`,
        details: evaluationsError?.details || 'Estructura correcta'
      };

      // Test 4: Verificar tabla resultados
      console.log('🔍 Probando tabla resultados...');
      const { data: resultsData, error: resultsError } = await supabase
        .from('resultados')
        .select('id')
        .limit(1);
      
      results.resultsTable = {
        status: resultsError ? 'error' : 'success',
        message: resultsError ? resultsError.message : `Tabla accesible (${resultsData?.length || 0} registros encontrados)`,
        details: resultsError?.details || 'Estructura correcta'
      };

    } catch (error) {
      console.error('❌ Error en pruebas de conexión:', error);
      results.generalError = {
        status: 'error',
        message: 'Error general de conexión',
        details: error.message
      };
    }

    setTestResults(results);
    setTesting(false);
  };

  useEffect(() => {
    runConnectionTests();
  }, []);

  const getStatusIcon = (status) => {
    switch (status) {
      case 'success':
        return <FaCheckCircle className="text-green-500" />;
      case 'error':
        return <FaExclamationTriangle className="text-red-500" />;
      default:
        return <FaSpinner className="text-gray-400 animate-spin" />;
    }
  };

  const getStatusColor = (status) => {
    switch (status) {
      case 'success':
        return 'border-green-200 bg-green-50';
      case 'error':
        return 'border-red-200 bg-red-50';
      default:
        return 'border-gray-200 bg-gray-50';
    }
  };

  return (
    <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
      <div className="flex items-center justify-between mb-6">
        <div className="flex items-center">
          <FaDatabase className="text-blue-500 text-xl mr-3" />
          <h3 className="text-lg font-semibold text-gray-900">Estado de Conexión</h3>
        </div>
        <button
          onClick={runConnectionTests}
          disabled={testing}
          className="flex items-center px-3 py-2 bg-blue-500 text-white rounded-lg hover:bg-blue-600 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
        >
          {testing ? (
            <FaSpinner className="animate-spin mr-2" />
          ) : (
            <FaRedo className="mr-2" />
          )}
          {testing ? 'Probando...' : 'Probar Conexión'}
        </button>
      </div>

      {/* Estado general */}
      <div className={`p-4 rounded-lg border mb-4 ${
        isRealDataAvailable ? 'border-green-200 bg-green-50' : 'border-yellow-200 bg-yellow-50'
      }`}>
        <div className="flex items-center">
          <div className={`w-3 h-3 rounded-full mr-3 ${
            isRealDataAvailable ? 'bg-green-500 animate-pulse' : 'bg-yellow-500'
          }`}></div>
          <div>
            <p className="font-medium text-gray-900">
              {isRealDataAvailable ? 'Datos Reales Disponibles' : 'Usando Datos Simulados'}
            </p>
            <p className="text-sm text-gray-600">{connectionStatus}</p>
          </div>
        </div>
      </div>

      {/* Resultados de pruebas */}
      <div className="space-y-3">
        {Object.entries(testResults).map(([testName, result]) => (
          <div
            key={testName}
            className={`p-3 rounded-lg border ${getStatusColor(result.status)}`}
          >
            <div className="flex items-start">
              <div className="mr-3 mt-0.5">
                {getStatusIcon(result.status)}
              </div>
              <div className="flex-1">
                <h4 className="font-medium text-gray-900 capitalize">
                  {testName.replace(/([A-Z])/g, ' $1').replace(/^./, str => str.toUpperCase())}
                </h4>
                <p className="text-sm text-gray-700 mt-1">{result.message}</p>
                {result.details && (
                  <p className="text-xs text-gray-500 mt-1">{result.details}</p>
                )}
              </div>
            </div>
          </div>
        ))}
      </div>

      {/* Recomendaciones */}
      {Object.values(testResults).some(r => r.status === 'error') && (
        <div className="mt-6 p-4 bg-blue-50 border border-blue-200 rounded-lg">
          <h4 className="font-medium text-blue-900 mb-2">💡 Recomendaciones:</h4>
          <ul className="text-sm text-blue-800 space-y-1">
            <li>• Verifica la configuración de Supabase en <code>src/api/supabaseClient.js</code></li>
            <li>• Asegúrate de que las tablas existan en tu base de datos</li>
            <li>• Revisa los permisos RLS (Row Level Security) si están habilitados</li>
            <li>• El sistema funcionará con datos simulados mientras se resuelven los problemas</li>
          </ul>
        </div>
      )}
    </div>
  );
};

export default ConnectionTestPanel;
