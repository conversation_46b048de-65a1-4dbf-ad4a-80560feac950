import React, { useState, useContext } from 'react';
import <PERSON>Header from '../ui/PageHeader';
import FilterPanel from './FilterPanel';
import DashboardToolbar from './DashboardToolbar';
import DashboardViewSelector from './DashboardViewSelector';
import { DashboardContext } from '../../context/DashboardContext';
import { useDashboardExport } from '../../hooks/useDashboardExport';
// Vistas del dashboard
import {
  ExecutiveView,
  KpisView,
  GeneralView,
  TrendsView,
  ComparativeView,
  StatisticalView,
  ExportView
} from './views';
import AptitudeAnalysisView from './views/AptitudeAnalysisView';
import FactorAnalysisView from './views/FactorAnalysisView';
import SyncTestView from './views/enhanced/SyncTestView';
import {
  FaChartPie,
  FaChartLine,
  FaTachometerAlt,
  FaProjectDiagram,
  FaBalanceScale,
  FaDownload,
  FaShare,
  FaRedo,
  Fa<PERSON><PERSON><PERSON>,
  Fa<PERSON><PERSON>,
  FaBrain
} from 'react-icons/fa';
import { toast } from 'react-toastify';

/**
 * Contenido principal del Dashboard.
 * Consume el DashboardContext para obtener datos y funciones.
 */
const DashboardContent = () => {
  const [selectedView, setSelectedView] = useState('executive');
  const [showFilters, setShowFilters] = useState(false);

  // Consumir datos y funciones del contexto
  const { data, loading, error, lastUpdated, refetch, applyFilters, filters } = useContext(DashboardContext);
  const { exportAsPDF, exportAsExcel } = useDashboardExport(data);

  // Configuración de vistas del dashboard (se mantiene igual)
  const dashboardViews = [
    { key: 'executive', label: 'Resumen Ejecutivo', description: 'Vista estratégica con hallazgos clave', icon: FaChartPie },
    { key: 'aptitudes', label: 'Análisis de Aptitudes', description: 'Exploración detallada por aptitud', icon: FaBrain },
    { key: 'factors', label: 'Análisis de Factores', description: 'Análisis de g, Gf y Gc', icon: FaProjectDiagram },
    { key: 'comparative', label: 'Análisis Comparativo', description: 'Benchmarking y comparaciones', icon: FaBalanceScale },
    { key: 'export', label: 'Exportación', description: 'Reportes y presentaciones', icon: FaDownload }
  ];

  const handleFiltersChange = (newFilters) => {
    applyFilters(newFilters);
    setShowFilters(false);
  };

  const handleShareDashboard = () => {
    const url = window.location.href;
    navigator.clipboard.writeText(url).then(() => {
      toast.success('URL copiada al portapapeles');
    }).catch(() => {
      toast.error('Error al copiar URL');
    });
  };

  const handleExport = (format) => {
    if (format === 'pdf') exportAsPDF();
    else if (format === 'excel') exportAsExcel();
    else toast.warn(`Formato de exportación '${format}' no soportado.`);
  };

  const renderCurrentView = () => {
    // Los datos ahora vienen directamente del contexto
    const { kpiData, aptitudeData, factorData } = data || {};

    switch (selectedView) {
      case 'executive':
        return <ExecutiveView loading={loading} kpiData={kpiData} />;
      // Nuevas vistas se añadirán aquí
      case 'aptitudes':
        return <AptitudeAnalysisView />;
      case 'factors':
        return <FactorAnalysisView />;
      case 'comparative':
        return <ComparativeView loading={loading} data={data} />;
      case 'export':
        return <ExportView loading={loading} onExport={handleExport} />;
      default:
        return <ExecutiveView loading={loading} kpiData={kpiData} />;
    }
  };

  return (
    <div className="min-h-screen bg-gray-50">
      <PageHeader
        title="📊 Dashboard de Análisis BAT-7"
        subtitle="Plataforma de análisis multidimensional"
      />
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <DashboardToolbar
          lastUpdated={lastUpdated}
          loading={loading}
          onRefresh={refetch}
          onShare={handleShareDashboard}
          onShowFilters={() => setShowFilters(true)}
        />
        <DashboardViewSelector
          views={dashboardViews}
          selectedView={selectedView}
          onSelectView={setSelectedView}
        />
        <div className="mb-8">
          {error && (
            <div className="mb-6 bg-red-50 border-l-4 border-red-400 p-4 rounded">
              <p className="text-sm text-red-700"><strong>Error:</strong> {error}</p>
            </div>
          )}
          {renderCurrentView()}
        </div>
        <FilterPanel
          isOpen={showFilters}
          onClose={() => setShowFilters(false)}
          onFiltersChange={handleFiltersChange}
          initialFilters={filters}
        />
      </div>
    </div>
  );
};

export default DashboardContent;