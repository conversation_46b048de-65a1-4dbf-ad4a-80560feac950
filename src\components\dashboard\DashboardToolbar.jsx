import React from 'react';
import PropTypes from 'prop-types';
import { Fa<PERSON>lock, Fa<PERSON><PERSON><PERSON>, FaRedo, FaShare } from 'react-icons/fa';
import { formatLastUpdated } from '../../utils/dateUtils';

/**
 * Barra de herramientas del dashboard con controles de actualización y filtros
 * @param {Object} props - Propiedades del componente
 * @param {Date|string|null} props.lastUpdated - Fecha de última actualización
 * @param {Function} props.onShowFilters - Callback para mostrar filtros
 * @param {Function} props.onRefresh - Callback para actualizar datos
 * @param {Function} props.onShare - Callback para compartir dashboard
 * @param {boolean} props.loading - Estado de carga
 */
const DashboardToolbar = ({
  lastUpdated = null,
  onShowFilters,
  onRefresh,
  onShare,
  loading = false
}) => {
  return (
    <div className="mb-8 flex flex-col lg:flex-row lg:items-center lg:justify-between gap-4">
      {/* Información de actualización */}
      <div className="flex items-center text-sm text-gray-600">
        <FaClock className="h-4 w-4 mr-2" />
        <span>
          {lastUpdated ? `Última actualización: ${formatLastUpdated(lastUpdated)}` : 'Cargando datos...'}
        </span>
      </div>

      {/* Controles */}
      <div className="flex items-center gap-3">
        <button
          onClick={onShowFilters}
          className="flex items-center px-4 py-2 bg-white border border-gray-300 rounded-lg hover:bg-gray-50 transition-colors"
        >
          <FaFilter className="h-4 w-4 mr-2" />
          Filtros
        </button>
        
        <button
          onClick={onRefresh}
          disabled={loading}
          className="flex items-center px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:opacity-50 transition-colors"
        >
          <FaRedo className={`h-4 w-4 mr-2 ${loading ? 'animate-spin' : ''}`} />
          Actualizar
        </button>
        
        <button
          onClick={onShare}
          className="flex items-center px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors"
        >
          <FaShare className="h-4 w-4 mr-2" />
          Compartir
        </button>
      </div>
    </div>
  );
};

DashboardToolbar.propTypes = {
  lastUpdated: PropTypes.oneOfType([
    PropTypes.instanceOf(Date),
    PropTypes.string
  ]),
  onShowFilters: PropTypes.func.isRequired,
  onRefresh: PropTypes.func.isRequired,
  onShare: PropTypes.func.isRequired,
  loading: PropTypes.bool
};

// defaultProps removido - usando parámetros por defecto en la función

export default DashboardToolbar;