import React from 'react';

const DashboardViewSelector = ({ views, selectedView, onSelectView }) => {
  return (
    <div className="mb-8">
      <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-3">
        {views.map((view) => (
          <button
            key={view.key}
            onClick={() => onSelectView(view.key)}
            className={`p-4 rounded-lg border-2 transition-all duration-200 text-left ${
              selectedView === view.key
                ? 'border-blue-500 bg-blue-50 text-blue-700'
                : 'border-gray-200 bg-white hover:border-blue-300 hover:bg-blue-50'
            }`}
          >
            <div className="flex items-center mb-2">
              <view.icon className="h-5 w-5 mr-2" />
              <span className="font-medium text-sm">{view.label}</span>
            </div>
            <p className="text-xs text-gray-600 leading-tight">
              {view.description}
            </p>
          </button>
        ))}
      </div>
    </div>
  );
};

export default DashboardViewSelector;