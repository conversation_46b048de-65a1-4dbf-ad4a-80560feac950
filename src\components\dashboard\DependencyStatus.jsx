import React from 'react';
import { FaCheckCircle, FaExclamationTriangle, FaInfoCircle, FaDownload } from 'react-icons/fa';

/**
 * Componente para mostrar el estado de las dependencias del Dashboard BAT-7
 */
const DependencyStatus = () => {
  const dependencies = [
    {
      name: 'Chart.js',
      package: 'chart.js react-chartjs-2',
      status: 'missing',
      description: 'Gráficos interactivos (líneas, radar, barras, scatter)',
      impact: 'Los gráficos se muestran como placeholders'
    },
    {
      name: 'PDF Export',
      package: 'jspdf html2canvas',
      status: 'missing',
      description: 'Exportación de informes en PDF',
      impact: 'Se exportan como archivos de texto'
    },
    {
      name: 'Crypto.js',
      package: 'crypto-js',
      status: 'missing',
      description: 'Firmas digitales y validación criptográfica',
      impact: 'Se usa implementación temporal de hash'
    },
    {
      name: 'QR Code',
      package: 'qrcode',
      status: 'optional',
      description: 'Códigos QR para validación de informes',
      impact: 'Funcionalidad opcional'
    }
  ];

  const getStatusIcon = (status) => {
    switch (status) {
      case 'installed':
        return <FaCheckCircle className="text-green-500" />;
      case 'missing':
        return <FaExclamationTriangle className="text-yellow-500" />;
      case 'optional':
        return <FaInfoCircle className="text-blue-500" />;
      default:
        return <FaExclamationTriangle className="text-gray-500" />;
    }
  };

  const getStatusColor = (status) => {
    switch (status) {
      case 'installed':
        return 'bg-green-50 border-green-200';
      case 'missing':
        return 'bg-yellow-50 border-yellow-200';
      case 'optional':
        return 'bg-blue-50 border-blue-200';
      default:
        return 'bg-gray-50 border-gray-200';
    }
  };

  const getStatusText = (status) => {
    switch (status) {
      case 'installed':
        return 'Instalado';
      case 'missing':
        return 'Faltante';
      case 'optional':
        return 'Opcional';
      default:
        return 'Desconocido';
    }
  };

  const missingDependencies = dependencies.filter(dep => dep.status === 'missing');
  const installCommand = missingDependencies.length > 0 
    ? `npm install ${missingDependencies.map(dep => dep.package).join(' ')}`
    : '';

  return (
    <div className="bg-white rounded-lg shadow-lg border border-gray-200 p-6 max-w-4xl mx-auto">
      <div className="flex items-center mb-6">
        <FaDownload className="text-blue-500 text-xl mr-3" />
        <h2 className="text-xl font-semibold text-gray-800">Estado de Dependencias - Dashboard BAT-7</h2>
      </div>

      {/* Resumen */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-6">
        <div className="bg-green-50 border border-green-200 rounded-lg p-4 text-center">
          <div className="text-2xl font-bold text-green-600">
            {dependencies.filter(d => d.status === 'installed').length}
          </div>
          <div className="text-sm text-green-700">Instaladas</div>
        </div>
        <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4 text-center">
          <div className="text-2xl font-bold text-yellow-600">
            {dependencies.filter(d => d.status === 'missing').length}
          </div>
          <div className="text-sm text-yellow-700">Faltantes</div>
        </div>
        <div className="bg-blue-50 border border-blue-200 rounded-lg p-4 text-center">
          <div className="text-2xl font-bold text-blue-600">
            {dependencies.filter(d => d.status === 'optional').length}
          </div>
          <div className="text-sm text-blue-700">Opcionales</div>
        </div>
      </div>

      {/* Lista de dependencias */}
      <div className="space-y-4 mb-6">
        {dependencies.map((dep, index) => (
          <div key={index} className={`border rounded-lg p-4 ${getStatusColor(dep.status)}`}>
            <div className="flex items-start justify-between">
              <div className="flex items-start">
                <div className="mr-3 mt-1">
                  {getStatusIcon(dep.status)}
                </div>
                <div className="flex-1">
                  <div className="flex items-center mb-1">
                    <h3 className="font-semibold text-gray-800 mr-2">{dep.name}</h3>
                    <span className={`px-2 py-1 text-xs rounded-full ${
                      dep.status === 'installed' ? 'bg-green-100 text-green-700' :
                      dep.status === 'missing' ? 'bg-yellow-100 text-yellow-700' :
                      'bg-blue-100 text-blue-700'
                    }`}>
                      {getStatusText(dep.status)}
                    </span>
                  </div>
                  <p className="text-sm text-gray-600 mb-1">{dep.description}</p>
                  <p className="text-xs text-gray-500">
                    <strong>Impacto:</strong> {dep.impact}
                  </p>
                  <code className="text-xs bg-gray-100 px-2 py-1 rounded mt-2 inline-block">
                    {dep.package}
                  </code>
                </div>
              </div>
            </div>
          </div>
        ))}
      </div>

      {/* Comando de instalación */}
      {missingDependencies.length > 0 && (
        <div className="bg-gray-50 border border-gray-200 rounded-lg p-4">
          <h3 className="font-semibold text-gray-800 mb-2 flex items-center">
            <FaDownload className="mr-2 text-blue-500" />
            Comando de Instalación
          </h3>
          <div className="bg-gray-800 text-green-400 p-3 rounded font-mono text-sm overflow-x-auto">
            {installCommand}
          </div>
          <p className="text-xs text-gray-600 mt-2">
            Ejecuta este comando en la terminal para instalar todas las dependencias faltantes.
          </p>
        </div>
      )}

      {/* Instrucciones post-instalación */}
      <div className="bg-blue-50 border border-blue-200 rounded-lg p-4 mt-4">
        <h3 className="font-semibold text-blue-800 mb-2 flex items-center">
          <FaInfoCircle className="mr-2" />
          Instrucciones Post-Instalación
        </h3>
        <div className="text-sm text-blue-700 space-y-1">
          <p>1. Instala las dependencias usando el comando de arriba</p>
          <p>2. Descomenta las importaciones en los archivos de componentes</p>
          <p>3. Reinicia el servidor de desarrollo: <code className="bg-blue-100 px-1 rounded">npm run dev</code></p>
          <p>4. Consulta el archivo <code className="bg-blue-100 px-1 rounded">INSTALL_DEPENDENCIES.md</code> para instrucciones detalladas</p>
        </div>
      </div>

      {/* Estado actual */}
      <div className="mt-6 p-4 bg-gradient-to-r from-blue-500 to-purple-600 text-white rounded-lg">
        <h3 className="font-semibold mb-2">🚀 Estado Actual del Dashboard</h3>
        <div className="text-sm space-y-1">
          <p>✅ <strong>Funcional:</strong> Filtros, vista individual, interpretaciones, plantillas</p>
          <p>⚠️ <strong>Limitado:</strong> Gráficos (placeholders), PDF (texto), firmas (temporal)</p>
          <p>🎯 <strong>Objetivo:</strong> Sistema profesional completo tras instalar dependencias</p>
        </div>
      </div>
    </div>
  );
};

export default DependencyStatus;
