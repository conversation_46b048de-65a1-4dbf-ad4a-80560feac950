import React, { useState } from 'react';
import { FaDownload, FaFilePdf, FaFileExcel, FaFilePowerpoint, FaShare, FaEnvelope } from 'react-icons/fa';

const ExecutiveExport = ({ data, onExport }) => {
  const [isExporting, setIsExporting] = useState(false);
  const [exportType, setExportType] = useState('pdf');

  const exportOptions = [
    {
      type: 'pdf',
      label: 'Reporte Ejecutivo PDF',
      icon: FaFilePdf,
      color: 'text-red-600',
      description: 'Informe completo para presentación ejecutiva'
    },
    {
      type: 'excel',
      label: 'Datos Excel',
      icon: FaFileExcel,
      color: 'text-green-600',
      description: 'Datos detallados para análisis adicional'
    },
    {
      type: 'powerpoint',
      label: 'Presentación PowerPoint',
      icon: FaFilePowerpoint,
      color: 'text-orange-600',
      description: 'Slides listos para junta directiva'
    }
  ];

  const handleExport = async (type) => {
    setIsExporting(true);
    try {
      await onExport(type, data);
    } catch (error) {
      console.error('Error en exportación:', error);
    } finally {
      setIsExporting(false);
    }
  };

  const handleShare = () => {
    const shareData = {
      title: 'Dashboard Ejecutivo BAT-7',
      text: 'Análisis completo de resultados institucionales',
      url: window.location.href
    };

    if (navigator.share) {
      navigator.share(shareData);
    } else {
      // Fallback: copiar al portapapeles
      navigator.clipboard.writeText(window.location.href);
      alert('URL copiada al portapapeles');
    }
  };

  return (
    <div className="bg-white rounded-lg shadow-lg p-6">
      <h3 className="text-lg font-semibold text-gray-800 mb-4 flex items-center">
        <FaDownload className="h-5 w-5 mr-2 text-blue-600" />
        Exportación Ejecutiva
      </h3>

      <div className="space-y-4">
        {/* Opciones de exportación */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          {exportOptions.map((option) => (
            <div
              key={option.type}
              className={`border rounded-lg p-4 cursor-pointer transition-all duration-200 hover:shadow-md ${
                exportType === option.type ? 'border-blue-500 bg-blue-50' : 'border-gray-200 hover:border-gray-300'
              }`}
              onClick={() => setExportType(option.type)}
            >
              <div className="flex items-center mb-2">
                <option.icon className={`h-6 w-6 mr-2 ${option.color}`} />
                <h4 className="font-medium text-gray-800">{option.label}</h4>
              </div>
              <p className="text-sm text-gray-600">{option.description}</p>
            </div>
          ))}
        </div>

        {/* Botones de acción */}
        <div className="flex flex-wrap gap-3 pt-4 border-t border-gray-200">
          <button
            onClick={() => handleExport(exportType)}
            disabled={isExporting}
            className="flex items-center px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
          >
            <FaDownload className="h-4 w-4 mr-2" />
            {isExporting ? 'Exportando...' : 'Exportar'}
          </button>

          <button
            onClick={handleShare}
            className="flex items-center px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors"
          >
            <FaShare className="h-4 w-4 mr-2" />
            Compartir
          </button>

          <button
            onClick={() => handleExport('email')}
            className="flex items-center px-4 py-2 bg-purple-600 text-white rounded-lg hover:bg-purple-700 transition-colors"
          >
            <FaEnvelope className="h-4 w-4 mr-2" />
            Enviar por Email
          </button>
        </div>

        {/* Configuraciones adicionales */}
        <div className="bg-gray-50 rounded-lg p-4">
          <h4 className="font-medium text-gray-800 mb-3">Configuraciones de Exportación</h4>
          <div className="space-y-2">
            <label className="flex items-center">
              <input type="checkbox" defaultChecked className="mr-2" />
              <span className="text-sm text-gray-700">Incluir gráficos y visualizaciones</span>
            </label>
            <label className="flex items-center">
              <input type="checkbox" defaultChecked className="mr-2" />
              <span className="text-sm text-gray-700">Incluir análisis de tendencias</span>
            </label>
            <label className="flex items-center">
              <input type="checkbox" defaultChecked className="mr-2" />
              <span className="text-sm text-gray-700">Incluir recomendaciones</span>
            </label>
            <label className="flex items-center">
              <input type="checkbox" className="mr-2" />
              <span className="text-sm text-gray-700">Incluir datos detallados por estudiante</span>
            </label>
          </div>
        </div>
      </div>
    </div>
  );
};

export default ExecutiveExport;