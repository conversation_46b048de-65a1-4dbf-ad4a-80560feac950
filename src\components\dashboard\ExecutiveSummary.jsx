import React from 'react';
import { FaChartLine, FaUsers, FaExclamationCircle, FaLightbulb, FaTrophy, FaFlag } from 'react-icons/fa';


const KPICard = ({ title, value, change, isPositive }) => (
  <div className="bg-white bg-opacity-20 rounded-lg p-4">
    <h3 className="text-lg font-semibold mb-2">{title}</h3>
    <div className="flex items-baseline mb-2">
      <span className="text-3xl font-bold mr-2">{value}</span>
    </div>
    {change && (
      <span className={`text-sm px-2 py-1 rounded ${isPositive ? 'bg-green-500' : 'bg-red-500'}`}>
        {change}
      </span>
    )}
  </div>
);

const FindingCard = ({ icon: Icon, title, message, impact }) => {
  const getImpactColor = () => {
    switch (impact) {
      case 'positive': return 'border-green-200 bg-green-50 text-green-800';
      case 'warning': return 'border-yellow-200 bg-yellow-50 text-yellow-800';
      case 'opportunity': return 'border-blue-200 bg-blue-50 text-blue-800';
      default: return 'border-gray-200 bg-gray-50 text-gray-800';
    }
  };

  return (
    <div className={`border rounded-lg p-4 ${getImpactColor()}`}>
      <div className="flex items-start">
        <Icon className="h-5 w-5 mr-3 mt-1" />
        <div>
          <h3 className="font-semibold mb-2">{title}</h3>
          <p className="text-sm leading-relaxed">{message}</p>
        </div>
      </div>
    </div>
  );
};

const ExecutiveSummary = ({ kpis, aptitudeData }) => {
  if (!kpis || !aptitudeData) {
    return <div className="text-center p-4">No hay datos disponibles para el resumen ejecutivo.</div>;
  }

  // Lógica para generar hallazgos dinámicamente
  const generateKeyFindings = () => {
    const findings = [];
    const aptitudes = Object.entries(aptitudeData);
    
    if (aptitudes.length > 0) {
      const [bestAptitude, bestScore] = aptitudes.reduce((max, item) => item[1] > max[1] ? item : max);
      const [worstAptitude, worstScore] = aptitudes.reduce((min, item) => item[1] < min[1] ? item : min);

      findings.push({
        icon: FaTrophy,
        title: 'Fortaleza Principal',
        message: `La aptitud '${bestAptitude}' muestra el rendimiento más alto con un puntaje promedio de ${bestScore.toFixed(1)}.`,
        impact: 'positive',
      });

      findings.push({
        icon: FaExclamationCircle,
        title: 'Área de Oportunidad',
        message: `La aptitud '${worstAptitude}' presenta el puntaje más bajo (${worstScore.toFixed(1)}), sugiriendo un área clave para intervención.`,
        impact: 'warning',
      });
    }

    if (kpis.averageGFactor > 100) {
      findings.push({
        icon: FaLightbulb,
        title: 'Inteligencia General Sólida',
        message: `El Factor G promedio de ${kpis.averageGFactor.toFixed(1)} indica una capacidad cognitiva general por encima de la media.`,
        impact: 'opportunity',
      });
    }

    return findings;
  };

  const keyFindings = generateKeyFindings();

  return (
    <div className="space-y-6">
      {/* Resumen Ejecutivo Principal */}
      <div className="bg-gradient-to-r from-blue-600 to-purple-600 text-white rounded-lg shadow-lg p-8">
        <h1 className="text-3xl font-bold mb-4">Resumen Ejecutivo</h1>
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
          <KPICard title="Total Evaluados" value={kpis.totalEvaluated} />
          <KPICard title="Factor G Promedio" value={kpis.averageGFactor.toFixed(1)} />
          <KPICard title="Nuevas Evaluaciones" value={kpis.newEvaluations} />
        </div>
      </div>

      {/* Narrativa Automática */}
      <div className="bg-white rounded-lg shadow-lg p-6">
        <h2 className="text-2xl font-bold text-gray-800 mb-6 flex items-center">
          <FaFlag className="h-6 w-6 mr-3 text-blue-600" />
          Hallazgos Clave del Período
        </h2>
        <div className="space-y-4">
          {keyFindings.map((finding, index) => (
            <FindingCard key={index} {...finding} />
          ))}
        </div>
      </div>
    </div>
  );
};

export default ExecutiveSummary;