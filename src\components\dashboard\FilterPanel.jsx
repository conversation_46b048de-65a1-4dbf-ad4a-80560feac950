import React, { useState, useEffect } from 'react';
import { FaFilter, FaTimes, FaCalendarAlt, FaUniversity, FaGraduationCap, FaVenusMars, FaBrain, FaUser, FaChalkboardTeacher, FaChild, FaUserGraduate } from 'react-icons/fa';
import { usePatientsList, usePsychologistsList, useRealDataConnection } from '../../hooks/useRealDataConnection';
import { Card, CardBody } from '../ui/Card'; // Asegúrate de que la ruta sea correcta

const FilterPanel = ({ isOpen, onClose, onFiltersChange, loading = false }) => {
  // Hooks para datos reales
  const { patients } = usePatientsList();
  const { psychologists } = usePsychologistsList();
  const { isRealDataAvailable, connectionStatus } = useRealDataConnection();

  const [isExpanded, setIsExpanded] = useState(false);
  const [localFilters, setLocalFilters] = useState({
    dateRange: { start: null, end: null },
    institution: null,
    educationalLevel: null,
    gender: null,
    aptitude: null,
    // Nuevos filtros específicos de BAT-7
    nivelAplicacion: null, // E, M, S
    categoriaEvaluado: null, // Escolares/Adultos
    rangoEdad: { min: null, max: null },
    cursoGrado: null,
    psicologo: null,
    pacienteEspecifico: null // Para análisis individual
  });

  // Mock data for available options - in a real app, this would come from the API
  const availableOptions = {
    institutions: ['1', '2', '3'],
    educationalLevels: ['E', 'M', 'S'],
    genders: ['M', 'F'],
    aptitudes: [
      { id: 'V', codigo: 'V', nombre: 'Verbal' },
      { id: 'E', codigo: 'E', nombre: 'Espacial' },
      { id: 'A', codigo: 'A', nombre: 'Atención' },
      { id: 'R', codigo: 'R', nombre: 'Razonamiento' },
      { id: 'N', codigo: 'N', nombre: 'Numérica' },
      { id: 'M', codigo: 'M', nombre: 'Mecánica' },
      { id: 'O', codigo: 'O', nombre: 'Ortografía' }
    ]
  };

  const levelNames = {
    'E': 'Elemental',
    'M': 'Medio',
    'S': 'Superior'
  };

  const genderNames = {
    'M': 'Masculino',
    'F': 'Femenino'
  };

  const presets = [
    { id: 'last-7-days', name: 'Últimos 7 días', icon: 'fas fa-calendar-week' },
    { id: 'last-30-days', name: 'Últimos 30 días', icon: 'fas fa-calendar-alt' },
    { id: 'this-month', name: 'Este mes', icon: 'fas fa-calendar' },
    { id: 'elementary-only', name: 'Solo Elemental', icon: 'fas fa-child' },
    { id: 'middle-only', name: 'Solo Medio', icon: 'fas fa-user-graduate' },
    { id: 'high-only', name: 'Solo Superior', icon: 'fas fa-graduation-cap' },
    { id: 'individual-analysis', name: 'Análisis Individual', icon: 'fas fa-user' }
  ];

  // Opciones para los nuevos filtros
  const nivelesAplicacion = [
    { value: 'E', label: 'Elemental (E)', description: 'Nivel básico de aplicación' },
    { value: 'M', label: 'Medio (M)', description: 'Nivel intermedio de aplicación' },
    { value: 'S', label: 'Superior (S)', description: 'Nivel avanzado de aplicación' }
  ];

  const categoriasEvaluado = [
    { value: 'escolares', label: 'Escolares', description: 'Estudiantes en edad escolar' },
    { value: 'adultos', label: 'Adultos', description: 'Población adulta' }
  ];

  const cursosGrados = [
    { value: '1-primaria', label: '1° Primaria' },
    { value: '2-primaria', label: '2° Primaria' },
    { value: '3-primaria', label: '3° Primaria' },
    { value: '4-primaria', label: '4° Primaria' },
    { value: '5-primaria', label: '5° Primaria' },
    { value: '6-primaria', label: '6° Primaria' },
    { value: '1-secundaria', label: '1° Secundaria' },
    { value: '2-secundaria', label: '2° Secundaria' },
    { value: '3-secundaria', label: '3° Secundaria' },
    { value: 'bachillerato', label: 'Bachillerato' },
    { value: 'universidad', label: 'Universidad' },
    { value: 'posgrado', label: 'Posgrado' }
  ];

  // Helper functions
  const updateFilter = (key, value) => {
    const newFilters = { ...localFilters, [key]: value };
    setLocalFilters(newFilters);
    if (onFiltersChange) {
      onFiltersChange(newFilters);
    }
  };

  const updateDateRange = (start, end) => {
    const newFilters = { 
      ...localFilters, 
      dateRange: { start, end } 
    };
    setLocalFilters(newFilters);
    if (onFiltersChange) {
      onFiltersChange(newFilters);
    }
  };

  const clearFilters = () => {
    const emptyFilters = {
      dateRange: { start: null, end: null },
      institution: null,
      educationalLevel: null,
      gender: null,
      aptitude: null
    };
    setLocalFilters(emptyFilters);
    if (onFiltersChange) {
      onFiltersChange(emptyFilters);
    }
  };

  const applyPreset = (presetId) => {
    const today = new Date();
    let newFilters = { ...localFilters };

    switch (presetId) {
      case 'last-7-days':
        const sevenDaysAgo = new Date(today);
        sevenDaysAgo.setDate(today.getDate() - 7);
        newFilters.dateRange = {
          start: sevenDaysAgo.toISOString().split('T')[0],
          end: today.toISOString().split('T')[0]
        };
        break;
      case 'last-30-days':
        const thirtyDaysAgo = new Date(today);
        thirtyDaysAgo.setDate(today.getDate() - 30);
        newFilters.dateRange = {
          start: thirtyDaysAgo.toISOString().split('T')[0],
          end: today.toISOString().split('T')[0]
        };
        break;
      case 'this-month':
        const firstDay = new Date(today.getFullYear(), today.getMonth(), 1);
        newFilters.dateRange = {
          start: firstDay.toISOString().split('T')[0],
          end: today.toISOString().split('T')[0]
        };
        break;
      case 'elementary-only':
        newFilters.educationalLevel = 'E';
        break;
      case 'middle-only':
        newFilters.educationalLevel = 'M';
        break;
      case 'high-only':
        newFilters.educationalLevel = 'S';
        break;
    }

    setLocalFilters(newFilters);
    if (onFiltersChange) {
      onFiltersChange(newFilters);
    }
  };

  const hasActiveFilters = () => {
    return (
      (localFilters.dateRange.start && localFilters.dateRange.end) ||
      localFilters.institution ||
      localFilters.educationalLevel ||
      localFilters.gender ||
      localFilters.aptitude
    );
  };

  const getActiveFiltersCount = () => {
    let count = 0;
    if (localFilters.dateRange.start && localFilters.dateRange.end) count++;
    if (localFilters.institution) count++;
    if (localFilters.educationalLevel) count++;
    if (localFilters.gender) count++;
    if (localFilters.aptitude) count++;
    return count;
  };

  // Don't render if not open
  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-white rounded-lg shadow-xl max-w-4xl w-full mx-4 max-h-[90vh] overflow-y-auto">
        <Card className="mb-0">
          <CardBody className="p-6">
        {/* Header del filtro */}
        <div className="flex items-center justify-between mb-4">
          <div className="flex items-center">
            <FaFilter className="text-blue-500 mr-2" />
            <h3 className="text-lg font-semibold text-gray-800">Filtros</h3>
            {hasActiveFilters() && (
              <span className="ml-2 bg-blue-500 text-white text-xs px-2 py-1 rounded-full">
                {getActiveFiltersCount()}
              </span>
            )}

            {/* Indicador de estado de conexión */}
            <div className="ml-3 flex items-center">
              <div className={`w-2 h-2 rounded-full mr-1 ${
                connectionStatus === 'connected' ? 'bg-green-500' :
                connectionStatus === 'simulated' ? 'bg-yellow-500' :
                connectionStatus === 'checking' ? 'bg-blue-500 animate-pulse' :
                'bg-red-500'
              }`}></div>
              <span className="text-xs text-gray-500">
                {connectionStatus === 'connected' ? 'Datos reales' :
                 connectionStatus === 'simulated' ? 'Datos simulados' :
                 connectionStatus === 'checking' ? 'Verificando...' :
                 'Sin conexión'}
              </span>
            </div>
          </div>
          
          <div className="flex items-center space-x-2">
            {hasActiveFilters() && (
              <button
                onClick={clearFilters}
                className="text-red-500 hover:text-red-700 text-sm font-medium transition-colors duration-200"
                title="Limpiar filtros"
              >
                <FaTimes className="mr-1" />
                Limpiar
              </button>
            )}
            
            <button
              onClick={() => setIsExpanded(!isExpanded)}
              className="text-blue-500 hover:text-blue-700 text-sm font-medium transition-colors duration-200"
            >
              {isExpanded ? 'Ocultar' : 'Mostrar'} filtros
            </button>

            <button
              onClick={onClose}
              className="text-gray-500 hover:text-gray-700 text-lg transition-colors duration-200"
              title="Cerrar"
            >
              <FaTimes />
            </button>
          </div>
        </div>

        {/* Presets rápidos */}
        <div className="mb-4">
          <h4 className="text-sm font-medium text-gray-700 mb-2">Filtros rápidos:</h4>
          <div className="flex flex-wrap gap-2">
            {presets.map(preset => (
              <button
                key={preset.id}
                onClick={() => applyPreset(preset.id)}
                className="flex items-center px-3 py-1 bg-gray-100 hover:bg-gray-200 text-gray-700 text-sm rounded-lg transition-colors duration-200"
                title={preset.name}
              >
                <i className={`${preset.icon} mr-1`}></i>
                {preset.name}
              </button>
            ))}
          </div>
        </div>

        {/* Filtros expandidos */}
        {isExpanded && (
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 pt-4 border-t border-gray-200 animate-slideInFromLeft">
            {/* Filtro de fecha */}
            <div>
              <label className="flex items-center text-sm font-medium text-gray-700 mb-2">
                <FaCalendarAlt className="mr-2 text-blue-500" />
                Rango de fechas
              </label>
              <div className="space-y-2">
                <input
                  type="date"
                  value={localFilters.dateRange.start || ''}
                  onChange={(e) => updateDateRange(e.target.value, localFilters.dateRange.end)}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 text-sm"
                  placeholder="Fecha inicio"
                />
                <input
                  type="date"
                  value={localFilters.dateRange.end || ''}
                  onChange={(e) => updateDateRange(localFilters.dateRange.start, e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 text-sm"
                  placeholder="Fecha fin"
                />
              </div>
            </div>

            {/* Filtro de institución */}
            <div>
              <label className="flex items-center text-sm font-medium text-gray-700 mb-2">
                <FaUniversity className="mr-2 text-green-500" />
                Institución
              </label>
              <select
                value={localFilters.institution || ''}
                onChange={(e) => updateFilter('institution', e.target.value || null)}
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 text-sm"
                disabled={loading}
              >
                <option value="">Todas las instituciones</option>
                {availableOptions.institutions.map(institution => (
                  <option key={institution} value={institution}>
                    Institución {institution}
                  </option>
                ))}
              </select>
            </div>

            {/* Filtro de nivel educativo */}
            <div>
              <label className="flex items-center text-sm font-medium text-gray-700 mb-2">
                <FaGraduationCap className="mr-2 text-purple-500" />
                Nivel educativo
              </label>
              <select
                value={localFilters.educationalLevel || ''}
                onChange={(e) => updateFilter('educationalLevel', e.target.value || null)}
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 text-sm"
              >
                <option value="">Todos los niveles</option>
                {availableOptions.educationalLevels.map(level => (
                  <option key={level} value={level}>
                    {levelNames[level] || level}
                  </option>
                ))}
              </select>
            </div>

            {/* Filtro de género */}
            <div>
              <label className="flex items-center text-sm font-medium text-gray-700 mb-2">
                <FaVenusMars className="mr-2 text-pink-500" />
                Género
              </label>
              <select
                value={localFilters.gender || ''}
                onChange={(e) => updateFilter('gender', e.target.value || null)}
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 text-sm"
              >
                <option value="">Todos los géneros</option>
                {availableOptions.genders.map(gender => (
                  <option key={gender} value={gender}>
                    {genderNames[gender] || gender}
                  </option>
                ))}
              </select>
            </div>

            {/* Filtro de aptitud */}
            <div>
              <label className="flex items-center text-sm font-medium text-gray-700 mb-2">
                <FaBrain className="mr-2 text-orange-500" />
                Aptitud específica
              </label>
              <select
                value={localFilters.aptitude || ''}
                onChange={(e) => updateFilter('aptitude', e.target.value || null)}
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 text-sm"
                disabled={loading}
              >
                <option value="">Todas las aptitudes</option>
                {availableOptions.aptitudes.map(aptitude => (
                  <option key={aptitude.id} value={aptitude.id}>
                    {aptitude.codigo} - {aptitude.nombre}
                  </option>
                ))}
              </select>
            </div>

            {/* Filtro de Nivel de Aplicación BAT-7 */}
            <div>
              <label className="flex items-center text-sm font-medium text-gray-700 mb-2">
                <FaGraduationCap className="mr-2 text-indigo-500" />
                Nivel BAT-7
              </label>
              <select
                value={localFilters.nivelAplicacion || ''}
                onChange={(e) => updateFilter('nivelAplicacion', e.target.value || null)}
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 text-sm"
              >
                <option value="">Todos los niveles</option>
                {nivelesAplicacion.map(nivel => (
                  <option key={nivel.value} value={nivel.value} title={nivel.description}>
                    {nivel.label}
                  </option>
                ))}
              </select>
            </div>

            {/* Filtro de Categoría de Evaluado */}
            <div>
              <label className="flex items-center text-sm font-medium text-gray-700 mb-2">
                <FaChild className="mr-2 text-yellow-500" />
                Categoría
              </label>
              <select
                value={localFilters.categoriaEvaluado || ''}
                onChange={(e) => updateFilter('categoriaEvaluado', e.target.value || null)}
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 text-sm"
              >
                <option value="">Todas las categorías</option>
                {categoriasEvaluado.map(categoria => (
                  <option key={categoria.value} value={categoria.value} title={categoria.description}>
                    {categoria.label}
                  </option>
                ))}
              </select>
            </div>

            {/* Filtro de Rango de Edad */}
            <div>
              <label className="flex items-center text-sm font-medium text-gray-700 mb-2">
                <FaUser className="mr-2 text-teal-500" />
                Rango de edad
              </label>
              <div className="space-y-2">
                <input
                  type="number"
                  value={localFilters.rangoEdad.min || ''}
                  onChange={(e) => updateFilter('rangoEdad', { ...localFilters.rangoEdad, min: e.target.value ? parseInt(e.target.value) : null })}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 text-sm"
                  placeholder="Edad mínima"
                  min="0"
                  max="100"
                />
                <input
                  type="number"
                  value={localFilters.rangoEdad.max || ''}
                  onChange={(e) => updateFilter('rangoEdad', { ...localFilters.rangoEdad, max: e.target.value ? parseInt(e.target.value) : null })}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 text-sm"
                  placeholder="Edad máxima"
                  min="0"
                  max="100"
                />
              </div>
            </div>

            {/* Filtro de Curso/Grado */}
            <div>
              <label className="flex items-center text-sm font-medium text-gray-700 mb-2">
                <FaUserGraduate className="mr-2 text-red-500" />
                Curso/Grado
              </label>
              <select
                value={localFilters.cursoGrado || ''}
                onChange={(e) => updateFilter('cursoGrado', e.target.value || null)}
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 text-sm"
              >
                <option value="">Todos los cursos</option>
                {cursosGrados.map(curso => (
                  <option key={curso.value} value={curso.value}>
                    {curso.label}
                  </option>
                ))}
              </select>
            </div>

            {/* Filtro de Psicólogo */}
            <div>
              <label className="flex items-center text-sm font-medium text-gray-700 mb-2">
                <FaChalkboardTeacher className="mr-2 text-blue-600" />
                Psicólogo
              </label>
              <select
                value={localFilters.psicologo || ''}
                onChange={(e) => updateFilter('psicologo', e.target.value || null)}
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 text-sm"
                disabled={loading}
              >
                <option value="">Todos los psicólogos</option>
                {psychologists.map(psicologo => (
                  <option key={psicologo.id} value={psicologo.id}>
                    {psicologo.nombre} {psicologo.apellido}
                  </option>
                ))}
              </select>
            </div>

            {/* Filtro de Paciente Específico (para análisis individual) */}
            <div>
              <label className="flex items-center text-sm font-medium text-gray-700 mb-2">
                <FaUser className="mr-2 text-green-600" />
                Paciente específico
              </label>
              <select
                value={localFilters.pacienteEspecifico || ''}
                onChange={(e) => updateFilter('pacienteEspecifico', e.target.value || null)}
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 text-sm"
                disabled={loading}
              >
                <option value="">Análisis agregado</option>
                {patients.map(paciente => (
                  <option key={paciente.id} value={paciente.id}>
                    {paciente.nombre} {paciente.apellido} - {paciente.documento}
                  </option>
                ))}
              </select>
              {localFilters.pacienteEspecifico && (
                <p className="text-xs text-blue-600 mt-1">
                  ℹ️ Análisis individual activado
                </p>
              )}
            </div>
          </div>
        )}

        {/* Resumen de filtros activos */}
        {hasActiveFilters() && (
          <div className="mt-4 pt-4 border-t border-gray-200">
            <h4 className="text-sm font-medium text-gray-700 mb-2">Filtros activos:</h4>
            <div className="flex flex-wrap gap-2">
              {localFilters.dateRange.start && localFilters.dateRange.end && (
                <span className="inline-flex items-center px-3 py-1 bg-blue-100 text-blue-800 text-sm rounded-full">
                  <FaCalendarAlt className="mr-1" />
                  {localFilters.dateRange.start} - {localFilters.dateRange.end}
                  <button
                    onClick={() => updateDateRange(null, null)}
                    className="ml-2 text-blue-600 hover:text-blue-800"
                  >
                    <FaTimes />
                  </button>
                </span>
              )}
              
              {localFilters.educationalLevel && (
                <span className="inline-flex items-center px-3 py-1 bg-purple-100 text-purple-800 text-sm rounded-full">
                  <FaGraduationCap className="mr-1" />
                  {levelNames[localFilters.educationalLevel]}
                  <button
                    onClick={() => updateFilter('educationalLevel', null)}
                    className="ml-2 text-purple-600 hover:text-purple-800"
                  >
                    <FaTimes />
                  </button>
                </span>
              )}
              
              {localFilters.gender && (
                <span className="inline-flex items-center px-3 py-1 bg-pink-100 text-pink-800 text-sm rounded-full">
                  <FaVenusMars className="mr-1" />
                  {genderNames[localFilters.gender]}
                  <button
                    onClick={() => updateFilter('gender', null)}
                    className="ml-2 text-pink-600 hover:text-pink-800"
                  >
                    <FaTimes />
                  </button>
                </span>
              )}

              {localFilters.nivelAplicacion && (
                <span className="inline-flex items-center px-3 py-1 bg-indigo-100 text-indigo-800 text-sm rounded-full">
                  <FaGraduationCap className="mr-1" />
                  Nivel {localFilters.nivelAplicacion}
                  <button
                    onClick={() => updateFilter('nivelAplicacion', null)}
                    className="ml-2 text-indigo-600 hover:text-indigo-800"
                  >
                    <FaTimes />
                  </button>
                </span>
              )}

              {localFilters.categoriaEvaluado && (
                <span className="inline-flex items-center px-3 py-1 bg-yellow-100 text-yellow-800 text-sm rounded-full">
                  <FaChild className="mr-1" />
                  {localFilters.categoriaEvaluado === 'escolares' ? 'Escolares' : 'Adultos'}
                  <button
                    onClick={() => updateFilter('categoriaEvaluado', null)}
                    className="ml-2 text-yellow-600 hover:text-yellow-800"
                  >
                    <FaTimes />
                  </button>
                </span>
              )}

              {(localFilters.rangoEdad.min || localFilters.rangoEdad.max) && (
                <span className="inline-flex items-center px-3 py-1 bg-teal-100 text-teal-800 text-sm rounded-full">
                  <FaUser className="mr-1" />
                  Edad: {localFilters.rangoEdad.min || '0'}-{localFilters.rangoEdad.max || '∞'}
                  <button
                    onClick={() => updateFilter('rangoEdad', { min: null, max: null })}
                    className="ml-2 text-teal-600 hover:text-teal-800"
                  >
                    <FaTimes />
                  </button>
                </span>
              )}

              {localFilters.pacienteEspecifico && (
                <span className="inline-flex items-center px-3 py-1 bg-green-100 text-green-800 text-sm rounded-full">
                  <FaUser className="mr-1" />
                  Análisis Individual
                  <button
                    onClick={() => updateFilter('pacienteEspecifico', null)}
                    className="ml-2 text-green-600 hover:text-green-800"
                  >
                    <FaTimes />
                  </button>
                </span>
              )}
            </div>
          </div>
        )}
          </CardBody>
        </Card>
      </div>
    </div>
  );
};

export default FilterPanel;