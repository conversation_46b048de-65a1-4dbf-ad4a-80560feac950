import React from 'react';
import { FaArrowUp, FaArrowDown, FaMinus } from 'react-icons/fa';

const KPICard = ({ 
  title, 
  value, 
  previousValue, 
  unit = '', 
  icon: Icon, 
  color = 'blue',
  format = 'number',
  target = null,
  alert = null
}) => {
  // Calcular tendencia
  const calculateTrend = () => {
    if (!previousValue || previousValue === 0) return { type: 'neutral', percentage: 0 };
    
    const change = ((value - previousValue) / previousValue) * 100;
    if (Math.abs(change) < 1) return { type: 'neutral', percentage: change };
    return {
      type: change > 0 ? 'up' : 'down',
      percentage: Math.abs(change)
    };
  };

  // Formatear valor
  const formatValue = (val) => {
    // Handle null, undefined, or non-numeric values
    if (val === null || val === undefined || isNaN(val)) return '0';
    
    const numVal = Number(val);
    if (format === 'percentage') return numVal.toFixed(1); // Solo el número, sin %
    if (format === 'currency') return numVal.toLocaleString();
    if (format === 'decimal') return numVal.toFixed(1);
    return numVal.toFixed(1); // Siempre 1 decimal por defecto
  };

  // Determinar estado del semáforo
  const getTrafficLightStatus = () => {
    if (!target) return 'neutral';
    if (value >= target * 0.9) return 'success'; // Verde: 90%+ del objetivo
    if (value >= target * 0.7) return 'warning'; // Amarillo: 70-89% del objetivo
    return 'danger'; // Rojo: <70% del objetivo
  };

  const trend = calculateTrend();
  const status = getTrafficLightStatus();
  const colorClasses = {
    blue: 'from-blue-500 to-blue-600',
    green: 'from-green-500 to-green-600',
    orange: 'from-orange-500 to-orange-600',
    purple: 'from-purple-500 to-purple-600',
    red: 'from-red-500 to-red-600',
    indigo: 'from-indigo-500 to-indigo-600'
  };

  const statusColors = {
    success: 'bg-green-100 text-green-800 border-green-200',
    warning: 'bg-yellow-100 text-yellow-800 border-yellow-200',
    danger: 'bg-red-100 text-red-800 border-red-200',
    neutral: 'bg-gray-100 text-gray-800 border-gray-200'
  };

  return (
    <div className="bg-white rounded-lg shadow-lg hover:shadow-xl transition-all duration-300 transform hover:-translate-y-1">
      {/* Header con gradiente */}
      <div className={`bg-gradient-to-r ${colorClasses[color]} text-white p-4 rounded-t-lg`}>
        <div className="flex items-center justify-between">
          <div className="flex items-center justify-center w-full">
            <Icon className="h-6 w-6 mr-2" />
            <h3 className="text-sm font-semibold text-center">{title}</h3>
          </div>
          {alert && (
            <div className="flex items-center">
              <div className="w-2 h-2 bg-red-400 rounded-full animate-pulse mr-1"></div>
              <span className="text-xs">Alerta</span>
            </div>
          )}
        </div>
      </div>

      {/* Contenido principal */}
      <div className="p-4">
        <div className="flex items-center justify-between mb-3">
          <div className="text-3xl font-bold text-gray-800 text-center w-full">
            {formatValue(value)}{unit}
          </div>
          
          {/* Indicador de tendencia */}
          {previousValue && (
            <div className={`flex items-center text-sm ${
              trend.type === 'up' ? 'text-green-600' : 
              trend.type === 'down' ? 'text-red-600' : 'text-gray-600'
            }`}>
              {trend.type === 'up' && <FaArrowUp className="h-4 w-4 mr-1" />}
              {trend.type === 'down' && <FaArrowDown className="h-4 w-4 mr-1" />}
              {trend.type === 'neutral' && <FaMinus className="h-4 w-4 mr-1" />}
              <span>{trend.percentage.toFixed(1)}%</span>
            </div>
          )}
        </div>

        {/* Semáforo de estado */}
        {target && (
          <div className="mb-3">
            <div className="flex items-center justify-between text-xs text-gray-600 mb-1">
              <span>Progreso hacia objetivo</span>
              <span>{formatValue(target)}{unit}</span>
            </div>
            <div className="w-full bg-gray-200 rounded-full h-2">
              <div 
                className={`h-2 rounded-full transition-all duration-500 ${
                  status === 'success' ? 'bg-green-500' :
                  status === 'warning' ? 'bg-yellow-500' : 'bg-red-500'
                }`}
                style={{ width: `${Math.min((value / target) * 100, 100)}%` }}
              ></div>
            </div>
            <div className={`text-xs mt-1 px-2 py-1 rounded-full border inline-block ${statusColors[status]}`}>
              {Math.round((value / target) * 100)}% del objetivo
            </div>
          </div>
        )}

        {/* Mensaje de alerta */}
        {alert && (
          <div className="bg-red-50 border-l-4 border-red-400 p-2 mt-3">
            <div className="flex">
              <div className="ml-2">
                <p className="text-xs text-red-700">{alert}</p>
              </div>
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default KPICard;