import React, { Suspense, lazy, memo, useMemo, useCallback } from 'react';
import { ErrorBoundary } from 'react-error-boundary';
import { Fa<PERSON><PERSON><PERSON>, FaExclamationTriangle } from 'react-icons/fa';

// Lazy loading de vistas pesadas
const ExecutiveView = lazy(() => import('./views/ExecutiveView'));
const KpisView = lazy(() => import('./views/KpisView'));
const GeneralView = lazy(() => import('./views/GeneralView'));
const TrendsView = lazy(() => import('./views/TrendsView'));
const ComparativeView = lazy(() => import('./views/ComparativeView'));
const StatisticalView = lazy(() => import('./views/StatisticalView'));
const ExportView = lazy(() => import('./views/ExportView'));
const EnhancedIndividualView = lazy(() => import('./views/EnhancedIndividualView'));

// Lazy loading de gráficos pesados
const RadarAptitudeChart = lazy(() => import('./charts/RadarAptitudeChart'));
const ComparativeBarChart = lazy(() => import('./charts/ComparativeBarChart'));
const AptitudeProfileChart = lazy(() => import('./charts/AptitudeProfileChart'));
const AttentionStyleQuadrant = lazy(() => import('./charts/AttentionStyleQuadrant'));

/**
 * Componente de carga optimizado
 */
const OptimizedLoader = memo(({ message = "Cargando..." }) => (
  <div className="flex items-center justify-center h-64">
    <div className="text-center">
      <FaSpinner className="animate-spin text-4xl text-blue-500 mx-auto mb-4" />
      <p className="text-gray-600">{message}</p>
    </div>
  </div>
));

/**
 * Componente de error optimizado
 */
const OptimizedErrorFallback = memo(({ error, resetErrorBoundary }) => (
  <div className="flex items-center justify-center h-64">
    <div className="text-center max-w-md">
      <FaExclamationTriangle className="text-4xl text-red-500 mx-auto mb-4" />
      <h3 className="text-lg font-semibold text-gray-900 mb-2">
        Error al cargar el componente
      </h3>
      <p className="text-gray-600 mb-4">
        {error?.message || 'Ha ocurrido un error inesperado'}
      </p>
      <button
        onClick={resetErrorBoundary}
        className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
      >
        Intentar de nuevo
      </button>
    </div>
  </div>
));

/**
 * HOC para optimizar componentes pesados
 */
const withOptimization = (WrappedComponent, displayName) => {
  const OptimizedComponent = memo((props) => {
    // Memoizar props complejas
    const memoizedProps = useMemo(() => ({
      ...props,
      // Optimizar objetos de datos grandes
      data: props.data ? { ...props.data } : null
    }), [props]);

    return (
      <ErrorBoundary
        FallbackComponent={OptimizedErrorFallback}
        onError={(error, errorInfo) => {
          console.error(`Error en ${displayName}:`, error, errorInfo);
        }}
      >
        <Suspense fallback={<OptimizedLoader message={`Cargando ${displayName}...`} />}>
          <WrappedComponent {...memoizedProps} />
        </Suspense>
      </ErrorBoundary>
    );
  });

  OptimizedComponent.displayName = `Optimized${displayName}`;
  return OptimizedComponent;
};

/**
 * Componentes optimizados
 */
export const OptimizedExecutiveView = withOptimization(ExecutiveView, 'ExecutiveView');
export const OptimizedKpisView = withOptimization(KpisView, 'KpisView');
export const OptimizedGeneralView = withOptimization(GeneralView, 'GeneralView');
export const OptimizedTrendsView = withOptimization(TrendsView, 'TrendsView');
export const OptimizedComparativeView = withOptimization(ComparativeView, 'ComparativeView');
export const OptimizedStatisticalView = withOptimization(StatisticalView, 'StatisticalView');
export const OptimizedExportView = withOptimization(ExportView, 'ExportView');
export const OptimizedEnhancedIndividualView = withOptimization(EnhancedIndividualView, 'EnhancedIndividualView');

/**
 * Gráficos optimizados
 */
export const OptimizedRadarChart = withOptimization(RadarAptitudeChart, 'RadarChart');
export const OptimizedBarChart = withOptimization(ComparativeBarChart, 'BarChart');
export const OptimizedLineChart = withOptimization(AptitudeProfileChart, 'LineChart');
export const OptimizedScatterChart = withOptimization(AttentionStyleQuadrant, 'ScatterChart');

/**
 * Hook para optimizar renderizado de listas grandes
 */
export const useVirtualizedList = (items, itemHeight = 50, containerHeight = 400) => {
  const [scrollTop, setScrollTop] = React.useState(0);
  
  const visibleItems = useMemo(() => {
    const startIndex = Math.floor(scrollTop / itemHeight);
    const endIndex = Math.min(
      startIndex + Math.ceil(containerHeight / itemHeight) + 1,
      items.length
    );
    
    return {
      startIndex,
      endIndex,
      items: items.slice(startIndex, endIndex),
      totalHeight: items.length * itemHeight,
      offsetY: startIndex * itemHeight
    };
  }, [items, itemHeight, containerHeight, scrollTop]);

  const handleScroll = useCallback((e) => {
    setScrollTop(e.target.scrollTop);
  }, []);

  return {
    visibleItems,
    handleScroll,
    containerHeight,
    itemHeight
  };
};

/**
 * Componente de lista virtualizada para grandes conjuntos de datos
 */
export const VirtualizedList = memo(({ 
  items, 
  renderItem, 
  itemHeight = 50, 
  containerHeight = 400,
  className = ""
}) => {
  const { visibleItems, handleScroll } = useVirtualizedList(items, itemHeight, containerHeight);

  return (
    <div 
      className={`overflow-auto ${className}`}
      style={{ height: containerHeight }}
      onScroll={handleScroll}
    >
      <div style={{ height: visibleItems.totalHeight, position: 'relative' }}>
        <div style={{ transform: `translateY(${visibleItems.offsetY}px)` }}>
          {visibleItems.items.map((item, index) => (
            <div key={visibleItems.startIndex + index} style={{ height: itemHeight }}>
              {renderItem(item, visibleItems.startIndex + index)}
            </div>
          ))}
        </div>
      </div>
    </div>
  );
});

/**
 * Hook para debounce de búsquedas
 */
export const useDebounce = (value, delay) => {
  const [debouncedValue, setDebouncedValue] = React.useState(value);

  React.useEffect(() => {
    const handler = setTimeout(() => {
      setDebouncedValue(value);
    }, delay);

    return () => {
      clearTimeout(handler);
    };
  }, [value, delay]);

  return debouncedValue;
};

/**
 * Hook para cache de datos
 */
export const useDataCache = (key, fetchFunction, dependencies = []) => {
  const [data, setData] = React.useState(null);
  const [loading, setLoading] = React.useState(false);
  const [error, setError] = React.useState(null);
  
  const cacheKey = `dashboard_cache_${key}`;
  const cacheTimeKey = `${cacheKey}_time`;
  const CACHE_DURATION = 5 * 60 * 1000; // 5 minutos

  const fetchData = useCallback(async () => {
    setLoading(true);
    setError(null);
    
    try {
      // Verificar cache
      const cachedData = localStorage.getItem(cacheKey);
      const cacheTime = localStorage.getItem(cacheTimeKey);
      
      if (cachedData && cacheTime) {
        const isValid = Date.now() - parseInt(cacheTime) < CACHE_DURATION;
        if (isValid) {
          setData(JSON.parse(cachedData));
          setLoading(false);
          return;
        }
      }
      
      // Fetch nuevos datos
      const result = await fetchFunction();
      
      // Guardar en cache
      localStorage.setItem(cacheKey, JSON.stringify(result));
      localStorage.setItem(cacheTimeKey, Date.now().toString());
      
      setData(result);
    } catch (err) {
      setError(err);
      console.error(`Error fetching data for ${key}:`, err);
    } finally {
      setLoading(false);
    }
  }, [key, fetchFunction, cacheKey, cacheTimeKey]);

  React.useEffect(() => {
    fetchData();
  }, dependencies);

  const clearCache = useCallback(() => {
    localStorage.removeItem(cacheKey);
    localStorage.removeItem(cacheTimeKey);
  }, [cacheKey, cacheTimeKey]);

  return { data, loading, error, refetch: fetchData, clearCache };
};

/**
 * Componente de imagen optimizada con lazy loading
 */
export const OptimizedImage = memo(({ 
  src, 
  alt, 
  className = "", 
  placeholder = "data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMjAwIiBoZWlnaHQ9IjIwMCIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj48cmVjdCB3aWR0aD0iMTAwJSIgaGVpZ2h0PSIxMDAlIiBmaWxsPSIjZGRkIi8+PC9zdmc+"
}) => {
  const [imageSrc, setImageSrc] = React.useState(placeholder);
  const [imageRef, setImageRef] = React.useState();

  React.useEffect(() => {
    let observer;
    
    if (imageRef && 'IntersectionObserver' in window) {
      observer = new IntersectionObserver(
        entries => {
          entries.forEach(entry => {
            if (entry.isIntersecting) {
              setImageSrc(src);
              observer.unobserve(imageRef);
            }
          });
        },
        { threshold: 0.1 }
      );
      
      observer.observe(imageRef);
    }
    
    return () => {
      if (observer && observer.unobserve) {
        observer.unobserve(imageRef);
      }
    };
  }, [imageRef, src]);

  return (
    <img
      ref={setImageRef}
      src={imageSrc}
      alt={alt}
      className={className}
      loading="lazy"
    />
  );
});

export default {
  OptimizedLoader,
  OptimizedErrorFallback,
  withOptimization,
  useVirtualizedList,
  VirtualizedList,
  useDebounce,
  useDataCache,
  OptimizedImage
};
