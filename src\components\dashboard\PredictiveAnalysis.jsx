import React from 'react';
import { FaBrain, FaChartLine, FaExclamationTriangle, FaLightbulb } from 'react-icons/fa';
import { LineChart, Line, XAxis, YAxis, CartesianGrid, Tooltip, ResponsiveContainer, ReferenceLine } from 'recharts';

const PredictiveAnalysis = ({ data, predictions }) => {
  // Datos simulados para predicciones (en una implementación real vendrían del backend)
  const predictionData = [
    { month: 'Ene', actual: 68, predicted: null },
    { month: 'Feb', actual: 72, predicted: null },
    { month: 'Mar', actual: 70, predicted: null },
    { month: 'Abr', actual: 75, predicted: null },
    { month: 'May', actual: 73, predicted: null },
    { month: 'Jun', actual: 78, predicted: null },
    { month: 'Jul', actual: null, predicted: 80 },
    { month: 'Ago', actual: null, predicted: 82 },
    { month: 'Sep', actual: null, predicted: 79 },
    { month: 'Oct', actual: null, predicted: 83 },
    { month: 'Nov', actual: null, predicted: 85 },
    { month: 'Dic', actual: null, predicted: 87 }
  ];

  const insights = [
    {
      type: 'positive',
      icon: FaChartLine,
      title: 'Tendencia Positiva Detectada',
      description: 'Se proyecta un incremento del 12% en el rendimiento general para los próximos 6 meses.',
      confidence: 85
    },
    {
      type: 'warning',
      icon: FaExclamationTriangle,
      title: 'Área de Atención Requerida',
      description: 'La aptitud mecánica muestra signos de estancamiento. Se recomienda intervención.',
      confidence: 78
    },
    {
      type: 'opportunity',
      icon: FaLightbulb,
      title: 'Oportunidad de Mejora',
      description: 'Los estudiantes de nivel medio tienen potencial no aprovechado en razonamiento.',
      confidence: 92
    }
  ];

  const riskFactors = [
    {
      factor: 'Deserción Académica',
      probability: 15,
      impact: 'Alto',
      students: 8
    },
    {
      factor: 'Bajo Rendimiento Sostenido',
      probability: 23,
      impact: 'Medio',
      students: 12
    },
    {
      factor: 'Necesidad de Apoyo Adicional',
      probability: 35,
      impact: 'Medio',
      students: 18
    }
  ];

  const getInsightColor = (type) => {
    switch (type) {
      case 'positive': return 'border-green-200 bg-green-50';
      case 'warning': return 'border-yellow-200 bg-yellow-50';
      case 'opportunity': return 'border-blue-200 bg-blue-50';
      default: return 'border-gray-200 bg-gray-50';
    }
  };

  const getInsightIconColor = (type) => {
    switch (type) {
      case 'positive': return 'text-green-600';
      case 'warning': return 'text-yellow-600';
      case 'opportunity': return 'text-blue-600';
      default: return 'text-gray-600';
    }
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="bg-white rounded-lg shadow-lg p-6">
        <h2 className="text-2xl font-bold text-gray-800 mb-2 flex items-center">
          <FaBrain className="h-6 w-6 mr-3 text-purple-600" />
          Análisis Predictivo e Inteligencia Institucional
        </h2>
        <p className="text-gray-600">
          Proyecciones basadas en tendencias históricas y algoritmos de machine learning
        </p>
      </div>

      {/* Gráfico de predicciones */}
      <div className="bg-white rounded-lg shadow-lg p-6">
        <h3 className="text-lg font-semibold text-gray-800 mb-4">
          Proyección de Rendimiento - Próximos 6 Meses
        </h3>
        <ResponsiveContainer width="100%" height={300}>
          <LineChart data={predictionData}>
            <CartesianGrid strokeDasharray="3 3" />
            <XAxis dataKey="month" />
            <YAxis domain={[60, 90]} />
            <Tooltip />
            <ReferenceLine x="Jun" stroke="#666" strokeDasharray="2 2" />
            <Line 
              type="monotone" 
              dataKey="actual" 
              stroke="#3B82F6" 
              strokeWidth={3}
              dot={{ fill: '#3B82F6', strokeWidth: 2, r: 4 }}
              name="Datos Reales"
            />
            <Line 
              type="monotone" 
              dataKey="predicted" 
              stroke="#10B981" 
              strokeWidth={2}
              strokeDasharray="5 5"
              dot={{ fill: '#10B981', strokeWidth: 2, r: 4 }}
              name="Predicción"
            />
          </LineChart>
        </ResponsiveContainer>
        <div className="mt-4 text-sm text-gray-600 flex items-center justify-center">
          <div className="flex items-center mr-6">
            <div className="w-4 h-0.5 bg-blue-500 mr-2"></div>
            <span>Datos Históricos</span>
          </div>
          <div className="flex items-center">
            <div className="w-4 h-0.5 bg-green-500 border-dashed mr-2"></div>
            <span>Proyección Predictiva</span>
          </div>
        </div>
      </div>

      {/* Insights clave */}
      <div className="bg-white rounded-lg shadow-lg p-6">
        <h3 className="text-lg font-semibold text-gray-800 mb-4">
          Insights Clave del Análisis Predictivo
        </h3>
        <div className="space-y-4">
          {insights.map((insight, index) => (
            <div key={index} className={`border rounded-lg p-4 ${getInsightColor(insight.type)}`}>
              <div className="flex items-start">
                <insight.icon className={`h-5 w-5 mr-3 mt-0.5 ${getInsightIconColor(insight.type)}`} />
                <div className="flex-1">
                  <h4 className="font-semibold text-gray-800">{insight.title}</h4>
                  <p className="text-gray-700 mt-1">{insight.description}</p>
                  <div className="mt-2 flex items-center">
                    <span className="text-xs text-gray-600 mr-2">Confianza:</span>
                    <div className="w-20 bg-gray-200 rounded-full h-2">
                      <div 
                        className="bg-blue-500 h-2 rounded-full" 
                        style={{ width: `${insight.confidence}%` }}
                      ></div>
                    </div>
                    <span className="text-xs text-gray-600 ml-2">{insight.confidence}%</span>
                  </div>
                </div>
              </div>
            </div>
          ))}
        </div>
      </div>

      {/* Análisis de riesgos */}
      <div className="bg-white rounded-lg shadow-lg p-6">
        <h3 className="text-lg font-semibold text-gray-800 mb-4">
          Análisis de Factores de Riesgo
        </h3>
        <div className="overflow-x-auto">
          <table className="w-full text-sm">
            <thead>
              <tr className="border-b border-gray-200">
                <th className="text-left py-3 px-4 font-semibold text-gray-700">Factor de Riesgo</th>
                <th className="text-center py-3 px-4 font-semibold text-gray-700">Probabilidad</th>
                <th className="text-center py-3 px-4 font-semibold text-gray-700">Impacto</th>
                <th className="text-center py-3 px-4 font-semibold text-gray-700">Estudiantes Afectados</th>
                <th className="text-center py-3 px-4 font-semibold text-gray-700">Acción</th>
              </tr>
            </thead>
            <tbody>
              {riskFactors.map((risk, index) => (
                <tr key={index} className="border-b border-gray-100 hover:bg-gray-50">
                  <td className="py-3 px-4 font-medium text-gray-800">{risk.factor}</td>
                  <td className="py-3 px-4 text-center">
                    <div className="flex items-center justify-center">
                      <div className="w-16 bg-gray-200 rounded-full h-2 mr-2">
                        <div 
                          className={`h-2 rounded-full ${
                            risk.probability > 30 ? 'bg-red-500' : 
                            risk.probability > 20 ? 'bg-yellow-500' : 'bg-green-500'
                          }`}
                          style={{ width: `${risk.probability}%` }}
                        ></div>
                      </div>
                      <span className="text-xs">{risk.probability}%</span>
                    </div>
                  </td>
                  <td className="py-3 px-4 text-center">
                    <span className={`px-2 py-1 rounded-full text-xs ${
                      risk.impact === 'Alto' ? 'bg-red-100 text-red-800' :
                      risk.impact === 'Medio' ? 'bg-yellow-100 text-yellow-800' :
                      'bg-green-100 text-green-800'
                    }`}>
                      {risk.impact}
                    </span>
                  </td>
                  <td className="py-3 px-4 text-center font-semibold text-gray-800">
                    {risk.students}
                  </td>
                  <td className="py-3 px-4 text-center">
                    <button className="text-blue-600 hover:text-blue-800 text-xs underline">
                      Ver Plan
                    </button>
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      </div>

      {/* Recomendaciones automáticas */}
      <div className="bg-white rounded-lg shadow-lg p-6">
        <h3 className="text-lg font-semibold text-gray-800 mb-4">
          Recomendaciones Automáticas del Sistema
        </h3>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
            <h4 className="font-semibold text-blue-800 mb-2">Acción Inmediata</h4>
            <p className="text-blue-700 text-sm mb-3">
              Implementar programa de refuerzo en aptitud mecánica para 15 estudiantes identificados.
            </p>
            <button className="bg-blue-600 text-white px-3 py-1 rounded text-xs hover:bg-blue-700">
              Generar Plan
            </button>
          </div>
          
          <div className="bg-green-50 border border-green-200 rounded-lg p-4">
            <h4 className="font-semibold text-green-800 mb-2">Oportunidad</h4>
            <p className="text-green-700 text-sm mb-3">
              Expandir programa de excelencia en razonamiento para estudiantes de alto potencial.
            </p>
            <button className="bg-green-600 text-white px-3 py-1 rounded text-xs hover:bg-green-700">
              Ver Detalles
            </button>
          </div>
        </div>
      </div>
    </div>
  );
};

export default PredictiveAnalysis;