import React from 'react';
import { FaLightbulb, FaExclamationTriangle, FaChartLine, FaUsers } from 'react-icons/fa';

/**
 * Componente para mostrar recomendaciones basadas en el análisis estadístico
 * Proporciona sugerencias accionables para mejorar el rendimiento de los estudiantes
 */
const RecommendationPanel = ({ medidasEstadisticas, gruposAnalisis, correlaciones }) => {
  // Generar recomendaciones basadas en los datos estadísticos
  const generateRecommendations = () => {
    if (!medidasEstadisticas || !gruposAnalisis || !correlaciones) {
      return [];
    }

    const recommendations = [];

    // Recomendaciones basadas en medidas de tendencia central
    if (medidasEstadisticas.medidas_por_aptitud) {
      // Encontrar aptitudes con menor rendimiento
      const aptitudesByPerformance = [...medidasEstadisticas.medidas_por_aptitud]
        .filter(aptitud => aptitud.percentil && aptitud.percentil.media !== undefined)
        .sort((a, b) => a.percentil.media - b.percentil.media);

      if (aptitudesByPerformance.length > 0) {
        const lowestPerformance = aptitudesByPerformance[0];
        if (lowestPerformance.percentil.media < 50) {
          recommendations.push({
            type: 'warning',
            title: `Reforzar Aptitud ${lowestPerformance.aptitud}`,
            description: `El rendimiento promedio en ${lowestPerformance.nombre_aptitud} es bajo (${lowestPerformance.percentil.media.toFixed(1)}). Considere implementar estrategias de refuerzo específicas para esta aptitud.`,
            priority: 'alta'
          });
        }

        // Aptitudes con alta variabilidad
        const aptitudesByVariability = [...medidasEstadisticas.medidas_por_aptitud]
          .filter(aptitud => aptitud.percentil && aptitud.percentil.desviacion_estandar !== undefined)
          .sort((a, b) => b.percentil.desviacion_estandar - a.percentil.desviacion_estandar);

        if (aptitudesByVariability.length > 0) {
          const highestVariability = aptitudesByVariability[0];
          if (highestVariability.percentil.desviacion_estandar > 20) {
            recommendations.push({
              type: 'info',
              title: `Grupo Heterogéneo en ${highestVariability.aptitud}`,
              description: `Alta variabilidad en ${highestVariability.nombre_aptitud} (DE=${highestVariability.percentil.desviacion_estandar.toFixed(1)}). Considere implementar estrategias de enseñanza diferenciada para atender los distintos niveles.`,
              priority: 'media'
            });
          }
        }
      }
    }

    // Recomendaciones basadas en grupos de riesgo y talento
    if (gruposAnalisis.grupos_riesgo && gruposAnalisis.grupos_talento) {
      // Contar estudiantes en grupos de riesgo por aptitud
      const riskGroupCounts = {};
      Object.entries(gruposAnalisis.grupos_riesgo).forEach(([aptitud, estudiantes]) => {
        riskGroupCounts[aptitud] = estudiantes.length;
      });

      // Encontrar aptitud con mayor número de estudiantes en riesgo
      const aptitudMayorRiesgo = Object.entries(riskGroupCounts)
        .sort((a, b) => b[1] - a[1])
        .filter(([_, count]) => count > 0)[0];

      if (aptitudMayorRiesgo) {
        const [aptitud, count] = aptitudMayorRiesgo;
        recommendations.push({
          type: 'danger',
          title: `Intervención Prioritaria en ${aptitud}`,
          description: `${count} estudiantes en grupo de riesgo para la aptitud ${aptitud}. Se recomienda una intervención focalizada para este grupo.`,
          priority: 'alta'
        });
      }

      // Contar estudiantes en grupos de talento por aptitud
      const talentGroupCounts = {};
      Object.entries(gruposAnalisis.grupos_talento).forEach(([aptitud, estudiantes]) => {
        talentGroupCounts[aptitud] = estudiantes.length;
      });

      // Encontrar aptitud con mayor número de estudiantes con talento
      const aptitudMayorTalento = Object.entries(talentGroupCounts)
        .sort((a, b) => b[1] - a[1])
        .filter(([_, count]) => count > 0)[0];

      if (aptitudMayorTalento) {
        const [aptitud, count] = aptitudMayorTalento;
        recommendations.push({
          type: 'success',
          title: `Programa de Enriquecimiento en ${aptitud}`,
          description: `${count} estudiantes con alto rendimiento en ${aptitud}. Considere implementar un programa de enriquecimiento para potenciar este talento.`,
          priority: 'media'
        });
      }
    }

    // Recomendaciones basadas en correlaciones
    if (correlaciones.interpretaciones && correlaciones.interpretaciones.length > 0) {
      // Encontrar correlaciones fuertes
      const strongCorrelations = correlaciones.interpretaciones
        .filter(item => Math.abs(item.correlacion) >= 0.7)
        .slice(0, 2);

      if (strongCorrelations.length > 0) {
        strongCorrelations.forEach(correlation => {
          recommendations.push({
            type: 'info',
            title: `Correlación Significativa`,
            description: correlation.interpretacion,
            priority: 'baja'
          });
        });
      }
    }

    // Si no hay suficientes recomendaciones, agregar algunas genéricas
    if (recommendations.length < 3) {
      recommendations.push({
        type: 'info',
        title: 'Seguimiento Continuo',
        description: 'Implemente evaluaciones periódicas para monitorear el progreso de los estudiantes y ajustar las estrategias pedagógicas según sea necesario.',
        priority: 'media'
      });
    }

    return recommendations;
  };

  const recommendations = generateRecommendations();

  // Si no hay datos o recomendaciones, mostrar mensaje
  if (recommendations.length === 0) {
    return (
      <div className="bg-gray-50 p-4 rounded-lg text-center">
        <FaLightbulb className="mx-auto text-gray-400 text-3xl mb-2" />
        <p className="text-gray-500">No hay suficientes datos para generar recomendaciones.</p>
      </div>
    );
  }

  // Iconos para cada tipo de recomendación
  const typeIcons = {
    success: <FaLightbulb className="text-green-500" />,
    info: <FaChartLine className="text-blue-500" />,
    warning: <FaExclamationTriangle className="text-yellow-500" />,
    danger: <FaExclamationTriangle className="text-red-500" />
  };

  // Colores para cada tipo de recomendación
  const typeColors = {
    success: 'bg-green-50 border-green-200',
    info: 'bg-blue-50 border-blue-200',
    warning: 'bg-yellow-50 border-yellow-200',
    danger: 'bg-red-50 border-red-200'
  };

  // Colores para las etiquetas de prioridad
  const priorityColors = {
    alta: 'bg-red-100 text-red-800',
    media: 'bg-yellow-100 text-yellow-800',
    baja: 'bg-blue-100 text-blue-800'
  };

  return (
    <div className="space-y-4">
      <h3 className="text-lg font-semibold text-gray-800 flex items-center">
        <FaLightbulb className="mr-2 text-yellow-500" />
        Recomendaciones Pedagógicas
      </h3>
      
      <p className="text-sm text-gray-600 mb-4">
        Basadas en el análisis estadístico de los resultados de las evaluaciones BAT-7.
        Estas recomendaciones pueden ayudar a mejorar el rendimiento de los estudiantes.
      </p>
      
      <div className="space-y-3">
        {recommendations.map((recommendation, index) => (
          <div 
            key={index} 
            className={`p-4 border rounded-lg flex items-start ${typeColors[recommendation.type]}`}
          >
            <div className="mr-3 mt-1">
              {typeIcons[recommendation.type]}
            </div>
            <div className="flex-1">
              <div className="flex justify-between items-start">
                <h4 className="font-medium text-gray-800">{recommendation.title}</h4>
                <span className={`text-xs px-2 py-1 rounded-full ${priorityColors[recommendation.priority]}`}>
                  Prioridad {recommendation.priority}
                </span>
              </div>
              <p className="text-sm text-gray-600 mt-1">{recommendation.description}</p>
            </div>
          </div>
        ))}
      </div>
    </div>
  );
};

export default RecommendationPanel;