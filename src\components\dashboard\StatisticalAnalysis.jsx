import React, { useState, useEffect } from 'react';
import { Card, CardHeader, CardBody } from '../ui/Card';
import { FaChartBar, FaCalculator, FaTable, FaExclamationTriangle, FaLightbulb, FaChartLine, FaUsers, FaBoxOpen, FaFilePdf, FaFileExcel, FaDownload } from 'react-icons/fa';
import StatisticsService from '../../services/statisticsService';
import ExportService from '../../services/exportService';
import RecommendationPanel from './RecommendationPanel';
import { toast } from 'react-toastify';
import { 
  BarChart, Bar, XAxis, YAxis, CartesianGrid, Tooltip, Legend, ResponsiveContainer,
  ComposedChart, Line, Area, ScatterChart, Scatter, ZAxis, ReferenceLine
} from 'recharts';

/**
 * Componente para análisis estadístico avanzado
 * Muestra medidas de tendencia central y dispersión para las aptitudes BAT-7
 */
const StatisticalAnalysis = ({ filters = {}, initialData = null }) => {
    const [loading, setLoading] = useState(true);
    const [error, setError] = useState(null);
    const [medidasEstadisticas, setMedidasEstadisticas] = useState(null);
    const [distribucionFrecuencias, setDistribucionFrecuencias] = useState(null);
    const [selectedAptitud, setSelectedAptitud] = useState('V');
    const [selectedMetrica, setSelectedMetrica] = useState('percentil');
    const [gruposAnalisis, setGruposAnalisis] = useState(null);
    const [correlaciones, setCorrelaciones] = useState(null);
    const [comparativeMode, setComparativeMode] = useState(false);
    const [selectedAptitudes, setSelectedAptitudes] = useState(['V', 'R']);
    const [exportLoading, setExportLoading] = useState(false);
const [preparedChartData, setPreparedChartData] = useState({
        medidas: null,
        distribucion: null,
        grupos: null
    });

    // Códigos de aptitudes BAT-7
    const aptitudes = [
        { codigo: 'V', nombre: 'Verbal' },
        { codigo: 'E', nombre: 'Espacial' },
        { codigo: 'A', nombre: 'Atención' },
        { codigo: 'R', nombre: 'Razonamiento' },
        { codigo: 'N', nombre: 'Numérico' },
        { codigo: 'M', nombre: 'Mecánico' },
        { codigo: 'O', nombre: 'Ortografía' }
    ];

    // Manejar exportación de datos
    const handleExport = async (format) => {
        try {
            setExportLoading(format);
            
            // Preparar datos para exportación
            const exportData = {
                title: `Análisis Estadístico BAT-7 - ${new Date().toLocaleDateString()}`,
                mode: comparativeMode ? 'comparativo' : 'individual',
                aptitud: selectedAptitud,
                aptitudes_seleccionadas: selectedAptitudes,
                metrica: selectedMetrica,
                medidas_estadisticas: medidasEstadisticas,
                distribucion_frecuencias: distribucionFrecuencias,
                grupos_analisis: gruposAnalisis,
                correlaciones: correlaciones,
                fecha_generacion: new Date().toISOString()
            };
            
            // Exportar según formato
            if (format === 'pdf') {
                await ExportService.exportToPDF(exportData, {
                    title: 'Análisis Estadístico BAT-7',
                    includeGraphics: true,
                    includeDetailedData: true
                });
                toast.success('Análisis estadístico exportado a PDF correctamente');
            } else if (format === 'excel') {
                await ExportService.exportToExcel(exportData, {
                    includeDetailedData: true
                });
                toast.success('Análisis estadístico exportado a Excel correctamente');
            }
            
        } catch (error) {
            console.error(`❌ [StatisticalAnalysis] Error al exportar a ${format}:`, error);
            toast.error(`Error al exportar a ${format}`);
        } finally {
            setExportLoading(false);
        }
    };

    // Cargar datos estadísticos
    useEffect(() => {
        const fetchData = async () => {
            try {
                setLoading(true);
                setError(null);

                // Si hay datos iniciales, usarlos
                if (initialData) {
                    console.log('📊 [StatisticalAnalysis] Usando datos iniciales proporcionados');
                    setMedidasEstadisticas(initialData.medidas);
                    setDistribucionFrecuencias(initialData.distribucion);
                    setGruposAnalisis(initialData.grupos);
                    setCorrelaciones(initialData.correlaciones);
                    setLoading(false);
                    return;
                }

                console.log('📊 [StatisticalAnalysis] Cargando datos estadísticos...');

                // Cargar todos los datos en paralelo
                const [medidas, distribucion, grupos, correlacionesData] = await Promise.all([
                    StatisticsService.getMedidasTodasAptitudes(filters),
                    StatisticsService.getDistribucionTodasAptitudes(filters),
                    StatisticsService.getAnalisisGrupos(filters),
                    StatisticsService.getAnalisisCorrelacion(filters)
                ]);

                setMedidasEstadisticas(medidas);
                setDistribucionFrecuencias(distribucion);
                setGruposAnalisis(grupos);
                setCorrelaciones(correlacionesData);

                console.log('✅ [StatisticalAnalysis] Datos estadísticos cargados');
            } catch (error) {
                console.error('❌ [StatisticalAnalysis] Error al cargar datos estadísticos:', error);
                setError('Error al cargar datos estadísticos');
                toast.error('Error al cargar análisis estadístico');
            } finally {
                setLoading(false);
            }
        };

        fetchData();
    }, [filters, initialData]);

    // Preparar datos para los gráficos cuando cambian las selecciones
    useEffect(() => {
        if (medidasEstadisticas && distribucionFrecuencias && gruposAnalisis) {
            // Validar que los datos tienen la estructura esperada
            if (!medidasEstadisticas.medidas_por_aptitud || !Array.isArray(medidasEstadisticas.medidas_por_aptitud)) {
                console.warn('⚠️ [StatisticalAnalysis] Estructura de medidasEstadisticas inválida');
                return;
            }

            if (!distribucionFrecuencias.distribuciones_por_aptitud || !Array.isArray(distribucionFrecuencias.distribuciones_por_aptitud)) {
                console.warn('⚠️ [StatisticalAnalysis] Estructura de distribucionFrecuencias inválida');
                return;
            }

            const aptitudSeleccionadaData = medidasEstadisticas.medidas_por_aptitud.find(
                m => m && m.aptitud === selectedAptitud
            );

            const distribucionSeleccionadaData = distribucionFrecuencias.distribuciones_por_aptitud.find(
                d => d && d.aptitud === selectedAptitud
            );

            const getStudentDataForGroup = (grupo) => {
                if (!grupo || !Array.isArray(grupo)) return [];

                return grupo.map(estudiante => {
                    if (!estudiante || !estudiante.aptitudes || !Array.isArray(estudiante.aptitudes)) {
                        return {
                            ...estudiante,
                            valor: 'N/A'
                        };
                    }

                    const aptitudData = estudiante.aptitudes.find(a => a && a.codigo === selectedAptitud);
                    return {
                        ...estudiante,
                        valor: aptitudData ? (selectedMetrica === 'percentil' ? aptitudData.percentil : aptitudData.pd) : 'N/A'
                    };
                });
            };

            const grupoRiesgo = getStudentDataForGroup(
                gruposAnalisis?.grupos_riesgo?.[selectedAptitud] || []
            );
            const grupoTalento = getStudentDataForGroup(
                gruposAnalisis?.grupos_talento?.[selectedAptitud] || []
            );

            setPreparedChartData({
                medidas: aptitudSeleccionadaData ? aptitudSeleccionadaData[selectedMetrica] : null,
                distribucion: distribucionSeleccionadaData ? (selectedMetrica === 'percentil'
                    ? distribucionSeleccionadaData.distribucion_percentil
                    : distribucionSeleccionadaData.distribucion_pd) : [],
                grupos: {
                    riesgo: grupoRiesgo,
                    talento: grupoTalento
                }
            });
        }
    }, [selectedAptitud, selectedMetrica, medidasEstadisticas, distribucionFrecuencias, gruposAnalisis]);

    // Renderizar tabla de medidas de tendencia central
    const renderTablaMedidas = () => {
        const { medidas } = preparedChartData;

        if (!medidas) {
            return (
                <div className="text-center py-8">
                    <p className="text-gray-500">No hay datos de medidas disponibles para la selección actual.</p>
                </div>
            );
        }

        // Datos para el diagrama de caja (box plot)
        const boxPlotData = [
            {
                name: selectedAptitud,
                min: medidas?.minimo || 0,
                q1: medidas?.q1 || 0,
                median: medidas?.mediana || 0,
                q3: medidas?.q3 || 0,
                max: medidas?.maximo || 0,
                mean: medidas?.media || 0
            }
        ];

        return (
            <div>
                {/* Box Plot */}
                <div className="mb-6">
                    <h4 className="text-sm font-medium text-gray-700 mb-2">Diagrama de Caja (Box Plot)</h4>
                    <div className="bg-white p-2 rounded-lg border border-gray-200" style={{ height: '200px' }}>
                        <ResponsiveContainer width="100%" height="100%">
                            <ComposedChart
                                key={`${selectedAptitud}-${selectedMetrica}`}
                                layout="vertical"
                                data={boxPlotData}
                                margin={{ top: 20, right: 30, left: 20, bottom: 5 }}
                            >
                                <CartesianGrid strokeDasharray="3 3" />
                                <XAxis type="number" domain={[0, selectedMetrica === 'percentil' ? 100 : 'auto']} />
                                <YAxis dataKey="name" type="category" scale="band" />
                                <Tooltip 
                                    formatter={(value) => value}
                                    labelFormatter={() => `${selectedAptitud} - ${selectedMetrica === 'percentil' ? 'Percentil' : 'Puntuación Directa'}`}
                                />
                                <Legend />
                                
                                {/* Línea para el rango completo (min a max) */}
                                <Line 
                                    dataKey="median" 
                                    stroke="transparent" 
                                    dot={false}
                                    activeDot={false}
                                    legendType="none"
                                />
                                
                                {/* Línea para el rango completo (min a max) */}
                                <Line 
                                    name="Rango (Min-Max)" 
                                    dataKey={(d) => [d.min, d.max]} 
                                    stroke="#8884d8" 
                                    strokeWidth={2}
                                    dot={{ stroke: '#8884d8', strokeWidth: 2, r: 4 }}
                                />
                                
                                {/* Caja para el rango intercuartílico (Q1 a Q3) */}
                                <Bar 
                                    name="Rango Intercuartílico (Q1-Q3)" 
                                    dataKey={(d) => [d.q1, d.q3]} 
                                    fill="#82ca9d" 
                                    barSize={30}
                                />
                                
                                {/* Línea para la mediana */}
                                <ReferenceLine 
                                    x={boxPlotData[0].median} 
                                    stroke="red" 
                                    strokeWidth={2} 
                                    label={{ value: 'Mediana', position: 'top', fill: 'red' }} 
                                />
                                
                                {/* Punto para la media */}
                                <Scatter 
                                    name="Media" 
                                    dataKey={(d) => [d.mean, d.name]} 
                                    fill="blue" 
                                    shape="star" 
                                    legendType="star"
                                />
                            </ComposedChart>
                        </ResponsiveContainer>
                    </div>
                    <p className="text-xs text-gray-500 mt-2 text-center">
                        Distribución de {selectedMetrica === 'percentil' ? 'percentiles' : 'puntuaciones directas'} para la aptitud {selectedAptitud}
                    </p>
                </div>

                {/* Tabla de medidas */}
                <h4 className="text-sm font-medium text-gray-700 mb-2">Medidas de Tendencia Central y Dispersión</h4>
                <div className="overflow-x-auto">
                    <table className="min-w-full bg-white border border-gray-200">
                        <thead>
                            <tr className="bg-gray-100">
                                <th className="py-2 px-4 border-b text-left">Medida</th>
                                <th className="py-2 px-4 border-b text-right">Valor</th>
                                <th className="py-2 px-4 border-b text-left">Interpretación</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td className="py-2 px-4 border-b font-medium">Media</td>
                                <td className="py-2 px-4 border-b text-right">{medidas?.media || 0}</td>
                                <td className="py-2 px-4 border-b text-sm">
                                    Promedio de todos los valores. Sensible a valores extremos.
                                </td>
                            </tr>
                            <tr>
                                <td className="py-2 px-4 border-b font-medium">Mediana</td>
                                <td className="py-2 px-4 border-b text-right">{medidas?.mediana || 0}</td>
                                <td className="py-2 px-4 border-b text-sm">
                                    Valor central que divide al grupo en dos mitades iguales.
                                </td>
                            </tr>
                            <tr>
                                <td className="py-2 px-4 border-b font-medium">Moda</td>
                                <td className="py-2 px-4 border-b text-right">{medidas?.moda || 0}</td>
                                <td className="py-2 px-4 border-b text-sm">
                                    Valor que aparece con mayor frecuencia.
                                </td>
                            </tr>
                            <tr>
                                <td className="py-2 px-4 border-b font-medium">Desviación Estándar</td>
                                <td className="py-2 px-4 border-b text-right">{medidas?.desviacion_estandar || 0}</td>
                                <td className="py-2 px-4 border-b text-sm">
                                    Dispersión de los datos respecto a la media. Mayor valor indica mayor heterogeneidad.
                                </td>
                            </tr>
                            <tr>
                                <td className="py-2 px-4 border-b font-medium">Rango Intercuartílico</td>
                                <td className="py-2 px-4 border-b text-right">{medidas?.rango_intercuartilico || 0}</td>
                                <td className="py-2 px-4 border-b text-sm">
                                    Diferencia entre Q3 y Q1. Muestra la dispersión del 50% central de los datos.
                                </td>
                            </tr>
                            <tr>
                                <td className="py-2 px-4 border-b font-medium">Mínimo</td>
                                <td className="py-2 px-4 border-b text-right">{medidas?.minimo || 0}</td>
                                <td className="py-2 px-4 border-b text-sm">
                                    Valor más bajo en el conjunto de datos.
                                </td>
                            </tr>
                            <tr>
                                <td className="py-2 px-4 border-b font-medium">Máximo</td>
                                <td className="py-2 px-4 border-b text-right">{medidas?.maximo || 0}</td>
                                <td className="py-2 px-4 border-b text-sm">
                                    Valor más alto en el conjunto de datos.
                                </td>
                            </tr>
                            <tr>
                                <td className="py-2 px-4 border-b font-medium">Q1 (Percentil 25)</td>
                                <td className="py-2 px-4 border-b text-right">{medidas?.q1 || 0}</td>
                                <td className="py-2 px-4 border-b text-sm">
                                    25% de los datos están por debajo de este valor.
                                </td>
                            </tr>
                            <tr>
                                <td className="py-2 px-4 border-b font-medium">Q3 (Percentil 75)</td>
                                <td className="py-2 px-4 border-b text-right">{medidas?.q3 || 0}</td>
                                <td className="py-2 px-4 border-b text-sm">
                                    75% de los datos están por debajo de este valor.
                                </td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>
        );
    };

    // Renderizar tabla de distribución de frecuencias
    const renderTablaDistribucion = () => {
        const { distribucion } = preparedChartData;

        if (!distribucion || distribucion.length === 0) {
            return (
                <div className="text-center py-8">
                    <p className="text-gray-500">No hay datos de distribución disponibles</p>
                </div>
            );
        }

        // Preparar datos para el histograma
        const histogramData = distribucion.map(bin => ({
            intervalo: `${bin.limite_inferior}-${bin.limite_superior}`,
            frecuencia: bin.frecuencia,
            porcentaje: bin.porcentaje
        }));

        return (
            <div>
                {/* Histograma */}
                <div className="mb-6">
                    <h4 className="text-sm font-medium text-gray-700 mb-2">Histograma de Distribución</h4>
                    <div className="bg-white p-2 rounded-lg border border-gray-200" style={{ height: '250px' }}>
                        <ResponsiveContainer width="100%" height="100%">
                            <BarChart
                                data={histogramData}
                                margin={{ top: 20, right: 30, left: 20, bottom: 40 }}
                            >
                                <CartesianGrid strokeDasharray="3 3" />
                                <XAxis 
                                    dataKey="intervalo" 
                                    angle={-45} 
                                    textAnchor="end"
                                    height={60}
                                    tick={{ fontSize: 10 }}
                                />
                                <YAxis 
                                    yAxisId="left"
                                    orientation="left"
                                    stroke="#8884d8"
                                    label={{ value: 'Frecuencia', angle: -90, position: 'insideLeft' }}
                                />
                                <YAxis 
                                    yAxisId="right"
                                    orientation="right"
                                    stroke="#82ca9d"
                                    label={{ value: 'Porcentaje', angle: 90, position: 'insideRight' }}
                                />
                                <Tooltip formatter={(value, name) => {
                                    return name === 'porcentaje' ? `${value}%` : value;
                                }} />
                                <Legend />
                                <Bar 
                                    yAxisId="left"
                                    dataKey="frecuencia" 
                                    name="Frecuencia" 
                                    fill="#8884d8" 
                                    radius={[4, 4, 0, 0]}
                                />
                                <Bar 
                                    yAxisId="right"
                                    dataKey="porcentaje" 
                                    name="Porcentaje" 
                                    fill="#82ca9d" 
                                    radius={[4, 4, 0, 0]}
                                />
                            </BarChart>
                        </ResponsiveContainer>
                    </div>
                    <p className="text-xs text-gray-500 mt-2 text-center">
                        Distribución de {selectedMetrica === 'percentil' ? 'percentiles' : 'puntuaciones directas'} para la aptitud {selectedAptitud}
                    </p>
                </div>

                {/* Tabla de distribución */}
                <h4 className="text-sm font-medium text-gray-700 mb-2">Tabla de Distribución de Frecuencias</h4>
                <div className="overflow-x-auto">
                    <table className="min-w-full bg-white border border-gray-200">
                        <thead>
                            <tr className="bg-gray-100">
                                <th className="py-2 px-4 border-b text-left">Intervalo</th>
                                <th className="py-2 px-4 border-b text-right">Frecuencia</th>
                                <th className="py-2 px-4 border-b text-right">Porcentaje</th>
                                <th className="py-2 px-4 border-b text-left">Visualización</th>
                            </tr>
                        </thead>
                        <tbody>
                            {distribucion.map((bin, index) => (
                                <tr key={index}>
                                    <td className="py-2 px-4 border-b">
                                        {bin.limite_inferior} - {bin.limite_superior}
                                    </td>
                                    <td className="py-2 px-4 border-b text-right">{bin.frecuencia}</td>
                                    <td className="py-2 px-4 border-b text-right">{bin.porcentaje}%</td>
                                    <td className="py-2 px-4 border-b">
                                        <div className="w-full bg-gray-200 rounded-full h-2.5">
                                            <div
                                                className="bg-blue-600 h-2.5 rounded-full"
                                                style={{ width: `${bin.porcentaje}%` }}
                                            ></div>
                                        </div>
                                    </td>
                                </tr>
                            ))}
                        </tbody>
                    </table>
                </div>
            </div>
        );
    };

    // Renderizar análisis de grupos de riesgo y talento
    const renderAnalisisGrupos = () => {
        const { grupos } = preparedChartData;

        if (!grupos) {
            return (
                <div className="text-center py-8">
                    <p className="text-gray-500">No hay datos de grupos disponibles.</p>
                </div>
            );
        }

        const { riesgo: grupoRiesgo, talento: grupoTalento } = grupos;

        return (
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                {/* Grupo de Riesgo */}
                <div className="bg-white rounded-lg shadow-md overflow-hidden">
                    <div className="bg-red-500 text-white p-3 flex items-center">
                        <FaExclamationTriangle className="mr-2" />
                        <h3 className="font-semibold">Grupo de Riesgo (Percentil ≤ 25)</h3>
                    </div>
                    <div className="p-4">
                        <p className="text-sm text-gray-600 mb-3">
                            Estudiantes que podrían necesitar apoyo adicional en esta aptitud.
                        </p>
                        {grupoRiesgo.length > 0 ? (
                            <div className="overflow-y-auto max-h-60">
                                <table className="min-w-full">
                                    <thead>
                                        <tr className="bg-gray-50">
                                            <th className="py-2 px-3 text-left text-xs font-medium text-gray-500">Estudiante</th>
                                            <th className="py-2 px-3 text-right text-xs font-medium text-gray-500">
                                                {selectedMetrica === 'percentil' ? 'Percentil' : 'Punt. Directa'}
                                            </th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        {grupoRiesgo.map((estudiante, index) => (
                                            <tr key={index} className="border-b">
                                                <td className="py-2 px-3 text-sm">
                                                    {estudiante.nombre} {estudiante.apellido}
                                                </td>
                                                <td className="py-2 px-3 text-right text-sm font-medium text-red-600">
                                                    {estudiante.valor}
                                                </td>
                                            </tr>
                                        ))}
                                    </tbody>
                                </table>
                            </div>
                        ) : (
                            <p className="text-center py-4 text-gray-500">No hay estudiantes en este grupo</p>
                        )}
                    </div>
                </div>

                {/* Grupo de Talento */}
                <div className="bg-white rounded-lg shadow-md overflow-hidden">
                    <div className="bg-green-500 text-white p-3 flex items-center">
                        <FaLightbulb className="mr-2" />
                        <h3 className="font-semibold">Grupo de Talento (Percentil ≥ 75)</h3>
                    </div>
                    <div className="p-4">
                        <p className="text-sm text-gray-600 mb-3">
                            Estudiantes con potencial para programas de enriquecimiento.
                        </p>
                        {grupoTalento.length > 0 ? (
                            <div className="overflow-y-auto max-h-60">
                                <table className="min-w-full">
                                    <thead>
                                        <tr className="bg-gray-50">
                                            <th className="py-2 px-3 text-left text-xs font-medium text-gray-500">Estudiante</th>
                                            <th className="py-2 px-3 text-right text-xs font-medium text-gray-500">
                                                {selectedMetrica === 'percentil' ? 'Percentil' : 'Punt. Directa'}
                                            </th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        {grupoTalento.map((estudiante, index) => (
                                            <tr key={index} className="border-b">
                                                <td className="py-2 px-3 text-sm">
                                                    {estudiante.nombre} {estudiante.apellido}
                                                </td>
                                                <td className="py-2 px-3 text-right text-sm font-medium text-green-600">
                                                    {estudiante.valor}
                                                </td>
                                            </tr>
                                        ))}
                                    </tbody>
                                </table>
                            </div>
                        ) : (
                            <p className="text-center py-4 text-gray-500">No hay estudiantes en este grupo</p>
                        )}
                    </div>
                </div>
            </div>
        );
    };

    // Renderizar matriz de correlaciones
    const renderMatrizCorrelaciones = () => {
        if (!correlaciones || !correlaciones.matriz_correlacion) {
            return (
                <div className="text-center py-8">
                    <p className="text-gray-500">No hay datos de correlación disponibles</p>
                </div>
            );
        }

        const matrizCorrelacion = correlaciones.matriz_correlacion;
        const aptitudesCodigos = Object.keys(matrizCorrelacion).sort();

        return (
            <div className="overflow-x-auto">
                <table className="min-w-full bg-white border border-gray-200">
                    <thead>
                        <tr className="bg-gray-100">
                            <th className="py-2 px-3 border-b">Aptitud</th>
                            {aptitudesCodigos.map(codigo => (
                                <th key={codigo} className="py-2 px-3 border-b text-center">{codigo}</th>
                            ))}
                        </tr>
                    </thead>
                    <tbody>
                        {aptitudesCodigos.map(codigo1 => (
                            <tr key={codigo1}>
                                <td className="py-2 px-3 border-b font-medium">{codigo1}</td>
                                {aptitudesCodigos.map(codigo2 => {
                                    const correlacion = matrizCorrelacion[codigo1][codigo2];
                                    let colorClass = 'bg-gray-100';

                                    if (codigo1 === codigo2) {
                                        colorClass = 'bg-gray-200';
                                    } else if (correlacion !== null) {
                                        const absCorrelacion = Math.abs(correlacion);
                                        if (absCorrelacion >= 0.7) {
                                            colorClass = correlacion > 0 ? 'bg-green-200' : 'bg-red-200';
                                        } else if (absCorrelacion >= 0.5) {
                                            colorClass = correlacion > 0 ? 'bg-green-100' : 'bg-red-100';
                                        } else if (absCorrelacion >= 0.3) {
                                            colorClass = correlacion > 0 ? 'bg-blue-50' : 'bg-orange-50';
                                        }
                                    }

                                    return (
                                        <td
                                            key={codigo2}
                                            className={`py-2 px-3 border-b text-center ${colorClass}`}
                                        >
                                            {codigo1 === codigo2 ? '1.00' :
                                                correlacion !== null ? correlacion.toFixed(2) : '-'}
                                        </td>
                                    );
                                })}
                            </tr>
                        ))}
                    </tbody>
                </table>
            </div>
        );
    };

    // Renderizar interpretaciones de correlaciones
    const renderInterpretacionesCorrelaciones = () => {
        if (!correlaciones || !correlaciones.interpretaciones || correlaciones.interpretaciones.length === 0) {
            return (
                <div className="text-center py-4">
                    <p className="text-gray-500">No hay correlaciones significativas para interpretar</p>
                </div>
            );
        }

        return (
            <div className="mt-4">
                <h4 className="font-semibold text-gray-700 mb-2">Interpretaciones Destacadas:</h4>
                <ul className="space-y-2">
                    {correlaciones.interpretaciones.slice(0, 5).map((item, index) => (
                        <li key={index} className="text-sm">
                            <span className={`inline-block w-16 text-center py-1 px-2 rounded-full text-white text-xs font-medium ${Math.abs(item.correlacion) >= 0.7 ? 'bg-purple-600' :
                                    Math.abs(item.correlacion) >= 0.5 ? 'bg-blue-600' :
                                        'bg-gray-600'
                                }`}>
                                r = {item.correlacion.toFixed(2)}
                            </span>
                            <span className="ml-2">{item.interpretacion}</span>
                        </li>
                    ))}
                </ul>
            </div>
        );
    };

    // Renderizar resumen estadístico
    const renderResumenEstadistico = () => {
        if (!medidasEstadisticas || !medidasEstadisticas.medidas_por_aptitud) {
            return null;
        }

        // Calcular promedios generales
        const promedios = {
            media_percentil: 0,
            desviacion_percentil: 0,
            media_pd: 0,
            desviacion_pd: 0,
            aptitud_mayor_media: { codigo: '', valor: 0 },
            aptitud_menor_media: { codigo: '', valor: 100 },
            aptitud_mayor_variabilidad: { codigo: '', valor: 0 }
        };

        let contadorAptitudes = 0;

        medidasEstadisticas.medidas_por_aptitud.forEach(aptitud => {
            if (aptitud.percentil && aptitud.puntuacion_directa) {
                promedios.media_percentil += aptitud.percentil.media || 0;
                promedios.desviacion_percentil += aptitud.percentil.desviacion_estandar || 0;
                promedios.media_pd += aptitud.puntuacion_directa.media || 0;
                promedios.desviacion_pd += aptitud.puntuacion_directa.desviacion_estandar || 0;

                // Encontrar aptitud con mayor media
                if ((aptitud.percentil.media || 0) > promedios.aptitud_mayor_media.valor) {
                    promedios.aptitud_mayor_media = {
                        codigo: aptitud.aptitud,
                        valor: aptitud.percentil.media || 0
                    };
                }

                // Encontrar aptitud con menor media
                if ((aptitud.percentil.media || 0) < promedios.aptitud_menor_media.valor) {
                    promedios.aptitud_menor_media = {
                        codigo: aptitud.aptitud,
                        valor: aptitud.percentil.media || 0
                    };
                }

                // Encontrar aptitud con mayor variabilidad
                if ((aptitud.percentil.desviacion_estandar || 0) > promedios.aptitud_mayor_variabilidad.valor) {
                    promedios.aptitud_mayor_variabilidad = {
                        codigo: aptitud.aptitud,
                        valor: aptitud.percentil.desviacion_estandar || 0
                    };
                }

                contadorAptitudes++;
            }
        });

        if (contadorAptitudes > 0) {
            promedios.media_percentil /= contadorAptitudes;
            promedios.desviacion_percentil /= contadorAptitudes;
            promedios.media_pd /= contadorAptitudes;
            promedios.desviacion_pd /= contadorAptitudes;
        }

        return (
            <div className="bg-blue-50 p-4 rounded-lg mb-6">
                <h3 className="text-lg font-semibold text-blue-800 mb-3">Resumen Estadístico General</h3>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                        <p className="text-sm text-blue-700">
                            <span className="font-medium">Media General (Percentil):</span>{' '}
                            {promedios.media_percentil.toFixed(1)}
                        </p>
                        <p className="text-sm text-blue-700">
                            <span className="font-medium">Variabilidad General:</span>{' '}
                            {promedios.desviacion_percentil.toFixed(1)}
                        </p>
                        <p className="text-sm text-blue-700">
                            <span className="font-medium">Aptitud con Mayor Rendimiento:</span>{' '}
                            {promedios.aptitud_mayor_media.codigo} ({promedios.aptitud_mayor_media.valor.toFixed(1)})
                        </p>
                    </div>
                    <div>
                        <p className="text-sm text-blue-700">
                            <span className="font-medium">Aptitud con Menor Rendimiento:</span>{' '}
                            {promedios.aptitud_menor_media.codigo} ({promedios.aptitud_menor_media.valor.toFixed(1)})
                        </p>
                        <p className="text-sm text-blue-700">
                            <span className="font-medium">Aptitud con Mayor Variabilidad:</span>{' '}
                            {promedios.aptitud_mayor_variabilidad.codigo} ({promedios.aptitud_mayor_variabilidad.valor.toFixed(1)})
                        </p>
                        <p className="text-sm text-blue-700">
                            <span className="font-medium">Interpretación:</span>{' '}
                            {promedios.desviacion_percentil > 20 ? 'Grupo heterogéneo' : 'Grupo homogéneo'}
                        </p>
                    </div>
                </div>
            </div>
        );
    };

    // Renderizar análisis comparativo
    const renderAnalisisComparativo = () => {
        if (!medidasEstadisticas || !medidasEstadisticas.medidas_por_aptitud) {
            return (
                <div className="text-center py-8">
                    <p className="text-gray-500">No hay datos disponibles para comparar</p>
                </div>
            );
        }

        // Filtrar las aptitudes seleccionadas
        const aptitudesData = medidasEstadisticas.medidas_por_aptitud.filter(
            m => selectedAptitudes.includes(m.aptitud)
        );

        if (aptitudesData.length === 0) {
            return (
                <div className="text-center py-8">
                    <p className="text-gray-500">Seleccione al menos una aptitud para comparar</p>
                </div>
            );
        }

        // Preparar datos para el gráfico comparativo
        const medidasComparativas = [
            { name: 'Media', label: 'Media' },
            { name: 'Mediana', label: 'Mediana' },
            { name: 'Desv. Estándar', label: 'Desviación Estándar' },
            { name: 'RIQ', label: 'Rango Intercuartílico' },
            { name: 'Mínimo', label: 'Mínimo' },
            { name: 'Máximo', label: 'Máximo' }
        ];

        // Crear datos para el gráfico de barras comparativo
        const barChartData = aptitudesData.map(aptitud => {
            const medidas = aptitud[selectedMetrica];
            return {
                aptitud: aptitud.aptitud,
                nombre: aptitud.nombre_aptitud,
                media: medidas?.media || 0,
                mediana: medidas?.mediana || 0,
                desviacion: medidas?.desviacion_estandar || 0,
                riq: medidas?.rango_intercuartilico || 0,
                minimo: medidas?.minimo || 0,
                maximo: medidas?.maximo || 0
            };
        });

        // Crear datos para el gráfico de radar
        const radarChartData = medidasComparativas.map(medida => {
            const data = { name: medida.label };
            aptitudesData.forEach(aptitud => {
                const medidas = aptitud[selectedMetrica];
                switch (medida.name) {
                    case 'Media':
                        data[aptitud.aptitud] = medidas?.media || 0;
                        break;
                    case 'Mediana':
                        data[aptitud.aptitud] = medidas?.mediana || 0;
                        break;
                    case 'Desv. Estándar':
                        data[aptitud.aptitud] = medidas?.desviacion_estandar || 0;
                        break;
                    case 'RIQ':
                        data[aptitud.aptitud] = medidas?.rango_intercuartilico || 0;
                        break;
                    case 'Mínimo':
                        data[aptitud.aptitud] = medidas?.minimo || 0;
                        break;
                    case 'Máximo':
                        data[aptitud.aptitud] = medidas?.maximo || 0;
                        break;
                    default:
                        data[aptitud.aptitud] = 0;
                }
            });
            return data;
        });

        // Crear datos para el gráfico de box plot comparativo
        const boxPlotData = aptitudesData.map(aptitud => {
            const medidas = aptitud[selectedMetrica];
            return {
                name: aptitud.aptitud,
                min: medidas?.minimo || 0,
                q1: medidas?.q1 || 0,
                median: medidas?.mediana || 0,
                q3: medidas?.q3 || 0,
                max: medidas?.maximo || 0,
                mean: medidas?.media || 0
            };
        });

        // Colores para cada aptitud
        const coloresPorAptitud = {
            'V': '#3B82F6', // Azul
            'E': '#10B981', // Verde
            'A': '#F59E0B', // Naranja
            'R': '#8B5CF6', // Púrpura
            'N': '#EC4899', // Rosa
            'M': '#EF4444', // Rojo
            'O': '#0EA5E9'  // Azul claro
        };

        return (
            <div>
                {/* Tabla comparativa */}
                <div className="mb-8">
                    <h4 className="text-sm font-medium text-gray-700 mb-2">Tabla Comparativa de Medidas</h4>
                    <div className="overflow-x-auto">
                        <table className="min-w-full bg-white border border-gray-200">
                            <thead>
                                <tr className="bg-gray-100">
                                    <th className="py-2 px-3 border-b text-left">Medida</th>
                                    {aptitudesData.map(aptitud => (
                                        <th 
                                            key={aptitud.aptitud} 
                                            className="py-2 px-3 border-b text-center"
                                            style={{ backgroundColor: coloresPorAptitud[aptitud.aptitud] + '20' }}
                                        >
                                            {aptitud.aptitud}
                                        </th>
                                    ))}
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td className="py-2 px-3 border-b font-medium">Media</td>
                                    {aptitudesData.map(aptitud => (
                                        <td 
                                            key={aptitud.aptitud} 
                                            className="py-2 px-3 border-b text-center"
                                            style={{ color: coloresPorAptitud[aptitud.aptitud] }}
                                        >
                                            {aptitud[selectedMetrica]?.media?.toFixed(2) || 0}
                                        </td>
                                    ))}
                                </tr>
                                <tr>
                                    <td className="py-2 px-3 border-b font-medium">Mediana</td>
                                    {aptitudesData.map(aptitud => (
                                        <td 
                                            key={aptitud.aptitud} 
                                            className="py-2 px-3 border-b text-center"
                                            style={{ color: coloresPorAptitud[aptitud.aptitud] }}
                                        >
                                            {aptitud[selectedMetrica]?.mediana?.toFixed(2) || 0}
                                        </td>
                                    ))}
                                </tr>
                                <tr>
                                    <td className="py-2 px-3 border-b font-medium">Desviación Estándar</td>
                                    {aptitudesData.map(aptitud => (
                                        <td 
                                            key={aptitud.aptitud} 
                                            className="py-2 px-3 border-b text-center"
                                            style={{ color: coloresPorAptitud[aptitud.aptitud] }}
                                        >
                                            {aptitud[selectedMetrica]?.desviacion_estandar?.toFixed(2) || 0}
                                        </td>
                                    ))}
                                </tr>
                                <tr>
                                    <td className="py-2 px-3 border-b font-medium">Rango Intercuartílico</td>
                                    {aptitudesData.map(aptitud => (
                                        <td 
                                            key={aptitud.aptitud} 
                                            className="py-2 px-3 border-b text-center"
                                            style={{ color: coloresPorAptitud[aptitud.aptitud] }}
                                        >
                                            {aptitud[selectedMetrica]?.rango_intercuartilico?.toFixed(2) || 0}
                                        </td>
                                    ))}
                                </tr>
                                <tr>
                                    <td className="py-2 px-3 border-b font-medium">Mínimo</td>
                                    {aptitudesData.map(aptitud => (
                                        <td 
                                            key={aptitud.aptitud} 
                                            className="py-2 px-3 border-b text-center"
                                            style={{ color: coloresPorAptitud[aptitud.aptitud] }}
                                        >
                                            {aptitud[selectedMetrica]?.minimo?.toFixed(2) || 0}
                                        </td>
                                    ))}
                                </tr>
                                <tr>
                                    <td className="py-2 px-3 border-b font-medium">Máximo</td>
                                    {aptitudesData.map(aptitud => (
                                        <td 
                                            key={aptitud.aptitud} 
                                            className="py-2 px-3 border-b text-center"
                                            style={{ color: coloresPorAptitud[aptitud.aptitud] }}
                                        >
                                            {aptitud[selectedMetrica]?.maximo?.toFixed(2) || 0}
                                        </td>
                                    ))}
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>

                {/* Gráfico de barras comparativo */}
                <div className="mb-8">
                    <h4 className="text-sm font-medium text-gray-700 mb-2">Comparativa de Medias y Medianas</h4>
                    <div className="bg-white p-2 rounded-lg border border-gray-200" style={{ height: '300px' }}>
                        <ResponsiveContainer width="100%" height="100%">
                            <BarChart
                                data={barChartData}
                                margin={{ top: 20, right: 30, left: 20, bottom: 5 }}
                            >
                                <CartesianGrid strokeDasharray="3 3" />
                                <XAxis dataKey="aptitud" />
                                <YAxis 
                                    domain={[0, selectedMetrica === 'percentil' ? 100 : 'auto']}
                                    label={{ value: selectedMetrica === 'percentil' ? 'Percentil' : 'Puntuación Directa', angle: -90, position: 'insideLeft' }}
                                />
                                <Tooltip />
                                <Legend />
                                <Bar dataKey="media" name="Media" fill="#8884d8" />
                                <Bar dataKey="mediana" name="Mediana" fill="#82ca9d" />
                            </BarChart>
                        </ResponsiveContainer>
                    </div>
                </div>

                {/* Box Plot comparativo */}
                <div className="mb-8">
                    <h4 className="text-sm font-medium text-gray-700 mb-2">Diagramas de Caja Comparativos</h4>
                    <div className="bg-white p-2 rounded-lg border border-gray-200" style={{ height: '300px' }}>
                        <ResponsiveContainer width="100%" height="100%">
                            <ComposedChart
                                layout="vertical"
                                data={boxPlotData}
                                margin={{ top: 20, right: 30, left: 20, bottom: 5 }}
                            >
                                <CartesianGrid strokeDasharray="3 3" />
                                <XAxis type="number" domain={[0, selectedMetrica === 'percentil' ? 100 : 'auto']} />
                                <YAxis dataKey="name" type="category" scale="band" />
                                <Tooltip />
                                <Legend />
                                
                                {boxPlotData.map((data, index) => (
                                    <React.Fragment key={data.name}>
                                        {/* Línea para el rango completo (min a max) */}
                                        <Line 
                                            name={`Rango ${data.name}`}
                                            dataKey={(d) => d.name === data.name ? [d.min, d.max] : null}
                                            stroke={coloresPorAptitud[data.name]}
                                            strokeWidth={2}
                                            dot={{ stroke: coloresPorAptitud[data.name], strokeWidth: 2, r: 4 }}
                                        />
                                        
                                        {/* Caja para el rango intercuartílico (Q1 a Q3) */}
                                        <Bar 
                                            name={`RIQ ${data.name}`}
                                            dataKey={(d) => d.name === data.name ? [d.q1, d.q3] : null}
                                            fill={coloresPorAptitud[data.name]}
                                            fillOpacity={0.6}
                                            barSize={20}
                                        />
                                        
                                        {/* Punto para la mediana */}
                                        <Scatter 
                                            name={`Mediana ${data.name}`}
                                            dataKey={(d) => d.name === data.name ? [d.median, d.name] : null}
                                            fill="black"
                                            shape="diamond"
                                        />
                                    </React.Fragment>
                                ))}
                            </ComposedChart>
                        </ResponsiveContainer>
                    </div>
                </div>

                {/* Interpretación comparativa */}
                <div className="bg-blue-50 p-4 rounded-lg">
                    <h4 className="font-semibold text-blue-800 mb-2">Interpretación Comparativa</h4>
                    <ul className="space-y-2 text-sm text-blue-700">
                        {aptitudesData.length > 0 && (
                            <>
                                <li>
                                    <span className="font-medium">Mayor rendimiento promedio:</span>{' '}
                                    {aptitudesData.reduce((max, aptitud) => 
                                        (aptitud[selectedMetrica]?.media || 0) > (max[selectedMetrica]?.media || 0) ? aptitud : max
                                    , aptitudesData[0]).aptitud}
                                </li>
                                <li>
                                    <span className="font-medium">Menor rendimiento promedio:</span>{' '}
                                    {aptitudesData.reduce((min, aptitud) => 
                                        (aptitud[selectedMetrica]?.media || 0) < (min[selectedMetrica]?.media || 0) ? aptitud : min
                                    , aptitudesData[0]).aptitud}
                                </li>
                                <li>
                                    <span className="font-medium">Mayor variabilidad:</span>{' '}
                                    {aptitudesData.reduce((max, aptitud) => 
                                        (aptitud[selectedMetrica]?.desviacion_estandar || 0) > (max[selectedMetrica]?.desviacion_estandar || 0) ? aptitud : max
                                    , aptitudesData[0]).aptitud}
                                </li>
                                <li>
                                    <span className="font-medium">Diferencia máxima entre aptitudes:</span>{' '}
                                    {Math.round(Math.max(...aptitudesData.map(a => a[selectedMetrica]?.media || 0)) - 
                                    Math.min(...aptitudesData.map(a => a[selectedMetrica]?.media || 0)))} puntos
                                </li>
                            </>
                        )}
                    </ul>
                </div>
            </div>
        );
    };

    // Renderizar componente principal
    return (
        <div className="space-y-6">
            {/* Título y descripción */}
            <div className="bg-white rounded-lg shadow-md p-6">
                <h2 className="text-2xl font-bold text-gray-800 mb-4 flex items-center">
                    <FaCalculator className="mr-2 text-blue-600" />
                    Análisis Estadístico Avanzado
                </h2>
                <p className="text-gray-600 mb-4">
                    Análisis detallado de medidas de tendencia central y dispersión para cada aptitud evaluada.
                    Este análisis permite identificar patrones, fortalezas y áreas de mejora a nivel grupal.
                </p>

                {/* Botones de exportación */}
                <div className="flex flex-wrap gap-2 mb-4">
                    <button
                        onClick={() => handleExport('pdf')}
                        disabled={loading || exportLoading}
                        className="flex items-center px-3 py-2 bg-red-600 text-white text-sm font-medium rounded hover:bg-red-700 disabled:bg-gray-400"
                    >
                        <FaFilePdf className="mr-2" />
                        {exportLoading === 'pdf' ? 'Exportando...' : 'Exportar a PDF'}
                    </button>
                    <button
                        onClick={() => handleExport('excel')}
                        disabled={loading || exportLoading}
                        className="flex items-center px-3 py-2 bg-green-600 text-white text-sm font-medium rounded hover:bg-green-700 disabled:bg-gray-400"
                    >
                        <FaFileExcel className="mr-2" />
                        {exportLoading === 'excel' ? 'Exportando...' : 'Exportar a Excel'}
                    </button>
                </div>

                {/* Resumen estadístico general */}
                {!comparativeMode && renderResumenEstadistico()}

                {/* Controles de selección */}
                <div className="flex flex-wrap gap-4 mt-4">
                    <div className="flex items-center mr-4">
                        <input
                            id="modo-individual"
                            type="radio"
                            name="modo-analisis"
                            checked={!comparativeMode}
                            onChange={() => setComparativeMode(false)}
                            className="w-4 h-4 text-blue-600 bg-gray-100 border-gray-300 focus:ring-blue-500"
                        />
                        <label htmlFor="modo-individual" className="ml-2 text-sm font-medium text-gray-700">
                            Análisis Individual
                        </label>
                    </div>
                    <div className="flex items-center">
                        <input
                            id="modo-comparativo"
                            type="radio"
                            name="modo-analisis"
                            checked={comparativeMode}
                            onChange={() => setComparativeMode(true)}
                            className="w-4 h-4 text-blue-600 bg-gray-100 border-gray-300 focus:ring-blue-500"
                        />
                        <label htmlFor="modo-comparativo" className="ml-2 text-sm font-medium text-gray-700">
                            Análisis Comparativo
                        </label>
                    </div>
                </div>

                <div className="flex flex-wrap gap-4 mt-4">
                    {!comparativeMode ? (
                        <>
                            <div>
                                <label className="block text-sm font-medium text-gray-700 mb-1">Aptitud:</label>
                                <select
                                    value={selectedAptitud}
                                    onChange={(e) => setSelectedAptitud(e.target.value)}
                                    className="bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block w-full p-2.5"
                                >
                                    {aptitudes.map((aptitud) => (
                                        <option key={aptitud.codigo} value={aptitud.codigo}>
                                            {aptitud.nombre} ({aptitud.codigo})
                                        </option>
                                    ))}
                                </select>
                            </div>
                        </>
                    ) : (
                        <div className="w-full">
                            <label className="block text-sm font-medium text-gray-700 mb-1">Aptitudes a comparar:</label>
                            <div className="flex flex-wrap gap-2">
                                {aptitudes.map((aptitud) => (
                                    <div key={aptitud.codigo} className="flex items-center">
                                        <input
                                            id={`aptitud-${aptitud.codigo}`}
                                            type="checkbox"
                                            checked={selectedAptitudes.includes(aptitud.codigo)}
                                            onChange={(e) => {
                                                if (e.target.checked) {
                                                    setSelectedAptitudes([...selectedAptitudes, aptitud.codigo]);
                                                } else {
                                                    setSelectedAptitudes(selectedAptitudes.filter(a => a !== aptitud.codigo));
                                                }
                                            }}
                                            className="w-4 h-4 text-blue-600 bg-gray-100 border-gray-300 rounded focus:ring-blue-500"
                                        />
                                        <label htmlFor={`aptitud-${aptitud.codigo}`} className="ml-1 mr-3 text-sm font-medium text-gray-700">
                                            {aptitud.nombre} ({aptitud.codigo})
                                        </label>
                                    </div>
                                ))}
                            </div>
                        </div>
                    )}
                    <div>
                        <label className="block text-sm font-medium text-gray-700 mb-1">Métrica:</label>
                        <select
                            value={selectedMetrica}
                            onChange={(e) => setSelectedMetrica(e.target.value)}
                            className="bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block w-full p-2.5"
                        >
                            <option value="percentil">Percentil (PC)</option>
                            <option value="puntuacion_directa">Puntuación Directa (PD)</option>
                        </select>
                    </div>
                </div>
            </div>

            {loading ? (
                <div className="bg-white rounded-lg shadow-md p-6 text-center">
                    <div className="animate-pulse flex flex-col items-center">
                        <div className="h-8 bg-gray-200 rounded w-3/4 mb-4"></div>
                        <div className="h-4 bg-gray-200 rounded w-1/2 mb-2"></div>
                        <div className="h-4 bg-gray-200 rounded w-5/6 mb-2"></div>
                        <div className="h-4 bg-gray-200 rounded w-3/4 mb-2"></div>
                    </div>
                </div>
            ) : error ? (
                <div className="bg-white rounded-lg shadow-md p-6">
                    <div className="text-center text-red-500">
                        <FaExclamationTriangle className="mx-auto text-3xl mb-2" />
                        <p>{error}</p>
                    </div>
                </div>
            ) : comparativeMode ? (
                <Card>
                    <CardHeader className="bg-gradient-to-r from-indigo-500 to-indigo-600 text-white">
                        <h3 className="text-lg font-semibold flex items-center justify-center">
                            <FaChartLine className="mr-2" />
                            Análisis Comparativo de Aptitudes
                        </h3>
                    </CardHeader>
                    <CardBody className="p-6">
                        {renderAnalisisComparativo()}
                    </CardBody>
                </Card>
            ) : (
                <>
                    {/* Medidas de Tendencia Central y Dispersión */}
                    <Card>
                        <CardHeader className="bg-gradient-to-r from-blue-500 to-blue-600 text-white">
                            <h3 className="text-lg font-semibold flex items-center justify-center">
                                <FaCalculator className="mr-2" />
                                Medidas de Tendencia Central y Dispersión
                            </h3>
                        </CardHeader>
                        <CardBody className="p-6">
                            {renderTablaMedidas()}
                        </CardBody>
                    </Card>

                    {/* Distribución de Frecuencias */}
                    <Card>
                        <CardHeader className="bg-gradient-to-r from-green-500 to-green-600 text-white">
                            <h3 className="text-lg font-semibold flex items-center justify-center">
                                <FaChartBar className="mr-2" />
                                Distribución de Frecuencias
                            </h3>
                        </CardHeader>
                        <CardBody className="p-6">
                            {renderTablaDistribucion()}
                        </CardBody>
                    </Card>

                    {/* Análisis de Grupos */}
                    <Card>
                        <CardHeader className="bg-gradient-to-r from-purple-500 to-purple-600 text-white">
                            <h3 className="text-lg font-semibold flex items-center justify-center">
                                <FaUsers className="mr-2" />
                                Análisis de Grupos de Riesgo y Talento
                            </h3>
                        </CardHeader>
                        <CardBody className="p-6">
                            {renderAnalisisGrupos()}
                        </CardBody>
                    </Card>

                    {/* Matriz de Correlaciones */}
                    <Card>
                        <CardHeader className="bg-gradient-to-r from-orange-500 to-orange-600 text-white">
                            <h3 className="text-lg font-semibold flex items-center justify-center">
                                <FaTable className="mr-2" />
                                Matriz de Correlaciones entre Aptitudes
                            </h3>
                        </CardHeader>
                        <CardBody className="p-6">
                            {renderMatrizCorrelaciones()}
                            {renderInterpretacionesCorrelaciones()}
                        </CardBody>
                    </Card>
                    
                    {/* Recomendaciones Pedagógicas */}
                    <Card>
                        <CardHeader className="bg-gradient-to-r from-yellow-500 to-yellow-600 text-white">
                            <h3 className="text-lg font-semibold flex items-center justify-center">
                                <FaLightbulb className="mr-2" />
                                Recomendaciones Pedagógicas
                            </h3>
                        </CardHeader>
                        <CardBody className="p-6">
                            <RecommendationPanel 
                                medidasEstadisticas={medidasEstadisticas} 
                                gruposAnalisis={gruposAnalisis} 
                                correlaciones={correlaciones} 
                            />
                        </CardBody>
                    </Card>
                </>
            )}
        </div>
    );
};

export default StatisticalAnalysis;