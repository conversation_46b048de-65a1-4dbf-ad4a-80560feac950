import React, { useState, useEffect } from 'react';
import { Card, CardHeader, CardBody } from '../ui/Card';
import { FaChartBar, FaCalculator, FaTable, FaExclamationTriangle, FaLightbulb, FaChartLine, FaUsers, FaBoxOpen, FaFilePdf, FaFileExcel, FaDownload } from 'react-icons/fa';
import StatisticsService from '../../services/statisticsService';
// Import the dynamic export service instead of the direct one
import DynamicExportService from '../../../dynamicExportService';
import RecommendationPanel from './RecommendationPanel';
import { toast } from 'react-toastify';
import { 
  BarChart, Bar, XAxis, YAxis, CartesianGrid, Tooltip, Legend, ResponsiveContainer,
  ComposedChart, Line, Area, ScatterChart, Scatter, ZAxis, ReferenceLine
} from 'recharts';

/**
 * Componente para análisis estadístico avanzado
 * Muestra medidas de tendencia central y dispersión para las aptitudes BAT-7
 */
const StatisticalAnalysis = ({ filters = {} }) => {
    const [loading, setLoading] = useState(true);
    const [error, setError] = useState(null);
    const [medidasEstadisticas, setMedidasEstadisticas] = useState(null);
    const [distribucionFrecuencias, setDistribucionFrecuencias] = useState(null);
    const [selectedAptitud, setSelectedAptitud] = useState('V');
    const [selectedMetrica, setSelectedMetrica] = useState('percentil');
    const [gruposAnalisis, setGruposAnalisis] = useState(null);
    const [correlaciones, setCorrelaciones] = useState(null);
    const [comparativeMode, setComparativeMode] = useState(false);
    const [selectedAptitudes, setSelectedAptitudes] = useState(['V', 'R']);
    const [exportLoading, setExportLoading] = useState(false);

    // Códigos de aptitudes BAT-7
    const aptitudes = [
        { codigo: 'V', nombre: 'Verbal' },
        { codigo: 'E', nombre: 'Espacial' },
        { codigo: 'A', nombre: 'Atención' },
        { codigo: 'R', nombre: 'Razonamiento' },
        { codigo: 'N', nombre: 'Numérico' },
        { codigo: 'M', nombre: 'Mecánico' },
        { codigo: 'O', nombre: 'Ortografía' }
    ];

    // Manejar exportación de datos
    const handleExport = async (format) => {
        try {
            setExportLoading(format);
            
            // Preparar datos para exportación
            const exportData = {
                title: `Análisis Estadístico BAT-7 - ${new Date().toLocaleDateString()}`,
                mode: comparativeMode ? 'comparativo' : 'individual',
                aptitud: selectedAptitud,
                aptitudes_seleccionadas: selectedAptitudes,
                metrica: selectedMetrica,
                medidas_estadisticas: medidasEstadisticas,
                distribucion_frecuencias: distribucionFrecuencias,
                grupos_analisis: gruposAnalisis,
                correlaciones: correlaciones,
                fecha_generacion: new Date().toISOString()
            };
            
            // Exportar según formato usando el servicio dinámico
            if (format === 'pdf') {
                await DynamicExportService.exportToPDF(exportData, {
                    title: 'Análisis Estadístico BAT-7',
                    includeGraphics: true,
                    includeDetailedData: true
                });
                toast.success('Análisis estadístico exportado a PDF correctamente');
            } else if (format === 'excel') {
                await DynamicExportService.exportToExcel(exportData, {
                    includeDetailedData: true
                });
                toast.success('Análisis estadístico exportado a Excel correctamente');
            }
            
        } catch (error) {
            console.error(`❌ [StatisticalAnalysis] Error al exportar a ${format}:`, error);
            toast.error(`Error al exportar a ${format}`);
        } finally {
            setExportLoading(false);
        }
    };

    // Cargar datos estadísticos
    useEffect(() => {
        const fetchData = async () => {
            try {
                setLoading(true);
                setError(null);

                console.log('📊 [StatisticalAnalysis] Cargando datos estadísticos...');

                // Cargar todos los datos en paralelo
                const [medidas, distribucion, grupos, correlacionesData] = await Promise.all([
                    StatisticsService.getMedidasTodasAptitudes(filters),
                    StatisticsService.getDistribucionTodasAptitudes(filters),
                    StatisticsService.getAnalisisGrupos(filters),
                    StatisticsService.getAnalisisCorrelacion(filters)
                ]);

                setMedidasEstadisticas(medidas);
                setDistribucionFrecuencias(distribucion);
                setGruposAnalisis(grupos);
                setCorrelaciones(correlacionesData);

                console.log('✅ [StatisticalAnalysis] Datos estadísticos cargados');
            } catch (error) {
                console.error('❌ [StatisticalAnalysis] Error al cargar datos estadísticos:', error);
                setError('Error al cargar datos estadísticos');
                toast.error('Error al cargar análisis estadístico');
            } finally {
                setLoading(false);
            }
        };

        fetchData();
    }, [filters]);

    // Renderizar componente principal
    return (
        <div className="space-y-6">
            <div className="bg-white rounded-lg shadow-lg p-6">
                <div className="flex flex-col md:flex-row md:items-center md:justify-between gap-4 mb-6">
                    <div>
                        <h2 className="text-2xl font-bold text-gray-800 flex items-center">
                            <FaCalculator className="mr-2 text-blue-600" />
                            Análisis Estadístico Avanzado
                        </h2>
                        <p className="text-gray-600 mt-1">
                            Medidas de tendencia central y dispersión para aptitudes BAT-7
                        </p>
                    </div>
                    
                    <div className="flex flex-wrap gap-2">
                        <button
                            onClick={() => handleExport('pdf')}
                            disabled={loading || exportLoading}
                            className="flex items-center px-3 py-2 bg-red-600 text-white rounded-lg hover:bg-red-700 transition-colors disabled:opacity-50"
                        >
                            {exportLoading === 'pdf' ? (
                                <span className="inline-block w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin mr-2"></span>
                            ) : (
                                <FaFilePdf className="mr-2" />
                            )}
                            Exportar PDF
                        </button>
                        
                        <button
                            onClick={() => handleExport('excel')}
                            disabled={loading || exportLoading}
                            className="flex items-center px-3 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors disabled:opacity-50"
                        >
                            {exportLoading === 'excel' ? (
                                <span className="inline-block w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin mr-2"></span>
                            ) : (
                                <FaFileExcel className="mr-2" />
                            )}
                            Exportar Excel
                        </button>
                    </div>
                </div>
                
                {loading ? (
                    <div className="py-12 text-center">
                        <div className="inline-block w-8 h-8 border-4 border-blue-500 border-t-transparent rounded-full animate-spin mb-4"></div>
                        <p className="text-gray-500">Cargando análisis estadístico...</p>
                    </div>
                ) : error ? (
                    <div className="py-12 text-center">
                        <FaExclamationTriangle className="text-4xl text-red-500 mx-auto mb-4" />
                        <p className="text-red-500">{error}</p>
                        <button
                            onClick={() => window.location.reload()}
                            className="mt-4 px-4 py-2 bg-blue-500 text-white rounded-lg hover:bg-blue-600"
                        >
                            <FaRedo className="inline-block mr-2" />
                            Reintentar
                        </button>
                    </div>
                ) : (
                    <div>
                        {/* Selector de aptitud y métrica */}
                        <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
                            <div>
                                <label className="block text-sm font-medium text-gray-700 mb-2">
                                    Seleccionar Aptitud:
                                </label>
                                <div className="flex flex-wrap gap-2">
                                    {aptitudes.map(aptitud => (
                                        <button
                                            key={aptitud.codigo}
                                            onClick={() => setSelectedAptitud(aptitud.codigo)}
                                            className={`px-3 py-1 rounded-full text-sm font-medium transition-colors ${
                                                selectedAptitud === aptitud.codigo
                                                    ? 'bg-blue-600 text-white'
                                                    : 'bg-gray-200 text-gray-700 hover:bg-gray-300'
                                            }`}
                                        >
                                            {aptitud.codigo} - {aptitud.nombre}
                                        </button>
                                    ))}
                                </div>
                            </div>
                            
                            <div>
                                <label className="block text-sm font-medium text-gray-700 mb-2">
                                    Seleccionar Métrica:
                                </label>
                                <div className="flex gap-4">
                                    <label className="inline-flex items-center">
                                        <input
                                            type="radio"
                                            name="metrica"
                                            value="percentil"
                                            checked={selectedMetrica === 'percentil'}
                                            onChange={() => setSelectedMetrica('percentil')}
                                            className="form-radio h-4 w-4 text-blue-600"
                                        />
                                        <span className="ml-2 text-gray-700">Percentil (PC)</span>
                                    </label>
                                    
                                    <label className="inline-flex items-center">
                                        <input
                                            type="radio"
                                            name="metrica"
                                            value="puntuacion_directa"
                                            checked={selectedMetrica === 'puntuacion_directa'}
                                            onChange={() => setSelectedMetrica('puntuacion_directa')}
                                            className="form-radio h-4 w-4 text-blue-600"
                                        />
                                        <span className="ml-2 text-gray-700">Puntuación Directa (PD)</span>
                                    </label>
                                </div>
                            </div>
                        </div>
                        
                        {/* Contenido principal */}
                        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                            {/* Medidas de tendencia central */}
                            <Card>
                                <CardHeader className="bg-blue-500 text-white">
                                    <h3 className="text-lg font-semibold flex items-center">
                                        <FaChartBar className="mr-2" />
                                        Medidas de Tendencia Central y Dispersión
                                    </h3>
                                </CardHeader>
                                <CardBody>
                                    {/* Contenido de medidas */}
                                    <p className="text-center text-gray-500">
                                        Análisis estadístico para aptitud {selectedAptitud} ({selectedMetrica})
                                    </p>
                                </CardBody>
                            </Card>
                            
                            {/* Distribución de frecuencias */}
                            <Card>
                                <CardHeader className="bg-green-500 text-white">
                                    <h3 className="text-lg font-semibold flex items-center">
                                        <FaChartLine className="mr-2" />
                                        Distribución de Frecuencias
                                    </h3>
                                </CardHeader>
                                <CardBody>
                                    {/* Contenido de distribución */}
                                    <p className="text-center text-gray-500">
                                        Histograma para aptitud {selectedAptitud} ({selectedMetrica})
                                    </p>
                                </CardBody>
                            </Card>
                        </div>
                        
                        {/* Análisis de grupos */}
                        <div className="mt-6">
                            <Card>
                                <CardHeader className="bg-purple-500 text-white">
                                    <h3 className="text-lg font-semibold flex items-center">
                                        <FaUsers className="mr-2" />
                                        Análisis de Grupos de Riesgo y Talento
                                    </h3>
                                </CardHeader>
                                <CardBody>
                                    {/* Contenido de grupos */}
                                    <p className="text-center text-gray-500">
                                        Análisis de grupos para aptitud {selectedAptitud}
                                    </p>
                                </CardBody>
                            </Card>
                        </div>
                        
                        {/* Matriz de correlaciones */}
                        <div className="mt-6">
                            <Card>
                                <CardHeader className="bg-orange-500 text-white">
                                    <h3 className="text-lg font-semibold flex items-center">
                                        <FaProjectDiagram className="mr-2" />
                                        Matriz de Correlaciones
                                    </h3>
                                </CardHeader>
                                <CardBody>
                                    {/* Contenido de correlaciones */}
                                    <p className="text-center text-gray-500">
                                        Correlaciones entre aptitudes
                                    </p>
                                </CardBody>
                            </Card>
                        </div>
                    </div>
                )}
            </div>
        </div>
    );
};

export default StatisticalAnalysis;