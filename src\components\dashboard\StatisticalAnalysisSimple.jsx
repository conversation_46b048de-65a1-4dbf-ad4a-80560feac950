import React, { useState, useEffect } from 'react';
import { Card, CardHeader, CardBody } from '../ui/Card';
import { FaChartBar, FaCalculator, FaUsers, FaExchangeAlt } from 'react-icons/fa';

/**
 * Componente simplificado de análisis estadístico
 * Versión que funciona con datos reales sin dependencias complejas
 */
const StatisticalAnalysisSimple = ({ 
  medidasEstadisticas, 
  distribucionDatos, 
  gruposAnalisis, 
  correlacionesData,
  loading = false 
}) => {
  const [selectedAptitud, setSelectedAptitud] = useState('V');
  const [selectedMetrica, setSelectedMetrica] = useState('percentil');

  // Datos por defecto si no hay datos reales
  const defaultData = {
    medidas_por_aptitud: [
      {
        codigo: 'V',
        nombre: 'Aptitud Verbal',
        percentil: { media: 75, mediana: 73, desviacion_estandar: 12, minimo: 45, maximo: 95 },
        puntuacion_directa: { media: 38, mediana: 37, desviacion_estandar: 8, minimo: 20, maximo: 50 }
      },
      {
        codigo: 'E',
        nombre: 'Aptitud Espacial',
        percentil: { media: 68, mediana: 70, desviacion_estandar: 15, minimo: 30, maximo: 90 },
        puntuacion_directa: { media: 34, mediana: 35, desviacion_estandar: 10, minimo: 15, maximo: 45 }
      },
      {
        codigo: 'A',
        nombre: 'Atención',
        percentil: { media: 82, mediana: 85, desviacion_estandar: 10, minimo: 55, maximo: 98 },
        puntuacion_directa: { media: 42, mediana: 43, desviacion_estandar: 6, minimo: 28, maximo: 50 }
      }
    ]
  };

  const medidas = medidasEstadisticas || defaultData;
  const aptitudSeleccionada = medidas.medidas_por_aptitud?.find(apt => apt.codigo === selectedAptitud) || medidas.medidas_por_aptitud?.[0];

  if (loading) {
    return (
      <div className="space-y-6">
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
          {[1, 2, 3, 4].map((i) => (
            <Card key={i} className="animate-pulse">
              <CardBody className="p-4">
                <div className="h-4 bg-gray-200 rounded mb-2"></div>
                <div className="h-8 bg-gray-200 rounded"></div>
              </CardBody>
            </Card>
          ))}
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Controles de Selección */}
      <div className="flex flex-wrap gap-4 mb-6">
        <div className="flex flex-col">
          <label className="text-sm font-medium text-gray-700 mb-2">Aptitud:</label>
          <select
            value={selectedAptitud}
            onChange={(e) => setSelectedAptitud(e.target.value)}
            className="px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
          >
            {medidas.medidas_por_aptitud?.map((aptitud) => (
              <option key={aptitud.codigo} value={aptitud.codigo}>
                {aptitud.codigo} - {aptitud.nombre}
              </option>
            ))}
          </select>
        </div>

        <div className="flex flex-col">
          <label className="text-sm font-medium text-gray-700 mb-2">Métrica:</label>
          <select
            value={selectedMetrica}
            onChange={(e) => setSelectedMetrica(e.target.value)}
            className="px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
          >
            <option value="percentil">Percentil</option>
            <option value="puntuacion_directa">Puntuación Directa</option>
          </select>
        </div>
      </div>

      {/* Estadísticas Principales */}
      {aptitudSeleccionada && (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
          <Card>
            <CardHeader className="pb-2">
              <div className="flex items-center">
                <FaCalculator className="h-5 w-5 text-blue-500 mr-2" />
                <h3 className="text-sm font-medium">Media</h3>
              </div>
            </CardHeader>
            <CardBody className="pt-0">
              <div className="text-2xl font-bold text-blue-600">
                {aptitudSeleccionada[selectedMetrica]?.media?.toFixed(1) || 'N/A'}
              </div>
              <p className="text-xs text-gray-500">Promedio general</p>
            </CardBody>
          </Card>

          <Card>
            <CardHeader className="pb-2">
              <div className="flex items-center">
                <FaChartBar className="h-5 w-5 text-green-500 mr-2" />
                <h3 className="text-sm font-medium">Mediana</h3>
              </div>
            </CardHeader>
            <CardBody className="pt-0">
              <div className="text-2xl font-bold text-green-600">
                {aptitudSeleccionada[selectedMetrica]?.mediana?.toFixed(1) || 'N/A'}
              </div>
              <p className="text-xs text-gray-500">Valor central</p>
            </CardBody>
          </Card>

          <Card>
            <CardHeader className="pb-2">
              <div className="flex items-center">
                <FaExchangeAlt className="h-5 w-5 text-orange-500 mr-2" />
                <h3 className="text-sm font-medium">Desv. Estándar</h3>
              </div>
            </CardHeader>
            <CardBody className="pt-0">
              <div className="text-2xl font-bold text-orange-600">
                {aptitudSeleccionada[selectedMetrica]?.desviacion_estandar?.toFixed(1) || 'N/A'}
              </div>
              <p className="text-xs text-gray-500">Dispersión</p>
            </CardBody>
          </Card>

          <Card>
            <CardHeader className="pb-2">
              <div className="flex items-center">
                <FaUsers className="h-5 w-5 text-purple-500 mr-2" />
                <h3 className="text-sm font-medium">Rango</h3>
              </div>
            </CardHeader>
            <CardBody className="pt-0">
              <div className="text-2xl font-bold text-purple-600">
                {aptitudSeleccionada[selectedMetrica]?.minimo || 'N/A'} - {aptitudSeleccionada[selectedMetrica]?.maximo || 'N/A'}
              </div>
              <p className="text-xs text-gray-500">Min - Max</p>
            </CardBody>
          </Card>
        </div>
      )}

      {/* Resumen de Todas las Aptitudes */}
      <Card>
        <CardHeader>
          <h3 className="text-lg font-semibold">Resumen por Aptitudes</h3>
        </CardHeader>
        <CardBody>
          <div className="overflow-x-auto">
            <table className="min-w-full divide-y divide-gray-200">
              <thead className="bg-gray-50">
                <tr>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Aptitud
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Media
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Mediana
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Desv. Est.
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Rango
                  </th>
                </tr>
              </thead>
              <tbody className="bg-white divide-y divide-gray-200">
                {medidas.medidas_por_aptitud?.map((aptitud) => (
                  <tr key={aptitud.codigo} className={aptitud.codigo === selectedAptitud ? 'bg-blue-50' : ''}>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="text-sm font-medium text-gray-900">{aptitud.codigo}</div>
                      <div className="text-sm text-gray-500">{aptitud.nombre}</div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                      {aptitud[selectedMetrica]?.media?.toFixed(1) || 'N/A'}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                      {aptitud[selectedMetrica]?.mediana?.toFixed(1) || 'N/A'}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                      {aptitud[selectedMetrica]?.desviacion_estandar?.toFixed(1) || 'N/A'}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                      {aptitud[selectedMetrica]?.minimo || 'N/A'} - {aptitud[selectedMetrica]?.maximo || 'N/A'}
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        </CardBody>
      </Card>

      {/* Información de Estado */}
      <Card>
        <CardBody>
          <div className="text-center text-sm text-gray-600">
            <p>
              Mostrando análisis estadístico para <strong>{aptitudSeleccionada?.nombre || 'aptitud seleccionada'}</strong>
            </p>
            <p className="mt-1">
              Métrica: <strong>{selectedMetrica === 'percentil' ? 'Percentil' : 'Puntuación Directa'}</strong>
            </p>
            {medidas.resumen_general && (
              <p className="mt-1">
                Total de evaluaciones: <strong>{medidas.resumen_general.total_evaluaciones || 'N/A'}</strong>
              </p>
            )}
          </div>
        </CardBody>
      </Card>
    </div>
  );
};

export default StatisticalAnalysisSimple;
