import React, { memo, useMemo } from 'react';
// import { Line } from 'react-chartjs-2'; // Comentado temporalmente
// import Chart.js imports comentados temporalmente

/**
 * Gráfico de Perfil Aptitudinal para BAT-7
 * Muestra las puntuaciones percentiles de las 8 aptitudes en un gráfico de líneas
 */
const AptitudeProfileChart = ({ 
  data, 
  showPercentiles = true, 
  showDirectScores = false,
  height = 400,
  interactive = true 
}) => {
  
  const aptitudeLabels = {
    V: 'Verbal',
    E: 'Espacial', 
    A: 'Atención',
    CON: 'Concentración',
    R: 'Razonamiento',
    N: 'Numérico',
    M: 'Mecánico',
    O: 'Ortografía'
  };

  const aptitudeOrder = ['V', 'E', 'A', 'CON', 'R', 'N', 'M', 'O'];

  // Configurar datos del gráfico
  const chartData = useMemo(() => {
    if (!data || !data.puntuaciones) {
      return {
        labels: aptitudeOrder.map(apt => aptitudeLabels[apt]),
        datasets: []
      };
    }

    const datasets = [];

    // Dataset para percentiles
    if (showPercentiles) {
      const percentileData = aptitudeOrder.map(apt => 
        data.puntuaciones[apt]?.pc || 0
      );

      datasets.push({
        label: 'Percentiles (Pc)',
        data: percentileData,
        borderColor: 'rgb(59, 130, 246)',
        backgroundColor: 'rgba(59, 130, 246, 0.1)',
        borderWidth: 3,
        pointBackgroundColor: 'rgb(59, 130, 246)',
        pointBorderColor: '#fff',
        pointBorderWidth: 2,
        pointRadius: 6,
        pointHoverRadius: 8,
        fill: true,
        tension: 0.3
      });
    }

    // Dataset para puntuaciones directas (opcional)
    if (showDirectScores) {
      const directData = aptitudeOrder.map(apt => 
        data.puntuaciones[apt]?.pd || 0
      );

      datasets.push({
        label: 'Puntuaciones Directas (PD)',
        data: directData,
        borderColor: 'rgb(16, 185, 129)',
        backgroundColor: 'rgba(16, 185, 129, 0.1)',
        borderWidth: 2,
        pointBackgroundColor: 'rgb(16, 185, 129)',
        pointBorderColor: '#fff',
        pointBorderWidth: 2,
        pointRadius: 4,
        pointHoverRadius: 6,
        fill: false,
        tension: 0.3,
        yAxisID: 'y1'
      });
    }

    return {
      labels: aptitudeOrder.map(apt => aptitudeLabels[apt]),
      datasets
    };
  }, [data, showPercentiles, showDirectScores]);

  // Configuración del gráfico
  const options = useMemo(() => ({
    responsive: true,
    maintainAspectRatio: false,
    interaction: {
      mode: interactive ? 'index' : 'none',
      intersect: false,
    },
    plugins: {
      title: {
        display: true,
        text: 'Perfil Aptitudinal BAT-7',
        font: {
          size: 16,
          weight: 'bold'
        },
        color: '#374151'
      },
      legend: {
        position: 'top',
        labels: {
          usePointStyle: true,
          padding: 20,
          font: {
            size: 12
          }
        }
      },
      tooltip: {
        enabled: interactive,
        backgroundColor: 'rgba(0, 0, 0, 0.8)',
        titleColor: '#fff',
        bodyColor: '#fff',
        borderColor: 'rgba(59, 130, 246, 0.5)',
        borderWidth: 1,
        cornerRadius: 8,
        displayColors: true,
        callbacks: {
          title: function(context) {
            return `Aptitud: ${context[0].label}`;
          },
          label: function(context) {
            const value = context.parsed.y;
            const aptitudeKey = aptitudeOrder[context.dataIndex];
            const nivel = data?.puntuaciones?.[aptitudeKey]?.nivel || 'N/A';
            
            if (context.dataset.label.includes('Percentiles')) {
              return `Percentil: ${value} (${nivel})`;
            } else {
              return `PD: ${value}`;
            }
          },
          afterLabel: function(context) {
            if (context.dataset.label.includes('Percentiles')) {
              const value = context.parsed.y;
              let interpretation = '';
              
              if (value >= 90) interpretation = 'Muy Alto';
              else if (value >= 75) interpretation = 'Alto';
              else if (value >= 60) interpretation = 'Medio-Alto';
              else if (value >= 40) interpretation = 'Medio';
              else if (value >= 25) interpretation = 'Medio-Bajo';
              else if (value >= 10) interpretation = 'Bajo';
              else interpretation = 'Muy Bajo';
              
              return `Nivel: ${interpretation}`;
            }
            return '';
          }
        }
      }
    },
    scales: {
      x: {
        grid: {
          display: true,
          color: 'rgba(0, 0, 0, 0.1)'
        },
        ticks: {
          font: {
            size: 11
          },
          maxRotation: 45
        }
      },
      y: {
        type: 'linear',
        display: true,
        position: 'left',
        min: 0,
        max: 100,
        grid: {
          display: true,
          color: 'rgba(0, 0, 0, 0.1)'
        },
        ticks: {
          stepSize: 10,
          font: {
            size: 11
          },
          callback: function(value) {
            return value + '%';
          }
        },
        title: {
          display: true,
          text: 'Percentiles',
          font: {
            size: 12,
            weight: 'bold'
          }
        }
      },
      ...(showDirectScores && {
        y1: {
          type: 'linear',
          display: true,
          position: 'right',
          min: 0,
          grid: {
            drawOnChartArea: false,
          },
          ticks: {
            font: {
              size: 11
            }
          },
          title: {
            display: true,
            text: 'Puntuaciones Directas',
            font: {
              size: 12,
              weight: 'bold'
            }
          }
        }
      })
    },
    // Líneas de referencia para niveles
    annotation: {
      annotations: {
        line1: {
          type: 'line',
          yMin: 75,
          yMax: 75,
          borderColor: 'rgba(34, 197, 94, 0.5)',
          borderWidth: 2,
          borderDash: [5, 5],
          label: {
            content: 'Alto (Pc 75)',
            enabled: true,
            position: 'end'
          }
        },
        line2: {
          type: 'line',
          yMin: 25,
          yMax: 25,
          borderColor: 'rgba(239, 68, 68, 0.5)',
          borderWidth: 2,
          borderDash: [5, 5],
          label: {
            content: 'Bajo (Pc 25)',
            enabled: true,
            position: 'end'
          }
        },
        line3: {
          type: 'line',
          yMin: 50,
          yMax: 50,
          borderColor: 'rgba(156, 163, 175, 0.5)',
          borderWidth: 1,
          borderDash: [3, 3],
          label: {
            content: 'Medio (Pc 50)',
            enabled: true,
            position: 'end'
          }
        }
      }
    }
  }), [data, interactive, showDirectScores]);

  // Función para obtener el color del nivel
  const getNivelColor = (percentil) => {
    if (percentil >= 90) return 'text-green-700';
    if (percentil >= 75) return 'text-green-600';
    if (percentil >= 60) return 'text-blue-600';
    if (percentil >= 40) return 'text-yellow-600';
    if (percentil >= 25) return 'text-orange-600';
    if (percentil >= 10) return 'text-red-600';
    return 'text-red-700';
  };

  if (!data || !data.puntuaciones) {
    return (
      <div className="flex items-center justify-center h-64 bg-gray-50 rounded-lg">
        <div className="text-center">
          <div className="text-gray-400 text-4xl mb-2">📊</div>
          <p className="text-gray-500">No hay datos disponibles para mostrar el perfil aptitudinal</p>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-4">
      {/* Gráfico principal - Placeholder temporal */}
      <div style={{ height: `${height}px` }} className="flex items-center justify-center bg-gray-100 rounded-lg border-2 border-dashed border-gray-300">
        <div className="text-center">
          <div className="text-4xl text-gray-400 mb-2">📈</div>
          <p className="text-gray-600 font-medium">Gráfico de Perfil Aptitudinal</p>
          <p className="text-sm text-gray-500 mt-1">Chart.js será instalado próximamente</p>
        </div>
      </div>

      {/* Resumen de puntuaciones */}
      <div className="grid grid-cols-2 md:grid-cols-4 gap-3 mt-4">
        {aptitudeOrder.map(apt => {
          const scores = data.puntuaciones[apt];
          if (!scores) return null;
          
          return (
            <div key={apt} className="bg-white border rounded-lg p-3 text-center">
              <div className="text-xs text-gray-500 mb-1">{aptitudeLabels[apt]}</div>
              <div className={`text-lg font-bold ${getNivelColor(scores.pc)}`}>
                Pc {scores.pc}
              </div>
              <div className="text-xs text-gray-400">
                PD {scores.pd}
              </div>
              <div className="text-xs mt-1">
                <span className={`px-2 py-1 rounded-full text-xs ${
                  scores.pc >= 75 ? 'bg-green-100 text-green-700' :
                  scores.pc >= 25 ? 'bg-yellow-100 text-yellow-700' :
                  'bg-red-100 text-red-700'
                }`}>
                  {scores.nivel}
                </span>
              </div>
            </div>
          );
        })}
      </div>

      {/* Leyenda de interpretación */}
      <div className="bg-gray-50 rounded-lg p-4">
        <h4 className="text-sm font-medium text-gray-700 mb-2">Interpretación de Niveles</h4>
        <div className="grid grid-cols-2 md:grid-cols-4 gap-2 text-xs">
          <div className="flex items-center">
            <div className="w-3 h-3 bg-green-500 rounded-full mr-2"></div>
            <span>Alto (Pc ≥ 75)</span>
          </div>
          <div className="flex items-center">
            <div className="w-3 h-3 bg-blue-500 rounded-full mr-2"></div>
            <span>Medio-Alto (Pc 60-74)</span>
          </div>
          <div className="flex items-center">
            <div className="w-3 h-3 bg-yellow-500 rounded-full mr-2"></div>
            <span>Medio (Pc 25-59)</span>
          </div>
          <div className="flex items-center">
            <div className="w-3 h-3 bg-red-500 rounded-full mr-2"></div>
            <span>Bajo (Pc &lt; 25)</span>
          </div>
        </div>
      </div>
    </div>
  );
};

export default memo(AptitudeProfileChart);
