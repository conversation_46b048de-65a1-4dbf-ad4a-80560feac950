import React, { memo, useMemo } from 'react';
// import { Scatter } from 'react-chartjs-2'; // Comentado temporalmente
// Chart.js imports comentados temporalmente

/**
 * Gráfico de Cuadrante de Estilo Atencional
 * Basado en las aptitudes de Atención (A) y Concentración (CON) del BAT-7
 * Muestra el estilo atencional del evaluado en un sistema de cuadrantes
 */
const AttentionStyleQuadrant = ({ data, height = 400, interactive = true }) => {
  
  // Calcular posición en el cuadrante basado en puntuaciones
  const attentionData = useMemo(() => {
    if (!data || !data.puntuaciones) {
      return null;
    }

    const atencion = data.puntuaciones.A?.pc || 50;
    const concentracion = data.puntuaciones.CON?.pc || data.puntuaciones.A?.pc || 50; // Fallback si no hay CON

    // Convertir percentiles a coordenadas del cuadrante (-50 a +50)
    const x = atencion - 50; // Eje X: Atención Selectiva
    const y = concentracion - 50; // Eje Y: Atención Sostenida

    return { x, y, atencion, concentracion };
  }, [data]);

  // Determinar el cuadrante y estilo atencional
  const getAttentionStyle = (x, y) => {
    if (x >= 0 && y >= 0) {
      return {
        cuadrante: 'I',
        estilo: 'Focalizado-Sostenido',
        descripcion: 'Excelente capacidad para mantener la atención en tareas específicas durante períodos prolongados',
        color: '#10B981', // Verde
        caracteristicas: [
          'Alta concentración en tareas específicas',
          'Mantiene el foco durante períodos largos',
          'Excelente para trabajos que requieren precisión',
          'Resistente a distracciones'
        ]
      };
    } else if (x < 0 && y >= 0) {
      return {
        cuadrante: 'II',
        estilo: 'Difuso-Sostenido',
        descripcion: 'Capacidad para mantener la atención durante tiempo prolongado pero con enfoque amplio',
        color: '#3B82F6', // Azul
        caracteristicas: [
          'Atención amplia y panorámica',
          'Buena resistencia mental',
          'Capacidad para tareas de larga duración',
          'Procesa múltiples estímulos simultáneamente'
        ]
      };
    } else if (x < 0 && y < 0) {
      return {
        cuadrante: 'III',
        estilo: 'Difuso-Variable',
        descripcion: 'Atención amplia pero variable en el tiempo, requiere cambios frecuentes de actividad',
        color: '#F59E0B', // Amarillo
        caracteristicas: [
          'Atención flexible y adaptable',
          'Prefiere variedad en las tareas',
          'Bueno para actividades creativas',
          'Requiere cambios frecuentes de estímulos'
        ]
      };
    } else {
      return {
        cuadrante: 'IV',
        estilo: 'Focalizado-Variable',
        descripcion: 'Capacidad para enfocar intensamente pero por períodos cortos',
        color: '#EF4444', // Rojo
        caracteristicas: [
          'Atención intensa pero breve',
          'Excelente para tareas de alta precisión',
          'Requiere descansos frecuentes',
          'Eficaz en ráfagas de concentración'
        ]
      };
    }
  };

  const attentionStyle = attentionData ? getAttentionStyle(attentionData.x, attentionData.y) : null;

  // Configuración del gráfico
  const chartData = useMemo(() => {
    if (!attentionData) {
      return { datasets: [] };
    }

    return {
      datasets: [
        {
          label: 'Estilo Atencional',
          data: [{ x: attentionData.x, y: attentionData.y }],
          backgroundColor: attentionStyle?.color || '#6B7280',
          borderColor: attentionStyle?.color || '#6B7280',
          borderWidth: 3,
          pointRadius: 12,
          pointHoverRadius: 15,
          pointBorderWidth: 3,
          pointBorderColor: '#fff'
        }
      ]
    };
  }, [attentionData, attentionStyle]);

  const options = useMemo(() => ({
    responsive: true,
    maintainAspectRatio: false,
    interaction: {
      mode: interactive ? 'point' : 'none',
    },
    plugins: {
      title: {
        display: true,
        text: 'Cuadrante de Estilo Atencional',
        font: {
          size: 16,
          weight: 'bold'
        },
        color: '#374151'
      },
      legend: {
        display: false
      },
      tooltip: {
        enabled: interactive,
        backgroundColor: 'rgba(0, 0, 0, 0.8)',
        titleColor: '#fff',
        bodyColor: '#fff',
        borderColor: attentionStyle?.color || '#6B7280',
        borderWidth: 2,
        cornerRadius: 8,
        callbacks: {
          title: () => attentionStyle?.estilo || 'Estilo Atencional',
          label: (context) => [
            `Atención Selectiva: Pc ${attentionData?.atencion || 'N/A'}`,
            `Atención Sostenida: Pc ${attentionData?.concentracion || 'N/A'}`,
            `Cuadrante: ${attentionStyle?.cuadrante || 'N/A'}`
          ]
        }
      }
    },
    scales: {
      x: {
        type: 'linear',
        position: 'center',
        min: -50,
        max: 50,
        grid: {
          display: true,
          color: (context) => {
            return context.tick.value === 0 ? 'rgba(0, 0, 0, 0.3)' : 'rgba(0, 0, 0, 0.1)';
          },
          lineWidth: (context) => {
            return context.tick.value === 0 ? 2 : 1;
          }
        },
        ticks: {
          stepSize: 25,
          callback: function(value) {
            if (value === -50) return 'Difusa';
            if (value === 0) return '';
            if (value === 50) return 'Selectiva';
            return '';
          },
          font: {
            size: 12,
            weight: 'bold'
          }
        },
        title: {
          display: true,
          text: 'Atención Selectiva →',
          font: {
            size: 14,
            weight: 'bold'
          },
          color: '#374151'
        }
      },
      y: {
        type: 'linear',
        position: 'center',
        min: -50,
        max: 50,
        grid: {
          display: true,
          color: (context) => {
            return context.tick.value === 0 ? 'rgba(0, 0, 0, 0.3)' : 'rgba(0, 0, 0, 0.1)';
          },
          lineWidth: (context) => {
            return context.tick.value === 0 ? 2 : 1;
          }
        },
        ticks: {
          stepSize: 25,
          callback: function(value) {
            if (value === -50) return 'Variable';
            if (value === 0) return '';
            if (value === 50) return 'Sostenida';
            return '';
          },
          font: {
            size: 12,
            weight: 'bold'
          }
        },
        title: {
          display: true,
          text: '↑ Atención Sostenida',
          font: {
            size: 14,
            weight: 'bold'
          },
          color: '#374151'
        }
      }
    },
    // Añadir anotaciones para los cuadrantes
    annotation: {
      annotations: {
        // Etiquetas de cuadrantes
        cuadrante1: {
          type: 'label',
          xValue: 25,
          yValue: 25,
          content: ['I', 'Focalizado-', 'Sostenido'],
          backgroundColor: 'rgba(16, 185, 129, 0.1)',
          borderColor: 'rgba(16, 185, 129, 0.3)',
          borderWidth: 1,
          font: {
            size: 10
          }
        },
        cuadrante2: {
          type: 'label',
          xValue: -25,
          yValue: 25,
          content: ['II', 'Difuso-', 'Sostenido'],
          backgroundColor: 'rgba(59, 130, 246, 0.1)',
          borderColor: 'rgba(59, 130, 246, 0.3)',
          borderWidth: 1,
          font: {
            size: 10
          }
        },
        cuadrante3: {
          type: 'label',
          xValue: -25,
          yValue: -25,
          content: ['III', 'Difuso-', 'Variable'],
          backgroundColor: 'rgba(245, 158, 11, 0.1)',
          borderColor: 'rgba(245, 158, 11, 0.3)',
          borderWidth: 1,
          font: {
            size: 10
          }
        },
        cuadrante4: {
          type: 'label',
          xValue: 25,
          yValue: -25,
          content: ['IV', 'Focalizado-', 'Variable'],
          backgroundColor: 'rgba(239, 68, 68, 0.1)',
          borderColor: 'rgba(239, 68, 68, 0.3)',
          borderWidth: 1,
          font: {
            size: 10
          }
        }
      }
    }
  }), [interactive, attentionStyle, attentionData]);

  if (!data || !data.puntuaciones) {
    return (
      <div className="flex items-center justify-center h-64 bg-gray-50 rounded-lg">
        <div className="text-center">
          <div className="text-gray-400 text-4xl mb-2">🎯</div>
          <p className="text-gray-500">No hay datos disponibles para el análisis atencional</p>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-4">
      {/* Gráfico principal - Placeholder temporal */}
      <div style={{ height: `${height}px` }} className="flex items-center justify-center bg-gray-100 rounded-lg border-2 border-dashed border-gray-300">
        <div className="text-center">
          <div className="text-4xl text-gray-400 mb-2">🎯</div>
          <p className="text-gray-600 font-medium">Cuadrante de Estilo Atencional</p>
          <p className="text-sm text-gray-500 mt-1">Chart.js será instalado próximamente</p>
        </div>
      </div>

      {/* Información del estilo atencional */}
      {attentionStyle && (
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-4">
          {/* Resultado del estilo */}
          <div className="bg-white border rounded-lg p-4">
            <div className="flex items-center mb-3">
              <div 
                className="w-4 h-4 rounded-full mr-3"
                style={{ backgroundColor: attentionStyle.color }}
              ></div>
              <h4 className="font-semibold text-gray-800">
                Estilo Atencional: {attentionStyle.estilo}
              </h4>
            </div>
            <p className="text-sm text-gray-600 mb-3">
              {attentionStyle.descripcion}
            </p>
            <div className="text-xs text-gray-500">
              <span className="font-medium">Cuadrante {attentionStyle.cuadrante}</span> • 
              <span className="ml-1">
                Atención: Pc {attentionData.atencion} | Concentración: Pc {attentionData.concentracion}
              </span>
            </div>
          </div>

          {/* Características */}
          <div className="bg-white border rounded-lg p-4">
            <h4 className="font-semibold text-gray-800 mb-3">Características Principales</h4>
            <ul className="space-y-2 text-sm text-gray-600">
              {attentionStyle.caracteristicas.map((caracteristica, index) => (
                <li key={index} className="flex items-start">
                  <span className="mr-2 text-gray-400">•</span>
                  <span>{caracteristica}</span>
                </li>
              ))}
            </ul>
          </div>
        </div>
      )}

      {/* Leyenda de cuadrantes */}
      <div className="bg-gray-50 rounded-lg p-4">
        <h4 className="text-sm font-medium text-gray-700 mb-3">Interpretación de Cuadrantes</h4>
        <div className="grid grid-cols-2 gap-3 text-xs">
          <div className="flex items-center">
            <div className="w-3 h-3 bg-green-500 rounded-full mr-2"></div>
            <span><strong>I - Focalizado-Sostenido:</strong> Alta precisión y resistencia</span>
          </div>
          <div className="flex items-center">
            <div className="w-3 h-3 bg-blue-500 rounded-full mr-2"></div>
            <span><strong>II - Difuso-Sostenido:</strong> Visión amplia y resistencia</span>
          </div>
          <div className="flex items-center">
            <div className="w-3 h-3 bg-yellow-500 rounded-full mr-2"></div>
            <span><strong>III - Difuso-Variable:</strong> Flexibilidad y creatividad</span>
          </div>
          <div className="flex items-center">
            <div className="w-3 h-3 bg-red-500 rounded-full mr-2"></div>
            <span><strong>IV - Focalizado-Variable:</strong> Intensidad en ráfagas</span>
          </div>
        </div>
      </div>
    </div>
  );
};

export default memo(AttentionStyleQuadrant);
