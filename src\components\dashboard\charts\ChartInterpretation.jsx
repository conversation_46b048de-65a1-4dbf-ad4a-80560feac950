/**
 * @file ChartInterpretation.jsx
 * @description Reusable chart interpretation component
 */

import React, { memo } from 'react';
import PropTypes from 'prop-types';

const ChartInterpretation = memo(({ totalStudents, levelsCount }) => {
  const getInterpretationMessage = () => {
    if (levelsCount === 1) {
      return 'La población está concentrada en un solo nivel educativo. Considere ampliar la cobertura para obtener una muestra más representativa.';
    }
    
    if (levelsCount === 2) {
      return 'La distribución abarca dos niveles educativos. Una mayor diversificación podría enriquecer el análisis.';
    }
    
    if (levelsCount >= 3) {
      return 'Esta distribución muestra una buena cobertura de niveles educativos, lo que permite un análisis más representativo de la población evaluada.';
    }
    
    return 'Esta distribución muestra la composición de la población evaluada por nivel educativo.';
  };

  return (
    <div className="mt-6 p-4 bg-blue-50 rounded-lg">
      <h4 className="font-medium text-blue-800 mb-2">
        <i className="fas fa-lightbulb mr-2" aria-hidden="true"></i>
        Interpretación
      </h4>
      <p className="text-sm text-blue-700">
        {getInterpretationMessage()}
      </p>
      <div className="mt-2 text-xs text-blue-600">
        <strong>Total de estudiantes evaluados:</strong> {totalStudents} | 
        <strong> Niveles representados:</strong> {levelsCount}
      </div>
    </div>
  );
});

ChartInterpretation.displayName = 'ChartInterpretation';

ChartInterpretation.propTypes = {
  totalStudents: PropTypes.number.isRequired,
  levelsCount: PropTypes.number.isRequired
};

export default ChartInterpretation;