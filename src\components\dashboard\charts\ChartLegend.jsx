/**
 * @file ChartLegend.jsx
 * @description Reusable chart legend component
 */

import React, { memo } from 'react';
import PropTypes from 'prop-types';
import { getEducationLevelColors } from '../../../utils/colorUtils.js';

const LegendItem = memo(({ item }) => {
  const colors = getEducationLevelColors(item.nivel);
  
  return (
    <div className="flex items-center justify-between">
      <div className="flex items-center">
        <div className={`w-4 h-4 rounded mr-3 ${colors.bg}`} aria-hidden="true"></div>
        <span className="font-medium text-gray-700">
          {item.nivel_nombre}
        </span>
      </div>
      <div className="text-right">
        <div className="font-bold text-gray-900">
          {item.total_estudiantes}
        </div>
        <div className="text-sm text-gray-500">
          {item.porcentaje}%
        </div>
      </div>
    </div>
  );
});

LegendItem.displayName = 'LegendItem';

const ChartLegend = memo(({ data }) => {
  if (!data || !data.length) return null;

  return (
    <div className="space-y-3" role="list" aria-label="Leyenda del gráfico">
      {data.map((item, index) => (
        <div key={`${item.nivel}-${index}`} role="listitem">
          <LegendItem item={item} />
        </div>
      ))}
    </div>
  );
});

ChartLegend.displayName = 'ChartLegend';

ChartLegend.propTypes = {
  data: PropTypes.arrayOf(PropTypes.shape({
    nivel: PropTypes.string.isRequired,
    nivel_nombre: PropTypes.string.isRequired,
    total_estudiantes: PropTypes.number.isRequired,
    porcentaje: PropTypes.number.isRequired
  })).isRequired
};

export default ChartLegend;