import React, { memo, useMemo } from 'react';
// import { Bar } from 'react-chartjs-2'; // Comentado temporalmente
// Chart.js imports comentados temporalmente

/**
 * Gráfico de Barras Comparativo BAT-7
 * Permite comparar puntuaciones entre diferentes evaluados o con normas poblacionales
 */
const ComparativeBarChart = ({ 
  data, 
  comparisonData = null,
  showNorms = true,
  orientation = 'vertical',
  height = 400,
  interactive = true 
}) => {
  
  const aptitudeLabels = {
    V: 'Verbal',
    E: 'Espacial', 
    A: 'Atención',
    R: 'Razonamiento',
    N: 'Numérico',
    M: 'Mecánico',
    O: 'Ortografía'
  };

  const aptitudeOrder = ['V', 'E', 'A', 'R', 'N', 'M', 'O'];

  // Configurar datos del gráfico
  const chartData = useMemo(() => {
    if (!data || !data.puntuaciones) {
      return {
        labels: aptitudeOrder.map(apt => aptitudeLabels[apt]),
        datasets: []
      };
    }

    const datasets = [];

    // Dataset principal
    const mainData = aptitudeOrder.map(apt => 
      data.puntuaciones[apt]?.pc || 0
    );

    datasets.push({
      label: `${data.nombre || 'Evaluado'} ${data.apellido || ''}`,
      data: mainData,
      backgroundColor: aptitudeOrder.map(apt => {
        const pc = data.puntuaciones[apt]?.pc || 0;
        if (pc >= 75) return 'rgba(34, 197, 94, 0.8)'; // Verde - Alto
        if (pc >= 60) return 'rgba(59, 130, 246, 0.8)'; // Azul - Medio-Alto
        if (pc >= 40) return 'rgba(245, 158, 11, 0.8)'; // Amarillo - Medio
        if (pc >= 25) return 'rgba(249, 115, 22, 0.8)'; // Naranja - Medio-Bajo
        return 'rgba(239, 68, 68, 0.8)'; // Rojo - Bajo
      }),
      borderColor: aptitudeOrder.map(apt => {
        const pc = data.puntuaciones[apt]?.pc || 0;
        if (pc >= 75) return 'rgb(34, 197, 94)';
        if (pc >= 60) return 'rgb(59, 130, 246)';
        if (pc >= 40) return 'rgb(245, 158, 11)';
        if (pc >= 25) return 'rgb(249, 115, 22)';
        return 'rgb(239, 68, 68)';
      }),
      borderWidth: 2,
      borderRadius: 4,
      borderSkipped: false
    });

    // Dataset de comparación
    if (comparisonData && comparisonData.puntuaciones) {
      const comparisonValues = aptitudeOrder.map(apt => 
        comparisonData.puntuaciones[apt]?.pc || 0
      );

      datasets.push({
        label: `${comparisonData.nombre || 'Comparación'} ${comparisonData.apellido || ''}`,
        data: comparisonValues,
        backgroundColor: 'rgba(156, 163, 175, 0.6)',
        borderColor: 'rgb(156, 163, 175)',
        borderWidth: 2,
        borderRadius: 4,
        borderSkipped: false
      });
    }

    // Línea de norma poblacional (Pc 50)
    if (showNorms) {
      datasets.push({
        label: 'Norma Poblacional (Pc 50)',
        data: aptitudeOrder.map(() => 50),
        type: 'line',
        backgroundColor: 'rgba(107, 114, 128, 0.1)',
        borderColor: 'rgb(107, 114, 128)',
        borderWidth: 2,
        borderDash: [5, 5],
        pointRadius: 0,
        fill: false,
        tension: 0
      });
    }

    return {
      labels: aptitudeOrder.map(apt => aptitudeLabels[apt]),
      datasets
    };
  }, [data, comparisonData, showNorms]);

  // Configuración del gráfico
  const options = useMemo(() => ({
    responsive: true,
    maintainAspectRatio: false,
    indexAxis: orientation === 'horizontal' ? 'y' : 'x',
    interaction: {
      mode: interactive ? 'index' : 'none',
      intersect: false,
    },
    plugins: {
      title: {
        display: true,
        text: 'Comparación de Puntuaciones Aptitudinales',
        font: {
          size: 16,
          weight: 'bold'
        },
        color: '#374151'
      },
      legend: {
        position: 'top',
        labels: {
          usePointStyle: true,
          padding: 20,
          font: {
            size: 12
          },
          filter: function(item, chart) {
            // Ocultar la leyenda de colores individuales
            return !item.text.includes('Individual');
          }
        }
      },
      tooltip: {
        enabled: interactive,
        backgroundColor: 'rgba(0, 0, 0, 0.8)',
        titleColor: '#fff',
        bodyColor: '#fff',
        borderColor: 'rgba(59, 130, 246, 0.5)',
        borderWidth: 1,
        cornerRadius: 8,
        callbacks: {
          title: function(context) {
            return `Aptitud: ${context[0].label}`;
          },
          label: function(context) {
            const value = context.parsed.y || context.parsed.x;
            const aptitudeKey = aptitudeOrder[context.dataIndex];
            
            if (context.dataset.label.includes('Norma')) {
              return `${context.dataset.label}: Pc ${value}`;
            }
            
            const nivel = data?.puntuaciones?.[aptitudeKey]?.nivel || 'N/A';
            return `${context.dataset.label}: Pc ${value} (${nivel})`;
          },
          afterLabel: function(context) {
            const value = context.parsed.y || context.parsed.x;
            
            if (context.dataset.label.includes('Norma')) {
              return 'Punto de referencia poblacional';
            }
            
            let interpretation = '';
            if (value >= 90) interpretation = 'Muy Alto - Fortaleza excepcional';
            else if (value >= 75) interpretation = 'Alto - Fortaleza significativa';
            else if (value >= 60) interpretation = 'Medio-Alto - Por encima del promedio';
            else if (value >= 40) interpretation = 'Medio - Dentro del promedio';
            else if (value >= 25) interpretation = 'Medio-Bajo - Ligeramente por debajo';
            else if (value >= 10) interpretation = 'Bajo - Requiere atención';
            else interpretation = 'Muy Bajo - Requiere intervención';
            
            return interpretation;
          }
        }
      }
    },
    scales: {
      x: {
        grid: {
          display: true,
          color: 'rgba(0, 0, 0, 0.1)'
        },
        ticks: {
          font: {
            size: 11
          },
          maxRotation: orientation === 'vertical' ? 45 : 0
        },
        title: {
          display: orientation === 'horizontal',
          text: 'Percentiles',
          font: {
            size: 12,
            weight: 'bold'
          }
        }
      },
      y: {
        beginAtZero: true,
        max: 100,
        grid: {
          display: true,
          color: 'rgba(0, 0, 0, 0.1)'
        },
        ticks: {
          stepSize: 10,
          font: {
            size: 11
          },
          callback: function(value) {
            return orientation === 'vertical' ? value + '%' : value;
          }
        },
        title: {
          display: orientation === 'vertical',
          text: 'Percentiles',
          font: {
            size: 12,
            weight: 'bold'
          }
        }
      }
    }
  }), [data, interactive, orientation]);

  if (!data || !data.puntuaciones) {
    return (
      <div className="flex items-center justify-center h-64 bg-gray-50 rounded-lg">
        <div className="text-center">
          <div className="text-gray-400 text-4xl mb-2">📊</div>
          <p className="text-gray-500">No hay datos disponibles para el gráfico de barras</p>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-4">
      {/* Gráfico principal - Placeholder temporal */}
      <div style={{ height: `${height}px` }} className="flex items-center justify-center bg-gray-100 rounded-lg border-2 border-dashed border-gray-300">
        <div className="text-center">
          <div className="text-4xl text-gray-400 mb-2">📊</div>
          <p className="text-gray-600 font-medium">Gráfico de Barras Comparativo</p>
          <p className="text-sm text-gray-500 mt-1">Chart.js será instalado próximamente</p>
        </div>
      </div>

      {/* Análisis comparativo */}
      {comparisonData && (
        <div className="bg-gray-50 rounded-lg p-4">
          <h4 className="font-semibold text-gray-800 mb-3">Análisis Comparativo</h4>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            {/* Ventajas del evaluado principal */}
            <div className="bg-green-50 border border-green-200 rounded-lg p-3">
              <h5 className="font-medium text-green-800 mb-2">Ventajas Identificadas</h5>
              <div className="space-y-1">
                {aptitudeOrder
                  .filter(apt => {
                    const mainPc = data.puntuaciones[apt]?.pc || 0;
                    const compPc = comparisonData.puntuaciones[apt]?.pc || 0;
                    return mainPc > compPc + 10; // Diferencia significativa
                  })
                  .map(apt => {
                    const mainPc = data.puntuaciones[apt]?.pc || 0;
                    const compPc = comparisonData.puntuaciones[apt]?.pc || 0;
                    const diff = mainPc - compPc;
                    return (
                      <div key={apt} className="text-sm text-green-700">
                        <span className="font-medium">{aptitudeLabels[apt]}:</span> +{diff} puntos
                      </div>
                    );
                  })}
                {aptitudeOrder.filter(apt => {
                  const mainPc = data.puntuaciones[apt]?.pc || 0;
                  const compPc = comparisonData.puntuaciones[apt]?.pc || 0;
                  return mainPc > compPc + 10;
                }).length === 0 && (
                  <div className="text-sm text-green-600 italic">
                    No se identificaron ventajas significativas
                  </div>
                )}
              </div>
            </div>

            {/* Áreas de oportunidad */}
            <div className="bg-orange-50 border border-orange-200 rounded-lg p-3">
              <h5 className="font-medium text-orange-800 mb-2">Áreas de Oportunidad</h5>
              <div className="space-y-1">
                {aptitudeOrder
                  .filter(apt => {
                    const mainPc = data.puntuaciones[apt]?.pc || 0;
                    const compPc = comparisonData.puntuaciones[apt]?.pc || 0;
                    return mainPc < compPc - 10; // Diferencia significativa
                  })
                  .map(apt => {
                    const mainPc = data.puntuaciones[apt]?.pc || 0;
                    const compPc = comparisonData.puntuaciones[apt]?.pc || 0;
                    const diff = compPc - mainPc;
                    return (
                      <div key={apt} className="text-sm text-orange-700">
                        <span className="font-medium">{aptitudeLabels[apt]}:</span> -{diff} puntos
                      </div>
                    );
                  })}
                {aptitudeOrder.filter(apt => {
                  const mainPc = data.puntuaciones[apt]?.pc || 0;
                  const compPc = comparisonData.puntuaciones[apt]?.pc || 0;
                  return mainPc < compPc - 10;
                }).length === 0 && (
                  <div className="text-sm text-orange-600 italic">
                    No se identificaron desventajas significativas
                  </div>
                )}
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Leyenda de colores */}
      <div className="bg-gray-50 rounded-lg p-4">
        <h4 className="text-sm font-medium text-gray-700 mb-3">Interpretación de Colores</h4>
        <div className="grid grid-cols-2 md:grid-cols-5 gap-2 text-xs">
          <div className="flex items-center">
            <div className="w-4 h-4 bg-green-500 rounded mr-2"></div>
            <span>Alto (Pc ≥ 75)</span>
          </div>
          <div className="flex items-center">
            <div className="w-4 h-4 bg-blue-500 rounded mr-2"></div>
            <span>Medio-Alto (Pc 60-74)</span>
          </div>
          <div className="flex items-center">
            <div className="w-4 h-4 bg-yellow-500 rounded mr-2"></div>
            <span>Medio (Pc 40-59)</span>
          </div>
          <div className="flex items-center">
            <div className="w-4 h-4 bg-orange-500 rounded mr-2"></div>
            <span>Medio-Bajo (Pc 25-39)</span>
          </div>
          <div className="flex items-center">
            <div className="w-4 h-4 bg-red-500 rounded mr-2"></div>
            <span>Bajo (Pc &lt; 25)</span>
          </div>
        </div>
      </div>
    </div>
  );
};

export default memo(ComparativeBarChart);
