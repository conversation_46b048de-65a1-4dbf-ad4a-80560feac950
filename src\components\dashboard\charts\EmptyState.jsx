/**
 * @file EmptyState.jsx
 * @description Reusable empty state component for charts
 */

import React, { memo } from 'react';

const EmptyState = memo(() => (
  <div className="text-center py-8 text-gray-500">
    <i className="fas fa-chart-pie text-4xl mb-4" aria-hidden="true"></i>
    <p className="text-lg font-medium mb-2">No hay datos disponibles</p>
    <p className="text-sm">
      Asegúrese de que los pacientes tengan nivel educativo asignado
    </p>
    <div className="mt-4 text-xs text-gray-400">
      Los datos se actualizarán automáticamente cuando estén disponibles
    </div>
  </div>
));

EmptyState.displayName = 'EmptyState';

export default EmptyState;