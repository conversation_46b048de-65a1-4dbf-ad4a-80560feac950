import React, { memo, useMemo, useCallback } from 'react';
import PropTypes from 'prop-types';
import {
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON>atter<PERSON><PERSON>,
  Line,
  Bar,
  Area,
  Pie,
  Cell,
  Radar,
  RadarChart as RadarChartComponent,
  PolarGrid,
  PolarAngleAxis,
  PolarRadiusAxis,
  Scatter,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  Legend,
  ResponsiveContainer
} from 'recharts';
import { CHART_COLORS, TOOLTIP_CONFIG, LEGEND_CONFIG, AXIS_CONFIG } from '../../../utils/analytics/chartThemes';
import { DataLoadingError } from '../../common/ErrorBoundary';
import { FaSpinner, FaChartBar } from 'react-icons/fa';

/**
 * Componente de gráfico interactivo reutilizable con manejo de errores
 * Soporta múltiples tipos de gráficos y configuraciones personalizables
 */
const InteractiveChart = memo(({
  data,
  type = 'line',
  title,
  subtitle,
  loading = false,
  error = null,
  config = {},
  onDataPointClick,
  onRetry,
  height = 400,
  showLegend = true,
  showTooltip = true,
  showGrid = true,
  colors = CHART_COLORS,
  responsive = true,
  className = ''
}) => {
  // Configuración por defecto del gráfico
  const defaultConfig = useMemo(() => ({
    xAxis: {
      dataKey: 'name',
      axisLine: true,
      tickLine: true,
      ...AXIS_CONFIG.xAxis,
      ...config.xAxis
    },
    yAxis: {
      axisLine: false,
      tickLine: false,
      ...AXIS_CONFIG.yAxis,
      ...config.yAxis
    },
    tooltip: {
      ...TOOLTIP_CONFIG,
      ...config.tooltip
    },
    legend: {
      ...LEGEND_CONFIG,
      ...config.legend
    }
  }), [config]);

  // Manejar click en puntos de datos
  const handleDataPointClick = useCallback((data, index) => {
    if (onDataPointClick) {
      onDataPointClick(data, index);
    }
  }, [onDataPointClick]);

  // Tooltip personalizado
  const CustomTooltip = useCallback(({ active, payload, label }) => {
    if (!active || !payload || !payload.length) return null;

    return (
      <div style={defaultConfig.tooltip.contentStyle}>
        <p style={defaultConfig.tooltip.labelStyle}>{label}</p>
        {payload.map((entry, index) => (
          <p key={index} style={{ ...defaultConfig.tooltip.itemStyle, color: entry.color }}>
            {`${entry.name}: ${typeof entry.value === 'number' ? entry.value.toFixed(2) : entry.value}`}
          </p>
        ))}
      </div>
    );
  }, [defaultConfig.tooltip]);

  // Renderizar estado de carga
  if (loading) {
    return (
      <div className={`flex items-center justify-center bg-gray-50 rounded-lg border ${className}`} style={{ height }}>
        <div className="text-center">
          <FaSpinner className="animate-spin text-blue-500 text-3xl mb-3 mx-auto" />
          <p className="text-gray-600">Cargando gráfico...</p>
        </div>
      </div>
    );
  }

  // Renderizar estado de error
  if (error) {
    return (
      <div className={`${className}`} style={{ height }}>
        <DataLoadingError 
          error={error} 
          onRetry={onRetry}
          message="Error al cargar el gráfico"
        />
      </div>
    );
  }

  // Validar datos
  if (!data || !Array.isArray(data) || data.length === 0) {
    return (
      <div className={`flex items-center justify-center bg-gray-50 rounded-lg border ${className}`} style={{ height }}>
        <div className="text-center p-8">
          <FaChartBar className="text-gray-400 text-4xl mb-3 mx-auto" />
          <h4 className="text-lg font-medium text-gray-600 mb-2">Sin datos disponibles</h4>
          <p className="text-gray-500 text-sm">
            No hay información para mostrar en este gráfico
          </p>
          {onRetry && (
            <button
              onClick={onRetry}
              className="mt-4 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
            >
              Actualizar datos
            </button>
          )}
        </div>
      </div>
    );
  }

  // Renderizar gráfico según el tipo
  const renderChart = () => {
    const commonProps = {
      data,
      onClick: handleDataPointClick,
      margin: { top: 20, right: 30, left: 20, bottom: 5 }
    };

    switch (type) {
      case 'line':
        return (
          <LineChart {...commonProps}>
            {showGrid && <CartesianGrid strokeDasharray="3 3" />}
            <XAxis {...defaultConfig.xAxis} />
            <YAxis {...defaultConfig.yAxis} />
            {showTooltip && <Tooltip content={<CustomTooltip />} />}
            {showLegend && <Legend {...defaultConfig.legend} />}
            <Line
              type="monotone"
              dataKey="value"
              stroke={colors[0]}
              strokeWidth={2}
              dot={{ r: 4 }}
              activeDot={{ r: 6 }}
            />
          </LineChart>
        );

      case 'bar':
        return (
          <BarChart {...commonProps}>
            {showGrid && <CartesianGrid strokeDasharray="3 3" />}
            <XAxis {...defaultConfig.xAxis} />
            <YAxis {...defaultConfig.yAxis} />
            {showTooltip && <Tooltip content={<CustomTooltip />} />}
            {showLegend && <Legend {...defaultConfig.legend} />}
            <Bar dataKey="value" fill={colors[0]} radius={[4, 4, 0, 0]} />
          </BarChart>
        );

      case 'area':
        return (
          <AreaChart {...commonProps}>
            {showGrid && <CartesianGrid strokeDasharray="3 3" />}
            <XAxis {...defaultConfig.xAxis} />
            <YAxis {...defaultConfig.yAxis} />
            {showTooltip && <Tooltip content={<CustomTooltip />} />}
            {showLegend && <Legend {...defaultConfig.legend} />}
            <Area
              type="monotone"
              dataKey="value"
              stroke={colors[0]}
              fill={colors[0]}
              fillOpacity={0.6}
            />
          </AreaChart>
        );

      case 'pie':
        return (
          <PieChart {...commonProps}>
            {showTooltip && <Tooltip content={<CustomTooltip />} />}
            {showLegend && <Legend {...defaultConfig.legend} />}
            <Pie
              data={data}
              cx="50%"
              cy="50%"
              outerRadius={120}
              fill="#8884d8"
              dataKey="value"
              label={({ name, percent }) => `${name} ${(percent * 100).toFixed(0)}%`}
            >
              {data.map((entry, index) => (
                <Cell key={`cell-${index}`} fill={colors[index % colors.length]} />
              ))}
            </Pie>
          </PieChart>
        );

      case 'radar':
        return (
          <RadarChartComponent {...commonProps} cx="50%" cy="50%" outerRadius="80%">
            <PolarGrid />
            <PolarAngleAxis dataKey="name" />
            <PolarRadiusAxis />
            {showTooltip && <Tooltip content={<CustomTooltip />} />}
            {showLegend && <Legend {...defaultConfig.legend} />}
            <Radar
              name="Valores"
              dataKey="value"
              stroke={colors[0]}
              fill={colors[0]}
              fillOpacity={0.3}
            />
          </RadarChartComponent>
        );

      case 'scatter':
        return (
          <ScatterChart {...commonProps}>
            {showGrid && <CartesianGrid strokeDasharray="3 3" />}
            <XAxis {...defaultConfig.xAxis} dataKey="x" />
            <YAxis {...defaultConfig.yAxis} dataKey="y" />
            {showTooltip && <Tooltip content={<CustomTooltip />} />}
            {showLegend && <Legend {...defaultConfig.legend} />}
            <Scatter name="Datos" data={data} fill={colors[0]} />
          </ScatterChart>
        );

      default:
        return (
          <div className="flex items-center justify-center h-full">
            <p className="text-red-600">Tipo de gráfico no soportado: {type}</p>
          </div>
        );
    }
  };

  return (
    <div className={`bg-white rounded-lg shadow-sm border ${className}`}>
      {(title || subtitle) && (
        <div className="p-4 border-b border-gray-200">
          {title && <h3 className="text-lg font-semibold text-gray-900">{title}</h3>}
          {subtitle && <p className="text-sm text-gray-600 mt-1">{subtitle}</p>}
        </div>
      )}
      
      <div className="p-4">
        {responsive ? (
          <ResponsiveContainer width="100%" height={height}>
            {renderChart()}
          </ResponsiveContainer>
        ) : (
          <div style={{ width: '100%', height }}>
            {renderChart()}
          </div>
        )}
      </div>
    </div>
  );
});

InteractiveChart.displayName = 'InteractiveChart';

InteractiveChart.propTypes = {
  data: PropTypes.array,
  type: PropTypes.oneOf(['line', 'bar', 'area', 'pie', 'radar', 'scatter']),
  title: PropTypes.string,
  subtitle: PropTypes.string,
  loading: PropTypes.bool,
  error: PropTypes.object,
  config: PropTypes.object,
  onDataPointClick: PropTypes.func,
  onRetry: PropTypes.func,
  height: PropTypes.number,
  showLegend: PropTypes.bool,
  showTooltip: PropTypes.bool,
  showGrid: PropTypes.bool,
  colors: PropTypes.array,
  responsive: PropTypes.bool,
  className: PropTypes.string
};

export default InteractiveChart;