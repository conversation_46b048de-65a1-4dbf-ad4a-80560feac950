/**
 * @file PieChart.jsx
 * @description Reusable SVG pie chart component
 */

import React, { memo } from 'react';
import PropTypes from 'prop-types';
import { calculateSegmentPath } from '../../../utils/chartUtils.js';
import { getEducationLevelFill } from '../../../utils/colorUtils.js';
import { PIE_CHART_CONFIG } from '../../../constants/chartConstants.js';

const PieSegment = memo(({ segment, index, animationKey }) => {
  const pathData = calculateSegmentPath(segment);
  
  if (!pathData) {
    console.warn(`Invalid segment data for index ${index}:`, segment);
    return null;
  }

  const fillColor = getEducationLevelFill(segment.nivel);

  return (
    <path
      d={pathData}
      fill={fillColor}
      stroke="white"
      strokeWidth={PIE_CHART_CONFIG.STROKE_WIDTH}
      className="hover:opacity-80 transition-all duration-300 hover:scale-105 cursor-pointer"
      style={{
        animation: `fadeInScale 0.6s ease-out ${index * PIE_CHART_CONFIG.ANIMATION_DELAY_STEP}s both`,
        transformOrigin: `${PIE_CHART_CONFIG.CENTER_X}px ${PIE_CHART_CONFIG.CENTER_Y}px`
      }}
      title={`${segment.nivel_nombre}: ${segment.total_estudiantes} estudiantes (${segment.safePercentage.toFixed(1)}%)`}
    />
  );
});

PieSegment.displayName = 'PieSegment';

const PieChart = memo(({ segments, totalStudents, animationKey }) => {
  if (!segments.length) return null;

  return (
    <div className="relative w-48 h-48 mx-auto">
      <svg
        viewBox="0 0 200 200"
        className="w-full h-full transform -rotate-90"
        key={animationKey}
        role="img"
        aria-label={`Gráfico circular mostrando distribución de ${totalStudents} estudiantes por nivel educativo`}
      >
        {segments.map((segment, index) => (
          <PieSegment
            key={`${segment.nivel}-${index}-${animationKey}`}
            segment={segment}
            index={index}
            animationKey={animationKey}
          />
        ))}
      </svg>

      {/* Center circle with total */}
      <div className="absolute inset-0 flex items-center justify-center">
        <div className="bg-white rounded-full w-16 h-16 flex items-center justify-center shadow-lg border-2 border-gray-100">
          <div className="text-center">
            <div className="text-lg font-bold text-gray-800 animate-pulse">
              {totalStudents}
            </div>
            <div className="text-xs text-gray-600">Total</div>
          </div>
        </div>
      </div>
    </div>
  );
});

PieChart.displayName = 'PieChart';

PieChart.propTypes = {
  segments: PropTypes.arrayOf(PropTypes.shape({
    nivel: PropTypes.string.isRequired,
    nivel_nombre: PropTypes.string.isRequired,
    total_estudiantes: PropTypes.number.isRequired,
    porcentaje: PropTypes.number.isRequired,
    safePercentage: PropTypes.number.isRequired,
    startAngle: PropTypes.number.isRequired,
    endAngle: PropTypes.number.isRequired
  })).isRequired,
  totalStudents: PropTypes.number.isRequired,
  animationKey: PropTypes.number.isRequired
};

export default PieChart;