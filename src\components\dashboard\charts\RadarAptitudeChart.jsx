import React, { memo, useMemo } from 'react';
// import { Radar } from 'react-chartjs-2'; // Comentado temporalmente
// Chart.js imports comentados temporalmente

/**
 * Gráfico Radar de Aptitudes BAT-7
 * Muestra el perfil aptitudinal en formato radar/araña
 * Ideal para visualizar fortalezas y debilidades de forma intuitiva
 */
const RadarAptitudeChart = ({ 
  data, 
  showComparison = false, 
  comparisonData = null,
  height = 400,
  interactive = true 
}) => {
  
  const aptitudeLabels = {
    V: 'Verbal',
    E: 'Espacial', 
    A: 'Atención',
    R: 'Razonamiento',
    N: 'Numérico',
    M: 'Mecánico',
    O: 'Ortografía'
  };

  const aptitudeOrder = ['V', 'E', 'A', 'R', 'N', 'M', 'O'];

  // Configurar datos del gráfico
  const chartData = useMemo(() => {
    if (!data || !data.puntuaciones) {
      return {
        labels: aptitudeOrder.map(apt => aptitudeLabels[apt]),
        datasets: []
      };
    }

    const datasets = [];

    // Dataset principal
    const mainData = aptitudeOrder.map(apt => 
      data.puntuaciones[apt]?.pc || 0
    );

    datasets.push({
      label: `${data.nombre || 'Evaluado'} ${data.apellido || ''}`,
      data: mainData,
      backgroundColor: 'rgba(59, 130, 246, 0.2)',
      borderColor: 'rgb(59, 130, 246)',
      borderWidth: 3,
      pointBackgroundColor: 'rgb(59, 130, 246)',
      pointBorderColor: '#fff',
      pointBorderWidth: 2,
      pointRadius: 6,
      pointHoverRadius: 8
    });

    // Dataset de comparación (opcional)
    if (showComparison && comparisonData) {
      const comparisonValues = aptitudeOrder.map(apt => 
        comparisonData.puntuaciones?.[apt]?.pc || 0
      );

      datasets.push({
        label: `${comparisonData.nombre || 'Comparación'} ${comparisonData.apellido || ''}`,
        data: comparisonValues,
        backgroundColor: 'rgba(16, 185, 129, 0.2)',
        borderColor: 'rgb(16, 185, 129)',
        borderWidth: 2,
        pointBackgroundColor: 'rgb(16, 185, 129)',
        pointBorderColor: '#fff',
        pointBorderWidth: 2,
        pointRadius: 4,
        pointHoverRadius: 6
      });
    }

    return {
      labels: aptitudeOrder.map(apt => aptitudeLabels[apt]),
      datasets
    };
  }, [data, showComparison, comparisonData]);

  // Configuración del gráfico
  const options = useMemo(() => ({
    responsive: true,
    maintainAspectRatio: false,
    interaction: {
      mode: interactive ? 'point' : 'none',
    },
    plugins: {
      title: {
        display: true,
        text: 'Perfil Aptitudinal - Vista Radar',
        font: {
          size: 16,
          weight: 'bold'
        },
        color: '#374151'
      },
      legend: {
        position: 'top',
        labels: {
          usePointStyle: true,
          padding: 20,
          font: {
            size: 12
          }
        }
      },
      tooltip: {
        enabled: interactive,
        backgroundColor: 'rgba(0, 0, 0, 0.8)',
        titleColor: '#fff',
        bodyColor: '#fff',
        borderColor: 'rgba(59, 130, 246, 0.5)',
        borderWidth: 1,
        cornerRadius: 8,
        callbacks: {
          title: function(context) {
            return `Aptitud: ${context[0].label}`;
          },
          label: function(context) {
            const value = context.parsed.r;
            const aptitudeKey = aptitudeOrder[context.dataIndex];
            const nivel = data?.puntuaciones?.[aptitudeKey]?.nivel || 'N/A';
            
            return `${context.dataset.label}: Pc ${value} (${nivel})`;
          },
          afterLabel: function(context) {
            const value = context.parsed.r;
            let interpretation = '';
            
            if (value >= 90) interpretation = 'Muy Alto - Fortaleza excepcional';
            else if (value >= 75) interpretation = 'Alto - Fortaleza significativa';
            else if (value >= 60) interpretation = 'Medio-Alto - Por encima del promedio';
            else if (value >= 40) interpretation = 'Medio - Dentro del promedio';
            else if (value >= 25) interpretation = 'Medio-Bajo - Ligeramente por debajo';
            else if (value >= 10) interpretation = 'Bajo - Requiere atención';
            else interpretation = 'Muy Bajo - Requiere intervención';
            
            return interpretation;
          }
        }
      }
    },
    scales: {
      r: {
        beginAtZero: true,
        min: 0,
        max: 100,
        ticks: {
          stepSize: 20,
          font: {
            size: 10
          },
          callback: function(value) {
            return value + '%';
          }
        },
        grid: {
          color: 'rgba(0, 0, 0, 0.1)',
          lineWidth: 1
        },
        angleLines: {
          color: 'rgba(0, 0, 0, 0.1)',
          lineWidth: 1
        },
        pointLabels: {
          font: {
            size: 12,
            weight: 'bold'
          },
          color: '#374151'
        }
      }
    }
  }), [data, interactive]);

  // Función para obtener el color del área según el promedio
  const getOverallPerformanceColor = () => {
    if (!data || !data.puntuaciones) return 'gray';
    
    const percentiles = Object.values(data.puntuaciones).map(p => p.pc);
    const average = percentiles.reduce((sum, pc) => sum + pc, 0) / percentiles.length;
    
    if (average >= 75) return 'green';
    if (average >= 60) return 'blue';
    if (average >= 40) return 'yellow';
    if (average >= 25) return 'orange';
    return 'red';
  };

  if (!data || !data.puntuaciones) {
    return (
      <div className="flex items-center justify-center h-64 bg-gray-50 rounded-lg">
        <div className="text-center">
          <div className="text-gray-400 text-4xl mb-2">🕸️</div>
          <p className="text-gray-500">No hay datos disponibles para el gráfico radar</p>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-4">
      {/* Gráfico principal - Placeholder temporal */}
      <div style={{ height: `${height}px` }} className="flex items-center justify-center bg-gray-100 rounded-lg border-2 border-dashed border-gray-300">
        <div className="text-center">
          <div className="text-4xl text-gray-400 mb-2">🕸️</div>
          <p className="text-gray-600 font-medium">Gráfico Radar de Aptitudes</p>
          <p className="text-sm text-gray-500 mt-1">Chart.js será instalado próximamente</p>
        </div>
      </div>

      {/* Análisis del perfil */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
        {/* Fortalezas */}
        <div className="bg-green-50 border border-green-200 rounded-lg p-4">
          <h4 className="font-semibold text-green-800 mb-2 flex items-center">
            <span className="w-3 h-3 bg-green-500 rounded-full mr-2"></span>
            Fortalezas
          </h4>
          <div className="space-y-1">
            {aptitudeOrder
              .filter(apt => data.puntuaciones[apt]?.pc >= 75)
              .map(apt => (
                <div key={apt} className="text-sm text-green-700">
                  <span className="font-medium">{apt}:</span> {aptitudeLabels[apt]} (Pc {data.puntuaciones[apt].pc})
                </div>
              ))}
            {aptitudeOrder.filter(apt => data.puntuaciones[apt]?.pc >= 75).length === 0 && (
              <div className="text-sm text-green-600 italic">
                No se identificaron fortalezas destacadas (Pc ≥ 75)
              </div>
            )}
          </div>
        </div>

        {/* Promedio */}
        <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
          <h4 className="font-semibold text-blue-800 mb-2 flex items-center">
            <span className="w-3 h-3 bg-blue-500 rounded-full mr-2"></span>
            Rendimiento Promedio
          </h4>
          <div className="space-y-1">
            {aptitudeOrder
              .filter(apt => {
                const pc = data.puntuaciones[apt]?.pc;
                return pc >= 25 && pc < 75;
              })
              .map(apt => (
                <div key={apt} className="text-sm text-blue-700">
                  <span className="font-medium">{apt}:</span> {aptitudeLabels[apt]} (Pc {data.puntuaciones[apt].pc})
                </div>
              ))}
          </div>
        </div>

        {/* Áreas de mejora */}
        <div className="bg-orange-50 border border-orange-200 rounded-lg p-4">
          <h4 className="font-semibold text-orange-800 mb-2 flex items-center">
            <span className="w-3 h-3 bg-orange-500 rounded-full mr-2"></span>
            Áreas de Mejora
          </h4>
          <div className="space-y-1">
            {aptitudeOrder
              .filter(apt => data.puntuaciones[apt]?.pc < 25)
              .map(apt => (
                <div key={apt} className="text-sm text-orange-700">
                  <span className="font-medium">{apt}:</span> {aptitudeLabels[apt]} (Pc {data.puntuaciones[apt].pc})
                </div>
              ))}
            {aptitudeOrder.filter(apt => data.puntuaciones[apt]?.pc < 25).length === 0 && (
              <div className="text-sm text-orange-600 italic">
                No se identificaron áreas críticas (Pc &lt; 25)
              </div>
            )}
          </div>
        </div>
      </div>

      {/* Resumen estadístico */}
      <div className="bg-gray-50 rounded-lg p-4">
        <h4 className="font-semibold text-gray-800 mb-3">Resumen Estadístico del Perfil</h4>
        <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
          <div className="text-center">
            <div className="text-2xl font-bold text-blue-600">
              {Math.round(
                Object.values(data.puntuaciones).reduce((sum, p) => sum + p.pc, 0) / 
                Object.values(data.puntuaciones).length
              )}
            </div>
            <div className="text-gray-600">Promedio Pc</div>
          </div>
          <div className="text-center">
            <div className="text-2xl font-bold text-green-600">
              {Math.max(...Object.values(data.puntuaciones).map(p => p.pc))}
            </div>
            <div className="text-gray-600">Máximo Pc</div>
          </div>
          <div className="text-center">
            <div className="text-2xl font-bold text-orange-600">
              {Math.min(...Object.values(data.puntuaciones).map(p => p.pc))}
            </div>
            <div className="text-gray-600">Mínimo Pc</div>
          </div>
          <div className="text-center">
            <div className="text-2xl font-bold text-purple-600">
              {Math.round(
                Math.sqrt(
                  Object.values(data.puntuaciones)
                    .map(p => p.pc)
                    .reduce((sum, pc, _, arr) => {
                      const mean = arr.reduce((s, p) => s + p, 0) / arr.length;
                      return sum + Math.pow(pc - mean, 2);
                    }, 0) / Object.values(data.puntuaciones).length
                )
              )}
            </div>
            <div className="text-gray-600">Desv. Estándar</div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default memo(RadarAptitudeChart);
