/**
 * @file CriticalKPIModule.jsx
 * @description 📈 Módulo de KPIs Críticos
 * Monitoreo de Indicadores Clave de Rendimiento con gráficos interactivos
 */

import React, { memo, useCallback } from 'react';
import PropTypes from 'prop-types';
import { 
  XCircleIcon,
  ArrowPathIcon
} from '@heroicons/react/24/outline';
import { useKPIData } from '../../../hooks/useKPIData.js';
import KPICard from './kpi/KPICard.jsx';
import AlertsPanel from './kpi/AlertsPanel.jsx';
import SummaryPanel from './kpi/SummaryPanel.jsx';

const CriticalKPIModule = memo(({ filters = {} }) => {
  const {
    kpis,
    chartData,
    alerts,
    loading,
    error,
    lastUpdate,
    kpiStats,
    hasAlerts,
    refetch
  } = useKPIData(filters);

  const handleRefresh = useCallback(() => {
    refetch();
  }, [refetch]);



  if (loading) {
    return (
      <div className="space-y-6">
        <div className="flex items-center justify-center py-12">
          <ArrowPathIcon className="h-8 w-8 animate-spin text-blue-500" />
          <span className="ml-2 text-gray-600">Cargando KPIs críticos...</span>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="bg-red-50 border border-red-200 rounded-lg p-6">
        <div className="flex items-center">
          <XCircleIcon className="h-6 w-6 text-red-500" />
          <h3 className="ml-2 text-lg font-medium text-red-800">Error al cargar KPIs</h3>
        </div>
        <p className="mt-2 text-red-700">{error}</p>
        <button 
          onClick={handleRefresh}
          className="mt-4 px-4 py-2 bg-red-600 text-white rounded-md hover:bg-red-700 transition-colors"
        >
          Reintentar
        </button>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold text-gray-900">📈 KPIs Críticos</h2>
          <p className="text-gray-600">Monitoreo de Indicadores Clave de Rendimiento</p>
        </div>
        <div className="flex items-center space-x-4">
          {lastUpdate && (
            <span className="text-sm text-gray-500">
              Actualizado: {lastUpdate.toLocaleTimeString()}
            </span>
          )}
          <button 
            onClick={handleRefresh}
            className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors flex items-center space-x-2"
          >
            <ArrowPathIcon className="h-4 w-4" />
            <span>Actualizar</span>
          </button>
        </div>
      </div>

      {/* Resumen */}
      <SummaryPanel chartData={chartData} />

      {/* Alertas */}
      {hasAlerts && <AlertsPanel alerts={alerts} />}

      {/* KPIs Grid */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {Object.values(kpis).map((kpi) => (
          <KPICard key={kpi.id} kpi={kpi} />
        ))}
      </div>

      {/* Información adicional */}
      <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
        <div className="flex items-start">
          <div className="flex-shrink-0">
            <svg className="h-5 w-5 text-blue-400" fill="currentColor" viewBox="0 0 20 20">
              <path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clipRule="evenodd" />
            </svg>
          </div>
          <div className="ml-3">
            <h3 className="text-sm font-medium text-blue-800">Acerca de los KPIs Críticos</h3>
            <div className="mt-2 text-sm text-blue-700">
              <p>Los KPIs críticos son indicadores clave que requieren monitoreo constante:</p>
              <ul className="mt-2 list-disc list-inside space-y-1">
                <li><strong>Excelente:</strong> Supera el objetivo en más del 5%</li>
                <li><strong>Bueno:</strong> Cumple o supera el objetivo</li>
                <li><strong>En Riesgo:</strong> Está entre el 90-100% del objetivo</li>
                <li><strong>Crítico:</strong> Está por debajo del 90% del objetivo</li>
              </ul>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

CriticalKPIModule.displayName = 'CriticalKPIModule';

CriticalKPIModule.propTypes = {
  filters: PropTypes.object
};

export default CriticalKPIModule;