/**
 * @file ExecutiveSummaryModule.jsx
 * @description 📊 Módulo de Resumen Ejecutivo
 * Visión estratégica y de alto nivel con los hallazgos más importantes
 */

import React, { useState, useEffect } from 'react';
import { 
  ChartBarIcon,
  TrendingUpIcon,
  TrendingDownIcon,
  ExclamationTriangleIcon,
  CheckCircleIcon,
  LightBulbIcon,
  ClockIcon,
  ArrowPathIcon,
  DocumentTextIcon
} from '@heroicons/react/24/outline';
import ExecutiveSummaryService from '../../../services/analytics/ExecutiveSummaryService.js';

const ExecutiveSummaryModule = ({ filters = {} }) => {
  const [summary, setSummary] = useState(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [selectedTab, setSelectedTab] = useState('insights');

  useEffect(() => {
    loadExecutiveSummary();
  }, [filters]);

  const loadExecutiveSummary = async () => {
    setLoading(true);
    setError(null);
    
    try {
      const summaryData = await ExecutiveSummaryService.getExecutiveSummary();
      setSummary(summaryData);
    } catch (err) {
      setError(err.message);
      console.error('Error cargando resumen ejecutivo:', err);
    } finally {
      setLoading(false);
    }
  };

  const getScoreColor = (score) => {
    if (score >= 85) return 'text-green-600 bg-green-50 border-green-200';
    if (score >= 75) return 'text-blue-600 bg-blue-50 border-blue-200';
    if (score >= 60) return 'text-yellow-600 bg-yellow-50 border-yellow-200';
    return 'text-red-600 bg-red-50 border-red-200';
  };

  const getInsightTypeColor = (type) => {
    switch (type) {
      case 'positive': return 'text-green-700 bg-green-50 border-green-200';
      case 'negative': return 'text-red-700 bg-red-50 border-red-200';
      case 'neutral': return 'text-blue-700 bg-blue-50 border-blue-200';
      default: return 'text-gray-700 bg-gray-50 border-gray-200';
    }
  };

  const getSeverityColor = (severity) => {
    switch (severity) {
      case 'high': return 'text-red-700 bg-red-50 border-red-200';
      case 'medium': return 'text-yellow-700 bg-yellow-50 border-yellow-200';
      case 'low': return 'text-blue-700 bg-blue-50 border-blue-200';
      default: return 'text-gray-700 bg-gray-50 border-gray-200';
    }
  };

  const getPriorityColor = (priority) => {
    switch (priority) {
      case 'urgent': return 'text-red-700 bg-red-100 border-red-300';
      case 'high': return 'text-orange-700 bg-orange-100 border-orange-300';
      case 'medium': return 'text-yellow-700 bg-yellow-100 border-yellow-300';
      case 'low': return 'text-blue-700 bg-blue-100 border-blue-300';
      default: return 'text-gray-700 bg-gray-100 border-gray-300';
    }
  };

  const ExecutiveScoreGauge = ({ score }) => {
    const radius = 45;
    const circumference = 2 * Math.PI * radius;
    const strokeDasharray = circumference;
    const strokeDashoffset = circumference - (score / 100) * circumference;

    return (
      <div className="relative w-32 h-32">
        <svg className="w-32 h-32 transform -rotate-90" viewBox="0 0 100 100">
          {/* Background circle */}
          <circle
            cx="50"
            cy="50"
            r={radius}
            stroke="currentColor"
            strokeWidth="8"
            fill="transparent"
            className="text-gray-200"
          />
          {/* Progress circle */}
          <circle
            cx="50"
            cy="50"
            r={radius}
            stroke="currentColor"
            strokeWidth="8"
            fill="transparent"
            strokeDasharray={strokeDasharray}
            strokeDashoffset={strokeDashoffset}
            className={
              score >= 85 ? 'text-green-500' :
              score >= 75 ? 'text-blue-500' :
              score >= 60 ? 'text-yellow-500' :
              'text-red-500'
            }
            strokeLinecap="round"
          />
        </svg>
        <div className="absolute inset-0 flex items-center justify-center">
          <div className="text-center">
            <div className="text-2xl font-bold">{score}</div>
            <div className="text-xs text-gray-500">Score</div>
          </div>
        </div>
      </div>
    );
  };

  const InsightCard = ({ insight }) => (
    <div className={`p-4 rounded-lg border ${getInsightTypeColor(insight.type)}`}>
      <div className="flex items-start justify-between">
        <div className="flex-1">
          <div className="flex items-center space-x-2">
            <h4 className="font-semibold">{insight.title}</h4>
            {insight.trend === 'up' ? (
              <TrendingUpIcon className="h-4 w-4 text-green-500" />
            ) : insight.trend === 'down' ? (
              <TrendingDownIcon className="h-4 w-4 text-red-500" />
            ) : null}
          </div>
          <p className="text-sm mt-1">{insight.description}</p>
        </div>
        <div className="text-right">
          <div className="text-lg font-bold">{insight.value}</div>
          <div className={`text-xs px-2 py-1 rounded-full ${
            insight.impact === 'high' ? 'bg-red-100 text-red-800' :
            insight.impact === 'medium' ? 'bg-yellow-100 text-yellow-800' :
            'bg-blue-100 text-blue-800'
          }`}>
            {insight.impact === 'high' ? 'Alto Impacto' :
             insight.impact === 'medium' ? 'Impacto Medio' : 'Bajo Impacto'}
          </div>
        </div>
      </div>
    </div>
  );

  const CriticalFindingCard = ({ finding }) => (
    <div className={`p-4 rounded-lg border ${getSeverityColor(finding.severity)}`}>
      <div className="flex items-start space-x-3">
        <div className="flex-shrink-0">
          {finding.severity === 'high' ? (
            <ExclamationTriangleIcon className="h-5 w-5 text-red-500" />
          ) : (
            <ExclamationTriangleIcon className="h-5 w-5 text-yellow-500" />
          )}
        </div>
        <div className="flex-1">
          <h4 className="font-semibold">{finding.title}</h4>
          <p className="text-sm mt-1">{finding.description}</p>
          {finding.recommendation && (
            <div className="mt-2 p-2 bg-blue-50 rounded text-sm">
              <strong>Recomendación:</strong> {finding.recommendation}
            </div>
          )}
        </div>
        <div className={`px-2 py-1 rounded-full text-xs font-medium ${
          finding.severity === 'high' ? 'bg-red-100 text-red-800' :
          finding.severity === 'medium' ? 'bg-yellow-100 text-yellow-800' :
          'bg-blue-100 text-blue-800'
        }`}>
          {finding.severity === 'high' ? 'Crítico' :
           finding.severity === 'medium' ? 'Importante' : 'Menor'}
        </div>
      </div>
    </div>
  );

  const RecommendationCard = ({ recommendation }) => (
    <div className={`p-4 rounded-lg border ${getPriorityColor(recommendation.priority)}`}>
      <div className="flex items-start justify-between mb-3">
        <div>
          <h4 className="font-semibold">{recommendation.title}</h4>
          <p className="text-sm text-gray-600 mt-1">{recommendation.description}</p>
        </div>
        <div className={`px-2 py-1 rounded-full text-xs font-medium ${getPriorityColor(recommendation.priority)}`}>
          {recommendation.priority === 'urgent' ? 'Urgente' :
           recommendation.priority === 'high' ? 'Alta' :
           recommendation.priority === 'medium' ? 'Media' : 'Baja'}
        </div>
      </div>
      
      <div className="space-y-2">
        <div>
          <h5 className="text-sm font-medium text-gray-700">Acciones:</h5>
          <ul className="text-sm text-gray-600 list-disc list-inside space-y-1">
            {recommendation.actions.map((action, index) => (
              <li key={index}>{action}</li>
            ))}
          </ul>
        </div>
        
        <div className="flex justify-between text-sm">
          <span><strong>Impacto esperado:</strong> {recommendation.expectedImpact}</span>
          <span><strong>Tiempo:</strong> {recommendation.timeline}</span>
        </div>
      </div>
    </div>
  );

  const PerformanceOverview = ({ overview }) => (
    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
      <div className="bg-white p-4 rounded-lg border border-gray-200">
        <div className="text-2xl font-bold text-blue-600">{overview.overallScore}%</div>
        <div className="text-sm text-gray-600">Percentil Promedio</div>
        <div className={`text-xs mt-1 px-2 py-1 rounded-full ${overview.performanceLevel.color === 'green' ? 'bg-green-100 text-green-800' :
          overview.performanceLevel.color === 'blue' ? 'bg-blue-100 text-blue-800' :
          overview.performanceLevel.color === 'yellow' ? 'bg-yellow-100 text-yellow-800' :
          'bg-red-100 text-red-800'}`}>
          {overview.performanceLevel.level}
        </div>
      </div>
      
      <div className="bg-white p-4 rounded-lg border border-gray-200">
        <div className="text-2xl font-bold text-green-600">{overview.totalEvaluations}</div>
        <div className="text-sm text-gray-600">Total Evaluaciones</div>
        <div className="text-xs text-gray-500 mt-1">
          {overview.monthlyActivity.evaluationsThisMonth} este mes
        </div>
      </div>
      
      <div className="bg-white p-4 rounded-lg border border-gray-200">
        <div className="text-2xl font-bold text-purple-600">{overview.totalParticipants}</div>
        <div className="text-sm text-gray-600">Total Participantes</div>
        <div className="text-xs text-gray-500 mt-1">
          {overview.completionRate.toFixed(1)}% completaron evaluaciones
        </div>
      </div>
      
      <div className="bg-white p-4 rounded-lg border border-gray-200">
        <div className="text-2xl font-bold text-orange-600">
          {overview.aptitudeBreakdown.highPerforming}/{overview.aptitudeBreakdown.total}
        </div>
        <div className="text-sm text-gray-600">Aptitudes Destacadas</div>
        <div className="text-xs text-gray-500 mt-1">
          {overview.aptitudeBreakdown.lowPerforming} necesitan atención
        </div>
      </div>
    </div>
  );

  if (loading) {
    return (
      <div className="flex items-center justify-center py-12">
        <ArrowPathIcon className="h-8 w-8 animate-spin text-blue-500" />
        <span className="ml-2 text-gray-600">Generando resumen ejecutivo...</span>
      </div>
    );
  }

  if (error) {
    return (
      <div className="bg-red-50 border border-red-200 rounded-lg p-6">
        <div className="flex items-center">
          <ExclamationTriangleIcon className="h-6 w-6 text-red-500" />
          <h3 className="ml-2 text-lg font-medium text-red-800">Error al generar resumen</h3>
        </div>
        <p className="mt-2 text-red-700">{error}</p>
        <button 
          onClick={loadExecutiveSummary}
          className="mt-4 px-4 py-2 bg-red-600 text-white rounded-md hover:bg-red-700 transition-colors"
        >
          Reintentar
        </button>
      </div>
    );
  }

  if (!summary) return null;

  return (
    <div className="space-y-6">
      {/* Header con Score Ejecutivo */}
      <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
        <div className="flex items-center justify-between">
          <div>
            <h2 className="text-2xl font-bold text-gray-900">📊 Resumen Ejecutivo</h2>
            <p className="text-gray-600">
              Período: {summary.period.label} | Actualizado: {new Date(summary.timestamp).toLocaleString()}
            </p>
          </div>
          <div className="flex items-center space-x-6">
            <ExecutiveScoreGauge score={summary.executiveScore} />
            <div>
              <div className="text-sm text-gray-600">Score Ejecutivo</div>
              <div className={`text-lg font-semibold ${
                summary.executiveScore >= 85 ? 'text-green-600' :
                summary.executiveScore >= 75 ? 'text-blue-600' :
                summary.executiveScore >= 60 ? 'text-yellow-600' :
                'text-red-600'
              }`}>
                {summary.executiveScore >= 85 ? 'Excelente' :
                 summary.executiveScore >= 75 ? 'Bueno' :
                 summary.executiveScore >= 60 ? 'Aceptable' : 'Necesita Mejora'}
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Overview de Rendimiento */}
      <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
        <h3 className="text-lg font-semibold text-gray-900 mb-4">Vista General del Rendimiento</h3>
        <PerformanceOverview overview={summary.performanceOverview} />
      </div>

      {/* Tabs de Navegación */}
      <div className="bg-white rounded-lg shadow-sm border border-gray-200">
        <div className="border-b border-gray-200">
          <nav className="flex space-x-8 px-6">
            {[
              { id: 'insights', label: 'Insights Clave', icon: LightBulbIcon },
              { id: 'findings', label: 'Hallazgos Críticos', icon: ExclamationTriangleIcon },
              { id: 'recommendations', label: 'Recomendaciones', icon: CheckCircleIcon },
              { id: 'trends', label: 'Análisis de Tendencias', icon: TrendingUpIcon }
            ].map((tab) => (
              <button
                key={tab.id}
                onClick={() => setSelectedTab(tab.id)}
                className={`flex items-center space-x-2 py-4 px-1 border-b-2 font-medium text-sm ${
                  selectedTab === tab.id
                    ? 'border-blue-500 text-blue-600'
                    : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                }`}
              >
                <tab.icon className="h-4 w-4" />
                <span>{tab.label}</span>
              </button>
            ))}
          </nav>
        </div>

        <div className="p-6">
          {selectedTab === 'insights' && (
            <div className="space-y-4">
              <h3 className="text-lg font-semibold text-gray-900">Insights Clave</h3>
              <div className="grid grid-cols-1 lg:grid-cols-2 gap-4">
                {summary.keyInsights.map((insight) => (
                  <InsightCard key={insight.id} insight={insight} />
                ))}
              </div>
            </div>
          )}

          {selectedTab === 'findings' && (
            <div className="space-y-4">
              <h3 className="text-lg font-semibold text-gray-900">
                Hallazgos Críticos ({summary.criticalFindings.length})
              </h3>
              {summary.criticalFindings.length === 0 ? (
                <div className="text-center py-8 text-gray-500">
                  <CheckCircleIcon className="h-12 w-12 mx-auto mb-2 text-green-500" />
                  <p>No se encontraron problemas críticos</p>
                </div>
              ) : (
                <div className="space-y-4">
                  {summary.criticalFindings.map((finding) => (
                    <CriticalFindingCard key={finding.id} finding={finding} />
                  ))}
                </div>
              )}
            </div>
          )}

          {selectedTab === 'recommendations' && (
            <div className="space-y-4">
              <h3 className="text-lg font-semibold text-gray-900">
                Recomendaciones Estratégicas ({summary.recommendations.length})
              </h3>
              <div className="space-y-4">
                {summary.recommendations.map((recommendation) => (
                  <RecommendationCard key={recommendation.id} recommendation={recommendation} />
                ))}
              </div>
            </div>
          )}

          {selectedTab === 'trends' && (
            <div className="space-y-4">
              <h3 className="text-lg font-semibold text-gray-900">Análisis de Tendencias</h3>
              <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
                <div className="flex items-center space-x-2">
                  <TrendingUpIcon className={`h-5 w-5 ${
                    summary.trendsAnalysis.evaluationVolumeTrend.direction === 'increasing' ? 'text-green-500' :
                    summary.trendsAnalysis.evaluationVolumeTrend.direction === 'decreasing' ? 'text-red-500' :
                    'text-blue-500'
                  }`} />
                  <h4 className="font-semibold">Volumen de Evaluaciones</h4>
                </div>
                <p className="text-sm text-gray-700 mt-2">
                  {summary.trendsAnalysis.evaluationVolumeTrend.description}
                </p>
                <div className="mt-3 flex items-center justify-between text-sm">
                  <span>Cambio: {summary.trendsAnalysis.evaluationVolumeTrend.percentage}%</span>
                  <span>Proyección próximo mes: {summary.trendsAnalysis.projections.nextMonth} evaluaciones</span>
                </div>
              </div>
            </div>
          )}
        </div>
      </div>

      {/* Footer con información adicional */}
      <div className="bg-gray-50 border border-gray-200 rounded-lg p-4">
        <div className="flex items-center justify-between text-sm text-gray-600">
          <div className="flex items-center space-x-4">
            <span>📈 Datos basados en {summary.performanceOverview.totalEvaluations} evaluaciones</span>
            <span>👥 {summary.performanceOverview.totalParticipants} participantes</span>
          </div>
          <div className="flex items-center space-x-2">
            <ClockIcon className="h-4 w-4" />
            <span>Generado: {new Date(summary.timestamp).toLocaleString()}</span>
            {summary.simulated && (
              <span className="px-2 py-1 bg-yellow-100 text-yellow-800 rounded-full text-xs">
                Datos Simulados
              </span>
            )}
          </div>
        </div>
      </div>
    </div>
  );
};

export default ExecutiveSummaryModule;