/**
 * @file OverviewModule.jsx
 * @description 📋 Módulo de Visión General - Estadísticas descriptivas globales
 */

import React, { useState, useEffect } from 'react';
import {
    UsersIcon,
    AcademicCapIcon,
    ChartBarIcon,
    ClockIcon,
    TrendingUpIcon,
    ExclamationTriangleIcon,
    InformationCircleIcon
} from '@heroicons/react/24/outline';
import {
    Tooltip,
    ResponsiveContainer,
    PieChart,
    Pie,
    Cell,
    RadarChart,
    PolarGrid,
    PolarAngleAxis,
    PolarRadiusAxis,
    Radar,
    Legend
} from 'recharts';

import DashboardService from '../../../services/DashboardService.js';

const OverviewModule = () => {
    const [data, setData] = useState(null);
    const [loading, setLoading] = useState(true);
    const [error, setError] = useState(null);

    useEffect(() => {
        loadOverviewData();
    }, []);

    const loadOverviewData = async () => {
        try {
            setLoading(true);
            const overviewData = await DashboardService.getOverviewData();
            setData(overviewData);
            setError(null);
        } catch (err) {
            console.error('Error cargando datos de visión general:', err);
            setError(err.message);
        } finally {
            setLoading(false);
        }
    };

    if (loading) {
        return (
            <div className="flex items-center justify-center h-64">
                <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-indigo-600"></div>
                <span className="ml-2 text-gray-600">Cargando visión general...</span>
            </div>
        );
    }

    if (error) {
        return (
            <div className="text-center py-12">
                <ExclamationTriangleIcon className="mx-auto h-12 w-12 text-red-400" />
                <h3 className="mt-2 text-sm font-medium text-gray-900">Error al cargar datos</h3>
                <p className="mt-1 text-sm text-gray-500">{error}</p>
                <button
                    onClick={loadOverviewData}
                    className="mt-4 inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-indigo-600 hover:bg-indigo-700"
                >
                    Reintentar
                </button>
            </div>
        );
    }

    if (!data) return null;

    // Mapeo de nombres completos a siglas para el RadarChart
    const aptitudeAbbreviations = React.useMemo(() => ({
        'Aptitud Verbal': 'V',
        'Aptitud Espacial': 'E',
        'Atención': 'A',
        'Razonamiento': 'R',
        'Aptitud Numérica': 'N',
        'Aptitud Mecánica': 'M',
        'Ortografía': 'O'
    }), []);

    // Preparar datos para RadarChart con validación de NaN y siglas
    const institutionalProfileData = React.useMemo(() => {
        if (!data.institutionalProfile?.datasets?.[0]?.data || !data.institutionalProfile?.labels) {
            console.log('⚠️ No hay datos de perfil institucional válidos');
            return [];
        }

        return data.institutionalProfile.datasets[0].data
            .map((value, index) => {
                const numericValue = parseFloat(value);
                const fullName = data.institutionalProfile.labels[index] || `Aptitud ${index + 1}`;
                const abbreviation = aptitudeAbbreviations[fullName] || fullName.charAt(0).toUpperCase();
                
                return {
                    aptitud: abbreviation,
                    percentil: isNaN(numericValue) || !isFinite(numericValue) ? 0 : Math.round(numericValue * 100) / 100
                };
            })
            .filter(item => item.aptitud && typeof item.percentil === 'number' && isFinite(item.percentil));
    }, [data.institutionalProfile, aptitudeAbbreviations]);

    // Colores para distribución
    const distributionColors = ['#3B82F6', '#10B981', '#F59E0B', '#EF4444', '#8B5CF6'];

    // Validar y limpiar datos de distribución para evitar NaN en PieChart
    const validDistributionData = React.useMemo(() => {
        if (!data.distributionData || !Array.isArray(data.distributionData)) {
            console.log('⚠️ No hay datos de distribución válidos, usando datos de fallback');
            return [{ name: 'Sin datos', value: 1, color: '#9CA3AF' }];
        }

        const processedData = data.distributionData
            .map((item, index) => {
                // Validación exhaustiva de cada campo
                const rawValue = item?.value;
                let numericValue;
                
                // Intentar múltiples formas de conversión
                if (typeof rawValue === 'number') {
                    numericValue = rawValue;
                } else if (typeof rawValue === 'string') {
                    numericValue = parseFloat(rawValue);
                } else {
                    numericValue = 0;
                }
                
                // Validación final y asignación de valor seguro
                const safeValue = (isNaN(numericValue) || numericValue <= 0 || !isFinite(numericValue)) ? 1 : Math.round(numericValue);
                const safeName = (item?.name && typeof item.name === 'string' && item.name.trim()) ? item.name.trim() : `Categoría ${index + 1}`;
                const safeColor = (item?.color && typeof item.color === 'string') ? item.color : distributionColors[index % distributionColors.length];
                
                return {
                    name: safeName,
                    value: safeValue,
                    color: safeColor
                };
            })
            .filter(item => item.name && item.value > 0 && isFinite(item.value));

        // Verificación final - si no hay datos válidos, usar fallback
        if (processedData.length === 0) {
            console.log('⚠️ Todos los datos fueron filtrados, usando fallback');
            return [{ name: 'Sin datos válidos', value: 1, color: '#9CA3AF' }];
        }

        console.log('✅ Datos de distribución procesados:', processedData);
        return processedData;
    }, [data.distributionData]);

    return (
        <div className="space-y-6">
            {/* Header */}
            <div className="flex items-center justify-between">
                <div>
                    <h2 className="text-2xl font-bold text-gray-900">Visión General</h2>
                    <p className="text-sm text-gray-600 mt-1">
                        Estadísticas descriptivas globales de la población evaluada
                    </p>
                </div>

                <div className="flex items-center space-x-2 text-sm text-gray-500">
                    <ClockIcon className="w-4 h-4" />
                    <span>Última actualización: {new Date(data.lastUpdate).toLocaleString('es-ES')}</span>
                    {data.isRealData && (
                        <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-green-100 text-green-800">
                            Datos reales
                        </span>
                    )}
                </div>
            </div>

            {/* Métricas principales */}
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
                <div className="bg-white p-6 rounded-lg border shadow-sm">
                    <div className="flex items-center">
                        <div className="flex-shrink-0">
                            <UsersIcon className="h-8 w-8 text-blue-600" />
                        </div>
                        <div className="ml-4">
                            <p className="text-sm font-medium text-gray-500">Total Pacientes</p>
                            <p className="text-2xl font-semibold text-gray-900">
                                {data.generalStats?.total_pacientes || 0}
                            </p>
                        </div>
                    </div>
                </div>

                <div className="bg-white p-6 rounded-lg border shadow-sm">
                    <div className="flex items-center">
                        <div className="flex-shrink-0">
                            <AcademicCapIcon className="h-8 w-8 text-green-600" />
                        </div>
                        <div className="ml-4">
                            <p className="text-sm font-medium text-gray-500">Pacientes Evaluados</p>
                            <p className="text-2xl font-semibold text-gray-900">
                                {data.generalStats?.pacientes_evaluados || 0}
                            </p>
                            <p className="text-sm text-gray-600">
                                {data.generalStats?.total_pacientes > 0
                                    ? `${((data.generalStats.pacientes_evaluados / data.generalStats.total_pacientes) * 100).toFixed(1)}% del total`
                                    : '0% del total'
                                }
                            </p>
                        </div>
                    </div>
                </div>

                <div className="bg-white p-6 rounded-lg border shadow-sm">
                    <div className="flex items-center">
                        <div className="flex-shrink-0">
                            <ChartBarIcon className="h-8 w-8 text-purple-600" />
                        </div>
                        <div className="ml-4">
                            <p className="text-sm font-medium text-gray-500">Rendimiento Promedio</p>
                            <p className="text-2xl font-semibold text-gray-900">
                                {data.generalStats?.percentil_promedio_general?.toFixed(1) || '0.0'}
                            </p>
                            <p className="text-sm text-gray-600">Percentil general</p>
                        </div>
                    </div>
                </div>

                <div className="bg-white p-6 rounded-lg border shadow-sm">
                    <div className="flex items-center">
                        <div className="flex-shrink-0">
                            <ClockIcon className="h-8 w-8 text-orange-600" />
                        </div>
                        <div className="ml-4">
                            <p className="text-sm font-medium text-gray-500">Evaluaciones Recientes</p>
                            <p className="text-2xl font-semibold text-gray-900">
                                {data.generalStats?.evaluaciones_ultima_semana || 0}
                            </p>
                            <p className="text-sm text-gray-600">Esta semana</p>
                        </div>
                    </div>
                </div>
            </div>

            {/* Gráficos principales */}
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                {/* Perfil Institucional - Radar Chart */}
                <div className="bg-white p-6 rounded-lg border shadow-sm">
                    <h3 className="text-lg font-medium text-gray-900 mb-4">
                        Perfil Institucional por Aptitudes
                    </h3>
                    {institutionalProfileData.length > 0 ? (
                        <ResponsiveContainer width="100%" height={300}>
                            <RadarChart data={institutionalProfileData}>
                                <PolarGrid />
                                <PolarAngleAxis dataKey="aptitud" />
                                <PolarRadiusAxis
                                    angle={90}
                                    domain={[0, 100]}
                                    tick={{ fontSize: 12 }}
                                />
                                <Radar
                                    name="Percentil Promedio"
                                    dataKey="percentil"
                                    stroke="#3B82F6"
                                    fill="#3B82F6"
                                    fillOpacity={0.2}
                                    strokeWidth={2}
                                />
                                <Tooltip
                                    formatter={(value) => [`${value.toFixed(1)}%`, 'Percentil']}
                                />
                            </RadarChart>
                        </ResponsiveContainer>
                    ) : (
                        <div className="flex items-center justify-center h-64 text-gray-500">
                            No hay datos disponibles
                        </div>
                    )}
                </div>

                {/* Distribución por Nivel - Pie Chart */}
                <div className="bg-white p-6 rounded-lg border shadow-sm">
                    <h3 className="text-lg font-medium text-gray-900 mb-4">
                        Distribución por Nivel Educativo
                    </h3>
                    {validDistributionData && validDistributionData.length > 0 ? (
                        <div>
                            <ResponsiveContainer width="100%" height={300}>
                                <PieChart>
                                    <Pie
                                        data={validDistributionData}
                                        cx="50%"
                                        cy="50%"
                                        labelLine={false}
                                        label={({ name, percent }) => `${name} ${(percent * 100).toFixed(0)}%`}
                                        outerRadius={80}
                                        fill="#8884d8"
                                        dataKey="value"
                                    >
                                        {validDistributionData.map((item, index) => (
                                            <Cell
                                                key={`cell-${index}`}
                                                fill={item.color || distributionColors[index % distributionColors.length]}
                                            />
                                        ))}
                                    </Pie>
                                    <Tooltip 
                                        formatter={(value, name) => [
                                            `${value} estudiante${value !== 1 ? 's' : ''}`,
                                            'Total'
                                        ]}
                                    />
                                    <Legend />
                                </PieChart>
                            </ResponsiveContainer>
                            
                            {/* Interpretación de la distribución */}
                            <div className="mt-4 p-3 bg-blue-50 rounded-md border-l-4 border-blue-400">
                                <div className="flex">
                                    <div className="flex-shrink-0">
                                        <InformationCircleIcon className="h-5 w-5 text-blue-400" />
                                    </div>
                                    <div className="ml-3">
                                        <p className="text-sm font-medium text-blue-800">Interpretación</p>
                                        <p className="text-sm text-blue-700 mt-1">
                                            Esta distribución muestra la composición de la población evaluada. Una 
                                            distribución equilibrada indica una cobertura adecuada en todos los 
                                            niveles educativos.
                                        </p>
                                    </div>
                                </div>
                            </div>
                        </div>
                    ) : (
                        <div className="flex items-center justify-center h-64 text-gray-500">
                            No hay datos de distribución disponibles
                        </div>
                    )}
                </div>
            </div>

            {/* Análisis Detallado */}
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                {/* Análisis del Perfil Institucional */}
                <div className="bg-white p-6 rounded-lg border shadow-sm">
                    <h3 className="text-lg font-medium text-gray-900 mb-4">
                        Análisis del Perfil Institucional
                    </h3>
                    {institutionalProfileData.length > 0 ? (
                        <div className="space-y-4">
                            {/* Aptitud más fuerte */}
                            {(() => {
                                const strongest = institutionalProfileData.reduce((max, apt) =>
                                    apt.percentil > max.percentil ? apt : max
                                );
                                const weakest = institutionalProfileData.reduce((min, apt) =>
                                    apt.percentil < min.percentil ? apt : min
                                );

                                return (
                                    <>
                                        <div className="p-3 bg-green-50 rounded-md border-l-4 border-green-400">
                                            <div className="flex">
                                                <div className="flex-shrink-0">
                                                    <TrendingUpIcon className="h-5 w-5 text-green-400" />
                                                </div>
                                                <div className="ml-3">
                                                    <p className="text-sm font-medium text-green-800">
                                                        Fortaleza Principal: {strongest.aptitud}
                                                    </p>
                                                    <p className="text-sm text-green-700">
                                                        Percentil {strongest.percentil.toFixed(1)}% - Rendimiento destacado
                                                    </p>
                                                </div>
                                            </div>
                                        </div>

                                        <div className="p-3 bg-yellow-50 rounded-md border-l-4 border-yellow-400">
                                            <div className="flex">
                                                <div className="flex-shrink-0">
                                                    <ExclamationTriangleIcon className="h-5 w-5 text-yellow-400" />
                                                </div>
                                                <div className="ml-3">
                                                    <p className="text-sm font-medium text-yellow-800">
                                                        Área de Mejora: {weakest.aptitud}
                                                    </p>
                                                    <p className="text-sm text-yellow-700">
                                                        Percentil {weakest.percentil.toFixed(1)}% - Requiere atención
                                                    </p>
                                                </div>
                                            </div>
                                        </div>
                                    </>
                                );
                            })()}

                            {/* Lista detallada de aptitudes */}
                            <div className="mt-4">
                                <h4 className="text-sm font-medium text-gray-900 mb-2">Detalle por Aptitudes</h4>
                                <div className="space-y-2">
                                    {institutionalProfileData
                                        .sort((a, b) => b.percentil - a.percentil)
                                        .map((apt, index) => (
                                            <div key={apt.aptitud} className="flex items-center justify-between p-2 bg-gray-50 rounded">
                                                <div className="flex items-center">
                                                    <span className={`inline-flex items-center justify-center w-6 h-6 rounded-full text-xs font-medium ${
                                                        index === 0 ? 'bg-green-100 text-green-800' :
                                                        index === institutionalProfileData.length - 1 ? 'bg-red-100 text-red-800' :
                                                        'bg-blue-100 text-blue-800'
                                                    }`}>
                                                        {index + 1}
                                                    </span>
                                                    <span className="ml-2 text-sm font-medium text-gray-900">
                                                        {apt.aptitud}
                                                    </span>
                                                </div>
                                                <div className="flex items-center">
                                                    <div className="w-20 bg-gray-200 rounded-full h-2 mr-2">
                                                        <div
                                                            className="bg-blue-600 h-2 rounded-full"
                                                            style={{ width: `${apt.percentil}%` }}
                                                        ></div>
                                                    </div>
                                                    <span className="text-sm font-medium text-gray-900">
                                                        {apt.percentil.toFixed(1)}%
                                                    </span>
                                                </div>
                                            </div>
                                        ))
                                    }
                                </div>
                            </div>
                        </div>
                    ) : (
                        <div className="text-center py-8 text-gray-500">
                            No hay datos de perfil disponibles
                        </div>
                    )}
                </div>

                {/* Estadísticas de Distribución */}
                <div className="bg-white p-6 rounded-lg border shadow-sm">
                    <h3 className="text-lg font-medium text-gray-900 mb-4">
                        Estadísticas de Distribución
                    </h3>
                    {validDistributionData.length > 0 ? (
                        <div className="space-y-4">
                            {/* Resumen estadístico */}
                            <div className="grid grid-cols-2 gap-4">
                                <div className="text-center p-3 bg-blue-50 rounded-lg">
                                    <p className="text-2xl font-bold text-blue-600">
                                        {validDistributionData.length}
                                    </p>
                                    <p className="text-sm text-blue-600">Niveles Representados</p>
                                </div>
                                <div className="text-center p-3 bg-green-50 rounded-lg">
                                    <p className="text-2xl font-bold text-green-600">
                                        {validDistributionData.reduce((sum, item) => sum + item.value, 0)}
                                    </p>
                                    <p className="text-sm text-green-600">Total Estudiantes</p>
                                </div>
                            </div>

                            {/* Distribución detallada */}
                            <div>
                                <h4 className="text-sm font-medium text-gray-900 mb-2">Distribución Detallada</h4>
                                <div className="space-y-2">
                                    {validDistributionData
                                        .sort((a, b) => b.value - a.value)
                                        .map((item, index) => {
                                            const total = validDistributionData.reduce((sum, d) => sum + d.value, 0);
                                            const percentage = ((item.value / total) * 100).toFixed(1);

                                            return (
                                                <div key={item.name} className="flex items-center justify-between p-2 bg-gray-50 rounded">
                                                    <div className="flex items-center">
                                                        <div
                                                            className="w-4 h-4 rounded-full mr-2"
                                                            style={{ backgroundColor: item.color }}
                                                        ></div>
                                                        <span className="text-sm font-medium text-gray-900">
                                                            {item.name}
                                                        </span>
                                                    </div>
                                                    <div className="flex items-center">
                                                        <span className="text-sm text-gray-600 mr-2">
                                                            {item.value} estudiante{item.value !== 1 ? 's' : ''}
                                                        </span>
                                                        <span className="text-sm font-medium text-gray-900">
                                                            {percentage}%
                                                        </span>
                                                    </div>
                                                </div>
                                            );
                                        })
                                    }
                                </div>
                            </div>

                            {/* Interpretación automática */}
                            <div className="p-3 bg-blue-50 rounded-md border-l-4 border-blue-400">
                                <div className="flex">
                                    <div className="flex-shrink-0">
                                        <InformationCircleIcon className="h-5 w-5 text-blue-400" />
                                    </div>
                                    <div className="ml-3">
                                        <p className="text-sm font-medium text-blue-800">Interpretación Automática</p>
                                        <p className="text-sm text-blue-700 mt-1">
                                            {(() => {
                                                const total = validDistributionData.reduce((sum, d) => sum + d.value, 0);
                                                const maxLevel = validDistributionData.reduce((max, item) =>
                                                    item.value > max.value ? item : max
                                                );
                                                const dominantPercentage = ((maxLevel.value / total) * 100).toFixed(1);

                                                if (parseFloat(dominantPercentage) > 60) {
                                                    return `La población está concentrada principalmente en ${maxLevel.name} (${dominantPercentage}%). Considere diversificar la muestra.`;
                                                } else if (validDistributionData.length <= 2) {
                                                    return `Distribución limitada a ${validDistributionData.length} niveles. Ampliar la cobertura mejoraría la representatividad.`;
                                                } else {
                                                    return `Distribución equilibrada entre ${validDistributionData.length} niveles educativos. Buena representatividad de la población.`;
                                                }
                                            })()}
                                        </p>
                                    </div>
                                </div>
                            </div>
                        </div>
                    ) : (
                        <div className="text-center py-8 text-gray-500">
                            No hay datos de distribución disponibles
                        </div>
                    )}
                </div>
            </div>

            {/* Alertas y Recomendaciones */}
            {(data.alerts?.length > 0 || data.recommendations?.length > 0) && (
                <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                    {/* Alertas */}
                    {data.alerts && data.alerts.length > 0 && (
                        <div className="bg-white p-6 rounded-lg border shadow-sm">
                            <h3 className="text-lg font-medium text-gray-900 mb-4 flex items-center">
                                <ExclamationTriangleIcon className="w-5 h-5 text-yellow-500 mr-2" />
                                Alertas del Sistema
                            </h3>
                            <div className="space-y-3">
                                {data.alerts.map((alert, index) => (
                                    <div
                                        key={alert.id || index}
                                        className={`p-3 rounded-md border-l-4 ${alert.type === 'error' ? 'bg-red-50 border-red-400' :
                                            alert.type === 'warning' ? 'bg-yellow-50 border-yellow-400' :
                                                'bg-blue-50 border-blue-400'
                                            }`}
                                    >
                                        <div className="flex">
                                            <div className="flex-shrink-0">
                                                {alert.type === 'error' ? (
                                                    <ExclamationTriangleIcon className="h-5 w-5 text-red-400" />
                                                ) : alert.type === 'warning' ? (
                                                    <ExclamationTriangleIcon className="h-5 w-5 text-yellow-400" />
                                                ) : (
                                                    <InformationCircleIcon className="h-5 w-5 text-blue-400" />
                                                )}
                                            </div>
                                            <div className="ml-3">
                                                <p className={`text-sm font-medium ${alert.type === 'error' ? 'text-red-800' :
                                                    alert.type === 'warning' ? 'text-yellow-800' :
                                                        'text-blue-800'
                                                    }`}>
                                                    {alert.message}
                                                </p>
                                            </div>
                                        </div>
                                    </div>
                                ))}
                            </div>
                        </div>
                    )}

                    {/* Recomendaciones */}
                    {data.recommendations && data.recommendations.length > 0 && (
                        <div className="bg-white p-6 rounded-lg border shadow-sm">
                            <h3 className="text-lg font-medium text-gray-900 mb-4 flex items-center">
                                <TrendingUpIcon className="w-5 h-5 text-green-500 mr-2" />
                                Recomendaciones
                            </h3>
                            <div className="space-y-3">
                                {data.recommendations.map((rec, index) => (
                                    <div
                                        key={index}
                                        className="p-3 rounded-md bg-green-50 border-l-4 border-green-400"
                                    >
                                        <div className="flex">
                                            <div className="flex-shrink-0">
                                                <TrendingUpIcon className="h-5 w-5 text-green-400" />
                                            </div>
                                            <div className="ml-3">
                                                <p className="text-sm font-medium text-green-800">
                                                    {rec.title}
                                                </p>
                                                <p className="text-sm text-green-700 mt-1">
                                                    {rec.description}
                                                </p>
                                                {rec.priority && (
                                                    <span className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium mt-2 ${rec.priority === 'high' ? 'bg-red-100 text-red-800' :
                                                        rec.priority === 'medium' ? 'bg-yellow-100 text-yellow-800' :
                                                            'bg-green-100 text-green-800'
                                                        }`}>
                                                        Prioridad {rec.priority === 'high' ? 'Alta' : rec.priority === 'medium' ? 'Media' : 'Baja'}
                                                    </span>
                                                )}
                                            </div>
                                        </div>
                                    </div>
                                ))}
                            </div>
                        </div>
                    )}
                </div>
            )}

            {/* Botón de actualización */}
            <div className="flex justify-center">
                <button
                    onClick={loadOverviewData}
                    disabled={loading}
                    className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 disabled:opacity-50"
                >
                    {loading ? (
                        <>
                            <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                            Actualizando...
                        </>
                    ) : (
                        <>
                            <TrendingUpIcon className="w-4 h-4 mr-2" />
                            Actualizar Datos
                        </>
                    )}
                </button>
            </div>
        </div>
    );
};

export default OverviewModule;