/**
 * @file AlertsPanel.jsx
 * @description Alerts panel component for KPI alerts display
 */

import React, { memo } from 'react';
import PropTypes from 'prop-types';
import { 
  ExclamationTriangleIcon,
  CheckCircleIcon,
  XCircleIcon
} from '@heroicons/react/24/outline';
import { ALERT_SEVERITY_CONFIG } from '../../../../constants/kpiConstants.js';

const AlertsPanel = memo(({ alerts = [] }) => (
  <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
    <h3 className="text-lg font-semibold text-gray-900 mb-4">
      Alertas de KPIs ({alerts.length})
    </h3>
    {alerts.length === 0 ? (
      <div className="text-center py-8 text-gray-500">
        <CheckCircleIcon className="h-12 w-12 mx-auto mb-2 text-green-500" />
        <p>Todos los KPIs están dentro de los rangos aceptables</p>
      </div>
    ) : (
      <div className="space-y-3">
        {alerts.map((alert) => {
          const severityConfig = ALERT_SEVERITY_CONFIG[alert.severity] || ALERT_SEVERITY_CONFIG.low;
          
          return (
            <div 
              key={alert.id}
              className={`p-4 rounded-lg border-l-4 ${
                alert.type === 'error' ? 'bg-red-50 border-red-400' :
                alert.type === 'warning' ? 'bg-yellow-50 border-yellow-400' :
                'bg-blue-50 border-blue-400'
              }`}
            >
              <div className="flex items-start">
                <div className="flex-shrink-0">
                  {alert.type === 'error' ? (
                    <XCircleIcon className="h-5 w-5 text-red-400" />
                  ) : alert.type === 'warning' ? (
                    <ExclamationTriangleIcon className="h-5 w-5 text-yellow-400" />
                  ) : (
                    <CheckCircleIcon className="h-5 w-5 text-blue-400" />
                  )}
                </div>
                <div className="ml-3 flex-1">
                  <h4 className="text-sm font-medium text-gray-900">
                    {alert.title}
                  </h4>
                  <p className="text-sm text-gray-700 mt-1">
                    {alert.message}
                  </p>
                  <div className="mt-2 flex items-center space-x-2 text-xs text-gray-500">
                    <span className={`px-2 py-1 rounded-full ${severityConfig.bgColor} ${severityConfig.textColor}`}>
                      {severityConfig.label} Prioridad
                    </span>
                  </div>
                </div>
              </div>
            </div>
          );
        })}
      </div>
    )}
  </div>
));

AlertsPanel.displayName = 'AlertsPanel';

AlertsPanel.propTypes = {
  alerts: PropTypes.arrayOf(PropTypes.shape({
    id: PropTypes.string.isRequired,
    type: PropTypes.oneOf(['error', 'warning', 'info']).isRequired,
    title: PropTypes.string.isRequired,
    message: PropTypes.string.isRequired,
    severity: PropTypes.oneOf(['high', 'medium', 'low']).isRequired
  }))
};

export default AlertsPanel;