/**
 * @file BulletChart.jsx
 * @description Optimized bullet chart component for KPI visualization
 */

import React, { memo, useMemo } from 'react';
import PropTypes from 'prop-types';
import { KPI_STATUS_CONFIG } from '../../../../constants/kpiConstants.js';

const BulletChart = memo(({ kpi }) => {
  const chartData = useMemo(() => {
    const maxWidth = Math.max(kpi.value, kpi.target) * 1.2;
    const valueWidth = Math.min((kpi.value / maxWidth) * 100, 100);
    const targetWidth = Math.min((kpi.target / maxWidth) * 100, 100);
    
    return { valueWidth, targetWidth };
  }, [kpi.value, kpi.target]);

  const statusConfig = KPI_STATUS_CONFIG[kpi.status] || KPI_STATUS_CONFIG.default;

  return (
    <div className="space-y-2">
      <div className="flex justify-between text-sm">
        <span className="font-medium">{kpi.name}</span>
        <span className={`font-semibold ${statusConfig.textColor}`}>
          {kpi.value} {kpi.unit}
        </span>
      </div>
      
      <div className="relative h-6 bg-gray-200 rounded-full overflow-hidden">
        {/* Progress bar */}
        <div 
          className={`h-full transition-all duration-500 ${statusConfig.chartColor}`}
          style={{ width: `${chartData.valueWidth}%` }}
        />
        {/* Target line */}
        <div 
          className="absolute top-0 h-full w-1 bg-gray-800"
          style={{ left: `${chartData.targetWidth}%` }}
        />
      </div>
      
      <div className="flex justify-between text-xs text-gray-500">
        <span>0</span>
        <span>Objetivo: {kpi.target} {kpi.unit}</span>
      </div>
    </div>
  );
});

BulletChart.displayName = 'BulletChart';

BulletChart.propTypes = {
  kpi: PropTypes.shape({
    name: PropTypes.string.isRequired,
    value: PropTypes.number.isRequired,
    target: PropTypes.number.isRequired,
    unit: PropTypes.string.isRequired,
    status: PropTypes.string.isRequired
  }).isRequired
};

export default BulletChart;