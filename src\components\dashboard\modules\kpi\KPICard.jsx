/**
 * @file KPICard.jsx
 * @description Individual KPI display card component
 */

import React, { memo } from 'react';
import PropTypes from 'prop-types';
import { 
  TrendingUpIcon, 
  TrendingDownIcon, 
  ExclamationTriangleIcon,
  CheckCircleIcon,
  XCircleIcon
} from '@heroicons/react/24/outline';
import { KPI_STATUS_CONFIG } from '../../../../constants/kpiConstants.js';
import Bullet<PERSON><PERSON> from './BulletChart.jsx';

const KPICard = memo(({ kpi }) => {
  const statusConfig = KPI_STATUS_CONFIG[kpi.status] || KPI_STATUS_CONFIG.default;

  const getTrendIcon = (trend, trendValue) => {
    if (trend === 'up') {
      return <TrendingUpIcon className={`h-4 w-4 ${trendValue > 0 ? 'text-green-500' : 'text-red-500'}`} />;
    } else if (trend === 'down') {
      return <TrendingDownIcon className={`h-4 w-4 ${trendValue < 0 ? 'text-red-500' : 'text-green-500'}`} />;
    }
    return null;
  };

  return (
    <div className={`p-6 rounded-lg border-2 ${statusConfig.cardClasses} transition-all duration-200 hover:shadow-md`}>
      <div className="flex items-start justify-between">
        <div className="flex-1">
          <div className="flex items-center space-x-2">
            {statusConfig.icon}
            <h3 className="font-semibold text-lg">{kpi.name}</h3>
            {kpi.critical && (
              <span className="px-2 py-1 text-xs font-medium bg-red-100 text-red-800 rounded-full">
                Crítico
              </span>
            )}
          </div>
          <p className="text-sm text-gray-600 mt-1">{kpi.description}</p>
        </div>
        <div className="text-right">
          <div className="text-2xl font-bold">
            {kpi.value} <span className="text-sm font-normal">{kpi.unit}</span>
          </div>
          <div className="flex items-center space-x-1 text-sm">
            {getTrendIcon(kpi.trend, kpi.trendValue)}
            <span className={kpi.trendValue >= 0 ? 'text-green-600' : 'text-red-600'}>
              {kpi.trendValue > 0 ? '+' : ''}{kpi.trendValue}%
            </span>
          </div>
        </div>
      </div>
      
      <div className="mt-4">
        <BulletChart kpi={kpi} />
      </div>
      
      <div className="mt-3 flex justify-between text-sm">
        <span>
          Objetivo: <span className="font-medium">{kpi.target} {kpi.unit}</span>
        </span>
        <span className={kpi.deviation >= 0 ? 'text-green-600' : 'text-red-600'}>
          {kpi.deviation >= 0 ? '+' : ''}{kpi.deviation} ({kpi.deviationPercent >= 0 ? '+' : ''}{kpi.deviationPercent}%)
        </span>
      </div>
      
      {kpi.error && (
        <div className="mt-2 p-2 bg-red-50 border border-red-200 rounded text-sm text-red-700">
          Error: {kpi.error}
        </div>
      )}
    </div>
  );
});

KPICard.displayName = 'KPICard';

KPICard.propTypes = {
  kpi: PropTypes.shape({
    id: PropTypes.string.isRequired,
    name: PropTypes.string.isRequired,
    value: PropTypes.number.isRequired,
    target: PropTypes.number.isRequired,
    unit: PropTypes.string.isRequired,
    status: PropTypes.oneOf(['excellent', 'good', 'warning', 'critical', 'error']).isRequired,
    trend: PropTypes.oneOf(['up', 'down', 'stable']),
    trendValue: PropTypes.number,
    deviation: PropTypes.number.isRequired,
    deviationPercent: PropTypes.number.isRequired,
    critical: PropTypes.bool,
    description: PropTypes.string,
    error: PropTypes.string
  }).isRequired
};

export default KPICard;