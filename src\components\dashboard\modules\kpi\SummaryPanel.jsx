/**
 * @file SummaryPanel.jsx
 * @description Summary panel component for KPI overview statistics
 */

import React, { memo } from 'react';
import PropTypes from 'prop-types';

const SummaryPanel = memo(({ chartData }) => {
  if (!chartData?.summary) return null;

  const { summary } = chartData;
  
  return (
    <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
      <h3 className="text-lg font-semibold text-gray-900 mb-4">Resumen de KPIs</h3>
      <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
        <div className="text-center">
          <div className="text-2xl font-bold text-green-600">{summary.excellent}</div>
          <div className="text-sm text-gray-600">Excelentes</div>
        </div>
        <div className="text-center">
          <div className="text-2xl font-bold text-blue-600">{summary.good}</div>
          <div className="text-sm text-gray-600">Buenos</div>
        </div>
        <div className="text-center">
          <div className="text-2xl font-bold text-yellow-600">{summary.warning}</div>
          <div className="text-sm text-gray-600">En Riesgo</div>
        </div>
        <div className="text-center">
          <div className="text-2xl font-bold text-red-600">{summary.criticalIssues}</div>
          <div className="text-sm text-gray-600">Críticos</div>
        </div>
      </div>
      <div className="mt-4 pt-4 border-t border-gray-200">
        <div className="flex justify-between text-sm text-gray-600">
          <span>Total de KPIs: {summary.total}</span>
          <span>KPIs Críticos: {summary.critical}</span>
        </div>
      </div>
    </div>
  );
});

SummaryPanel.displayName = 'SummaryPanel';

SummaryPanel.propTypes = {
  chartData: PropTypes.shape({
    summary: PropTypes.shape({
      total: PropTypes.number.isRequired,
      critical: PropTypes.number.isRequired,
      excellent: PropTypes.number.isRequired,
      good: PropTypes.number.isRequired,
      warning: PropTypes.number.isRequired,
      criticalIssues: PropTypes.number.isRequired
    })
  })
};

export default SummaryPanel;