import React, { memo } from 'react';
import { FaInfoCircle, FaChartBar, FaAward } from 'react-icons/fa';

/**
 * Tabla de Puntuaciones BAT-7
 * Muestra las puntuaciones PD y Pc de todas las aptitudes con interpretaciones
 */
const ScoresTable = ({ data, showInterpretations = true, compact = false }) => {
  
  const aptitudeInfo = {
    V: {
      name: 'Verbal',
      description: 'Capacidad para comprender y utilizar el lenguaje',
      color: 'blue'
    },
    E: {
      name: 'Espacial',
      description: 'Habilidad para visualizar y manipular objetos en el espacio',
      color: 'green'
    },
    A: {
      name: 'Atención',
      description: 'Capacidad para mantener la concentración',
      color: 'yellow'
    },
    CON: {
      name: 'Concentración',
      description: 'Habilidad para enfocar la atención sostenidamente',
      color: 'orange'
    },
    R: {
      name: '<PERSON>zonamient<PERSON>',
      description: 'Capacidad de pensamiento lógico y resolución de problemas',
      color: 'purple'
    },
    N: {
      name: '<PERSON><PERSON><PERSON><PERSON><PERSON>',
      description: 'Habilidad para trabajar con números y conceptos matemáticos',
      color: 'red'
    },
    M: {
      name: 'Mecánico',
      description: 'Comprensión de principios mecánicos y físicos',
      color: 'indigo'
    },
    O: {
      name: 'Ortografía',
      description: 'Conocimiento de reglas ortográficas y escritura correcta',
      color: 'pink'
    }
  };

  const getNivelColor = (nivel) => {
    const colors = {
      'Muy Alto': 'bg-green-100 text-green-800 border-green-200',
      'Alto': 'bg-green-50 text-green-700 border-green-100',
      'Medio-Alto': 'bg-blue-50 text-blue-700 border-blue-100',
      'Medio': 'bg-yellow-50 text-yellow-700 border-yellow-100',
      'Medio-Bajo': 'bg-orange-50 text-orange-700 border-orange-100',
      'Bajo': 'bg-red-50 text-red-700 border-red-100',
      'Muy Bajo': 'bg-red-100 text-red-800 border-red-200'
    };
    return colors[nivel] || 'bg-gray-50 text-gray-700 border-gray-100';
  };

  const getPercentileColor = (pc) => {
    if (pc >= 90) return 'text-green-700 font-bold';
    if (pc >= 75) return 'text-green-600 font-semibold';
    if (pc >= 60) return 'text-blue-600 font-semibold';
    if (pc >= 40) return 'text-yellow-600 font-medium';
    if (pc >= 25) return 'text-orange-600 font-medium';
    if (pc >= 10) return 'text-red-600 font-medium';
    return 'text-red-700 font-bold';
  };

  const getInterpretation = (pc) => {
    if (pc >= 90) return 'Rendimiento excepcional, muy superior al promedio';
    if (pc >= 75) return 'Rendimiento alto, superior al promedio';
    if (pc >= 60) return 'Rendimiento medio-alto, ligeramente superior';
    if (pc >= 40) return 'Rendimiento promedio, dentro de lo esperado';
    if (pc >= 25) return 'Rendimiento medio-bajo, ligeramente inferior';
    if (pc >= 10) return 'Rendimiento bajo, requiere atención';
    return 'Rendimiento muy bajo, requiere intervención';
  };

  if (!data || !data.puntuaciones) {
    return (
      <div className="text-center py-8">
        <FaChartBar className="mx-auto text-4xl text-gray-300 mb-4" />
        <p className="text-gray-500">No hay datos de puntuaciones disponibles</p>
      </div>
    );
  }

  const aptitudes = Object.keys(data.puntuaciones);

  return (
    <div className="space-y-4">
      {/* Tabla principal */}
      <div className="overflow-x-auto">
        <table className="min-w-full divide-y divide-gray-200 bg-white rounded-lg shadow">
          <thead className="bg-gray-50">
            <tr>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Aptitud
              </th>
              <th className="px-6 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider">
                PD
              </th>
              <th className="px-6 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider">
                Percentil
              </th>
              <th className="px-6 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider">
                Nivel
              </th>
              {showInterpretations && !compact && (
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Interpretación
                </th>
              )}
            </tr>
          </thead>
          <tbody className="bg-white divide-y divide-gray-200">
            {aptitudes.map((aptitud, index) => {
              const scores = data.puntuaciones[aptitud];
              const info = aptitudeInfo[aptitud];
              
              return (
                <tr key={aptitud} className={`hover:bg-gray-50 ${index % 2 === 0 ? 'bg-white' : 'bg-gray-25'}`}>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="flex items-center">
                      <div className={`w-3 h-3 rounded-full mr-3 bg-${info?.color || 'gray'}-400`}></div>
                      <div>
                        <div className="text-sm font-medium text-gray-900">
                          {aptitud} - {info?.name || aptitud}
                        </div>
                        {!compact && (
                          <div className="text-xs text-gray-500">
                            {info?.description}
                          </div>
                        )}
                      </div>
                    </div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-center">
                    <span className="text-sm font-medium text-gray-900">
                      {scores.pd}
                    </span>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-center">
                    <span className={`text-lg ${getPercentileColor(scores.pc)}`}>
                      {scores.pc}
                    </span>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-center">
                    <span className={`inline-flex px-3 py-1 text-xs font-medium rounded-full border ${getNivelColor(scores.nivel)}`}>
                      {scores.nivel}
                    </span>
                  </td>
                  {showInterpretations && !compact && (
                    <td className="px-6 py-4">
                      <div className="text-sm text-gray-600">
                        {getInterpretation(scores.pc)}
                      </div>
                    </td>
                  )}
                </tr>
              );
            })}
          </tbody>
        </table>
      </div>

      {/* Resumen estadístico */}
      {!compact && (
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mt-6">
          <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
            <div className="flex items-center">
              <FaAward className="text-blue-500 text-xl mr-3" />
              <div>
                <h4 className="text-sm font-medium text-blue-800">Fortalezas</h4>
                <p className="text-xs text-blue-600">
                  {aptitudes.filter(apt => data.puntuaciones[apt].pc >= 75).length} aptitudes altas
                </p>
              </div>
            </div>
          </div>

          <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4">
            <div className="flex items-center">
              <FaChartBar className="text-yellow-500 text-xl mr-3" />
              <div>
                <h4 className="text-sm font-medium text-yellow-800">Promedio</h4>
                <p className="text-xs text-yellow-600">
                  Pc {Math.round(aptitudes.reduce((sum, apt) => sum + data.puntuaciones[apt].pc, 0) / aptitudes.length)}
                </p>
              </div>
            </div>
          </div>

          <div className="bg-orange-50 border border-orange-200 rounded-lg p-4">
            <div className="flex items-center">
              <FaInfoCircle className="text-orange-500 text-xl mr-3" />
              <div>
                <h4 className="text-sm font-medium text-orange-800">Áreas de mejora</h4>
                <p className="text-xs text-orange-600">
                  {aptitudes.filter(apt => data.puntuaciones[apt].pc < 25).length} aptitudes bajas
                </p>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Leyenda de interpretación */}
      {showInterpretations && (
        <div className="bg-gray-50 rounded-lg p-4 mt-4">
          <h4 className="text-sm font-medium text-gray-700 mb-3 flex items-center">
            <FaInfoCircle className="mr-2 text-blue-500" />
            Guía de Interpretación de Percentiles
          </h4>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-3 text-xs">
            <div className="space-y-2">
              <div className="flex items-center justify-between">
                <span>Pc 90-99:</span>
                <span className="px-2 py-1 bg-green-100 text-green-800 rounded">Muy Alto</span>
              </div>
              <div className="flex items-center justify-between">
                <span>Pc 75-89:</span>
                <span className="px-2 py-1 bg-green-50 text-green-700 rounded">Alto</span>
              </div>
              <div className="flex items-center justify-between">
                <span>Pc 60-74:</span>
                <span className="px-2 py-1 bg-blue-50 text-blue-700 rounded">Medio-Alto</span>
              </div>
              <div className="flex items-center justify-between">
                <span>Pc 40-59:</span>
                <span className="px-2 py-1 bg-yellow-50 text-yellow-700 rounded">Medio</span>
              </div>
            </div>
            <div className="space-y-2">
              <div className="flex items-center justify-between">
                <span>Pc 25-39:</span>
                <span className="px-2 py-1 bg-orange-50 text-orange-700 rounded">Medio-Bajo</span>
              </div>
              <div className="flex items-center justify-between">
                <span>Pc 10-24:</span>
                <span className="px-2 py-1 bg-red-50 text-red-700 rounded">Bajo</span>
              </div>
              <div className="flex items-center justify-between">
                <span>Pc 1-9:</span>
                <span className="px-2 py-1 bg-red-100 text-red-800 rounded">Muy Bajo</span>
              </div>
              <div className="text-gray-500 text-xs mt-2">
                <strong>Nota:</strong> Los percentiles indican el porcentaje de personas que obtuvieron puntuaciones iguales o menores.
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default memo(ScoresTable);
