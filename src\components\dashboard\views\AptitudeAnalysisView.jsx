import React, { useContext, useState, useMemo } from 'react';
import { DashboardContext } from '../../../context/DashboardContext';
import { FaBrain } from 'react-icons/fa';
import ComparisonBarChart from '../../charts/ComparisonBarChart';

const APTITUDES = {
  V: 'Verbal', E: 'Espacial', A: 'Atención', CON: 'Concentración',
  R: 'Razonamiento', N: 'Numérica', M: 'Mecánica', O: 'Ortografía'
};

const AptitudeSelector = ({ selected, onSelect }) => (
  <div className="bg-white p-4 rounded-lg shadow mb-6">
    <h3 className="font-semibold mb-2">Seleccionar Aptitudes</h3>
    <div className="grid grid-cols-2 md:grid-cols-4 gap-2">
      {Object.entries(APTITUDES).map(([key, name]) => (
        <label key={key} className="flex items-center space-x-2 p-2 rounded-md hover:bg-gray-100 cursor-pointer">
          <input
            type="checkbox"
            checked={selected.includes(key)}
            onChange={() => onSelect(key)}
            className="h-4 w-4 rounded border-gray-300 text-indigo-600 focus:ring-indigo-500"
          />
          <span className="text-sm">{name}</span>
        </label>
      ))}
    </div>
  </div>
);

const AptitudeAnalysisView = () => {
  const { loading, data, error } = useContext(DashboardContext);
  const [selectedAptitudes, setSelectedAptitudes] = useState(['V', 'R', 'N']);

  const handleSelectAptitude = (aptitudeKey) => {
    setSelectedAptitudes(prev =>
      prev.includes(aptitudeKey)
        ? prev.filter(a => a !== aptitudeKey)
        : [...prev, aptitudeKey]
    );
  };

  const filteredChartData = useMemo(() => {
    if (!data?.aptitudeData) return [];
    if (selectedAptitudes.length === 0) return [];
    
    return selectedAptitudes
      .map(key => ({
        name: APTITUDES[key],
        value: data.aptitudeData[key] || 0,
      }))
      .sort((a, b) => b.value - a.value);
  }, [data, selectedAptitudes]);

  if (loading) return <div>Cargando datos de aptitudes...</div>;
  if (error) return <div className="text-red-500">Error al cargar los datos: {error}</div>;

  return (
    <div className="space-y-6">
      <div className="bg-white p-6 rounded-lg shadow">
        <h2 className="text-2xl font-bold text-gray-800 flex items-center">
          <FaBrain className="mr-3 text-indigo-500" />
          Análisis Detallado de Aptitudes
        </h2>
        <p className="text-gray-600 mt-1">
          Seleccione una o más aptitudes para comparar su rendimiento a través de las poblaciones filtradas.
        </p>
      </div>
      
      <AptitudeSelector selected={selectedAptitudes} onSelect={handleSelectAptitude} />
      
      {filteredChartData.length > 0 ? (
        <ComparisonBarChart
          data={filteredChartData}
          title="Comparativa de Aptitudes Seleccionadas"
        />
      ) : (
        <div className="text-center text-gray-500 py-8 bg-white rounded-lg shadow">
          Seleccione al menos una aptitud para ver el gráfico.
        </div>
      )}
    </div>
  );
};

export default AptitudeAnalysisView;