import React, { memo, useState } from 'react';
import { FaBalanceScale, FaUniversity, FaUserTie, FaUsers, FaGraduationCap } from 'react-icons/fa';
import ComparativaGeneroEnhanced from './enhanced/ComparativaGeneroEnhanced';

// Mock data (debería venir de una fuente compartida o API)
const mockData = {
  institutions: [
    { id: 'inst1', name: 'Colegio San José' },
    { id: 'inst2', name: 'Liceo Moderno' },
  ],
  populations: [
    { id: 'pop1', name: 'Escolares 1º-2º ESO' },
    { id: 'pop2', name: 'Adultos con bachillerato' },
  ],
};

const GroupSelector = ({ group, onUpdate, title }) => {
  return (
    <div className="bg-gray-50 p-4 rounded-lg border">
      <h3 className="font-semibold text-lg mb-4">{title}</h3>
      <div className="space-y-4">
        <div>
          <label className="block text-sm font-medium mb-1"><FaUniversity className="inline mr-2"/>Institución</label>
          <select name="institution" onChange={e => onUpdate(group, 'institution', e.target.value)} className="input w-full">
            <option value="">Cualquiera</option>
            {mockData.institutions.map(i => <option key={i.id} value={i.id}>{i.name}</option>)}
          </select>
        </div>
        <div>
          <label className="block text-sm font-medium mb-1"><FaUsers className="inline mr-2"/>Población</label>
          <select name="population" onChange={e => onUpdate(group, 'population', e.target.value)} className="input w-full">
            <option value="">Cualquiera</option>
            {mockData.populations.map(p => <option key={p.id} value={p.id}>{p.name}</option>)}
          </select>
        </div>
      </div>
    </div>
  );
};

const ComparativeView = ({ loading, error, data, filters }) => {
  const [comparison, setComparison] = useState({
    groupA: { institution: '', population: '' },
    groupB: { institution: '', population: '' },
  });

  const handleGroupUpdate = (group, key, value) => {
    setComparison(prev => ({
      ...prev,
      [group]: { ...prev[group], [key]: value }
    }));
  };

  if (loading) return <div>Cargando...</div>;
  if (error) return <div className="text-red-500">Error: {error}</div>;

  return (
    <div className="space-y-6">
      <div className="bg-white p-6 rounded-lg shadow">
        <h2 className="text-2xl font-bold text-gray-800 flex items-center">
          <FaBalanceScale className="mr-3 text-indigo-500" />
          Análisis Comparativo A/B
        </h2>
        <p className="text-gray-600 mt-1">
          Configure dos grupos para comparar sus resultados lado a lado.
        </p>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        <GroupSelector group="groupA" onUpdate={handleGroupUpdate} title="Grupo A" />
        <GroupSelector group="groupB" onUpdate={handleGroupUpdate} title="Grupo B" />
      </div>

      <div className="bg-white p-6 rounded-lg shadow">
        <h3 className="text-xl font-bold mb-4">Resultados de la Comparación</h3>
        <div className="text-center py-10 border-2 border-dashed rounded-lg">
          <p className="text-gray-500">La visualización de resultados comparativos está en construcción.</p>
          <p className="text-xs text-gray-400 mt-2">Se mostrarán tablas y gráficos comparando métricas clave.</p>
          <div className="mt-4 text-left inline-block text-xs bg-gray-100 p-4 rounded">
            <p><strong>Grupo A:</strong> {JSON.stringify(comparison.groupA)}</p>
            <p><strong>Grupo B:</strong> {JSON.stringify(comparison.groupB)}</p>
          </div>
        </div>
      </div>

      <ComparativaGeneroEnhanced
        data={data}
        loading={loading}
        filters={filters}
      />
    </div>
  );
};

export default memo(ComparativeView);
