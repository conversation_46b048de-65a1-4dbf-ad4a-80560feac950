import React, { useState, useEffect } from 'react';
import { Card, CardHeader, CardBody } from '../../ui/Card';
import { FaUsers, FaCalendarAlt, FaBrain, FaChartBar } from 'react-icons/fa';
import { BarChart, Bar, XAxis, YAxis, CartesianGrid, Tooltip, Legend, ResponsiveContainer, RadarChart, PolarGrid, PolarAngleAxis, PolarRadiusAxis, Radar } from 'recharts';
import DashboardService from '../../../services/DashboardService';

/**
 * Vista de Análisis Comparativo
 * Compara rendimiento por género, edad y aptitudes
 */
const ComparisonView = ({ loading, comparisonData }) => {
  const [realComparisonData, setRealComparisonData] = useState(null);
  const [loadingComparison, setLoadingComparison] = useState(true);
  const [selectedComparison, setSelectedComparison] = useState('genero');

  // Cargar datos reales de comparación
  useEffect(() => {
    const loadComparisonData = async () => {
      try {
        console.log('🔍 [ComparisonView] Cargando datos REALES de comparación...');
        const datos = await DashboardService.getComparisonData();
        setRealComparisonData(datos);
        console.log('✅ [ComparisonView] Datos de comparación cargados:', datos);
      } catch (error) {
        console.error('❌ [ComparisonView] Error cargando comparación:', error);
        setRealComparisonData(null);
      } finally {
        setLoadingComparison(false);
      }
    };

    loadComparisonData();
  }, []);

  const dataToUse = realComparisonData || comparisonData || {
    porGenero: [],
    porEdad: [],
    porAptitud: []
  };

  // Procesar datos para gráficos
  const processDataForChart = (data, type) => {
    if (!data || data.length === 0) return [];

    if (type === 'aptitud') {
      return data.map(item => ({
        name: item.codigo,
        fullName: item.nombre,
        promedio: parseFloat(item.promedio?.toFixed(1) || 0),
        total: item.total || 0,
        desviacion: parseFloat(item.desviacion?.toFixed(1) || 0)
      }));
    }

    // Para género y edad
    const aptitudes = ['V', 'E', 'A', 'R', 'N', 'M', 'O'];
    const result = [];

    aptitudes.forEach(aptitud => {
      const chartItem = { aptitud };
      data.forEach(categoria => {
        const aptitudData = categoria.datos?.find(d => d.aptitud === aptitud);
        if (aptitudData) {
          chartItem[categoria.categoria] = parseFloat(aptitudData.promedio?.toFixed(1) || 0);
        }
      });
      result.push(chartItem);
    });

    return result;
  };

  const getChartData = () => {
    switch (selectedComparison) {
      case 'genero':
        return processDataForChart(dataToUse.porGenero, 'genero');
      case 'edad':
        return processDataForChart(dataToUse.porEdad, 'edad');
      case 'aptitud':
        return processDataForChart(dataToUse.porAptitud, 'aptitud');
      default:
        return [];
    }
  };

  const getRadarData = () => {
    if (selectedComparison === 'aptitud') return [];
    
    const data = selectedComparison === 'genero' ? dataToUse.porGenero : dataToUse.porEdad;
    if (!data || data.length === 0) return [];

    return data.map(categoria => ({
      categoria: categoria.categoria,
      data: categoria.datos?.map(d => ({
        aptitud: d.aptitud,
        promedio: parseFloat(d.promedio?.toFixed(1) || 0)
      })) || []
    }));
  };

  if (loading || loadingComparison) {
    return (
      <div className="space-y-6">
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          {[1, 2, 3].map((i) => (
            <Card key={i} className="animate-pulse">
              <CardBody className="p-4">
                <div className="h-4 bg-gray-200 rounded mb-2"></div>
                <div className="h-8 bg-gray-200 rounded"></div>
              </CardBody>
            </Card>
          ))}
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Controles de Comparación */}
      <Card>
        <CardHeader>
          <h3 className="text-lg font-semibold">Análisis Comparativo</h3>
        </CardHeader>
        <CardBody>
          <div className="flex flex-wrap gap-4">
            <button
              onClick={() => setSelectedComparison('genero')}
              className={`flex items-center px-4 py-2 rounded-lg transition-colors ${
                selectedComparison === 'genero'
                  ? 'bg-blue-500 text-white'
                  : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
              }`}
            >
              <FaUsers className="mr-2" />
              Por Género
            </button>
            <button
              onClick={() => setSelectedComparison('edad')}
              className={`flex items-center px-4 py-2 rounded-lg transition-colors ${
                selectedComparison === 'edad'
                  ? 'bg-blue-500 text-white'
                  : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
              }`}
            >
              <FaCalendarAlt className="mr-2" />
              Por Edad
            </button>
            <button
              onClick={() => setSelectedComparison('aptitud')}
              className={`flex items-center px-4 py-2 rounded-lg transition-colors ${
                selectedComparison === 'aptitud'
                  ? 'bg-blue-500 text-white'
                  : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
              }`}
            >
              <FaBrain className="mr-2" />
              Por Aptitud
            </button>
          </div>
        </CardBody>
      </Card>

      {/* Gráfico Principal */}
      <Card>
        <CardHeader>
          <div className="flex items-center">
            <FaChartBar className="h-5 w-5 text-blue-500 mr-2" />
            <h3 className="text-lg font-semibold">
              Comparación {selectedComparison === 'genero' ? 'por Género' : 
                         selectedComparison === 'edad' ? 'por Edad' : 'por Aptitud'}
            </h3>
          </div>
        </CardHeader>
        <CardBody>
          <div className="h-96">
            <ResponsiveContainer width="100%" height="100%">
              {selectedComparison === 'aptitud' ? (
                <BarChart data={getChartData()}>
                  <CartesianGrid strokeDasharray="3 3" />
                  <XAxis dataKey="name" />
                  <YAxis />
                  <Tooltip 
                    formatter={(value, name) => [value, name === 'promedio' ? 'Promedio' : name]}
                    labelFormatter={(label) => {
                      const item = getChartData().find(d => d.name === label);
                      return item?.fullName || label;
                    }}
                  />
                  <Legend />
                  <Bar dataKey="promedio" fill="#3B82F6" name="Promedio" />
                </BarChart>
              ) : (
                <BarChart data={getChartData()}>
                  <CartesianGrid strokeDasharray="3 3" />
                  <XAxis dataKey="aptitud" />
                  <YAxis />
                  <Tooltip />
                  <Legend />
                  {selectedComparison === 'genero' && (
                    <>
                      <Bar dataKey="masculino" fill="#3B82F6" name="Masculino" />
                      <Bar dataKey="femenino" fill="#10B981" name="Femenino" />
                    </>
                  )}
                  {selectedComparison === 'edad' && (
                    <>
                      <Bar dataKey="15-19" fill="#3B82F6" name="15-19 años" />
                      <Bar dataKey="20-24" fill="#10B981" name="20-24 años" />
                      <Bar dataKey="25-29" fill="#F59E0B" name="25-29 años" />
                      <Bar dataKey="30+" fill="#EF4444" name="30+ años" />
                    </>
                  )}
                </BarChart>
              )}
            </ResponsiveContainer>
          </div>
        </CardBody>
      </Card>

      {/* Estadísticas Resumidas */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
        {selectedComparison === 'aptitud' ? (
          getChartData().map((aptitud) => (
            <Card key={aptitud.name}>
              <CardHeader className="pb-2">
                <h4 className="font-medium">{aptitud.fullName}</h4>
              </CardHeader>
              <CardBody className="pt-0">
                <div className="space-y-2">
                  <div className="flex justify-between">
                    <span className="text-sm text-gray-600">Promedio:</span>
                    <span className="font-semibold">{aptitud.promedio}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-sm text-gray-600">Evaluaciones:</span>
                    <span className="font-semibold">{aptitud.total}</span>
                  </div>
                  {aptitud.desviacion && (
                    <div className="flex justify-between">
                      <span className="text-sm text-gray-600">Desv. Est.:</span>
                      <span className="font-semibold">{aptitud.desviacion}</span>
                    </div>
                  )}
                </div>
              </CardBody>
            </Card>
          ))
        ) : (
          (selectedComparison === 'genero' ? dataToUse.porGenero : dataToUse.porEdad).map((categoria) => (
            <Card key={categoria.categoria}>
              <CardHeader className="pb-2">
                <h4 className="font-medium capitalize">{categoria.categoria}</h4>
              </CardHeader>
              <CardBody className="pt-0">
                <div className="space-y-1">
                  {categoria.datos?.slice(0, 3).map((aptitud) => (
                    <div key={aptitud.aptitud} className="flex justify-between text-sm">
                      <span className="text-gray-600">{aptitud.aptitud}:</span>
                      <span className="font-medium">{aptitud.promedio?.toFixed(1) || 'N/A'}</span>
                    </div>
                  ))}
                  {categoria.datos?.length > 3 && (
                    <div className="text-xs text-gray-500 text-center pt-1">
                      +{categoria.datos.length - 3} más
                    </div>
                  )}
                </div>
              </CardBody>
            </Card>
          ))
        )}
      </div>

      {/* Información de Estado */}
      <Card>
        <CardBody>
          <div className="text-center text-sm text-gray-600">
            <p>
              Mostrando comparación <strong>{selectedComparison === 'genero' ? 'por género' : 
                                            selectedComparison === 'edad' ? 'por grupos de edad' : 'por aptitudes'}</strong>
            </p>
            <p className="mt-1">
              Datos basados en evaluaciones completadas • Última actualización: {new Date().toLocaleString()}
            </p>
          </div>
        </CardBody>
      </Card>
    </div>
  );
};

export default ComparisonView;
