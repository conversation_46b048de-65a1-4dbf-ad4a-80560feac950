import React, { useState, useEffect, useMemo } from 'react';
import { FaUser, FaChartLine, FaTable, FaFileAlt, FaDownload, Fa<PERSON>rint, Fa<PERSON><PERSON>seye, Fa<PERSON><PERSON>er, FaSignature, FaCog } from 'react-icons/fa';
import { Card, CardHeader, CardBody } from '../../ui/Card';
import AptitudeProfileChart from '../charts/AptitudeProfileChart';
import RadarAptitudeChart from '../charts/RadarAptitudeChart';
import ComparativeBarChart from '../charts/ComparativeBarChart';
import AttentionStyleQuadrant from '../charts/AttentionStyleQuadrant';
import ScoresTable from '../tables/ScoresTable';
import { interpretacionesAptitudes, obtenerInterpretacion } from '../../../data/interpretacionesAptitudes';
import PDFExportService from '../../../services/PDFExportService';
import DigitalSignatureService from '../../../services/DigitalSignatureService';

/**
 * Vista Individual Mejorada del Dashboard BAT-7
 * Incluye todas las funcionalidades profesionales para análisis individual
 */
const EnhancedIndividualView = ({ data, loading, filters, onFiltersChange }) => {
  const [activeTab, setActiveTab] = useState('profile');
  const [selectedPatient, setSelectedPatient] = useState(null);
  const [isExporting, setIsExporting] = useState(false);
  const [exportProgress, setExportProgress] = useState('');

  // Extraer datos del paciente seleccionado
  const patientData = useMemo(() => {
    if (!data || data.type !== 'individual') return null;
    return data.patient;
  }, [data]);

  // Tabs disponibles
  const tabs = [
    { id: 'profile', label: 'Perfil', icon: FaUser },
    { id: 'scores', label: 'Puntuaciones', icon: FaTable },
    { id: 'line-chart', label: 'Gráfico Líneas', icon: FaChartLine },
    { id: 'radar-chart', label: 'Gráfico Radar', icon: FaSpider },
    { id: 'bar-chart', label: 'Gráfico Barras', icon: FaTable },
    { id: 'attention', label: 'Estilo Atencional', icon: FaBullseye },
    { id: 'interpretation', label: 'Interpretación', icon: FaFileAlt },
    { id: 'signature', label: 'Firma Digital', icon: FaSignature },
    { id: 'settings', label: 'Configuración', icon: FaCog }
  ];

  // Funciones de exportación
  const handleExportPDF = async (type = 'complete') => {
    if (!patientData) return;

    setIsExporting(true);
    setExportProgress('Preparando informe...');

    try {
      let result;
      
      switch (type) {
        case 'complete':
          setExportProgress('Generando informe completo...');
          result = await PDFExportService.exportElementAsPDF(
            'individual-report-content',
            `Informe_BAT7_${patientData.nombre}_${patientData.apellido}.pdf`
          );
          break;
          
        case 'scores':
          setExportProgress('Generando tabla de puntuaciones...');
          result = await PDFExportService.exportElementAsPDF(
            'scores-table-content',
            `Puntuaciones_BAT7_${patientData.nombre}_${patientData.apellido}.pdf`
          );
          break;
          
        case 'chart':
          setExportProgress('Exportando gráfico...');
          result = await PDFExportService.exportElementAsPDF(
            'chart-content',
            `Grafico_BAT7_${patientData.nombre}_${patientData.apellido}.pdf`
          );
          break;
          
        default:
          throw new Error('Tipo de exportación no válido');
      }

      if (result.success) {
        setExportProgress('¡Exportación completada!');
        setTimeout(() => {
          setIsExporting(false);
          setExportProgress('');
        }, 2000);
      }
    } catch (error) {
      console.error('Error exportando PDF:', error);
      setExportProgress('Error en la exportación');
      setTimeout(() => {
        setIsExporting(false);
        setExportProgress('');
      }, 3000);
    }
  };

  const handlePrint = () => {
    window.print();
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-500 mx-auto mb-4"></div>
          <p className="text-gray-600">Cargando datos del paciente...</p>
        </div>
      </div>
    );
  }

  if (!patientData) {
    return (
      <div className="text-center py-12">
        <div className="text-gray-400 text-6xl mb-4">👤</div>
        <h3 className="text-xl font-semibold text-gray-700 mb-2">Selecciona un Paciente</h3>
        <p className="text-gray-500">
          Usa los filtros para seleccionar un paciente específico y ver su informe individual.
        </p>
      </div>
    );
  }

  return (
    <div id="individual-report-content" className="space-y-6">
      {/* Header del informe */}
      <div className="bg-gradient-to-r from-blue-600 to-purple-600 text-white rounded-lg p-6 relative overflow-hidden">
        <div className="relative z-10">
          <div className="flex items-center justify-between">
            <div>
              <h1 className="text-2xl font-bold mb-2">
                Informe Individual BAT-7
              </h1>
              <p className="text-blue-100">
                {patientData.nombre} {patientData.apellido} - {patientData.documento}
              </p>
            </div>
            
            {/* Botones de acción */}
            <div className="flex space-x-2">
              {/* Dropdown de exportación */}
              <div className="relative group">
                <button 
                  className="bg-white bg-opacity-20 hover:bg-opacity-30 px-4 py-2 rounded-lg transition-colors flex items-center"
                  disabled={isExporting}
                >
                  {isExporting ? (
                    <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                  ) : (
                    <FaDownload className="mr-2" />
                  )}
                  {isExporting ? 'Exportando...' : 'Exportar PDF'}
                </button>
                
                {/* Dropdown menu */}
                <div className="absolute right-0 mt-2 w-48 bg-white rounded-lg shadow-lg border opacity-0 invisible group-hover:opacity-100 group-hover:visible transition-all duration-200 z-10">
                  <div className="py-1">
                    <button
                      onClick={() => handleExportPDF('complete')}
                      disabled={isExporting}
                      className="w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 flex items-center"
                    >
                      <FaFileAlt className="mr-2 text-blue-500" />
                      Informe completo
                    </button>
                    <button
                      onClick={() => handleExportPDF('scores')}
                      disabled={isExporting}
                      className="w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 flex items-center"
                    >
                      <FaTable className="mr-2 text-green-500" />
                      Solo puntuaciones
                    </button>
                    <button
                      onClick={() => handleExportPDF('chart')}
                      disabled={isExporting}
                      className="w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 flex items-center"
                    >
                      <FaChartLine className="mr-2 text-purple-500" />
                      Solo gráfico
                    </button>
                  </div>
                </div>
              </div>

              <button 
                onClick={handlePrint}
                className="bg-white bg-opacity-20 hover:bg-opacity-30 px-4 py-2 rounded-lg transition-colors flex items-center"
              >
                <FaPrint className="mr-2" />
                Imprimir
              </button>
            </div>
          </div>

          {/* Progress indicator */}
          {isExporting && exportProgress && (
            <div className="mt-4 bg-white bg-opacity-20 rounded-lg p-3">
              <div className="flex items-center text-sm">
                <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                {exportProgress}
              </div>
            </div>
          )}
        </div>

        {/* Decorative background */}
        <div className="absolute top-0 right-0 w-64 h-64 bg-white bg-opacity-10 rounded-full -mr-32 -mt-32"></div>
        <div className="absolute bottom-0 left-0 w-48 h-48 bg-white bg-opacity-5 rounded-full -ml-24 -mb-24"></div>
      </div>

      {/* Navigation tabs */}
      <div className="border-b border-gray-200">
        <nav className="flex space-x-8 overflow-x-auto">
          {tabs.map(tab => {
            const Icon = tab.icon;
            return (
              <button
                key={tab.id}
                onClick={() => setActiveTab(tab.id)}
                className={`flex items-center py-2 px-1 border-b-2 font-medium text-sm whitespace-nowrap ${
                  activeTab === tab.id
                    ? 'border-blue-500 text-blue-600'
                    : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                }`}
              >
                <Icon className="mr-2" />
                {tab.label}
              </button>
            );
          })}
        </nav>
      </div>

      {/* Tab content */}
      <div className="min-h-96">
        {activeTab === 'profile' && (
          <Card>
            <CardHeader>
              <h3 className="text-lg font-semibold">Información del Evaluado</h3>
            </CardHeader>
            <CardBody>
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                <div>
                  <h4 className="font-medium text-gray-700 mb-3">Datos Personales</h4>
                  <div className="space-y-2 text-sm">
                    <div><span className="font-medium">Nombre:</span> {patientData.nombre} {patientData.apellido}</div>
                    <div><span className="font-medium">Documento:</span> {patientData.documento}</div>
                    <div><span className="font-medium">Edad:</span> {patientData.edad} años</div>
                    <div><span className="font-medium">Género:</span> {patientData.genero}</div>
                  </div>
                </div>
                
                <div>
                  <h4 className="font-medium text-gray-700 mb-3">Información Académica</h4>
                  <div className="space-y-2 text-sm">
                    <div><span className="font-medium">Curso:</span> {patientData.curso || 'No especificado'}</div>
                    <div><span className="font-medium">Institución:</span> {patientData.institucion || 'No especificada'}</div>
                    <div><span className="font-medium">Nivel BAT-7:</span> {patientData.nivelBat7 || 'M'}</div>
                  </div>
                </div>
                
                <div>
                  <h4 className="font-medium text-gray-700 mb-3">Evaluación</h4>
                  <div className="space-y-2 text-sm">
                    <div><span className="font-medium">Fecha:</span> {patientData.fechaEvaluacion || 'No especificada'}</div>
                    <div><span className="font-medium">Psicólogo:</span> {patientData.psicologo || 'No asignado'}</div>
                    <div><span className="font-medium">Estado:</span> 
                      <span className="ml-1 px-2 py-1 bg-green-100 text-green-800 rounded-full text-xs">
                        Completada
                      </span>
                    </div>
                  </div>
                </div>
              </div>
            </CardBody>
          </Card>
        )}

        {activeTab === 'scores' && (
          <Card>
            <CardHeader>
              <h3 className="text-lg font-semibold">Tabla de Puntuaciones</h3>
            </CardHeader>
            <CardBody>
              <div id="scores-table-content">
                <ScoresTable 
                  data={patientData}
                  showInterpretation={true}
                  showPercentiles={true}
                />
              </div>
            </CardBody>
          </Card>
        )}

        {/* Resto de tabs... */}
        {activeTab === 'line-chart' && (
          <Card>
            <CardHeader>
              <h3 className="text-lg font-semibold">Perfil Aptitudinal - Gráfico de Líneas</h3>
            </CardHeader>
            <CardBody>
              <div id="chart-content">
                <AptitudeProfileChart 
                  data={patientData}
                  showPercentiles={true}
                  height={400}
                  interactive={true}
                />
              </div>
            </CardBody>
          </Card>
        )}

        {/* Agregar más tabs según necesidad */}
      </div>
    </div>
  );
};

export default EnhancedIndividualView;
