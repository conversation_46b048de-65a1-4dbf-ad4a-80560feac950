import React, { memo } from 'react';
import ExecutiveSummary from '../ExecutiveSummary';
import EstadisticasGeneralesEnhanced from './enhanced/EstadisticasGeneralesEnhanced';

/**
 * Vista Ejecutiva del Dashboard
 * Muestra resumen ejecutivo con insights y hallazgos clave.
 * Recibe los datos como props desde el componente Dashboard.
 */
const ExecutiveView = ({ loading, estadisticasGenerales, datosPerfilInstitucional, kpiData, alertsData, trendData, error }) => {

  if (loading) {
    return (
      <div className="space-y-6">
        <div className="bg-white rounded-lg shadow-lg p-8 animate-pulse">
          <div className="h-8 bg-gray-200 rounded mb-6 w-3/4"></div>
          <div className="space-y-4">
            <div className="h-4 bg-gray-200 rounded"></div>
            <div className="h-4 bg-gray-200 rounded w-5/6"></div>
            <div className="h-4 bg-gray-200 rounded w-1/2"></div>
          </div>
        </div>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {[...Array(3)].map((_, i) => (
            <div key={i} className="bg-white rounded-lg shadow-lg p-6 animate-pulse">
              <div className="h-6 bg-gray-200 rounded mb-4 w-1/2"></div>
              <div className="h-24 bg-gray-200 rounded"></div>
            </div>
          ))}
        </div>
      </div>
    );
  }

  if (error) {
    return <div className="text-red-500 text-center p-4 bg-red-50 rounded-lg">Error al cargar la vista ejecutiva: {error}</div>;
  }

  return (
    <div className="space-y-6">
      {/* Integración de EstadisticasGenerales */}
      <EstadisticasGeneralesEnhanced
        data={estadisticasGenerales}
        loading={loading}
        previousData={null}
      />
      {/* Pasamos los datos relevantes del contexto al componente de resumen */}
      <ExecutiveSummary
        kpis={kpiData}
        aptitudeData={datosPerfilInstitucional}
      />
    </div>
  );
};

export default memo(ExecutiveView);
