import React, { memo } from 'react';
import ExecutiveExport from '../ExecutiveExport';

/**
 * Vista de Exportación del Dashboard
 * Permite exportar reportes en múltiples formatos
 */
const ExportView = ({ loading, onExport, onExportComplete, estadisticasGenerales }) => {
  const handleExport = async (format, options) => {
    if (onExport) {
      await onExport(format, options);
    }
  };

  const handleExportComplete = (result) => {
    if (onExportComplete) {
      onExportComplete(result);
    }
  };

  return (
    <ExecutiveExport
      data={estadisticasGenerales}
      onExport={handleExport}
      onExportComplete={handleExportComplete}
      loading={loading}
    />
  );
};

export default memo(ExportView);
