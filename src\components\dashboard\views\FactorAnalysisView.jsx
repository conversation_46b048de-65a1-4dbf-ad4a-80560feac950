import React, { useContext, useMemo } from 'react';
import { DashboardContext } from '../../../context/DashboardContext';
import { FaProjectDiagram } from 'react-icons/fa';

const FACTOR_NAMES = {
  g: 'Factor General (g)',
  Gf: 'Inteligencia Fluida (Gf)',
  Gc: 'Inteligencia Cristalizada (Gc)',
};

const FactorChart = ({ data }) => {
  if (!data || data.length === 0) {
    return <div className="text-center text-gray-500 py-8">No hay datos de factores para mostrar.</div>;
  }
  
  const maxValue = Math.max(...data.map(item => item.value), 120);

  return (
    <div className="bg-white p-6 rounded-lg shadow">
      <h3 className="font-semibold mb-4">Puntajes Promedio de Factores Intelectuales</h3>
      <div className="space-y-4">
        {data.map(({ name, value }) => (
          <div key={name} className="flex items-center">
            <div className="w-48 text-sm font-medium text-gray-700">{name}</div>
            <div className="flex-grow bg-gray-200 rounded-full h-6 mr-4">
              <div
                className="bg-gradient-to-r from-purple-400 to-pink-500 h-6 rounded-full text-white text-xs flex items-center justify-end pr-2"
                style={{ width: `${(value / maxValue) * 100}%` }}
              >
                {value.toFixed(1)}
              </div>
            </div>
          </div>
        ))}
      </div>
    </div>
  );
};

const FactorAnalysisView = () => {
  const { loading, data, error } = useContext(DashboardContext);

  const chartData = useMemo(() => {
    if (!data?.factorData) return [];
    return Object.entries(data.factorData).map(([key, value]) => ({
      name: FACTOR_NAMES[key] || key,
      value: value,
    }));
  }, [data]);

  if (loading) return <div>Cargando datos de factores...</div>;
  if (error) return <div className="text-red-500">Error al cargar los datos: {error}</div>;

  return (
    <div className="space-y-6">
      <div className="bg-white p-6 rounded-lg shadow">
        <h2 className="text-2xl font-bold text-gray-800 flex items-center">
          <FaProjectDiagram className="mr-3 text-purple-500" />
          Análisis de Factores Intelectuales (g, Gf, Gc)
        </h2>
        <p className="text-gray-600 mt-1">
          Visualización de los puntajes promedio para los factores de inteligencia de alto nivel.
        </p>
      </div>
      
      <FactorChart data={chartData} />
    </div>
  );
};

export default FactorAnalysisView;