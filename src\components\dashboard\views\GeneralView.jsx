import React, { memo } from 'react';
import { Card, CardHeader, CardBody } from '../../ui/Card';
import { <PERSON><PERSON>hart } from '../../charts/BarChart';
import {
  FaUsers,
  FaUser<PERSON>heck,
  FaClipboardList,
  FaChartLine,
  FaCalendarAlt,
  FaClock,
  FaBrain
} from 'react-icons/fa';
import StudentsByLevel from './StudentsByLevel';

/**
 * Vista General del Dashboard
 * Muestra estadísticas principales y gráficos básicos
 */
const GeneralView = ({ loading, estadisticasGenerales, datosPerfilInstitucional, datosDistribucionNivel, data }) => {

  if (loading) {
    return (
      <div className="space-y-6">
        {/* Skeleton para cards */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {[1, 2, 3, 4, 5, 6].map((i) => (
            <div key={i} className="bg-white rounded-lg shadow-lg p-6 animate-pulse">
              <div className="h-4 bg-gray-200 rounded mb-4"></div>
              <div className="h-8 bg-gray-200 rounded mb-2"></div>
              <div className="h-3 bg-gray-200 rounded"></div>
            </div>
          ))}
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Cards de estadísticas principales */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {/* Total Pacientes */}
        <Card className="hover:shadow-lg transition-shadow duration-200">
          <CardHeader className="bg-gradient-to-r from-blue-500 to-blue-600 text-white">
            <h3 className="text-lg font-semibold flex items-center justify-center">
              <FaUsers className="mr-2" />
              Total Pacientes
            </h3>
          </CardHeader>
          <CardBody className="p-6 text-center">
            <div className="text-3xl font-bold text-blue-600 mb-2">
              {estadisticasGenerales?.total_pacientes || 0}
            </div>
            <p className="text-gray-600">Pacientes registrados</p>
          </CardBody>
        </Card>

        {/* Pacientes Evaluados */}
        <Card className="hover:shadow-lg transition-shadow duration-200">
          <CardHeader className="bg-gradient-to-r from-green-500 to-green-600 text-white">
            <h3 className="text-lg font-semibold flex items-center justify-center">
              <FaUserCheck className="mr-2" />
              Pacientes Evaluados
            </h3>
          </CardHeader>
          <CardBody className="p-6 text-center">
            <div className="text-3xl font-bold text-green-600 mb-2">
              {estadisticasGenerales?.pacientes_evaluados || 0}
            </div>
            <p className="text-gray-600">Con al menos 1 test</p>
          </CardBody>
        </Card>

        {/* Total Evaluaciones */}
        <Card className="hover:shadow-lg transition-shadow duration-200">
          <CardHeader className="bg-gradient-to-r from-purple-500 to-purple-600 text-white">
            <h3 className="text-lg font-semibold flex items-center justify-center">
              <FaClipboardList className="mr-2" />
              Total Evaluaciones
            </h3>
          </CardHeader>
          <CardBody className="p-6 text-center">
            <div className="text-3xl font-bold text-purple-600 mb-2">
              {estadisticasGenerales?.total_evaluaciones || 0}
            </div>
            <p className="text-gray-600">Tests completados</p>
          </CardBody>
        </Card>

        {/* Percentil Promedio */}
        <Card className="hover:shadow-lg transition-shadow duration-200">
          <CardHeader className="bg-gradient-to-r from-orange-500 to-orange-600 text-white">
            <h3 className="text-lg font-semibold flex items-center justify-center">
              <FaChartLine className="mr-2" />
              Percentil Promedio
            </h3>
          </CardHeader>
          <CardBody className="p-6 text-center">
            <div className="text-3xl font-bold text-orange-600 mb-2">
              {estadisticasGenerales?.percentil_promedio_general || 0}
            </div>
            <p className="text-gray-600">Rendimiento general</p>
          </CardBody>
        </Card>

        {/* Último Mes */}
        <Card className="hover:shadow-lg transition-shadow duration-200">
          <CardHeader className="bg-gradient-to-r from-indigo-500 to-indigo-600 text-white">
            <h3 className="text-lg font-semibold flex items-center justify-center">
              <FaCalendarAlt className="mr-2" />
              Último Mes
            </h3>
          </CardHeader>
          <CardBody className="p-6 text-center">
            <div className="text-3xl font-bold text-indigo-600 mb-2">
              {estadisticasGenerales?.evaluaciones_ultimo_mes || 0}
            </div>
            <p className="text-gray-600">Evaluaciones realizadas</p>
          </CardBody>
        </Card>

        {/* Última Semana */}
        <Card className="hover:shadow-lg transition-shadow duration-200">
          <CardHeader className="bg-gradient-to-r from-pink-500 to-pink-600 text-white">
            <h3 className="text-lg font-semibold flex items-center justify-center">
              <FaClock className="mr-2" />
              Última Semana
            </h3>
          </CardHeader>
          <CardBody className="p-6 text-center">
            <div className="text-3xl font-bold text-pink-600 mb-2">
              {estadisticasGenerales?.evaluaciones_ultima_semana || 0}
            </div>
            <p className="text-gray-600">Evaluaciones recientes</p>
          </CardBody>
        </Card>
      </div>

      {/* Gráficos principales */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Distribución por Nivel */}
        <StudentsByLevel
          data={{ estudiantesPorNivelData: datosDistribucionNivel || [] }}
          loading={loading}
          error={null}
        />

        {/* Perfil Institucional */}
        <Card>
          <CardHeader className="bg-gradient-to-r from-green-500 to-green-600 text-white">
            <h3 className="text-lg font-semibold flex items-center justify-center">
              <FaBrain className="mr-2" />
              Perfil de Aptitud Institucional
            </h3>
          </CardHeader>
          <CardBody className="p-6">
            <div className="h-80">
              {datosPerfilInstitucional ? (
                <BarChart
                  data={datosPerfilInstitucional}
                  options={{
                    responsive: true,
                    maintainAspectRatio: false,
                    scales: {
                      y: {
                        beginAtZero: true,
                        max: 100,
                        title: {
                          display: true,
                          text: 'Percentil'
                        }
                      }
                    },
                    plugins: {
                      legend: {
                        display: false
                      },
                      tooltip: {
                        callbacks: {
                          label: function(context) {
                            return `${context.label}: ${context.parsed.y} percentil`;
                          }
                        }
                      }
                    }
                  }}
                />
              ) : (
                <div className="flex items-center justify-center h-full text-gray-500">
                  <div className="text-center">
                    <i className="fas fa-chart-bar text-4xl mb-2"></i>
                    <p>Cargando datos...</p>
                  </div>
                </div>
              )}
            </div>
            <div className="mt-4 text-xs bg-green-50 p-3 rounded">
              <strong>Interpretación:</strong> Identifica las fortalezas y debilidades a nivel macro. Guía estrategias pedagógicas generales.
            </div>
          </CardBody>
        </Card>
      </div>
    </div>
  );
};

export default memo(GeneralView);
