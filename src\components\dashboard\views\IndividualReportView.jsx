import React, { memo, useState, useEffect } from 'react';
import { FaUser, FaChartLine, FaTable, FaFileAlt, FaDownload, FaPrint, <PERSON>a<PERSON>ye, <PERSON>a<PERSON><PERSON><PERSON>e, FaFilePdf, <PERSON>a<PERSON><PERSON>ner, FaCog, FaSignature, FaSpider } from 'react-icons/fa';
import { Card, CardHeader, CardBody } from '../../ui/Card';
import AptitudeProfileChart from '../charts/AptitudeProfileChart';
import RadarAptitudeChart from '../charts/RadarAptitudeChart';
import ComparativeBarChart from '../charts/ComparativeBarChart';
import ScoresTable from '../tables/ScoresTable';
import AttentionStyleQuadrant from '../charts/AttentionStyleQuadrant';
import { interpretacionesAptitudes, obtenerInterpretacion } from '../../../data/interpretacionesAptitudes';
import PDFExportService from '../../../services/PDFExportService';
import InterpretationCustomizationService from '../../../services/InterpretationCustomizationService';
import DigitalSignatureService from '../../../services/DigitalSignatureService';
import ReportTemplateService from '../../../services/ReportTemplateService';
import supabase from '../../../api/supabaseClient';

/**
 * Vista de Informe Individual del Dashboard
 * Se activa cuando se selecciona un paciente específico en los filtros
 * Muestra análisis detallado de un único evaluado
 */
const IndividualReportView = ({ loading, data, filters }) => {
  const [selectedPatient, setSelectedPatient] = useState(null);
  const [reportData, setReportData] = useState(null);
  const [activeTab, setActiveTab] = useState('profile');
  const [isExporting, setIsExporting] = useState(false);
  const [exportProgress, setExportProgress] = useState('');

  // Detectar cuando se selecciona un paciente específico
  useEffect(() => {
    if (filters?.pacienteEspecifico) {
      // Simular carga de datos del paciente específico
      loadPatientData(filters.pacienteEspecifico);
    } else {
      setSelectedPatient(null);
      setReportData(null);
    }
  }, [filters?.pacienteEspecifico]);

  const loadPatientData = async (patientId) => {
    try {
      // Aquí iría la llamada real a la API para obtener datos del paciente
      // Por ahora usamos datos simulados
      const mockPatientData = {
        id: patientId,
        nombre: 'María González',
        apellido: 'Rodríguez',
        documento: '12345678',
        edad: 16,
        genero: 'Femenino',
        curso: '2-secundaria',
        institucion: 'Colegio San José',
        fechaEvaluacion: '2024-01-15',
        psicologo: 'Dr. Juan Pérez',
        nivelBat7: 'M',
        puntuaciones: {
          V: { pd: 45, pc: 78, nivel: 'Alto' },
          E: { pd: 38, pc: 65, nivel: 'Medio' },
          A: { pd: 52, pc: 85, nivel: 'Alto' },
          R: { pd: 41, pc: 72, nivel: 'Medio-Alto' },
          N: { pd: 35, pc: 58, nivel: 'Medio' },
          M: { pd: 29, pc: 45, nivel: 'Medio-Bajo' },
          O: { pd: 48, pc: 82, nivel: 'Alto' }
        },
        factores: {
          g: { pc: 73, nivel: 'Medio-Alto' },
          Gf: { pc: 69, nivel: 'Medio' },
          Gc: { pc: 77, nivel: 'Alto' }
        },
        estiloAtencional: {
          cuadrante: 'Focalizado-Sostenido',
          descripcion: 'Capacidad para mantener la atención en tareas específicas'
        }
      };

      setSelectedPatient(mockPatientData);
      setReportData(mockPatientData);
    } catch (error) {
      console.error('Error cargando datos del paciente:', error);
    }
  };

  // Funciones de exportación
  const handleExportPDF = async (type = 'complete') => {
    if (!reportData) return;

    setIsExporting(true);
    setExportProgress('Preparando informe...');

    try {
      let result;

      switch (type) {
        case 'complete':
          setExportProgress('Generando informe completo...');
          result = await PDFExportService.exportIndividualReport(reportData, {
            includeCharts: true,
            includeInterpretations: true,
            includeRecommendations: true
          });
          break;

        case 'scores':
          setExportProgress('Generando tabla de puntuaciones...');
          result = await PDFExportService.exportScoresOnly(reportData);
          break;

        case 'chart':
          setExportProgress('Exportando gráfico...');
          result = await PDFExportService.exportFromHTML(
            'aptitude-chart',
            `Grafico_BAT7_${reportData.nombre}_${reportData.apellido}.pdf`
          );
          break;

        default:
          throw new Error('Tipo de exportación no válido');
      }

      if (result.success) {
        setExportProgress('¡Exportación completada!');
        setTimeout(() => {
          setIsExporting(false);
          setExportProgress('');
        }, 2000);
      }
    } catch (error) {
      console.error('Error exportando PDF:', error);
      setExportProgress('Error en la exportación');
      setTimeout(() => {
        setIsExporting(false);
        setExportProgress('');
      }, 3000);
    }
  };

  const handlePrint = () => {
    window.print();
  };

  const tabs = [
    { id: 'profile', label: 'Perfil', icon: FaUser },
    { id: 'scores', label: 'Puntuaciones', icon: FaTable },
    { id: 'chart', label: 'Gráfico Líneas', icon: FaChartLine },
    { id: 'radar', label: 'Gráfico Radar', icon: FaSpider },
    { id: 'bars', label: 'Gráfico Barras', icon: FaTable },
    { id: 'attention', label: 'Estilo Atencional', icon: FaBullseye },
    { id: 'interpretation', label: 'Interpretación', icon: FaFileAlt },
    { id: 'templates', label: 'Plantillas', icon: FaCog },
    { id: 'signature', label: 'Firma Digital', icon: FaSignature }
  ];

  const getNivelColor = (nivel) => {
    const colors = {
      'Muy Alto': 'text-green-700 bg-green-100',
      'Alto': 'text-green-600 bg-green-50',
      'Medio-Alto': 'text-blue-600 bg-blue-50',
      'Medio': 'text-yellow-600 bg-yellow-50',
      'Medio-Bajo': 'text-orange-600 bg-orange-50',
      'Bajo': 'text-red-600 bg-red-50',
      'Muy Bajo': 'text-red-700 bg-red-100'
    };
    return colors[nivel] || 'text-gray-600 bg-gray-50';
  };

  if (loading) {
    return (
      <div className="space-y-6">
        <div className="bg-white rounded-lg shadow-lg p-8 animate-pulse">
          <div className="h-8 bg-gray-200 rounded mb-6 w-3/4"></div>
          <div className="space-y-4">
            <div className="h-4 bg-gray-200 rounded"></div>
            <div className="h-4 bg-gray-200 rounded w-5/6"></div>
            <div className="h-4 bg-gray-200 rounded w-1/2"></div>
          </div>
        </div>
      </div>
    );
  }

  if (!filters?.pacienteEspecifico) {
    return (
      <Card>
        <CardBody className="text-center py-12">
          <FaUser className="mx-auto text-6xl text-gray-300 mb-4" />
          <h3 className="text-xl font-semibold text-gray-600 mb-2">
            Análisis Individual
          </h3>
          <p className="text-gray-500 mb-6">
            Selecciona un paciente específico en los filtros para ver su informe individual detallado.
          </p>
          <div className="bg-blue-50 border border-blue-200 rounded-lg p-4 max-w-md mx-auto">
            <p className="text-blue-700 text-sm">
              💡 <strong>Tip:</strong> Usa el filtro "Paciente específico" para activar esta vista
            </p>
          </div>
        </CardBody>
      </Card>
    );
  }

  if (!reportData) {
    return (
      <Card>
        <CardBody className="text-center py-8">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-500 mx-auto mb-4"></div>
          <p className="text-gray-600">Cargando datos del paciente...</p>
        </CardBody>
      </Card>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header del paciente */}
      <Card>
        <CardHeader className="bg-gradient-to-r from-blue-500 to-blue-600 text-white">
          <div className="flex items-center justify-between">
            <div className="flex items-center">
              <FaUser className="text-2xl mr-3" />
              <div>
                <h2 className="text-xl font-bold">
                  {reportData.nombre} {reportData.apellido}
                </h2>
                <p className="text-blue-100">
                  {reportData.documento} • {reportData.edad} años • {reportData.genero}
                </p>
              </div>
            </div>
            <div className="flex space-x-2">
              {/* Dropdown de exportación */}
              <div className="relative group">
                <button
                  className="bg-white bg-opacity-20 hover:bg-opacity-30 px-3 py-2 rounded-lg transition-colors flex items-center"
                  disabled={isExporting}
                >
                  {isExporting ? (
                    <FaSpinner className="mr-2 animate-spin" />
                  ) : (
                    <FaFilePdf className="mr-2" />
                  )}
                  {isExporting ? 'Exportando...' : 'Exportar PDF'}
                </button>

                {/* Dropdown menu */}
                <div className="absolute right-0 mt-2 w-48 bg-white rounded-lg shadow-lg border opacity-0 invisible group-hover:opacity-100 group-hover:visible transition-all duration-200 z-10">
                  <div className="py-1">
                    <button
                      onClick={() => handleExportPDF('complete')}
                      disabled={isExporting}
                      className="w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 flex items-center"
                    >
                      <FaFileAlt className="mr-2 text-blue-500" />
                      Informe completo
                    </button>
                    <button
                      onClick={() => handleExportPDF('scores')}
                      disabled={isExporting}
                      className="w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 flex items-center"
                    >
                      <FaTable className="mr-2 text-green-500" />
                      Solo puntuaciones
                    </button>
                    <button
                      onClick={() => handleExportPDF('chart')}
                      disabled={isExporting}
                      className="w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 flex items-center"
                    >
                      <FaChartLine className="mr-2 text-purple-500" />
                      Solo gráfico
                    </button>
                  </div>
                </div>
              </div>

              <button
                onClick={handlePrint}
                className="bg-white bg-opacity-20 hover:bg-opacity-30 px-3 py-2 rounded-lg transition-colors flex items-center"
              >
                <FaPrint className="mr-2" />
                Imprimir
              </button>
            </div>

            {/* Progress indicator */}
            {isExporting && exportProgress && (
              <div className="absolute top-full left-0 right-0 mt-2 bg-white bg-opacity-90 rounded-lg p-2">
                <div className="flex items-center text-sm text-blue-700">
                  <FaSpinner className="mr-2 animate-spin" />
                  {exportProgress}
                </div>
              </div>
            )}
          </div>
        </CardHeader>
        <CardBody>
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
            <div>
              <span className="text-gray-500">Institución:</span>
              <p className="font-medium">{reportData.institucion}</p>
            </div>
            <div>
              <span className="text-gray-500">Curso:</span>
              <p className="font-medium">{reportData.curso}</p>
            </div>
            <div>
              <span className="text-gray-500">Fecha evaluación:</span>
              <p className="font-medium">{reportData.fechaEvaluacion}</p>
            </div>
            <div>
              <span className="text-gray-500">Psicólogo:</span>
              <p className="font-medium">{reportData.psicologo}</p>
            </div>
          </div>
        </CardBody>
      </Card>

      {/* Tabs de navegación */}
      <div className="border-b border-gray-200">
        <nav className="-mb-px flex space-x-8">
          {tabs.map(tab => {
            const Icon = tab.icon;
            return (
              <button
                key={tab.id}
                onClick={() => setActiveTab(tab.id)}
                className={`py-2 px-1 border-b-2 font-medium text-sm flex items-center ${
                  activeTab === tab.id
                    ? 'border-blue-500 text-blue-600'
                    : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                }`}
              >
                <Icon className="mr-2" />
                {tab.label}
              </button>
            );
          })}
        </nav>
      </div>

      {/* Contenido de las tabs */}
      {activeTab === 'profile' && (
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          <Card>
            <CardHeader>
              <h3 className="text-lg font-semibold">Información Personal</h3>
            </CardHeader>
            <CardBody>
              <dl className="space-y-3">
                <div className="flex justify-between">
                  <dt className="text-gray-500">Nombre completo:</dt>
                  <dd className="font-medium">{reportData.nombre} {reportData.apellido}</dd>
                </div>
                <div className="flex justify-between">
                  <dt className="text-gray-500">Documento:</dt>
                  <dd className="font-medium">{reportData.documento}</dd>
                </div>
                <div className="flex justify-between">
                  <dt className="text-gray-500">Edad:</dt>
                  <dd className="font-medium">{reportData.edad} años</dd>
                </div>
                <div className="flex justify-between">
                  <dt className="text-gray-500">Género:</dt>
                  <dd className="font-medium">{reportData.genero}</dd>
                </div>
                <div className="flex justify-between">
                  <dt className="text-gray-500">Nivel BAT-7:</dt>
                  <dd className="font-medium">
                    <span className="bg-blue-100 text-blue-800 px-2 py-1 rounded-full text-sm">
                      {reportData.nivelBat7}
                    </span>
                  </dd>
                </div>
              </dl>
            </CardBody>
          </Card>

          <Card>
            <CardHeader>
              <h3 className="text-lg font-semibold">Resumen de Resultados</h3>
            </CardHeader>
            <CardBody>
              <div className="space-y-4">
                <div>
                  <h4 className="font-medium text-gray-700 mb-2">Factor General (g)</h4>
                  <div className="flex items-center justify-between">
                    <span className="text-2xl font-bold text-blue-600">
                      Pc {reportData.factores.g.pc}
                    </span>
                    <span className={`px-3 py-1 rounded-full text-sm font-medium ${getNivelColor(reportData.factores.g.nivel)}`}>
                      {reportData.factores.g.nivel}
                    </span>
                  </div>
                </div>
                
                <div className="border-t pt-4">
                  <h4 className="font-medium text-gray-700 mb-2">Estilo Atencional</h4>
                  <p className="text-sm text-gray-600 mb-1">{reportData.estiloAtencional.cuadrante}</p>
                  <p className="text-xs text-gray-500">{reportData.estiloAtencional.descripcion}</p>
                </div>
              </div>
            </CardBody>
          </Card>
        </div>
      )}

      {activeTab === 'scores' && (
        <Card>
          <CardHeader>
            <h3 className="text-lg font-semibold">Tabla de Puntuaciones</h3>
          </CardHeader>
          <CardBody>
            <ScoresTable
              data={reportData}
              showInterpretations={true}
              compact={false}
            />
          </CardBody>
        </Card>
      )}

      {activeTab === 'chart' && (
        <Card>
          <CardHeader>
            <h3 className="text-lg font-semibold">Perfil Aptitudinal</h3>
          </CardHeader>
          <CardBody>
            <div id="aptitude-chart">
              <AptitudeProfileChart
                data={reportData}
                showPercentiles={true}
                showDirectScores={false}
                height={400}
                interactive={true}
              />
            </div>
          </CardBody>
        </Card>
      )}

      {activeTab === 'radar' && (
        <Card>
          <CardHeader>
            <h3 className="text-lg font-semibold">Perfil Aptitudinal - Vista Radar</h3>
          </CardHeader>
          <CardBody>
            <RadarAptitudeChart
              data={reportData}
              showComparison={false}
              height={400}
              interactive={true}
            />
          </CardBody>
        </Card>
      )}

      {activeTab === 'bars' && (
        <Card>
          <CardHeader>
            <h3 className="text-lg font-semibold">Perfil Aptitudinal - Gráfico de Barras</h3>
          </CardHeader>
          <CardBody>
            <ComparativeBarChart
              data={reportData}
              showNorms={true}
              orientation="vertical"
              height={400}
              interactive={true}
            />
          </CardBody>
        </Card>
      )}

      {activeTab === 'attention' && (
        <Card>
          <CardHeader>
            <h3 className="text-lg font-semibold">Análisis de Estilo Atencional</h3>
          </CardHeader>
          <CardBody>
            <AttentionStyleQuadrant
              data={reportData}
              height={400}
              interactive={true}
            />
          </CardBody>
        </Card>
      )}

      {activeTab === 'interpretation' && (
        <Card>
          <CardHeader>
            <h3 className="text-lg font-semibold">Interpretación Cualitativa</h3>
          </CardHeader>
          <CardBody>
            <div className="space-y-6">
              {/* Interpretaciones por aptitud */}
              {Object.entries(reportData.puntuaciones).map(([aptitud, scores]) => {
                const interpretacion = obtenerInterpretacion(aptitud, scores.pc);
                if (!interpretacion) return null;

                const isHigh = scores.pc >= 70;
                const isMedium = scores.pc >= 30 && scores.pc < 70;
                const isLow = scores.pc < 30;

                return (
                  <div key={aptitud} className={`border rounded-lg p-4 ${
                    isHigh ? 'bg-green-50 border-green-200' :
                    isMedium ? 'bg-blue-50 border-blue-200' :
                    'bg-orange-50 border-orange-200'
                  }`}>
                    <div className="flex items-center justify-between mb-3">
                      <h4 className={`font-semibold ${
                        isHigh ? 'text-green-800' :
                        isMedium ? 'text-blue-800' :
                        'text-orange-800'
                      }`}>
                        {aptitud} - {interpretacionesAptitudes[aptitud]?.descripcion?.split('.')[0] || 'Aptitud'}
                      </h4>
                      <span className={`px-3 py-1 rounded-full text-sm font-medium ${
                        isHigh ? 'bg-green-100 text-green-700' :
                        isMedium ? 'bg-blue-100 text-blue-700' :
                        'bg-orange-100 text-orange-700'
                      }`}>
                        Pc {scores.pc} - {interpretacion.nivel}
                      </span>
                    </div>

                    <p className={`text-sm mb-3 ${
                      isHigh ? 'text-green-700' :
                      isMedium ? 'text-blue-700' :
                      'text-orange-700'
                    }`}>
                      {interpretacion.descripcion}
                    </p>

                    <div className="space-y-2">
                      <h5 className={`text-sm font-medium ${
                        isHigh ? 'text-green-800' :
                        isMedium ? 'text-blue-800' :
                        'text-orange-800'
                      }`}>
                        {isHigh ? 'Fortalezas identificadas:' :
                         isMedium ? 'Características observadas:' :
                         'Áreas que requieren atención:'}
                      </h5>
                      <ul className={`text-sm space-y-1 ${
                        isHigh ? 'text-green-700' :
                        isMedium ? 'text-blue-700' :
                        'text-orange-700'
                      }`}>
                        {interpretacion.caracteristicas.slice(0, 3).map((caracteristica, index) => (
                          <li key={index} className="flex items-start">
                            <span className="mr-2">•</span>
                            <span>{caracteristica}</span>
                          </li>
                        ))}
                      </ul>
                    </div>
                  </div>
                );
              })}

              {/* Resumen general */}
              <div className="bg-gray-50 border border-gray-200 rounded-lg p-4">
                <h4 className="font-semibold text-gray-800 mb-3">Resumen General</h4>
                <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                  <div className="text-center">
                    <div className="text-2xl font-bold text-green-600">
                      {Object.values(reportData.puntuaciones).filter(s => s.pc >= 70).length}
                    </div>
                    <div className="text-sm text-gray-600">Fortalezas</div>
                  </div>
                  <div className="text-center">
                    <div className="text-2xl font-bold text-blue-600">
                      {Object.values(reportData.puntuaciones).filter(s => s.pc >= 30 && s.pc < 70).length}
                    </div>
                    <div className="text-sm text-gray-600">Promedio</div>
                  </div>
                  <div className="text-center">
                    <div className="text-2xl font-bold text-orange-600">
                      {Object.values(reportData.puntuaciones).filter(s => s.pc < 30).length}
                    </div>
                    <div className="text-sm text-gray-600">A desarrollar</div>
                  </div>
                </div>
              </div>

              {/* Recomendaciones generales */}
              <div className="bg-purple-50 border border-purple-200 rounded-lg p-4">
                <h4 className="font-semibold text-purple-800 mb-3">Recomendaciones Generales</h4>
                <div className="space-y-2 text-sm text-purple-700">
                  {Object.entries(reportData.puntuaciones).filter(([_, scores]) => scores.pc >= 70).length > 0 && (
                    <p>• <strong>Potenciar fortalezas:</strong> Continuar desarrollando las aptitudes donde muestra alto rendimiento.</p>
                  )}
                  {Object.entries(reportData.puntuaciones).filter(([_, scores]) => scores.pc < 30).length > 0 && (
                    <p>• <strong>Intervención específica:</strong> Implementar estrategias de apoyo en las áreas que requieren desarrollo.</p>
                  )}
                  <p>• <strong>Seguimiento:</strong> Realizar evaluaciones periódicas para monitorear el progreso.</p>
                  <p>• <strong>Enfoque integral:</strong> Considerar el perfil completo al diseñar intervenciones educativas.</p>
                </div>
              </div>
            </div>
          </CardBody>
        </Card>
      )}

      {activeTab === 'templates' && (
        <Card>
          <CardHeader>
            <h3 className="text-lg font-semibold">Gestión de Plantillas</h3>
          </CardHeader>
          <CardBody>
            <div className="space-y-6">
              {/* Selector de plantilla */}
              <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
                <h4 className="font-semibold text-blue-800 mb-3">Plantillas Disponibles</h4>
                <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                  {ReportTemplateService.listTemplates().map(template => (
                    <div key={template.id} className="bg-white border rounded-lg p-3 hover:shadow-md transition-shadow cursor-pointer">
                      <h5 className="font-medium text-gray-800">{template.name}</h5>
                      <p className="text-sm text-gray-600 mt-1">{template.description}</p>
                      <div className="text-xs text-gray-500 mt-2">
                        {template.sectionsCount} secciones
                      </div>
                    </div>
                  ))}
                </div>
              </div>

              {/* Personalización de interpretaciones */}
              <div className="bg-green-50 border border-green-200 rounded-lg p-4">
                <h4 className="font-semibold text-green-800 mb-3">Interpretaciones Personalizadas</h4>
                <div className="space-y-3">
                  <div className="flex items-center justify-between">
                    <span className="text-sm text-green-700">Contexto institucional:</span>
                    <span className="text-sm font-medium text-green-800">Activado</span>
                  </div>
                  <div className="flex items-center justify-between">
                    <span className="text-sm text-green-700">Ajustes por edad:</span>
                    <span className="text-sm font-medium text-green-800">Adolescente</span>
                  </div>
                  <div className="flex items-center justify-between">
                    <span className="text-sm text-green-700">Enfoque:</span>
                    <span className="text-sm font-medium text-green-800">Educativo</span>
                  </div>
                </div>
              </div>

              {/* Vista previa de secciones */}
              <div className="bg-gray-50 border border-gray-200 rounded-lg p-4">
                <h4 className="font-semibold text-gray-800 mb-3">Secciones del Informe</h4>
                <div className="space-y-2">
                  {['Encabezado', 'Datos del Evaluado', 'Puntuaciones', 'Gráficos', 'Interpretación', 'Recomendaciones', 'Firma'].map((section, index) => (
                    <div key={section} className="flex items-center justify-between py-2 px-3 bg-white rounded border">
                      <span className="text-sm font-medium text-gray-700">{section}</span>
                      <div className="flex items-center space-x-2">
                        <span className="text-xs text-green-600">✓ Incluida</span>
                        <button className="text-xs text-blue-600 hover:text-blue-800">Personalizar</button>
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            </div>
          </CardBody>
        </Card>
      )}

      {activeTab === 'signature' && (
        <Card>
          <CardHeader>
            <h3 className="text-lg font-semibold">Firma Digital del Informe</h3>
          </CardHeader>
          <CardBody>
            <div className="space-y-6">
              {/* Estado de la firma */}
              <div className="bg-green-50 border border-green-200 rounded-lg p-4">
                <div className="flex items-center justify-between">
                  <div>
                    <h4 className="font-semibold text-green-800">Informe Firmado Digitalmente</h4>
                    <p className="text-sm text-green-700 mt-1">
                      Firmado por: Dr. Juan Pérez (Lic. PSI-2024-001)
                    </p>
                    <p className="text-xs text-green-600 mt-1">
                      Fecha: {new Date().toLocaleString('es-ES')}
                    </p>
                  </div>
                  <div className="text-green-600">
                    <FaSignature className="text-2xl" />
                  </div>
                </div>
              </div>

              {/* Información del certificado */}
              <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
                <h4 className="font-semibold text-blue-800 mb-3">Información del Certificado</h4>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
                  <div>
                    <span className="text-blue-700 font-medium">Psicólogo:</span>
                    <p className="text-blue-800">Dr. Juan Pérez</p>
                  </div>
                  <div>
                    <span className="text-blue-700 font-medium">Licencia:</span>
                    <p className="text-blue-800">PSI-2024-001</p>
                  </div>
                  <div>
                    <span className="text-blue-700 font-medium">Institución:</span>
                    <p className="text-blue-800">Colegio de Psicólogos</p>
                  </div>
                  <div>
                    <span className="text-blue-700 font-medium">Validez:</span>
                    <p className="text-blue-800">Hasta 31/12/2026</p>
                  </div>
                </div>
              </div>

              {/* Validación de integridad */}
              <div className="bg-purple-50 border border-purple-200 rounded-lg p-4">
                <h4 className="font-semibold text-purple-800 mb-3">Validación de Integridad</h4>
                <div className="space-y-2">
                  <div className="flex items-center justify-between">
                    <span className="text-sm text-purple-700">Hash del documento:</span>
                    <span className="text-xs font-mono text-purple-800 bg-purple-100 px-2 py-1 rounded">
                      SHA256: a1b2c3d4...
                    </span>
                  </div>
                  <div className="flex items-center justify-between">
                    <span className="text-sm text-purple-700">Timestamp:</span>
                    <span className="text-sm text-purple-800">Verificado ✓</span>
                  </div>
                  <div className="flex items-center justify-between">
                    <span className="text-sm text-purple-700">Certificado:</span>
                    <span className="text-sm text-purple-800">Válido ✓</span>
                  </div>
                </div>
              </div>

              {/* Código QR de validación */}
              <div className="bg-gray-50 border border-gray-200 rounded-lg p-4 text-center">
                <h4 className="font-semibold text-gray-800 mb-3">Código de Validación</h4>
                <div className="inline-block bg-white p-4 rounded-lg border">
                  <div className="w-24 h-24 bg-gray-200 rounded flex items-center justify-center">
                    <span className="text-xs text-gray-500">QR Code</span>
                  </div>
                </div>
                <p className="text-xs text-gray-600 mt-2">
                  Escanea para validar la autenticidad del informe
                </p>
                <p className="text-xs text-blue-600 mt-1">
                  bat7.verify.com/signature/abc123
                </p>
              </div>
            </div>
          </CardBody>
        </Card>
      )}
    </div>
  );
};

export default memo(IndividualReportView);
