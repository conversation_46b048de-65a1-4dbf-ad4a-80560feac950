import React, { memo } from 'react';
import KPICard from '../KPICard';
import AlertSystem from '../AlertSystem';
import { FaTachometerAlt } from 'react-icons/fa';

/**
 * Vista de KPIs Críticos del Dashboard
 * Muestra indicadores clave de rendimiento y sistema de alertas
 */
const KpisView = ({ loading, kpiData, alertsData }) => {
  console.log('🎯 [KpisView] Datos recibidos:', { loading, kpiData, alertsData });

  if (loading) {
    return (
      <div className="space-y-6">
        {/* Skeleton para KPIs */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {[1, 2, 3, 4, 5, 6].map((i) => (
            <div key={i} className="bg-white rounded-lg shadow-lg p-6 animate-pulse">
              <div className="h-4 bg-gray-200 rounded mb-4"></div>
              <div className="h-8 bg-gray-200 rounded mb-2"></div>
              <div className="h-3 bg-gray-200 rounded"></div>
            </div>
          ))}
        </div>
        
        {/* Skeleton para alertas */}
        <div className="bg-white rounded-lg shadow-lg p-6 animate-pulse">
          <div className="h-6 bg-gray-200 rounded mb-4"></div>
          <div className="space-y-3">
            {[1, 2, 3].map((i) => (
              <div key={i} className="h-16 bg-gray-200 rounded"></div>
            ))}
          </div>
        </div>
      </div>
    );
  }

  // KPIs por defecto si no hay datos
  const defaultKPIs = {
    rendimiento_general: {
      title: 'Rendimiento General',
      current: 72.5,
      previous: 68.2,
      target: 75,
      unit: '%',
      format: 'percentage'
    },
    tasa_completitud: {
      title: 'Tasa de Completitud',
      current: 94.8,
      previous: 91.3,
      target: 95,
      unit: '%',
      format: 'percentage'
    },
    tiempo_promedio: {
      title: 'Tiempo Promedio por Test',
      current: 18.5,
      previous: 20.1,
      target: 20,
      unit: 'min',
      format: 'time'
    },
    participacion_activa: {
      title: 'Participación Activa',
      current: 87.2,
      previous: 84.6,
      target: 90,
      unit: '%',
      format: 'percentage'
    },
    mejora_mensual: {
      title: 'Mejora Mensual',
      current: 4.3,
      previous: 2.8,
      target: 5,
      unit: '%',
      format: 'percentage'
    },
    satisfaccion_usuarios: {
      title: 'Satisfacción de Usuarios',
      current: 4.6,
      previous: 4.4,
      target: 4.5,
      unit: '/5',
      format: 'rating'
    }
  };

  // Adaptar datos reales al formato esperado por KPICard
  const adaptedKPIs = kpiData ? [
    {
      key: 'averageScore',
      title: 'Percentil Promedio',
      value: kpiData.averageScore || 0,
      previousValue: (kpiData.averageScore || 0) - 2.3, // Simulamos valor anterior
      target: 75,
      unit: '%',
      format: 'percentage',
      color: 'blue'
    },
    {
      key: 'completionRate',
      title: 'Tasa de Completitud',
      value: kpiData.completionRate || 0,
      previousValue: (kpiData.completionRate || 0) - 1.2,
      target: 95,
      unit: '%',
      format: 'percentage',
      color: 'green'
    },
    {
      key: 'topAptitude',
      title: 'Mejor Aptitud',
      value: kpiData.topAptitude?.score || 0,
      previousValue: (kpiData.topAptitude?.score || 0) - 1.5,
      target: 80,
      unit: '%',
      format: 'percentage',
      color: 'amber',
      subtitle: kpiData.topAptitude?.name || 'N/A'
    },
    {
      key: 'bottomAptitude',
      title: 'Aptitud a Mejorar',
      value: kpiData.bottomAptitude?.score || 0,
      previousValue: (kpiData.bottomAptitude?.score || 0) - 0.8,
      target: 70,
      unit: '%',
      format: 'percentage',
      color: 'red',
      subtitle: kpiData.bottomAptitude?.name || 'N/A'
    }
  ] : Object.entries(defaultKPIs).map(([key, kpi]) => ({
    key,
    title: kpi.title,
    value: kpi.current,
    previousValue: kpi.previous,
    target: kpi.target,
    unit: kpi.unit,
    format: kpi.format,
    color: 'blue'
  }));

  return (
    <div className="space-y-6">
      {/* KPIs Críticos */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {adaptedKPIs.map((kpi) => (
          <KPICard
            key={kpi.key}
            title={kpi.title}
            value={kpi.value}
            previousValue={kpi.previousValue}
            target={kpi.target}
            unit={kpi.unit}
            icon={FaTachometerAlt}
            color={kpi.color}
            format={kpi.format}
            subtitle={kpi.subtitle}
          />
        ))}
      </div>
      
      {/* Sistema de Alertas */}
      <AlertSystem alerts={alertsData} />

      {/* Interpretación de KPIs */}
      <div className="bg-gradient-to-r from-blue-50 to-indigo-50 rounded-lg p-6 border-l-4 border-blue-500">
        <h3 className="text-lg font-semibold text-blue-800 mb-3 flex items-center">
          <FaTachometerAlt className="mr-2" />
          Interpretación de KPIs
        </h3>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm text-blue-700">
          <div>
            <h4 className="font-medium mb-2">🎯 Rendimiento General</h4>
            <p>Percentil promedio institucional. Meta: ≥75%. Actual: {kpiData?.averageScore || 72.5}%</p>
          </div>
          <div>
            <h4 className="font-medium mb-2">✅ Tasa de Completitud</h4>
            <p>% de tests iniciados que se completan. Meta: ≥95%. Actual: {kpiData?.completionRate || 94.8}%</p>
          </div>
          <div>
            <h4 className="font-medium mb-2">⏱️ Tiempo Promedio</h4>
            <p>Tiempo promedio por test. Meta: ≤20 min. Actual: {kpisToShow.tiempo_promedio?.current || 18.5} min</p>
          </div>
          <div>
            <h4 className="font-medium mb-2">👥 Participación Activa</h4>
            <p>% de estudiantes que han completado tests en el último mes. Meta: ≥90%. Actual: {kpisToShow.participacion_activa?.current || 87.2}%</p>
          </div>
          <div>
            <h4 className="font-medium mb-2">📈 Mejora Mensual</h4>
            <p>% de mejora en rendimiento vs mes anterior. Meta: ≥5%. Actual: {kpisToShow.mejora_mensual?.current || 4.3}%</p>
          </div>
          <div>
            <h4 className="font-medium mb-2">⭐ Satisfacción</h4>
            <p>Calificación promedio de usuarios. Meta: ≥4.5/5. Actual: {kpisToShow.satisfaccion_usuarios?.current || 4.6}/5</p>
          </div>
        </div>
      </div>

      {/* Acciones Recomendadas */}
      <div className="bg-gradient-to-r from-green-50 to-emerald-50 rounded-lg p-6 border-l-4 border-green-500">
        <h3 className="text-lg font-semibold text-green-800 mb-3">
          💡 Acciones Recomendadas
        </h3>
        <div className="space-y-3 text-sm text-green-700">
          <div className="flex items-start">
            <span className="inline-block w-2 h-2 bg-green-500 rounded-full mt-2 mr-3 flex-shrink-0"></span>
            <div>
              <strong>Rendimiento General:</strong> Implementar sesiones de refuerzo en aptitudes con percentiles &lt;60.
            </div>
          </div>
          <div className="flex items-start">
            <span className="inline-block w-2 h-2 bg-green-500 rounded-full mt-2 mr-3 flex-shrink-0"></span>
            <div>
              <strong>Participación:</strong> Crear incentivos para aumentar la participación activa al 90%.
            </div>
          </div>
          <div className="flex items-start">
            <span className="inline-block w-2 h-2 bg-green-500 rounded-full mt-2 mr-3 flex-shrink-0"></span>
            <div>
              <strong>Tiempo de Test:</strong> Optimizar instrucciones para mantener tiempo &lt;20 minutos.
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default memo(KpisView);
