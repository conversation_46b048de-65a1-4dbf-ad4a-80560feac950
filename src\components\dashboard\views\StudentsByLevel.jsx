import React from 'react';
import PropTypes from 'prop-types';
import { Card, CardHeader, CardBody } from '../../ui/Card';
import { FaExclamationTriangle } from 'react-icons/fa';
import PieChart from '../charts/PieChart.jsx';
import ChartLegend from '../charts/ChartLegend.jsx';
import ChartInterpretation from '../charts/ChartInterpretation.jsx';
import EmptyState from '../charts/EmptyState.jsx';
import { useStudentDistributionData } from '../../../hooks/useStudentDistributionData.js';
import { useChartAnimation } from '../../../hooks/useChartAnimation.js';

/**
 * Widget: Distribución de Estudiantes por Nivel
 * Gráfico de torta mostrando la distribución de estudiantes por nivel educativo
 */
const StudentsByLevel = ({ data, loading, error }) => {
  const estudiantesPorNivelData = data?.estudiantesPorNivelData || [];
  
  // Use custom hooks for data processing and animation
  const { validatedData, segments, totalStudents, hasData } = useStudentDistributionData(estudiantesPorNivelData);
  const animationKey = useChartAnimation(estudiantesPorNivelData);

  if (loading) {
    return (
      <Card>
        <CardHeader className="bg-gradient-to-r from-blue-500 to-blue-600 text-white">
          <h3 className="text-lg font-semibold">
            <i className="fas fa-chart-pie mr-2 animate-spin"></i>
            Distribución por Nivel Educativo
          </h3>
        </CardHeader>
        <CardBody>
          <div className="animate-pulse">
            <div className="w-48 h-48 bg-gradient-to-r from-blue-200 to-purple-200 rounded-full mx-auto mb-4 animate-pulse"></div>
            <div className="space-y-3">
              {Array.from({ length: 3 }).map((_, i) => (
                <div key={i} className="flex items-center animate-pulse" style={{ animationDelay: `${i * 0.1}s` }}>
                  <div className="w-4 h-4 bg-gray-300 rounded mr-3"></div>
                  <div className="h-4 bg-gray-300 rounded flex-1"></div>
                  <div className="w-12 h-4 bg-gray-300 rounded ml-2"></div>
                </div>
              ))}
            </div>
          </div>
        </CardBody>
      </Card>
    );
  }

  if (error) {
    return (
      <Card>
        <CardHeader className="bg-gradient-to-r from-red-500 to-red-600 text-white">
          <h3 className="text-lg font-semibold">
            <i className="fas fa-chart-pie mr-2"></i>
            Distribución por Nivel Educativo
          </h3>
        </CardHeader>
        <CardBody>
          <div className="text-center py-8">
            <FaExclamationTriangle className="text-red-500 text-4xl mx-auto mb-4" />
            <p className="text-gray-600 mb-4">{error}</p>
            {/* El botón de reintentar se maneja a nivel de DashboardContext */}
          </div>
        </CardBody>
      </Card>
    );
  }

  return (
    <Card>
      <CardHeader className="bg-gradient-to-r from-blue-500 to-blue-600 text-white">
        <h3 className="text-lg font-semibold">
          <i className="fas fa-chart-pie mr-2"></i>
          Distribución por Nivel Educativo
        </h3>
      </CardHeader>
      <CardBody>
        {hasData ? (
          <>
            {/* Gráfico de torta */}
            <div className="mb-6">
              <PieChart 
                segments={segments}
                totalStudents={totalStudents}
                animationKey={animationKey}
              />
            </div>

            {/* Leyenda */}
            <ChartLegend data={validatedData} />

            {/* Interpretación */}
            <ChartInterpretation 
              totalStudents={totalStudents}
              levelsCount={validatedData.length}
            />
          </>
        ) : (
          <EmptyState />
        )}
      </CardBody>
    </Card>
  );
};

StudentsByLevel.propTypes = {
  data: PropTypes.shape({
    estudiantesPorNivelData: PropTypes.arrayOf(PropTypes.shape({
      nivel: PropTypes.string,
      nivel_nombre: PropTypes.string,
      total_estudiantes: PropTypes.oneOfType([PropTypes.string, PropTypes.number]),
      porcentaje: PropTypes.oneOfType([PropTypes.string, PropTypes.number])
    }))
  }),
  loading: PropTypes.bool,
  error: PropTypes.string
};

StudentsByLevel.defaultProps = {
  data: { estudiantesPorNivelData: [] },
  loading: false,
  error: null
};
 
export default StudentsByLevel;