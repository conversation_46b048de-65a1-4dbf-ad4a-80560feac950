import React, { memo, useState, useEffect } from 'react';
import Trend<PERSON>hart from '../TrendChart';
import { Card, CardHeader, CardBody } from '../../ui/Card';
import { FaChartLine, FaArrowUp, FaArrowDown, FaMinus } from 'react-icons/fa';
import DashboardService from '../../../services/DashboardService';

/**
 * Vista de Análisis de Tendencias del Dashboard
 * Muestra evolución temporal de métricas clave
 */
const TrendsView = ({ loading, trendData }) => {
  const [realTrendData, setRealTrendData] = useState(null);
  const [loadingTrends, setLoadingTrends] = useState(true);

  // Cargar datos reales de tendencias
  useEffect(() => {
    const loadTrendsData = async () => {
      try {
        console.log('📈 [TrendsView] Cargando datos REALES de tendencias...');
        const datos = await DashboardService.getTrendsData();
        setRealTrendData(datos);
        console.log('✅ [TrendsView] Datos de tendencias cargados:', datos);
      } catch (error) {
        console.error('❌ [TrendsView] Error cargando tendencias:', error);
        setRealTrendData(null);
      } finally {
        setLoadingTrends(false);
      }
    };

    loadTrendsData();
  }, []);

  // Datos de tendencias por defecto si no hay datos reales
  const defaultTrendData = [
    { name: 'Ene', rendimiento: 68, participacion: 82, satisfaccion: 4.2 },
    { name: 'Feb', rendimiento: 70, participacion: 85, satisfaccion: 4.3 },
    { name: 'Mar', rendimiento: 69, participacion: 87, satisfaccion: 4.4 },
    { name: 'Abr', rendimiento: 72, participacion: 89, satisfaccion: 4.5 },
    { name: 'May', rendimiento: 74, participacion: 91, satisfaccion: 4.6 },
    { name: 'Jun', rendimiento: 73, participacion: 88, satisfaccion: 4.4 }
  ];

  const trendsToShow = realTrendData && realTrendData.length > 0 ? realTrendData :
                      (trendData && trendData.length > 0 ? trendData : defaultTrendData);

  // Calcular tendencias
  const calculateTrend = (data, key) => {
    if (!data || data.length < 2) return { direction: 'stable', percentage: 0 };
    
    const first = data[0][key];
    const last = data[data.length - 1][key];
    const percentage = ((last - first) / first * 100).toFixed(1);
    
    if (percentage > 2) return { direction: 'up', percentage: `+${percentage}` };
    if (percentage < -2) return { direction: 'down', percentage };
    return { direction: 'stable', percentage: percentage };
  };

  const rendimientoTrend = calculateTrend(trendsToShow, 'rendimiento');
  const participacionTrend = calculateTrend(trendsToShow, 'participacion');
  const satisfaccionTrend = calculateTrend(trendsToShow, 'satisfaccion');

  const getTrendIcon = (direction) => {
    switch (direction) {
      case 'up': return <FaArrowUp className="text-green-500" />;
      case 'down': return <FaArrowDown className="text-red-500" />;
      default: return <FaMinus className="text-gray-500" />;
    }
  };

  const getTrendColor = (direction) => {
    switch (direction) {
      case 'up': return 'text-green-600';
      case 'down': return 'text-red-600';
      default: return 'text-gray-600';
    }
  };

  if (loading || loadingTrends) {
    return (
      <div className="space-y-6">
        <div className="bg-white rounded-lg shadow-lg p-6 animate-pulse">
          <div className="h-6 bg-gray-200 rounded mb-4"></div>
          <div className="h-80 bg-gray-200 rounded"></div>
        </div>
        
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          <div className="bg-white rounded-lg shadow-lg p-6 animate-pulse">
            <div className="h-6 bg-gray-200 rounded mb-4"></div>
            <div className="h-64 bg-gray-200 rounded"></div>
          </div>
          <div className="bg-white rounded-lg shadow-lg p-6 animate-pulse">
            <div className="h-6 bg-gray-200 rounded mb-4"></div>
            <div className="h-64 bg-gray-200 rounded"></div>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Gráfico principal de tendencias */}
      <Card>
        <CardHeader className="bg-gradient-to-r from-blue-500 to-blue-600 text-white">
          <h3 className="text-lg font-semibold flex items-center justify-center">
            <FaChartLine className="mr-2" />
            Evolución de Métricas Clave - Últimos 6 Meses
          </h3>
        </CardHeader>
        <CardBody className="p-6">
          <TrendChart
            data={trendsToShow}
            height={400}
            colors={['#3B82F6', '#10B981', '#F59E0B']}
          />
        </CardBody>
      </Card>

      {/* Indicadores de tendencia */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        <Card className="hover:shadow-lg transition-shadow duration-200">
          <CardBody className="p-6 text-center">
            <div className="flex items-center justify-center mb-3">
              {getTrendIcon(rendimientoTrend.direction)}
              <span className="ml-2 text-lg font-semibold">Rendimiento</span>
            </div>
            <div className={`text-2xl font-bold ${getTrendColor(rendimientoTrend.direction)} mb-2`}>
              {rendimientoTrend.percentage}%
            </div>
            <p className="text-gray-600 text-sm">Cambio en los últimos 6 meses</p>
          </CardBody>
        </Card>

        <Card className="hover:shadow-lg transition-shadow duration-200">
          <CardBody className="p-6 text-center">
            <div className="flex items-center justify-center mb-3">
              {getTrendIcon(participacionTrend.direction)}
              <span className="ml-2 text-lg font-semibold">Participación</span>
            </div>
            <div className={`text-2xl font-bold ${getTrendColor(participacionTrend.direction)} mb-2`}>
              {participacionTrend.percentage}%
            </div>
            <p className="text-gray-600 text-sm">Cambio en los últimos 6 meses</p>
          </CardBody>
        </Card>

        <Card className="hover:shadow-lg transition-shadow duration-200">
          <CardBody className="p-6 text-center">
            <div className="flex items-center justify-center mb-3">
              {getTrendIcon(satisfaccionTrend.direction)}
              <span className="ml-2 text-lg font-semibold">Satisfacción</span>
            </div>
            <div className={`text-2xl font-bold ${getTrendColor(satisfaccionTrend.direction)} mb-2`}>
              {satisfaccionTrend.percentage}%
            </div>
            <p className="text-gray-600 text-sm">Cambio en los últimos 6 meses</p>
          </CardBody>
        </Card>
      </div>

      {/* Gráficos individuales */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <Card>
          <CardHeader className="bg-gradient-to-r from-blue-500 to-blue-600 text-white">
            <h3 className="text-lg font-semibold flex items-center justify-center">
              <FaChartLine className="mr-2" />
              Tendencia de Rendimiento
            </h3>
          </CardHeader>
          <CardBody className="p-6">
            <TrendChart
              data={trendsToShow.map(d => ({ name: d.name, rendimiento: d.rendimiento }))}
              type="area"
              height={300}
              colors={['#3B82F6']}
            />
          </CardBody>
        </Card>

        <Card>
          <CardHeader className="bg-gradient-to-r from-green-500 to-green-600 text-white">
            <h3 className="text-lg font-semibold flex items-center justify-center">
              <FaChartLine className="mr-2" />
              Evolución de Participación
            </h3>
          </CardHeader>
          <CardBody className="p-6">
            <TrendChart
              data={trendsToShow.map(d => ({ name: d.name, participacion: d.participacion }))}
              type="area"
              height={300}
              colors={['#10B981']}
            />
          </CardBody>
        </Card>
      </div>

      {/* Análisis de tendencias */}
      <div className="bg-gradient-to-r from-blue-50 to-indigo-50 rounded-lg p-6 border-l-4 border-blue-500">
        <h3 className="text-lg font-semibold text-blue-800 mb-3">
          📊 Análisis de Tendencias
        </h3>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm text-blue-700">
          <div>
            <h4 className="font-medium mb-2">🎯 Rendimiento General</h4>
            <p>
              {rendimientoTrend.direction === 'up' && 'Tendencia positiva. El rendimiento está mejorando consistentemente.'}
              {rendimientoTrend.direction === 'down' && 'Tendencia negativa. Se requiere intervención para mejorar el rendimiento.'}
              {rendimientoTrend.direction === 'stable' && 'Tendencia estable. El rendimiento se mantiene constante.'}
            </p>
          </div>
          <div>
            <h4 className="font-medium mb-2">👥 Participación</h4>
            <p>
              {participacionTrend.direction === 'up' && 'Excelente. La participación estudiantil está aumentando.'}
              {participacionTrend.direction === 'down' && 'Preocupante. La participación está disminuyendo, revisar estrategias de motivación.'}
              {participacionTrend.direction === 'stable' && 'Estable. Mantener estrategias actuales de participación.'}
            </p>
          </div>
        </div>
      </div>
    </div>
  );
};

export default memo(TrendsView);
