import React, { useState, useEffect } from 'react';
import { Card, CardHeader, CardBody } from '../../../ui/Card';
import supabase from '../../../../api/supabaseClient';
import { FaVenusMars, FaMars, FaVenus } from 'react-icons/fa';

/**
 * Componente mejorado para mostrar comparativa de rendimiento por género
 * Integrado con la nueva arquitectura del dashboard
 */
const ComparativaGeneroEnhanced = ({ data, loading: parentLoading, filters = {} }) => {
  const [localData, setLocalData] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);

  useEffect(() => {
    const fetchComparativaData = async () => {
      try {
        setLoading(true);
        setError(null);

        // Intentar usar datos del contexto primero
        if (data?.comparativaGenero) {
          setLocalData(data.comparativaGenero);
          setLoading(false);
          return;
        }

        // Si no hay datos del contexto, obtener directamente de Supabase
        console.log('📊 [ComparativaGenero] Obteniendo datos de comparativa...');
        
        const { data: resultados, error } = await supabase
          .from('resultados')
          .select(`
            percentil,
            aptitudes:aptitud_id (codigo, nombre),
            pacientes:paciente_id (
              genero,
              nivel_educativo,
              institucion_id
            )
          `)
          .not('percentil', 'is', null);

        if (error) throw error;

        // Procesar datos para crear comparativa por género
        const comparativaData = processComparativaData(resultados, filters);
        setLocalData(comparativaData);

      } catch (err) {
        console.error('❌ [ComparativaGenero] Error:', err);
        setError(err.message);
        // Usar datos de ejemplo en caso de error
        setLocalData(generateSampleComparativaData());
      } finally {
        setLoading(false);
      }
    };

    fetchComparativaData();
  }, [data, filters]);

  // Procesar datos para crear comparativa por género
  const processComparativaData = (resultados, filters) => {
    const aptitudes = ['V', 'E', 'A', 'R', 'N', 'M', 'O'];
    const generos = ['M', 'F'];
    const comparativa = [];

    aptitudes.forEach(codigo => {
      generos.forEach(genero => {
        // Filtrar resultados por aptitud y género
        const resultadosAptitudGenero = resultados.filter(r => {
          const aptitudCodigo = r.aptitudes?.codigo;
          const pacienteGenero = r.pacientes?.genero;
          
          // Aplicar filtros adicionales si existen
          let cumpleFiltros = true;
          if (filters.testLevel && filters.testLevel !== 'all') {
            const nivelEducativo = r.pacientes?.nivel_educativo;
            const nivelNormalizado = normalizeEducationLevel(nivelEducativo);
            cumpleFiltros = cumpleFiltros && nivelNormalizado === filters.testLevel;
          }
          
          return aptitudCodigo === codigo && pacienteGenero === genero && cumpleFiltros;
        });

        if (resultadosAptitudGenero.length > 0) {
          const percentiles = resultadosAptitudGenero.map(r => r.percentil);
          const promedio = percentiles.reduce((sum, p) => sum + p, 0) / percentiles.length;
          
          comparativa.push({
            codigo,
            genero,
            promedio_percentil: promedio,
            cantidad_estudiantes: resultadosAptitudGenero.length,
            nivel: filters.testLevel || 'all'
          });
        }
      });
    });

    return comparativa;
  };

  // Normalizar nivel educativo
  const normalizeEducationLevel = (nivel) => {
    if (!nivel) return 'S';
    const nivelLower = nivel.toLowerCase();
    if (nivelLower.includes('elemental') || nivelLower.includes('primaria')) return 'E';
    if (nivelLower.includes('medio') || nivelLower.includes('secundaria')) return 'M';
    return 'S';
  };

  // Generar datos de ejemplo
  const generateSampleComparativaData = () => {
    const aptitudes = ['V', 'E', 'A', 'R', 'N', 'M', 'O'];
    const generos = ['M', 'F'];
    const sampleData = [];

    aptitudes.forEach(codigo => {
      generos.forEach(genero => {
        const promedio = Math.random() * 40 + 30; // 30-70
        const cantidad = Math.floor(Math.random() * 50) + 20; // 20-70
        
        sampleData.push({
          codigo,
          genero,
          promedio_percentil: promedio,
          cantidad_estudiantes: cantidad,
          nivel: 'all'
        });
      });
    });

    return sampleData;
  };

  const aptitudes = ['V', 'E', 'A', 'R', 'N', 'M', 'O'];
  const aptitudeNames = {
    'V': 'Verbal',
    'E': 'Espacial',
    'A': 'Abstracto',
    'R': 'Razonamiento',
    'N': 'Numérico',
    'M': 'Mecánico',
    'O': 'Ortografía'
  };

  const selectedNivel = filters.testLevel;
  const filteredData = selectedNivel === 'all' || !selectedNivel
    ? localData
    : localData.filter(item => item.nivel === selectedNivel);

  if (loading || parentLoading) {
    return (
      <Card>
        <CardHeader className="bg-gradient-to-r from-pink-500 to-pink-600 text-white">
          <h3 className="text-lg font-semibold flex items-center">
            <FaVenusMars className="mr-2" />
            Comparativa por Género
          </h3>
        </CardHeader>
        <CardBody>
          <div className="animate-pulse h-64 bg-gray-200 rounded"></div>
        </CardBody>
      </Card>
    );
  }

  return (
    <Card>
      <CardHeader className="bg-gradient-to-r from-pink-500 to-pink-600 text-white">
        <h3 className="text-lg font-semibold flex items-center">
          <FaVenusMars className="mr-2" />
          Comparativa por Género
        </h3>
      </CardHeader>
      <CardBody>
        {error && (
          <div className="mb-4 p-3 bg-yellow-50 border border-yellow-200 rounded-lg">
            <p className="text-yellow-800 text-sm">
              ⚠️ {error} - Mostrando datos de ejemplo
            </p>
          </div>
        )}
        
        {filteredData.length > 0 ? (
          <>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              {aptitudes.map(codigo => {
                const aptitudData = filteredData.filter(item => item.codigo === codigo);
                const masculino = aptitudData.find(item => item.genero === 'M');
                const femenino = aptitudData.find(item => item.genero === 'F');

                if (!masculino && !femenino) return null;

                return (
                  <div key={codigo} className="border rounded-lg p-4 hover:shadow-md transition-shadow">
                    <h4 className="font-medium text-gray-800 mb-3 text-center">
                      {aptitudeNames[codigo]} ({codigo})
                    </h4>
                    
                    <div className="space-y-3">
                      {masculino && (
                        <div className="flex items-center justify-between">
                          <div className="flex items-center">
                            <FaMars className="text-blue-500 mr-2" />
                            <span className="text-sm font-medium">Masculino</span>
                          </div>
                          <div className="text-right">
                            <div className="text-sm font-bold text-blue-600">
                              {masculino.promedio_percentil.toFixed(1)}
                            </div>
                            <div className="text-xs text-gray-500">
                              {masculino.cantidad_estudiantes} estudiantes
                            </div>
                          </div>
                        </div>
                      )}
                      
                      {femenino && (
                        <div className="flex items-center justify-between">
                          <div className="flex items-center">
                            <FaVenus className="text-pink-500 mr-2" />
                            <span className="text-sm font-medium">Femenino</span>
                          </div>
                          <div className="text-right">
                            <div className="text-sm font-bold text-pink-600">
                              {femenino.promedio_percentil.toFixed(1)}
                            </div>
                            <div className="text-xs text-gray-500">
                              {femenino.cantidad_estudiantes} estudiantes
                            </div>
                          </div>
                        </div>
                      )}
                      
                      {masculino && femenino && (
                        <div className="pt-2 border-t border-gray-200">
                          <div className="text-xs text-gray-600 text-center">
                            Diferencia: {Math.abs(masculino.promedio_percentil - femenino.promedio_percentil).toFixed(1)} puntos
                          </div>
                        </div>
                      )}
                    </div>
                  </div>
                );
              })}
            </div>

            <div className="mt-6 p-4 bg-pink-50 rounded-lg">
              <h4 className="font-medium text-pink-800 mb-2 flex items-center">
                <FaVenusMars className="mr-2" />
                Interpretación
              </h4>
              <p className="text-sm text-pink-700">
                Comparativa de rendimiento promedio por género en cada aptitud. 
                Las diferencias pueden indicar patrones de fortalezas específicas que requieren enfoques pedagógicos diferenciados.
              </p>
            </div>
          </>
        ) : (
          <div className="text-center py-8 text-gray-500">
            <FaVenusMars className="text-4xl mb-4 mx-auto" />
            <p>No hay datos de comparativa por género disponibles</p>
          </div>
        )}
      </CardBody>
    </Card>
  );
};

export default ComparativaGeneroEnhanced;
