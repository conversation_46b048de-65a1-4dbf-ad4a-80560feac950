import React, { useState, useEffect } from 'react';
import { Card, CardHeader, CardBody } from '../../../ui/Card';
import supabase from '../../../../api/supabaseClient';
import { FaChartBar, FaInfoCircle } from 'react-icons/fa';

/**
 * Componente mejorado para mostrar la distribución de rendimiento por nivel educativo
 * Integrado con la nueva arquitectura del dashboard
 */
const DistribucionRendimientoEnhanced = ({ data, loading: parentLoading, filters = {} }) => {
  const [localData, setLocalData] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);

  useEffect(() => {
    const fetchDistribucionData = async () => {
      try {
        setLoading(true);
        setError(null);

        // Intentar usar datos del contexto primero
        if (data?.distribucionRendimiento) {
          setLocalData(data.distribucionRendimiento);
          setLoading(false);
          return;
        }

        // Si no hay datos del contexto, obtener directamente de Supabase
        console.log('📊 [DistribucionRendimiento] Obteniendo datos de distribución...');

        // Consulta optimizada para obtener distribución de rendimiento
        const { data: resultados, error } = await supabase
          .from('resultados')
          .select(`
            percentil,
            aptitudes:aptitud_id (codigo, nombre),
            pacientes:paciente_id (
              nivel_educativo,
              genero,
              institucion_id
            )
          `)
          .not('percentil', 'is', null);

        if (error) throw error;

        // Procesar datos para crear distribución por nivel y categoría
        const distribucionData = processDistribucionData(resultados, filters);
        setLocalData(distribucionData);

      } catch (err) {
        console.error('❌ [DistribucionRendimiento] Error:', err);
        setError(err.message);
        // Usar datos de ejemplo en caso de error
        setLocalData(generateSampleDistribucionData());
      } finally {
        setLoading(false);
      }
    };

    fetchDistribucionData();
  }, [data, filters]);

  // Procesar datos para crear distribución por nivel y categoría
  const processDistribucionData = (resultados, filters) => {
    const niveles = ['E', 'M', 'S'];
    const categorias = ['Bajo', 'Promedio', 'Alto'];
    const distribucion = [];

    niveles.forEach(nivel => {
      // Filtrar resultados por nivel educativo
      const resultadosNivel = resultados.filter(r => {
        const nivelEducativo = r.pacientes?.nivel_educativo;
        return normalizeEducationLevel(nivelEducativo) === nivel;
      });

      categorias.forEach(categoria => {
        // Clasificar por percentil
        const estudiantesCategoria = resultadosNivel.filter(r => {
          const percentil = r.percentil;
          if (categoria === 'Bajo') return percentil <= 25;
          if (categoria === 'Promedio') return percentil > 25 && percentil <= 75;
          if (categoria === 'Alto') return percentil > 75;
          return false;
        });

        const cantidadEstudiantes = estudiantesCategoria.length;
        const porcentaje = resultadosNivel.length > 0
          ? (cantidadEstudiantes / resultadosNivel.length) * 100
          : 0;

        distribucion.push({
          nivel,
          categoria_rendimiento: categoria,
          cantidad_estudiantes: cantidadEstudiantes,
          porcentaje: porcentaje
        });
      });
    });

    return distribucion;
  };

  // Normalizar nivel educativo
  const normalizeEducationLevel = (nivel) => {
    if (!nivel) return 'S';
    const nivelLower = nivel.toLowerCase();
    if (nivelLower.includes('elemental') || nivelLower.includes('primaria')) return 'E';
    if (nivelLower.includes('medio') || nivelLower.includes('secundaria')) return 'M';
    return 'S'; // Superior por defecto
  };

  // Generar datos de ejemplo
  const generateSampleDistribucionData = () => {
    const niveles = ['E', 'M', 'S'];
    const categorias = ['Bajo', 'Promedio', 'Alto'];
    const sampleData = [];

    niveles.forEach(nivel => {
      categorias.forEach(categoria => {
        const baseCount = Math.floor(Math.random() * 50) + 10;
        const percentage = categoria === 'Promedio' ?
          Math.random() * 30 + 40 : // 40-70% para promedio
          Math.random() * 25 + 5;   // 5-30% para bajo/alto

        sampleData.push({
          nivel,
          categoria_rendimiento: categoria,
          cantidad_estudiantes: baseCount,
          porcentaje: percentage
        });
      });
    });

    return sampleData;
  };
  const niveles = ['E', 'M', 'S'];
  const categorias = ['Bajo', 'Promedio', 'Alto'];
  
  const getColorForCategoria = (categoria) => {
    const colors = {
      'Bajo': 'bg-red-500',
      'Promedio': 'bg-yellow-500', 
      'Alto': 'bg-green-500'
    };
    return colors[categoria] || 'bg-gray-500';
  };

  if (loading || parentLoading) {
    return (
      <Card>
        <CardHeader className="bg-gradient-to-r from-orange-500 to-orange-600 text-white">
          <h3 className="text-lg font-semibold flex items-center">
            <FaChartBar className="mr-2" />
            Distribución de Rendimiento
          </h3>
        </CardHeader>
        <CardBody>
          <div className="animate-pulse h-64 bg-gray-200 rounded"></div>
        </CardBody>
      </Card>
    );
  }

  return (
    <Card>
      <CardHeader className="bg-gradient-to-r from-orange-500 to-orange-600 text-white">
        <h3 className="text-lg font-semibold flex items-center">
          <FaChartBar className="mr-2" />
          Distribución de Rendimiento por Nivel
        </h3>
      </CardHeader>
      <CardBody>
        {error && (
          <div className="mb-4 p-3 bg-yellow-50 border border-yellow-200 rounded-lg">
            <p className="text-yellow-800 text-sm">
              ⚠️ {error} - Mostrando datos de ejemplo
            </p>
          </div>
        )}

        {localData && localData.length > 0 ? (
          <>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-6">
              {niveles.map(nivel => {
                const nivelData = localData.filter(item => item.nivel === nivel);
                const nivelNombre = nivel === 'E' ? 'Elemental' : nivel === 'M' ? 'Medio' : 'Superior';
                
                return (
                  <div key={nivel} className="border rounded-lg p-4 hover:shadow-md transition-shadow">
                    <h4 className="font-medium text-gray-800 mb-3 text-center">
                      Nivel {nivelNombre}
                    </h4>
                    <div className="space-y-2">
                      {categorias.map(categoria => {
                        const categoriaData = nivelData.filter(item => item.categoria_rendimiento === categoria);
                        const totalEstudiantes = categoriaData.reduce((sum, item) => sum + item.cantidad_estudiantes, 0);
                        const promedioPorcentaje = categoriaData.length > 0 
                          ? categoriaData.reduce((sum, item) => sum + item.porcentaje, 0) / categoriaData.length 
                          : 0;
                        
                        return (
                          <div key={categoria} className="flex items-center justify-between">
                            <div className="flex items-center">
                              <div className={`w-4 h-4 rounded mr-2 ${getColorForCategoria(categoria)}`}></div>
                              <span className="text-sm">{categoria}</span>
                            </div>
                            <div className="text-right">
                              <div className="text-sm font-medium">{totalEstudiantes}</div>
                              <div className="text-xs text-gray-500">{promedioPorcentaje.toFixed(1)}%</div>
                            </div>
                          </div>
                        );
                      })}
                    </div>
                  </div>
                );
              })}
            </div>
            
            <div className="p-4 bg-orange-50 rounded-lg">
              <h4 className="font-medium text-orange-800 mb-2 flex items-center">
                <FaInfoCircle className="mr-2" />
                Interpretación
              </h4>
              <p className="text-sm text-orange-700">
                Muestra la distribución de estudiantes en categorías de rendimiento por nivel educativo.
                Identifica niveles con alta concentración en "Bajo" que requieren intervención pedagógica específica.
              </p>
            </div>
          </>
        ) : (
          <div className="text-center py-8 text-gray-500">
            <FaChartBar className="text-4xl mb-4 mx-auto" />
            <p>No hay datos de distribución disponibles</p>
          </div>
        )}
      </CardBody>
    </Card>
  );
};

export default DistribucionRendimientoEnhanced;