import React from 'react';
import { Card, CardBody } from '../../../ui/Card';
import { FaArrowUp, FaArrowDown, FaMinus, FaUsers, FaUser<PERSON>heck, FaClipboardList, FaChartLine, FaCalendarAlt, FaClock } from 'react-icons/fa';

/**
 * Widget de estadísticas generales del dashboard mejorado
 * Muestra métricas clave en formato de tarjetas con indicadores de tendencia
 * Integrado con la nueva arquitectura del dashboard
 */
const EstadisticasGeneralesEnhanced = ({ data, loading, previousData }) => {
  const estadisticas = [
    {
      titulo: 'Total Pacientes',
      valor: data?.total_pacientes || data?.totalPacientes || 0,
      icono: FaUsers,
      color: 'blue',
      descripcion: 'Pacientes registrados'
    },
    {
      titulo: 'Pacientes Evaluados',
      valor: data?.pacientes_evaluados || data?.pacientesEvaluados || 0,
      icono: FaUserCheck,
      color: 'green',
      descripcion: 'Con al menos 1 test'
    },
    {
      titulo: 'Total Evaluaciones',
      valor: data?.total_evaluaciones || data?.totalEvaluaciones || 0,
      icono: FaClipboardList,
      color: 'purple',
      descripcion: 'Tests completados'
    },
    {
      titulo: 'Percentil Promedio',
      valor: data?.percentil_promedio_general || data?.promedioGeneral || 0,
      icono: FaChartLine,
      color: 'orange',
      descripcion: 'Rendimiento general',
      formato: 'decimal'
    },
    {
      titulo: 'Evaluaciones (30 días)',
      valor: data?.evaluaciones_ultimo_mes || data?.evaluacionesUltimoMes || 0,
      icono: FaCalendarAlt,
      color: 'indigo',
      descripcion: 'Último mes'
    },
    {
      titulo: 'Evaluaciones (7 días)',
      valor: data?.evaluaciones_ultima_semana || data?.evaluacionesUltimaSemana || 0,
      icono: FaClock,
      color: 'teal',
      descripcion: 'Última semana'
    }
  ];

  const getColorClasses = (color) => {
    const colors = {
      blue: 'bg-blue-500 text-white',
      green: 'bg-green-500 text-white',
      purple: 'bg-purple-500 text-white',
      orange: 'bg-orange-500 text-white',
      indigo: 'bg-indigo-500 text-white',
      teal: 'bg-teal-500 text-white'
    };
    return colors[color] || 'bg-gray-500 text-white';
  };

  const formatValue = (valor, formato) => {
    if (formato === 'decimal') {
      return Number(valor).toFixed(1);
    }
    return Number(valor).toLocaleString();
  };

  const getTrendIcon = (current, previous) => {
    if (!previous || previous === 0) return <FaMinus className="text-gray-400" />;
    
    if (current > previous) return <FaArrowUp className="text-green-500" />;
    if (current < previous) return <FaArrowDown className="text-red-500" />;
    return <FaMinus className="text-gray-400" />;
  };

  const getTrendPercentage = (current, previous) => {
    if (!previous || previous === 0) return null;
    
    const percentage = ((current - previous) / previous) * 100;
    return Math.abs(percentage).toFixed(1);
  };

  if (loading) {
    return (
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
        {[...Array(6)].map((_, index) => (
          <Card key={index} className="animate-pulse">
            <CardBody className="p-6">
              <div className="flex items-center justify-between">
                <div className="space-y-2">
                  <div className="h-4 bg-gray-200 rounded w-24"></div>
                  <div className="h-8 bg-gray-200 rounded w-16"></div>
                  <div className="h-3 bg-gray-200 rounded w-20"></div>
                </div>
                <div className="w-12 h-12 bg-gray-200 rounded-full"></div>
              </div>
            </CardBody>
          </Card>
        ))}
      </div>
    );
  }

  return (
    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
      {estadisticas.map((stat, index) => {
        const IconComponent = stat.icono;
        const previousValue = previousData?.[Object.keys(data || {}).find(key => 
          data[key] === stat.valor
        )];
        
        return (
          <Card key={index} className="hover:shadow-lg transition-shadow duration-200">
            <CardBody className="p-6">
              <div className="flex items-center justify-between">
                <div className="space-y-2">
                  <h3 className="text-sm font-medium text-gray-600">
                    {stat.titulo}
                  </h3>
                  <div className="flex items-center space-x-2">
                    <p className="text-2xl font-bold text-gray-900">
                      {formatValue(stat.valor, stat.formato)}
                    </p>
                    {previousValue && (
                      <div className="flex items-center space-x-1">
                        {getTrendIcon(stat.valor, previousValue)}
                        {getTrendPercentage(stat.valor, previousValue) && (
                          <span className="text-xs text-gray-500">
                            {getTrendPercentage(stat.valor, previousValue)}%
                          </span>
                        )}
                      </div>
                    )}
                  </div>
                  <p className="text-xs text-gray-500">
                    {stat.descripcion}
                  </p>
                </div>
                <div className={`p-3 rounded-full ${getColorClasses(stat.color)}`}>
                  <IconComponent className="w-6 h-6" />
                </div>
              </div>
            </CardBody>
          </Card>
        );
      })}
    </div>
  );
};

export default EstadisticasGeneralesEnhanced;
