import React, { useState, useEffect } from 'react';
import { Card, CardHeader, CardBody } from '../../../ui/Card';
import supabase from '../../../../api/supabaseClient';
import { FaProjectDiagram, FaLink, FaUnlink, FaLightbulb } from 'react-icons/fa';

/**
 * Componente mejorado para mostrar la matriz de correlación entre aptitudes
 * Integrado con la nueva arquitectura del dashboard
 */
const MatrizCorrelacionEnhanced = ({ data, loading: parentLoading, filters = {} }) => {
  const [localData, setLocalData] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);

  useEffect(() => {
    const fetchCorrelacionData = async () => {
      try {
        setLoading(true);
        setError(null);

        // Intentar usar datos del contexto primero
        if (data?.matrizCorrelacion) {
          setLocalData(data.matrizCorrelacion);
          setLoading(false);
          return;
        }

        // Si no hay datos del contexto, obtener directamente de Supabase
        console.log('📊 [MatrizCorrelacion] Obteniendo datos de correlación...');
        
        // Consulta para obtener resultados y calcular correlaciones
        const { data: resultados, error } = await supabase
          .from('resultados')
          .select(`
            percentil,
            aptitudes:aptitud_id (codigo, nombre),
            pacientes:paciente_id (
              id,
              nivel_educativo,
              genero,
              institucion_id
            )
          `)
          .not('percentil', 'is', null);

        if (error) throw error;

        // Procesar datos para crear matriz de correlación
        const correlacionData = calculateCorrelationMatrix(resultados);
        setLocalData(correlacionData);

      } catch (err) {
        console.error('❌ [MatrizCorrelacion] Error:', err);
        setError(err.message);
        // Usar datos de ejemplo en caso de error
        setLocalData(generateSampleCorrelationData());
      } finally {
        setLoading(false);
      }
    };

    fetchCorrelacionData();
  }, [data, filters]);

  // Calcular matriz de correlación
  const calculateCorrelationMatrix = (resultados) => {
    const aptitudes = ['V', 'E', 'A', 'R', 'N', 'M', 'O'];
    const correlaciones = [];

    // Agrupar resultados por paciente y aptitud
    const pacienteAptitudes = {};
    resultados.forEach(resultado => {
      const pacienteId = resultado.pacientes?.id;
      const aptitudCodigo = resultado.aptitudes?.codigo;
      
      if (pacienteId && aptitudCodigo && resultado.percentil !== null) {
        if (!pacienteAptitudes[pacienteId]) {
          pacienteAptitudes[pacienteId] = {};
        }
        pacienteAptitudes[pacienteId][aptitudCodigo] = resultado.percentil;
      }
    });

    // Calcular correlaciones entre pares de aptitudes
    for (let i = 0; i < aptitudes.length; i++) {
      for (let j = i + 1; j < aptitudes.length; j++) {
        const apt1 = aptitudes[i];
        const apt2 = aptitudes[j];
        
        const correlation = calculatePearsonCorrelation(pacienteAptitudes, apt1, apt2);
        
        correlaciones.push({
          aptitud_1: apt1,
          aptitud_2: apt2,
          correlacion: correlation
        });
      }
    }

    return correlaciones;
  };

  // Calcular correlación de Pearson entre dos aptitudes
  const calculatePearsonCorrelation = (pacienteAptitudes, apt1, apt2) => {
    const pairs = [];
    
    Object.values(pacienteAptitudes).forEach(aptitudes => {
      if (aptitudes[apt1] !== undefined && aptitudes[apt2] !== undefined) {
        pairs.push([aptitudes[apt1], aptitudes[apt2]]);
      }
    });

    if (pairs.length < 3) return 0; // Necesitamos al menos 3 pares para una correlación significativa

    const n = pairs.length;
    const sumX = pairs.reduce((sum, pair) => sum + pair[0], 0);
    const sumY = pairs.reduce((sum, pair) => sum + pair[1], 0);
    const sumXY = pairs.reduce((sum, pair) => sum + pair[0] * pair[1], 0);
    const sumX2 = pairs.reduce((sum, pair) => sum + pair[0] * pair[0], 0);
    const sumY2 = pairs.reduce((sum, pair) => sum + pair[1] * pair[1], 0);

    const numerator = n * sumXY - sumX * sumY;
    const denominator = Math.sqrt((n * sumX2 - sumX * sumX) * (n * sumY2 - sumY * sumY));

    return denominator === 0 ? 0 : numerator / denominator;
  };

  // Generar datos de ejemplo
  const generateSampleCorrelationData = () => {
    const aptitudes = ['V', 'E', 'A', 'R', 'N', 'M', 'O'];
    const sampleData = [];

    for (let i = 0; i < aptitudes.length; i++) {
      for (let j = i + 1; j < aptitudes.length; j++) {
        const correlation = (Math.random() - 0.5) * 1.6; // Rango de -0.8 a 0.8
        sampleData.push({
          aptitud_1: aptitudes[i],
          aptitud_2: aptitudes[j],
          correlacion: correlation
        });
      }
    }

    return sampleData;
  };

  const aptitudes = ['V', 'E', 'A', 'R', 'N', 'M', 'O'];
  
  const getCorrelation = (apt1, apt2) => {
    if (apt1 === apt2) return 1;
    const correlation = localData.find(item => 
      (item.aptitud_1 === apt1 && item.aptitud_2 === apt2) ||
      (item.aptitud_1 === apt2 && item.aptitud_2 === apt1)
    );
    return correlation?.correlacion || 0;
  };

  const getColorForCorrelation = (value) => {
    const absValue = Math.abs(value);
    if (absValue >= 0.7) return value > 0 ? 'bg-green-600' : 'bg-red-600';
    if (absValue >= 0.5) return value > 0 ? 'bg-green-400' : 'bg-red-400';
    if (absValue >= 0.3) return value > 0 ? 'bg-green-200' : 'bg-red-200';
    return 'bg-gray-100';
  };

  const getTextColorForCorrelation = (value) => {
    const absValue = Math.abs(value);
    return absValue >= 0.5 ? 'text-white' : 'text-gray-800';
  };

  if (loading || parentLoading) {
    return (
      <Card>
        <CardHeader className="bg-gradient-to-r from-indigo-500 to-indigo-600 text-white">
          <h3 className="text-lg font-semibold flex items-center">
            <FaProjectDiagram className="mr-2" />
            Matriz de Correlación
          </h3>
        </CardHeader>
        <CardBody>
          <div className="animate-pulse h-64 bg-gray-200 rounded"></div>
        </CardBody>
      </Card>
    );
  }

  return (
    <Card>
      <CardHeader className="bg-gradient-to-r from-indigo-500 to-indigo-600 text-white">
        <h3 className="text-lg font-semibold flex items-center">
          <FaProjectDiagram className="mr-2" />
          Matriz de Correlación entre Aptitudes
        </h3>
      </CardHeader>
      <CardBody>
        {error && (
          <div className="mb-4 p-3 bg-yellow-50 border border-yellow-200 rounded-lg">
            <p className="text-yellow-800 text-sm">
              ⚠️ {error} - Mostrando datos de ejemplo
            </p>
          </div>
        )}
        
        {localData && localData.length > 0 ? (
          <>
            <div className="overflow-x-auto mb-6">
              <table className="w-full">
                <thead>
                  <tr>
                    <th className="w-12 h-12"></th>
                    {aptitudes.map(apt => (
                      <th key={apt} className="w-12 h-12 text-center font-bold text-gray-700">
                        {apt}
                      </th>
                    ))}
                  </tr>
                </thead>
                <tbody>
                  {aptitudes.map(apt1 => (
                    <tr key={apt1}>
                      <td className="w-12 h-12 text-center font-bold text-gray-700 bg-gray-50">
                        {apt1}
                      </td>
                      {aptitudes.map(apt2 => {
                        const correlation = getCorrelation(apt1, apt2);
                        return (
                          <td 
                            key={apt2} 
                            className={`w-12 h-12 text-center text-xs font-medium border ${getColorForCorrelation(correlation)} ${getTextColorForCorrelation(correlation)}`}
                            title={`Correlación ${apt1}-${apt2}: ${correlation.toFixed(3)}`}
                          >
                            {correlation.toFixed(2)}
                          </td>
                        );
                      })}
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>

            {/* Leyenda */}
            <div className="grid grid-cols-2 md:grid-cols-4 gap-2 mb-6 text-sm">
              <div className="flex items-center">
                <div className="w-4 h-4 bg-green-600 rounded mr-2"></div>
                <span>Correlación fuerte positiva (≥0.7)</span>
              </div>
              <div className="flex items-center">
                <div className="w-4 h-4 bg-green-400 rounded mr-2"></div>
                <span>Correlación moderada positiva (0.5-0.7)</span>
              </div>
              <div className="flex items-center">
                <div className="w-4 h-4 bg-red-400 rounded mr-2"></div>
                <span>Correlación moderada negativa (-0.5 a -0.7)</span>
              </div>
              <div className="flex items-center">
                <div className="w-4 h-4 bg-gray-100 border rounded mr-2"></div>
                <span>Correlación débil (&lt;0.3)</span>
              </div>
            </div>

            {/* Correlaciones destacadas */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="p-4 bg-green-50 rounded-lg">
                <h4 className="font-medium text-green-800 mb-2 flex items-center">
                  <FaLink className="mr-2" />
                  Correlaciones Más Fuertes
                </h4>
                {localData
                  .filter(item => Math.abs(item.correlacion) >= 0.5)
                  .sort((a, b) => Math.abs(b.correlacion) - Math.abs(a.correlacion))
                  .slice(0, 3)
                  .map((item, index) => (
                    <div key={index} className="text-sm text-green-700">
                      <strong>{item.aptitud_1}-{item.aptitud_2}</strong>: {item.correlacion.toFixed(3)}
                    </div>
                  ))}
              </div>

              <div className="p-4 bg-blue-50 rounded-lg">
                <h4 className="font-medium text-blue-800 mb-2 flex items-center">
                  <FaUnlink className="mr-2" />
                  Aptitudes Independientes
                </h4>
                {localData
                  .filter(item => Math.abs(item.correlacion) < 0.2)
                  .slice(0, 3)
                  .map((item, index) => (
                    <div key={index} className="text-sm text-blue-700">
                      <strong>{item.aptitud_1}-{item.aptitud_2}</strong>: {item.correlacion.toFixed(3)}
                    </div>
                  ))}
              </div>
            </div>

            <div className="mt-6 p-4 bg-indigo-50 rounded-lg">
              <h4 className="font-medium text-indigo-800 mb-2 flex items-center">
                <FaLightbulb className="mr-2" />
                Interpretación
              </h4>
              <p className="text-sm text-indigo-700">
                Las correlaciones fuertes indican aptitudes que tienden a desarrollarse juntas. 
                Las correlaciones débiles sugieren habilidades independientes que requieren enfoques pedagógicos específicos.
              </p>
            </div>
          </>
        ) : (
          <div className="text-center py-8 text-gray-500">
            <FaProjectDiagram className="text-4xl mb-4 mx-auto" />
            <p>No hay datos de correlación disponibles</p>
          </div>
        )}
      </CardBody>
    </Card>
  );
};

export default MatrizCorrelacionEnhanced;
