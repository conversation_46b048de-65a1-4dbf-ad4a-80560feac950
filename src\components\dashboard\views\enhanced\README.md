# Enhanced Analytics Views

Este directorio contiene las vistas mejoradas del dashboard con capacidades analytics avanzadas.

## Estructura

```
enhanced/
├── EnhancedAnalyticsView.jsx     # Vista principal de analytics mejorados
├── PatientProgressView.jsx       # Vista de progreso de pacientes
├── InteractiveChartsView.jsx     # Vista de gráficos interactivos
├── CustomDashboardView.jsx       # Vista de dashboard personalizable
├── ScheduledReportsView.jsx      # Vista de reportes programados
└── SystemMetricsView.jsx         # Vista de métricas del sistema
```

## Integración

Estas vistas se integran con el dashboard principal existente en `/src/pages/admin/Dashboard.jsx` agregándose al array `dashboardViews`.

## Dependencias

- Recharts para visualizaciones
- date-fns para manejo de fechas
- lodash para utilidades
- html2canvas para capturas de pantalla
- jspdf para exportación PDF

## Uso

Cada vista sigue el patrón establecido por las vistas existentes, recibiendo props:
- `data`: Datos del dashboard
- `loading`: Estado de carga
- `filters`: Filtros aplicados