/**
 * Vista de Prueba de Sincronización
 * Componente temporal para verificar que los servicios analytics estén sincronizados
 */

import React, { useState, useEffect } from 'react';
import supabase from '../../../../api/supabaseClient';
import PatientProgressService from '../../../../services/analytics/PatientProgressService';
import IntegratedAnalyticsService from '../../../../services/analytics/IntegratedAnalyticsService';

const SyncTestView = () => {
  const [testResults, setTestResults] = useState(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);

  const runTests = async () => {
    setLoading(true);
    setError(null);

    try {
      console.log('🔧 Iniciando pruebas de sincronización...');

      const results = {
        timestamp: new Date().toISOString(),
        tests: {},
        summary: { total: 0, passed: 0, failed: 0 }
      };

      // Prueba 1: Conexión con Supabase
      try {
        const { data, error } = await supabase
          .from('pacientes')
          .select('count', { count: 'exact', head: true });

        results.tests.supabaseConnection = {
          name: 'Conexión con Supabase',
          success: !error,
          message: error ? error.message : `Conectado. ${data.count || 0} pacientes`,
          details: { patientCount: data.count || 0 }
        };
      } catch (err) {
        results.tests.supabaseConnection = {
          name: 'Conexión con Supabase',
          success: false,
          message: err.message
        };
      }

      // Prueba 2: Datos de resultados
      try {
        const { data, error } = await supabase
          .from('resultados')
          .select('count', { count: 'exact', head: true });

        results.tests.resultsData = {
          name: 'Datos de Resultados',
          success: !error,
          message: error ? error.message : `${data.count || 0} resultados disponibles`,
          details: { resultsCount: data.count || 0 }
        };
      } catch (err) {
        results.tests.resultsData = {
          name: 'Datos de Resultados',
          success: false,
          message: err.message
        };
      }

      // Prueba 3: Servicio de Analytics Integrado
      try {
        const dashboardData = await IntegratedAnalyticsService.getAllDashboardData();

        results.tests.integratedAnalytics = {
          name: 'Servicio Analytics Integrado',
          success: true,
          message: 'Datos del dashboard obtenidos correctamente',
          details: {
            hasGeneralStats: !!dashboardData.estadisticasGenerales,
            hasAdvancedMetrics: !!dashboardData.advancedMetrics,
            lastUpdated: dashboardData.lastUpdated
          }
        };
      } catch (err) {
        results.tests.integratedAnalytics = {
          name: 'Servicio Analytics Integrado',
          success: false,
          message: err.message
        };
      }

      // Calcular resumen
      Object.values(results.tests).forEach(test => {
        results.summary.total++;
        if (test.success) {
          results.summary.passed++;
        } else {
          results.summary.failed++;
        }
      });

      setTestResults(results);
      console.log('✅ Pruebas completadas:', results.summary);

    } catch (err) {
      setError(err.message);
      console.error('❌ Error en pruebas:', err);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    // Ejecutar pruebas automáticamente al cargar
    runTests();
  }, []);

  const getStatusColor = (success, skipped = false) => {
    if (skipped) return 'text-yellow-600 bg-yellow-50';
    return success ? 'text-green-600 bg-green-50' : 'text-red-600 bg-red-50';
  };

  const getStatusIcon = (success, skipped = false) => {
    if (skipped) return '⏭️';
    return success ? '✅' : '❌';
  };

  return (
    <div className="p-6 bg-white rounded-lg shadow-lg">
      <div className="mb-6">
        <h2 className="text-2xl font-bold text-gray-900 mb-2">
          🔧 Pruebas de Sincronización de Analytics
        </h2>
        <p className="text-gray-600">
          Verificación de la sincronización entre los servicios analytics y Supabase
        </p>
      </div>

      <div className="mb-6">
        <button
          onClick={runTests}
          disabled={loading}
          className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:opacity-50 transition-colors"
        >
          {loading ? '🔄 Ejecutando Pruebas...' : '🚀 Ejecutar Pruebas'}
        </button>
      </div>

      {error && (
        <div className="mb-6 p-4 bg-red-50 border-l-4 border-red-400 rounded">
          <div className="flex">
            <div className="flex-shrink-0">
              <span className="text-red-400">❌</span>
            </div>
            <div className="ml-3">
              <p className="text-sm text-red-700">
                <strong>Error:</strong> {error}
              </p>
            </div>
          </div>
        </div>
      )}

      {testResults && (
        <div className="space-y-6">
          {/* Resumen */}
          <div className="bg-gray-50 p-4 rounded-lg">
            <h3 className="text-lg font-semibold mb-3">📋 Resumen de Pruebas</h3>
            <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
              <div className="text-center">
                <div className="text-2xl font-bold text-blue-600">
                  {testResults.summary.total}
                </div>
                <div className="text-sm text-gray-600">Total</div>
              </div>
              <div className="text-center">
                <div className="text-2xl font-bold text-green-600">
                  {testResults.summary.passed}
                </div>
                <div className="text-sm text-gray-600">Exitosas</div>
              </div>
              <div className="text-center">
                <div className="text-2xl font-bold text-red-600">
                  {testResults.summary.failed}
                </div>
                <div className="text-sm text-gray-600">Fallidas</div>
              </div>
              <div className="text-center">
                <div className="text-2xl font-bold text-yellow-600">
                  {testResults.summary.skipped}
                </div>
                <div className="text-sm text-gray-600">Omitidas</div>
              </div>
            </div>
            <div className="mt-4 text-center">
              <div className="text-lg font-semibold">
                Tasa de Éxito: {testResults.summary.successRate}%
              </div>
            </div>
          </div>

          {/* Detalles de Pruebas */}
          <div>
            <h3 className="text-lg font-semibold mb-3">🔍 Detalles de Pruebas</h3>
            <div className="space-y-3">
              {Object.entries(testResults.tests).map(([testName, result]) => (
                <div
                  key={testName}
                  className={`p-4 rounded-lg border ${
                    result.skipped 
                      ? 'border-yellow-200 bg-yellow-50' 
                      : result.success 
                        ? 'border-green-200 bg-green-50' 
                        : 'border-red-200 bg-red-50'
                  }`}
                >
                  <div className="flex items-start justify-between">
                    <div className="flex items-center">
                      <span className="text-xl mr-3">
                        {getStatusIcon(result.success, result.skipped)}
                      </span>
                      <div>
                        <h4 className="font-semibold capitalize">
                          {testName.replace(/([A-Z])/g, ' $1').trim()}
                        </h4>
                        <p className="text-sm text-gray-600">
                          {result.message || 'Sin mensaje'}
                        </p>
                      </div>
                    </div>
                    <span className={`px-2 py-1 rounded text-xs font-medium ${
                      getStatusColor(result.success, result.skipped)
                    }`}>
                      {result.skipped ? 'OMITIDA' : result.success ? 'EXITOSA' : 'FALLIDA'}
                    </span>
                  </div>

                  {result.error && (
                    <div className="mt-3 p-3 bg-red-100 rounded text-sm text-red-700">
                      <strong>Error:</strong> {result.error}
                    </div>
                  )}

                  {result.data && (
                    <div className="mt-3">
                      <details className="cursor-pointer">
                        <summary className="text-sm font-medium text-gray-700 hover:text-gray-900">
                          Ver Datos
                        </summary>
                        <pre className="mt-2 p-3 bg-gray-100 rounded text-xs overflow-auto">
                          {JSON.stringify(result.data, null, 2)}
                        </pre>
                      </details>
                    </div>
                  )}
                </div>
              ))}
            </div>
          </div>

          {/* Información Adicional */}
          <div className="bg-blue-50 p-4 rounded-lg">
            <h3 className="text-lg font-semibold mb-2">ℹ️ Información</h3>
            <ul className="text-sm text-gray-700 space-y-1">
              <li>• Las pruebas verifican la conectividad con Supabase</li>
              <li>• Se valida la sincronización de datos entre servicios</li>
              <li>• Los servicios analytics avanzados se prueban automáticamente</li>
              <li>• Esta vista es temporal y se puede remover en producción</li>
            </ul>
          </div>

          <div className="text-xs text-gray-500 text-center">
            Pruebas ejecutadas el: {testResults.timestamp.toLocaleString()}
          </div>
        </div>
      )}

      {loading && (
        <div className="flex items-center justify-center py-12">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
          <span className="ml-3 text-gray-600">Ejecutando pruebas de sincronización...</span>
        </div>
      )}
    </div>
  );
};

export default SyncTestView;