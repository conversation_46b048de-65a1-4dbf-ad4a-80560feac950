/**
 * @file DiagnosticoReports.jsx
 * @description Componente de diagnóstico para verificar el estado de los datos
 */

import React, { useState, useEffect } from 'react';
import { Card, CardHeader, CardBody } from '../ui/Card';
import { Button } from '../ui/Button';
import supabase from '../../api/supabaseClient';

const DiagnosticoReports = () => {
  const [diagnostico, setDiagnostico] = useState({
    pacientes: 0,
    resultados: 0,
    aptitudes: 0,
    informes: 0,
    conexion: false,
    usuario: null,
    errores: []
  });
  const [loading, setLoading] = useState(false);

  const ejecutarDiagnostico = async () => {
    setLoading(true);
    const errores = [];
    const resultado = {
      pacientes: 0,
      resultados: 0,
      aptitudes: 0,
      informes: 0,
      conexion: false,
      usuario: null,
      errores: []
    };

    try {
      // Verificar conexión
      const { data: conexionTest, error: conexionError } = await supabase
        .from('pacientes')
        .select('count', { count: 'exact', head: true });
      
      if (conexionError) {
        errores.push(`Error de conexión: ${conexionError.message}`);
      } else {
        resultado.conexion = true;
        resultado.pacientes = conexionTest || 0;
      }

      // Verificar usuario actual
      const { data: { user }, error: userError } = await supabase.auth.getUser();
      if (userError) {
        errores.push(`Error de autenticación: ${userError.message}`);
      } else {
        resultado.usuario = user;
      }

      // Contar resultados
      const { count: resultadosCount, error: resultadosError } = await supabase
        .from('resultados')
        .select('*', { count: 'exact', head: true });
      
      if (resultadosError) {
        errores.push(`Error contando resultados: ${resultadosError.message}`);
      } else {
        resultado.resultados = resultadosCount || 0;
      }

      // Contar aptitudes
      const { count: aptitudesCount, error: aptitudesError } = await supabase
        .from('aptitudes')
        .select('*', { count: 'exact', head: true });
      
      if (aptitudesError) {
        errores.push(`Error contando aptitudes: ${aptitudesError.message}`);
      } else {
        resultado.aptitudes = aptitudesCount || 0;
      }

      // Contar informes generados
      const { count: informesCount, error: informesError } = await supabase
        .from('informes_generados')
        .select('*', { count: 'exact', head: true });
      
      if (informesError) {
        errores.push(`Error contando informes: ${informesError.message}`);
      } else {
        resultado.informes = informesCount || 0;
      }

      resultado.errores = errores;
      setDiagnostico(resultado);

    } catch (error) {
      errores.push(`Error general: ${error.message}`);
      resultado.errores = errores;
      setDiagnostico(resultado);
    } finally {
      setLoading(false);
    }
  };

  const probarConsultaCompleta = async () => {
    try {
      console.log('🔍 Probando consulta completa...');
      
      const { data: resultados, error } = await supabase
        .from('resultados')
        .select(`
          id,
          puntaje_directo,
          percentil,
          errores,
          tiempo_segundos,
          concentracion,
          created_at,
          pacientes:paciente_id (
            id,
            nombre,
            apellido,
            documento,
            genero
          ),
          aptitudes:aptitud_id (
            codigo,
            nombre,
            descripcion
          )
        `)
        .limit(5);

      if (error) {
        console.error('❌ Error en consulta completa:', error);
      } else {
        console.log('✅ Consulta completa exitosa:', resultados);
      }
    } catch (error) {
      console.error('❌ Error general en consulta:', error);
    }
  };

  useEffect(() => {
    ejecutarDiagnostico();
  }, []);

  return (
    <Card className="mb-6 border-l-4 border-yellow-500">
      <CardHeader className="bg-yellow-50">
        <div className="flex items-center justify-between">
          <h3 className="text-lg font-semibold text-yellow-800">
            🔧 Diagnóstico del Sistema
          </h3>
          <div className="flex gap-2">
            <Button
              onClick={ejecutarDiagnostico}
              disabled={loading}
              size="sm"
              className="bg-yellow-600 text-white hover:bg-yellow-700"
            >
              {loading ? 'Diagnosticando...' : 'Actualizar'}
            </Button>
            <Button
              onClick={probarConsultaCompleta}
              size="sm"
              className="bg-blue-600 text-white hover:bg-blue-700"
            >
              Probar Consulta
            </Button>
          </div>
        </div>
      </CardHeader>
      <CardBody>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 mb-6">
          {/* Conexión */}
          <div className={`p-4 rounded-lg ${diagnostico.conexion ? 'bg-green-50 border border-green-200' : 'bg-red-50 border border-red-200'}`}>
            <div className="flex items-center">
              <i className={`fas fa-wifi text-2xl mr-3 ${diagnostico.conexion ? 'text-green-600' : 'text-red-600'}`}></i>
              <div>
                <p className="text-sm text-gray-600">Conexión</p>
                <p className={`font-bold ${diagnostico.conexion ? 'text-green-600' : 'text-red-600'}`}>
                  {diagnostico.conexion ? 'Conectado' : 'Desconectado'}
                </p>
              </div>
            </div>
          </div>

          {/* Pacientes */}
          <div className="p-4 bg-blue-50 border border-blue-200 rounded-lg">
            <div className="flex items-center">
              <i className="fas fa-users text-2xl text-blue-600 mr-3"></i>
              <div>
                <p className="text-sm text-gray-600">Pacientes</p>
                <p className="text-2xl font-bold text-blue-600">{diagnostico.pacientes}</p>
              </div>
            </div>
          </div>

          {/* Resultados */}
          <div className="p-4 bg-purple-50 border border-purple-200 rounded-lg">
            <div className="flex items-center">
              <i className="fas fa-chart-line text-2xl text-purple-600 mr-3"></i>
              <div>
                <p className="text-sm text-gray-600">Resultados</p>
                <p className="text-2xl font-bold text-purple-600">{diagnostico.resultados}</p>
              </div>
            </div>
          </div>

          {/* Informes */}
          <div className="p-4 bg-orange-50 border border-orange-200 rounded-lg">
            <div className="flex items-center">
              <i className="fas fa-file-alt text-2xl text-orange-600 mr-3"></i>
              <div>
                <p className="text-sm text-gray-600">Informes</p>
                <p className="text-2xl font-bold text-orange-600">{diagnostico.informes}</p>
              </div>
            </div>
          </div>
        </div>

        {/* Usuario actual */}
        {diagnostico.usuario && (
          <div className="mb-4 p-3 bg-gray-50 rounded-lg">
            <h4 className="font-semibold text-gray-800 mb-2">👤 Usuario Actual:</h4>
            <div className="text-sm text-gray-600">
              <p><strong>Email:</strong> {diagnostico.usuario.email}</p>
              <p><strong>ID:</strong> {diagnostico.usuario.id}</p>
              <p><strong>Rol:</strong> {diagnostico.usuario.user_metadata?.tipo_usuario || 'No definido'}</p>
            </div>
          </div>
        )}

        {/* Errores */}
        {diagnostico.errores.length > 0 && (
          <div className="p-3 bg-red-50 border border-red-200 rounded-lg">
            <h4 className="font-semibold text-red-800 mb-2">❌ Errores Detectados:</h4>
            <ul className="text-sm text-red-700 space-y-1">
              {diagnostico.errores.map((error, index) => (
                <li key={index}>• {error}</li>
              ))}
            </ul>
          </div>
        )}

        {/* Estado general */}
        <div className="mt-4 p-3 bg-gray-50 rounded-lg">
          <h4 className="font-semibold text-gray-800 mb-2">📊 Estado General:</h4>
          <div className="text-sm text-gray-600 space-y-1">
            <p>• <strong>Base de datos:</strong> {diagnostico.conexion ? '✅ Conectada' : '❌ Sin conexión'}</p>
            <p>• <strong>Datos disponibles:</strong> {diagnostico.resultados > 0 ? '✅ Hay resultados' : '❌ Sin resultados'}</p>
            <p>• <strong>Autenticación:</strong> {diagnostico.usuario ? '✅ Usuario logueado' : '❌ Sin usuario'}</p>
            <p>• <strong>Informes:</strong> {diagnostico.informes > 0 ? `✅ ${diagnostico.informes} informes generados` : '⚠️ Sin informes'}</p>
          </div>
        </div>

        <div className="mt-4 text-xs text-gray-500">
          <p>💡 <strong>Tip:</strong> Abre la consola del navegador (F12) para ver logs detallados de la carga de datos.</p>
        </div>
      </CardBody>
    </Card>
  );
};

export default DiagnosticoReports;
