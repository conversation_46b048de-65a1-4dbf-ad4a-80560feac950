import React, { useState, useEffect, useRef } from 'react';

/**
 * Componente de monitoreo de performance para debugging
 * Muestra métricas en tiempo real de la aplicación
 */
const PerformanceMonitor = ({ enabled = false, position = 'bottom-right' }) => {
  const [metrics, setMetrics] = useState({
    renderCount: 0,
    lastRenderTime: 0,
    avgRenderTime: 0,
    memoryUsage: 0,
    connectionStatus: 'unknown'
  });

  const renderCountRef = useRef(0);
  const renderTimesRef = useRef([]);
  const startTimeRef = useRef(performance.now());

  // Incrementar contador de renders (SIN useEffect para evitar bucle infinito)
  renderCountRef.current += 1;
  const currentRenderTime = performance.now() - startTimeRef.current;

  // Solo actualizar métricas cada 10 renders para evitar bucles
  if (renderCountRef.current % 10 === 0) {
    renderTimesRef.current.push(currentRenderTime);

    // Mantener solo los últimos 10 renders para el promedio
    if (renderTimesRef.current.length > 10) {
      renderTimesRef.current.shift();
    }

    const avgTime = renderTimesRef.current.reduce((a, b) => a + b, 0) / renderTimesRef.current.length;

    // Usar setTimeout para evitar setState durante render
    setTimeout(() => {
      setMetrics(prev => ({
        ...prev,
        renderCount: renderCountRef.current,
        lastRenderTime: currentRenderTime,
        avgRenderTime: avgTime
      }));
    }, 0);
  }

  startTimeRef.current = performance.now();

  // Monitorear memoria y conexión
  useEffect(() => {
    const interval = setInterval(() => {
      // Memoria (si está disponible)
      const memory = performance.memory ? {
        used: Math.round(performance.memory.usedJSHeapSize / 1048576), // MB
        total: Math.round(performance.memory.totalJSHeapSize / 1048576) // MB
      } : null;

      // Estado de conexión
      const connectionStatus = navigator.onLine ? 'online' : 'offline';

      setMetrics(prev => ({
        ...prev,
        memoryUsage: memory,
        connectionStatus
      }));
    }, 1000);

    return () => clearInterval(interval);
  }, []);

  if (!enabled) return null;

  const positionClasses = {
    'top-left': 'top-4 left-4',
    'top-right': 'top-4 right-4',
    'bottom-left': 'bottom-4 left-4',
    'bottom-right': 'bottom-4 right-4'
  };

  return (
    <div className={`fixed ${positionClasses[position]} z-50 bg-black bg-opacity-80 text-white p-3 rounded-lg text-xs font-mono max-w-xs`}>
      <div className="mb-2 font-bold text-yellow-400">🔧 Performance Monitor</div>
      
      <div className="space-y-1">
        <div className="flex justify-between">
          <span>Renders:</span>
          <span className="text-green-400">{metrics.renderCount}</span>
        </div>
        
        <div className="flex justify-between">
          <span>Last Render:</span>
          <span className={`${metrics.lastRenderTime > 16 ? 'text-red-400' : 'text-green-400'}`}>
            {metrics.lastRenderTime.toFixed(2)}ms
          </span>
        </div>
        
        <div className="flex justify-between">
          <span>Avg Render:</span>
          <span className={`${metrics.avgRenderTime > 16 ? 'text-red-400' : 'text-green-400'}`}>
            {metrics.avgRenderTime.toFixed(2)}ms
          </span>
        </div>
        
        {metrics.memoryUsage && (
          <div className="flex justify-between">
            <span>Memory:</span>
            <span className="text-blue-400">
              {metrics.memoryUsage.used}/{metrics.memoryUsage.total}MB
            </span>
          </div>
        )}
        
        <div className="flex justify-between">
          <span>Connection:</span>
          <span className={`${metrics.connectionStatus === 'online' ? 'text-green-400' : 'text-red-400'}`}>
            {metrics.connectionStatus}
          </span>
        </div>
      </div>
      
      {/* Indicadores visuales */}
      <div className="mt-2 flex space-x-1">
        <div 
          className={`w-2 h-2 rounded-full ${metrics.lastRenderTime > 16 ? 'bg-red-400' : 'bg-green-400'}`}
          title="Render Performance"
        />
        <div 
          className={`w-2 h-2 rounded-full ${metrics.connectionStatus === 'online' ? 'bg-green-400' : 'bg-red-400'}`}
          title="Connection Status"
        />
        <div 
          className={`w-2 h-2 rounded-full ${metrics.memoryUsage && metrics.memoryUsage.used > 100 ? 'bg-yellow-400' : 'bg-green-400'}`}
          title="Memory Usage"
        />
      </div>
    </div>
  );
};

export default PerformanceMonitor;
