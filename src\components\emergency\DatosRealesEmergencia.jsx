/**
 * @file DatosRealesEmergencia.jsx
 * @description COMPONENTE DE EMERGENCIA - Acceso directo a datos reales de la tabla resultados
 */

import React, { useState, useEffect } from 'react';
import { Card, CardHeader, CardBody } from '../ui/Card';
import { Button } from '../ui/Button';
import supabase from '../../api/supabaseClient';
import InformesService from '../../services/InformesService';

const DatosRealesEmergencia = () => {
  const [pacientesReales, setPacientesReales] = useState([]);
  const [loading, setLoading] = useState(true);
  const [generandoInforme, setGenerandoInforme] = useState(null);
  const [generandoTodos, setGenerandoTodos] = useState(false);

  useEffect(() => {
    cargarDatosRealesDirectos();
  }, []);

  const cargarDatosRealesDirectos = async () => {
    try {
      setLoading(true);
      console.log('🚨 [DatosRealesEmergencia] Acceso directo a tabla resultados...');

      // Acceso directo a la tabla resultados - TODOS los pacientes con datos reales
      const { data: resultados, error } = await supabase
        .from('resultados')
        .select(`
          id,
          puntaje_directo,
          percentil,
          errores,
          tiempo_segundos,
          concentracion,
          created_at,
          pacientes:paciente_id (
            id,
            nombre,
            apellido,
            documento,
            genero,
            nivel_educativo,
            fecha_nacimiento,
            ocupacion,
            estado_civil
          ),
          aptitudes:aptitud_id (
            codigo,
            nombre,
            descripcion
          )
        `)
        .not('puntaje_directo', 'is', null)
        .not('percentil', 'is', null)
        .not('pacientes.nombre', 'is', null)
        .order('created_at', { ascending: false });

      if (error) throw error;

      console.log('✅ [DatosRealesEmergencia] Datos reales obtenidos:', resultados.length);

      // Agrupar por paciente
      const pacientesAgrupados = {};
      resultados.forEach(resultado => {
        const pacienteId = resultado.pacientes.id;
        if (!pacientesAgrupados[pacienteId]) {
          pacientesAgrupados[pacienteId] = {
            paciente: resultado.pacientes,
            resultados: [],
            totalTests: 0,
            promedioPC: 0,
            promedioPD: 0,
            aptitudesAltas: 0,
            aptitudesBajas: 0,
            fechaUltima: resultado.created_at
          };
        }

        pacientesAgrupados[pacienteId].resultados.push(resultado);
        pacientesAgrupados[pacienteId].totalTests++;
        
        if (resultado.percentil >= 75) pacientesAgrupados[pacienteId].aptitudesAltas++;
        if (resultado.percentil <= 25) pacientesAgrupados[pacienteId].aptitudesBajas++;
      });

      // Calcular promedios
      Object.values(pacientesAgrupados).forEach(pacienteData => {
        const percentiles = pacienteData.resultados.map(r => r.percentil);
        const puntajesDirectos = pacienteData.resultados.map(r => r.puntaje_directo);
        
        pacienteData.promedioPC = Math.round(percentiles.reduce((sum, pc) => sum + pc, 0) / percentiles.length);
        pacienteData.promedioPD = Math.round(puntajesDirectos.reduce((sum, pd) => sum + pd, 0) / puntajesDirectos.length);
      });

      const pacientesArray = Object.values(pacientesAgrupados).sort((a, b) => 
        new Date(b.fechaUltima) - new Date(a.fechaUltima)
      );

      setPacientesReales(pacientesArray);
      console.log('🎉 [DatosRealesEmergencia] Pacientes procesados:', pacientesArray.length);

    } catch (error) {
      console.error('❌ [DatosRealesEmergencia] Error:', error);
    } finally {
      setLoading(false);
    }
  };

  const generarInformeEmergencia = async (pacienteId, nombreCompleto) => {
    try {
      setGenerandoInforme(pacienteId);
      console.log('🚨 [DatosRealesEmergencia] Generando informe de emergencia para:', nombreCompleto);

      await InformesService.generarInformeAutomaticoConDatosReales(pacienteId);

      // Recargar datos
      await cargarDatosRealesDirectos();

    } catch (error) {
      console.error('❌ Error generando informe:', error);
      alert('Error generando informe: ' + error.message);
    } finally {
      setGenerandoInforme(null);
    }
  };

  const generarTodosLosInformes = async () => {
    try {
      setGenerandoTodos(true);
      console.log('🚨 [DatosRealesEmergencia] Generando informes para todos los pacientes...');

      for (const pacienteData of pacientesReales) {
        const paciente = pacienteData.paciente;
        console.log(`Generando informe para: ${paciente.nombre} ${paciente.apellido}`);

        try {
          await InformesService.generarInformeAutomaticoConDatosReales(paciente.id);
        } catch (error) {
          console.error(`Error generando informe para ${paciente.nombre}:`, error);
        }
      }

      alert(`✅ Informes generados para ${pacientesReales.length} pacientes`);
      await cargarDatosRealesDirectos();

    } catch (error) {
      console.error('❌ Error generando todos los informes:', error);
      alert('Error generando informes: ' + error.message);
    } finally {
      setGenerandoTodos(false);
    }
  };

  if (loading) {
    return (
      <Card className="mb-6">
        <CardBody className="text-center py-8">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-red-600 mx-auto mb-4"></div>
          <p className="text-red-600 font-semibold">🚨 CARGANDO DATOS REALES DIRECTAMENTE...</p>
        </CardBody>
      </Card>
    );
  }

  return (
    <Card className="mb-6 border-2 border-red-500">
      <CardHeader className="bg-red-50 border-b border-red-200">
        <h2 className="text-xl font-bold text-red-800">
          🚨 ACCESO DIRECTO A DATOS REALES - MÉTODO DE EMERGENCIA
        </h2>
        <p className="text-sm text-red-600 mt-1">
          Datos extraídos directamente de la tabla `resultados` sin funciones RPC
        </p>
      </CardHeader>
      <CardBody>
        <div className="mb-4 bg-yellow-50 border border-yellow-200 p-4 rounded-lg">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-yellow-800 font-semibold">
                ✅ {pacientesReales.length} pacientes encontrados con datos reales
              </p>
              <p className="text-sm text-yellow-700 mt-1">
                Total de evaluaciones: {pacientesReales.reduce((sum, p) => sum + p.totalTests, 0)}
              </p>
            </div>
            <Button
              onClick={generarTodosLosInformes}
              disabled={generandoTodos}
              className="bg-green-600 text-white hover:bg-green-700"
            >
              {generandoTodos ? (
                <>
                  <i className="fas fa-spinner fa-spin mr-2"></i>
                  Generando Todos...
                </>
              ) : (
                <>
                  <i className="fas fa-magic mr-2"></i>
                  Generar Todos los Informes
                </>
              )}
            </Button>
          </div>
        </div>

        <div className="space-y-4">
          {pacientesReales.map((pacienteData, index) => {
            const paciente = pacienteData.paciente;
            const isFemale = paciente.genero === 'femenino';
            
            return (
              <div key={paciente.id} className={`p-4 rounded-lg border-2 ${
                isFemale ? 'border-pink-200 bg-pink-50' : 'border-blue-200 bg-blue-50'
              }`}>
                <div className="flex items-center justify-between mb-3">
                  <div className="flex items-center">
                    <div className={`w-12 h-12 rounded-full flex items-center justify-center mr-4 ${
                      isFemale ? 'bg-pink-200' : 'bg-blue-200'
                    }`}>
                      <i className={`fas ${isFemale ? 'fa-venus text-pink-600' : 'fa-mars text-blue-600'} text-xl`}></i>
                    </div>
                    <div>
                      <h3 className="text-lg font-bold text-gray-900">
                        {paciente.nombre} {paciente.apellido}
                      </h3>
                      <p className="text-sm text-gray-600">
                        Doc: {paciente.documento} • {paciente.nivel_educativo}
                      </p>
                    </div>
                  </div>
                  
                  <Button
                    onClick={() => generarInformeEmergencia(paciente.id, `${paciente.nombre} ${paciente.apellido}`)}
                    disabled={generandoInforme === paciente.id}
                    className="bg-red-600 text-white hover:bg-red-700"
                  >
                    {generandoInforme === paciente.id ? (
                      <>
                        <i className="fas fa-spinner fa-spin mr-2"></i>
                        Generando...
                      </>
                    ) : (
                      <>
                        <i className="fas fa-file-medical mr-2"></i>
                        Generar Informe
                      </>
                    )}
                  </Button>
                </div>

                <div className="grid grid-cols-2 md:grid-cols-5 gap-4 text-sm">
                  <div className="text-center p-2 bg-white rounded border">
                    <div className="font-bold text-blue-600">{pacienteData.totalTests}</div>
                    <div className="text-gray-600">Tests</div>
                  </div>
                  <div className="text-center p-2 bg-white rounded border">
                    <div className="font-bold text-green-600">{pacienteData.promedioPC}</div>
                    <div className="text-gray-600">PC Prom</div>
                  </div>
                  <div className="text-center p-2 bg-white rounded border">
                    <div className="font-bold text-purple-600">{pacienteData.promedioPD}</div>
                    <div className="text-gray-600">PD Prom</div>
                  </div>
                  <div className="text-center p-2 bg-white rounded border">
                    <div className="font-bold text-yellow-600">{pacienteData.aptitudesAltas}</div>
                    <div className="text-gray-600">Altas</div>
                  </div>
                  <div className="text-center p-2 bg-white rounded border">
                    <div className="font-bold text-orange-600">{pacienteData.aptitudesBajas}</div>
                    <div className="text-gray-600">Bajas</div>
                  </div>
                </div>

                <div className="mt-3 text-xs text-gray-500">
                  Aptitudes evaluadas: {pacienteData.resultados.map(r => r.aptitudes.codigo).join(', ')}
                </div>
              </div>
            );
          })}
        </div>

        <div className="mt-6 p-4 bg-green-50 border border-green-200 rounded-lg">
          <p className="text-green-800 font-semibold">
            🎉 DATOS REALES CONFIRMADOS
          </p>
          <p className="text-sm text-green-700 mt-1">
            Todos los datos mostrados provienen directamente de la tabla `resultados` con puntajes PD y percentiles PC reales.
          </p>
        </div>
      </CardBody>
    </Card>
  );
};

export default DatosRealesEmergencia;
