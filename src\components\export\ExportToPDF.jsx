import React from 'react';
import { toast } from 'react-toastify';

/**
 * Componente para exportar resultados a PDF
 * Utiliza la API de impresión del navegador para generar PDFs
 */
const ExportToPDF = ({ data, filename = 'resultados-bat7', disabled = false }) => {
  
  const generatePDFContent = (results) => {
    const currentDate = new Date().toLocaleDateString('es-ES', {
      year: 'numeric',
      month: 'long',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });

    let html = `
      <!DOCTYPE html>
      <html>
      <head>
        <meta charset="UTF-8">
        <title>Reporte BAT-7 - Resultados de Evaluaciones</title>
        <style>
          body {
            font-family: Arial, sans-serif;
            margin: 20px;
            color: #333;
            line-height: 1.4;
          }
          .header {
            text-align: center;
            border-bottom: 3px solid #2563eb;
            padding-bottom: 20px;
            margin-bottom: 30px;
          }
          .header h1 {
            color: #dc2626;
            margin: 0;
            font-size: 28px;
          }
          .header h2 {
            color: #2563eb;
            margin: 5px 0;
            font-size: 20px;
          }
          .header .date {
            color: #666;
            font-size: 14px;
            margin-top: 10px;
          }
          .patient-section {
            margin-bottom: 40px;
            page-break-inside: avoid;
          }
          .patient-header {
            background: linear-gradient(135deg, #2563eb, #3b82f6);
            color: white;
            padding: 15px;
            border-radius: 8px;
            margin-bottom: 15px;
          }
          .patient-info {
            display: flex;
            justify-content: space-between;
            align-items: center;
          }
          .patient-name {
            font-size: 18px;
            font-weight: bold;
          }
          .patient-details {
            font-size: 14px;
            opacity: 0.9;
          }
          .results-table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 10px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
          }
          .results-table th {
            background-color: #f8fafc;
            border: 1px solid #e2e8f0;
            padding: 12px 8px;
            text-align: center;
            font-weight: bold;
            color: #374151;
            font-size: 12px;
          }
          .results-table td {
            border: 1px solid #e2e8f0;
            padding: 10px 8px;
            text-align: center;
            font-size: 11px;
          }
          .results-table tr:nth-child(even) {
            background-color: #f9fafb;
          }
          .test-code {
            font-weight: bold;
            color: #2563eb;
          }
          .score-pd {
            background-color: #fef3c7;
            color: #d97706;
            padding: 4px 8px;
            border-radius: 4px;
            font-weight: bold;
          }
          .score-pc {
            background-color: #dbeafe;
            color: #2563eb;
            padding: 4px 8px;
            border-radius: 4px;
            font-weight: bold;
          }
          .interpretation {
            font-size: 10px;
            padding: 2px 6px;
            border-radius: 3px;
            font-weight: bold;
          }
          .interpretation.superior { background-color: #dcfce7; color: #166534; }
          .interpretation.alto { background-color: #dbeafe; color: #1d4ed8; }
          .interpretation.medio { background-color: #fef3c7; color: #d97706; }
          .interpretation.bajo { background-color: #fee2e2; color: #dc2626; }
          .no-results {
            text-align: center;
            color: #6b7280;
            font-style: italic;
            padding: 20px;
          }
          .summary {
            background-color: #f0f9ff;
            border: 1px solid #0ea5e9;
            border-radius: 8px;
            padding: 15px;
            margin-top: 30px;
          }
          .summary h3 {
            color: #0369a1;
            margin-top: 0;
          }
          .footer {
            margin-top: 40px;
            text-align: center;
            font-size: 12px;
            color: #6b7280;
            border-top: 1px solid #e5e7eb;
            padding-top: 20px;
          }
          @media print {
            body { margin: 0; }
            .patient-section { page-break-inside: avoid; }
          }
        </style>
      </head>
      <body>
        <div class="header">
          <h1>BAT-7</h1>
          <h2>Batería de Aptitudes - Reporte de Resultados</h2>
          <div class="date">Generado el ${currentDate}</div>
        </div>
    `;

    results.forEach(patientGroup => {
      const patient = patientGroup.paciente;
      const hasResults = patientGroup.resultados.length > 0;

      html += `
        <div class="patient-section">
          <div class="patient-header">
            <div class="patient-info">
              <div>
                <div class="patient-name">${patient.nombre} ${patient.apellido}</div>
                <div class="patient-details">
                  ${patient.documento ? `Documento: ${patient.documento}` : ''}
                  ${patient.genero ? ` • Género: ${patient.genero}` : ''}
                </div>
              </div>
              <div class="patient-details">
                ${hasResults ? `${patientGroup.resultados.length} evaluación(es)` : 'Sin evaluaciones'}
              </div>
            </div>
          </div>
      `;

      if (hasResults) {
        html += `
          <table class="results-table">
            <thead>
              <tr>
                <th>Test</th>
                <th>Puntaje Directo (PD)</th>
                <th>Percentil (PC)</th>
                <th>Interpretación</th>
                <th>Errores</th>
                <th>Tiempo</th>
                <th>Fecha</th>
              </tr>
            </thead>
            <tbody>
        `;

        patientGroup.resultados.forEach(result => {
          const interpretationClass = result.interpretacion.toLowerCase().replace(' ', '');
          html += `
            <tr>
              <td>
                <div class="test-code">${result.test}</div>
                <div style="font-size: 10px; color: #6b7280;">${result.testName}</div>
              </td>
              <td><span class="score-pd">${result.puntajePD}</span></td>
              <td>
                ${result.puntajePC !== 'N/A' ? `<span class="score-pc">${result.puntajePC}</span>` : 'Pendiente'}
              </td>
              <td>
                ${result.interpretacion !== 'Pendiente' ? 
                  `<span class="interpretation ${interpretationClass}">${result.interpretacion}</span>` : 
                  'Pendiente'
                }
              </td>
              <td>${result.errores}</td>
              <td>${result.tiempo}</td>
              <td>${result.fecha}</td>
            </tr>
          `;
        });

        html += `
            </tbody>
          </table>
        `;
      } else {
        html += `
          <div class="no-results">
            Este paciente aún no ha completado ninguna evaluación.
          </div>
        `;
      }

      html += `</div>`;
    });

    // Resumen general
    const totalPatients = results.length;
    const totalResults = results.reduce((sum, p) => sum + p.resultados.length, 0);
    const patientsWithResults = results.filter(p => p.resultados.length > 0).length;

    html += `
      <div class="summary">
        <h3>Resumen General</h3>
        <p><strong>Total de pacientes:</strong> ${totalPatients}</p>
        <p><strong>Pacientes con evaluaciones:</strong> ${patientsWithResults}</p>
        <p><strong>Total de evaluaciones realizadas:</strong> ${totalResults}</p>
        <p><strong>Promedio de evaluaciones por paciente:</strong> ${totalPatients > 0 ? (totalResults / totalPatients).toFixed(1) : 0}</p>
      </div>

      <div class="footer">
        <p>Sistema BAT-7 - Batería de Aptitudes</p>
        <p>Reporte generado automáticamente el ${currentDate}</p>
      </div>

      </body>
      </html>
    `;

    return html;
  };

  const handleExport = () => {
    if (!data || data.length === 0) {
      toast.error('No hay datos para exportar');
      return;
    }

    try {
      // Generar contenido HTML
      const htmlContent = generatePDFContent(data);
      
      // Crear ventana para impresión
      const printWindow = window.open('', '_blank');
      printWindow.document.write(htmlContent);
      printWindow.document.close();
      
      // Configurar impresión
      printWindow.onload = () => {
        printWindow.focus();
        printWindow.print();
        
        // Cerrar ventana después de imprimir
        printWindow.onafterprint = () => {
          printWindow.close();
        };
      };

      toast.success('Generando PDF... Se abrirá el diálogo de impresión');
      
    } catch (error) {
      console.error('Error al generar PDF:', error);
      toast.error('Error al generar el PDF');
    }
  };

  return (
    <button
      onClick={handleExport}
      disabled={disabled || !data || data.length === 0}
      className={`inline-flex items-center px-4 py-2 rounded-lg text-sm font-medium transition-colors ${
        disabled || !data || data.length === 0
          ? 'bg-gray-100 text-gray-400 cursor-not-allowed'
          : 'bg-red-500 text-white hover:bg-red-600 shadow-md hover:shadow-lg'
      }`}
      title="Exportar resultados a PDF"
    >
      <i className="fas fa-file-pdf mr-2"></i>
      Exportar PDF
    </button>
  );
};

export default ExportToPDF;
