/**
 * @file InformesFaltantesGenerados.jsx
 * @description Componente para mostrar los 5 informes faltantes que se acaban de generar
 */

import React, { useState, useEffect } from 'react';
import { Card, CardHeader, CardBody } from '../ui/Card';
import { Button } from '../ui/Button';
import supabase from '../../api/supabaseClient';
import InformeViewer from '../reports/InformeViewer';
import GraficoResultados from '../graficos/GraficoResultados';

const InformesFaltantesGenerados = () => {
  const [informesFaltantes, setInformesFaltantes] = useState([]);
  const [loading, setLoading] = useState(true);
  const [informeViendose, setInformeViendose] = useState(null);
  const [expandedPatients, setExpandedPatients] = useState(new Set());
  const [graficoViendose, setGraficoViendose] = useState(null);

  // IDs de los pacientes que faltaban
  const pacientesFaltantes = [
    '65c27c22-b667-42b0-9151-2068b3f655da', // Mariana
    '47399632-0d7f-4a8e-b2c1-11d51e022b09', // Camila
    '57b9aa9d-51b9-4d4a-bf90-b50ac94b2846', // María
    '41836cc4-94a6-4d9a-b0f7-b7eec7c2f5a0', // Ana Sofia
    'e1081a6e-ebbf-4d11-a322-ddfa022a1f89'  // Henry
  ];

  useEffect(() => {
    cargarInformesFaltantes();
  }, []);

  const cargarInformesFaltantes = async () => {
    try {
      setLoading(true);
      console.log('📋 [InformesFaltantes] Cargando informes con datos detallados...');

      // Obtener informes con contenido completo
      const { data: informes, error: errorInformes } = await supabase
        .from('informes_generados')
        .select(`
          id,
          titulo,
          descripcion,
          fecha_generacion,
          metadatos,
          contenido,
          pacientes:paciente_id (
            id,
            nombre,
            apellido,
            documento,
            genero
          )
        `)
        .in('paciente_id', pacientesFaltantes)
        .eq('tipo_informe', 'completo')
        .eq('estado', 'generado')
        .gte('fecha_generacion', '2025-08-01 23:45:00')
        .order('fecha_generacion', { ascending: false });

      if (errorInformes) throw errorInformes;

      // Obtener resultados detallados para cada paciente
      const informesConDetalles = await Promise.all(
        (informes || []).map(async (informe) => {
          const { data: resultados, error: errorResultados } = await supabase
            .from('resultados')
            .select(`
              id,
              puntaje_directo,
              percentil,
              errores,
              tiempo_segundos,
              concentracion,
              created_at,
              aptitudes:aptitud_id (
                codigo,
                nombre,
                descripcion
              )
            `)
            .eq('paciente_id', informe.pacientes.id)
            .not('puntaje_directo', 'is', null)
            .not('percentil', 'is', null)
            .order('created_at', { ascending: false });

          if (errorResultados) {
            console.error('Error obteniendo resultados para', informe.pacientes.nombre, errorResultados);
            return { ...informe, resultados: [] };
          }

          // Calcular estadísticas detalladas
          const estadisticas = {
            totalTests: resultados.length,
            promedioPC: Math.round(resultados.reduce((sum, r) => sum + r.percentil, 0) / resultados.length),
            promedioPD: Math.round(resultados.reduce((sum, r) => sum + r.puntaje_directo, 0) / resultados.length),
            aptitudesAltas: resultados.filter(r => r.percentil >= 75).length,
            aptitudesBajas: resultados.filter(r => r.percentil <= 25).length,
            totalErrores: resultados.reduce((sum, r) => sum + (r.errores || 0), 0),
            aptitudesEvaluadas: [...new Set(resultados.map(r => r.aptitudes.codigo))],
            aptitudMasAlta: resultados.reduce((max, r) => r.percentil > max.percentil ? r : max, resultados[0] || {}),
            aptitudMasBaja: resultados.reduce((min, r) => r.percentil < min.percentil ? r : min, resultados[0] || {})
          };

          return {
            ...informe,
            resultados,
            estadisticas
          };
        })
      );

      console.log('✅ [InformesFaltantes] Informes con detalles cargados:', informesConDetalles.length);
      setInformesFaltantes(informesConDetalles);

    } catch (error) {
      console.error('❌ [InformesFaltantes] Error:', error);
    } finally {
      setLoading(false);
    }
  };

  const verInforme = (informeId) => {
    setInformeViendose(informeId);
  };

  const copiarId = (id, nombre) => {
    navigator.clipboard.writeText(id);
    alert(`ID copiado: ${id}\nPaciente: ${nombre}`);
  };

  const togglePatientExpansion = (pacienteId) => {
    const newExpanded = new Set(expandedPatients);
    if (newExpanded.has(pacienteId)) {
      newExpanded.delete(pacienteId);
    } else {
      newExpanded.add(pacienteId);
    }
    setExpandedPatients(newExpanded);
  };

  const formatTiempo = (segundos) => {
    if (!segundos) return 'N/A';
    const minutos = Math.floor(segundos / 60);
    const segs = segundos % 60;
    return `${minutos}:${String(segs).padStart(2, '0')}`;
  };

  // Función para obtener nivel aptitudinal según nueva escala
  const getNivelAptitudinal = (percentil) => {
    if (percentil >= 98) return {
      nivel: 'Muy alto',
      text: 'text-purple-700',
      bg: 'bg-purple-100',
      interpretacion: 'Rendimiento excepcional, superior al 98% del grupo normativo.'
    };
    if (percentil >= 85) return {
      nivel: 'Alto',
      text: 'text-green-700',
      bg: 'bg-green-100',
      interpretacion: 'Rendimiento claramente por encima de la media.'
    };
    if (percentil >= 70) return {
      nivel: 'Medio-alto',
      text: 'text-blue-700',
      bg: 'bg-blue-100',
      interpretacion: 'Rendimiento ligeramente superior a la media.'
    };
    if (percentil >= 31) return {
      nivel: 'Medio',
      text: 'text-gray-700',
      bg: 'bg-gray-100',
      interpretacion: 'Rendimiento dentro del rango promedio.'
    };
    if (percentil >= 16) return {
      nivel: 'Medio-bajo',
      text: 'text-yellow-700',
      bg: 'bg-yellow-100',
      interpretacion: 'Rendimiento ligeramente inferior a la media.'
    };
    if (percentil >= 3) return {
      nivel: 'Bajo',
      text: 'text-orange-700',
      bg: 'bg-orange-100',
      interpretacion: 'Rendimiento claramente por debajo de la media.'
    };
    return {
      nivel: 'Muy bajo',
      text: 'text-red-700',
      bg: 'bg-red-100',
      interpretacion: 'Rendimiento críticamente bajo, requiere atención especial.'
    };
  };

  const getInterpretacionColor = (percentil) => {
    const nivel = getNivelAptitudinal(percentil);
    return { text: nivel.text, bg: nivel.bg, label: nivel.nivel };
  };

  const mostrarGrafico = (informe) => {
    setGraficoViendose(informe);
  };

  const generarInforme = async (pacienteId, nombrePaciente) => {
    if (!confirm(`¿Generar nuevo informe para ${nombrePaciente}?`)) {
      return;
    }

    try {
      console.log('📄 [InformesFaltantes] Generando informe para:', pacienteId);

      // Llamar a la función RPC para generar informe
      const { data, error } = await supabase.rpc('generar_informe_directo', {
        p_paciente_id: pacienteId
      });

      if (error) throw error;

      console.log('✅ [InformesFaltantes] Informe generado exitosamente');
      alert(`Informe generado exitosamente para ${nombrePaciente}`);

      // Recargar la lista
      await cargarInformesFaltantes();

    } catch (error) {
      console.error('❌ [InformesFaltantes] Error generando informe:', error);
      alert('Error generando informe: ' + error.message);
    }
  };

  const eliminarInforme = async (informeId, nombrePaciente) => {
    if (!confirm(`¿Estás seguro de que deseas eliminar el informe de ${nombrePaciente}?`)) {
      return;
    }

    try {
      console.log('🗑️ [InformesFaltantes] Eliminando informe:', informeId);

      const { error } = await supabase
        .from('informes_generados')
        .update({ estado: 'eliminado' })
        .eq('id', informeId);

      if (error) throw error;

      console.log('✅ [InformesFaltantes] Informe eliminado exitosamente');
      alert(`Informe de ${nombrePaciente} eliminado exitosamente`);

      // Recargar la lista
      await cargarInformesFaltantes();

    } catch (error) {
      console.error('❌ [InformesFaltantes] Error eliminando informe:', error);
      alert('Error eliminando informe: ' + error.message);
    }
  };

  if (loading) {
    return (
      <Card className="mb-6">
        <CardBody className="text-center py-8">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-green-600 mx-auto mb-4"></div>
          <p className="text-green-600 font-semibold">📋 Cargando informes faltantes generados...</p>
        </CardBody>
      </Card>
    );
  }

  return (
    <>
      <Card className="mb-6 border-2 border-green-500">
        <CardHeader className="bg-green-50 border-b border-green-200">
          <div className="flex items-center">
            <div>
              <h2 className="text-xl font-bold text-green-800">
                ✅ INFORMES FALTANTES GENERADOS EXITOSAMENTE v3.0
              </h2>
              <p className="text-sm text-green-600 mt-1">
                Informes psicométricos completos con gráficos y análisis cualitativo
              </p>
            </div>
          </div>
        </CardHeader>
        <CardBody>

          <div className="space-y-6">
            {informesFaltantes.map((informe, index) => {
              const paciente = informe.pacientes;
              const estadisticas = informe.estadisticas || {};
              const resultados = informe.resultados || [];
              const isFemale = paciente?.genero === 'femenino';
              const isExpanded = expandedPatients.has(paciente?.id);
              const datosReales = informe.metadatos?.datos_reales === 'true';

              return (
                <Card key={informe.id} className={`overflow-hidden shadow-lg border-2 ${
                  datosReales ? 'border-green-200' : 'border-gray-200'
                }`}>
                  {/* Header del paciente */}
                  <CardHeader
                    className={`cursor-pointer transition-colors ${
                      isFemale
                        ? 'bg-gradient-to-r from-pink-300 to-pink-400 hover:from-pink-400 hover:to-pink-500'
                        : 'bg-gradient-to-r from-blue-300 to-blue-400 hover:from-blue-400 hover:to-blue-500'
                    }`}
                    onClick={() => togglePatientExpansion(paciente?.id)}
                  >
                    <div className="flex items-center justify-between text-white">
                      <div className="flex items-center flex-1">
                        <button className="mr-3 text-white hover:text-gray-200 transition-colors">
                          <i className={`fas ${isExpanded ? 'fa-chevron-down' : 'fa-chevron-right'} text-lg`}></i>
                        </button>

                        <div className={`w-12 h-12 rounded-full flex items-center justify-center mr-4 ${
                          isFemale
                            ? 'bg-pink-500 border-2 border-pink-300'
                            : 'bg-blue-500 border-2 border-blue-300'
                        }`}>
                          <i className={`fas ${isFemale ? 'fa-venus text-white' : 'fa-mars text-white'} text-xl`}></i>
                        </div>

                        <div className="flex-1">
                          <h3 className="text-lg font-bold">
                            {paciente?.nombre} {paciente?.apellido}
                          </h3>
                          <p className={`text-sm ${isFemale ? 'text-pink-100' : 'text-blue-100'}`}>
                            Doc: {paciente?.documento} • {estadisticas.totalTests} tests completados
                            {!isExpanded && (
                              <span className={`ml-2 text-xs ${isFemale ? 'text-pink-200' : 'text-blue-200'}`}>
                                • Haz clic para expandir
                              </span>
                            )}
                          </p>
                        </div>
                      </div>

                      <div className="flex items-center space-x-2">
                        {/* 1. Botón Generar */}
                        <Button
                          onClick={(e) => {
                            e.stopPropagation();
                            generarInforme(paciente?.id, `${paciente?.nombre} ${paciente?.apellido}`);
                          }}
                          className="bg-green-500 bg-opacity-90 text-white hover:bg-opacity-100 border border-green-400"
                        >
                          <i className="fas fa-plus mr-2"></i>
                          Generar
                        </Button>

                        {/* 2. Botón Ver */}
                        <Button
                          onClick={(e) => {
                            e.stopPropagation();
                            verInforme(informe.id);
                          }}
                          className="bg-blue-500 bg-opacity-90 text-white hover:bg-opacity-100 border border-blue-400"
                        >
                          <i className="fas fa-eye mr-2"></i>
                          Ver
                        </Button>

                        {/* 3. Botón Eliminar */}
                        <Button
                          onClick={(e) => {
                            e.stopPropagation();
                            eliminarInforme(informe.id, `${paciente?.nombre} ${paciente?.apellido}`);
                          }}
                          className="bg-red-500 bg-opacity-90 text-white hover:bg-opacity-100 border border-red-400"
                        >
                          <i className="fas fa-trash mr-2"></i>
                          Eliminar
                        </Button>

                        <span className={`text-xs ${isFemale ? 'text-pink-100' : 'text-blue-100'} ml-3`}>
                          {new Date(informe.fecha_generacion).toLocaleDateString('es-ES')}
                        </span>
                      </div>
                    </div>
                  </CardHeader>

                  {/* Estadísticas resumidas */}
                  <div className="px-6 py-4 bg-gray-50 border-b">
                    <div className="grid grid-cols-2 md:grid-cols-6 gap-4 text-center">
                      <div className="bg-white p-3 rounded-lg border">
                        <div className="text-2xl font-bold text-blue-600">{estadisticas.totalTests || 0}</div>
                        <div className="text-xs text-gray-600">Tests</div>
                      </div>
                      <div className="bg-white p-3 rounded-lg border">
                        <div className="text-2xl font-bold text-green-600">{estadisticas.promedioPC || 0}</div>
                        <div className="text-xs text-gray-600">PC Prom</div>
                      </div>
                      <div className="bg-white p-3 rounded-lg border">
                        <div className="text-2xl font-bold text-purple-600">{estadisticas.promedioPD || 0}</div>
                        <div className="text-xs text-gray-600">PD Prom</div>
                      </div>
                      <div className="bg-white p-3 rounded-lg border">
                        <div className="text-2xl font-bold text-yellow-600">{estadisticas.aptitudesAltas || 0}</div>
                        <div className="text-xs text-gray-600">Altas</div>
                      </div>
                      <div className="bg-white p-3 rounded-lg border">
                        <div className="text-2xl font-bold text-red-600">{estadisticas.totalErrores || 0}</div>
                        <div className="text-xs text-gray-600">Errores</div>
                      </div>
                      <div className="bg-white p-3 rounded-lg border">
                        <div className="text-2xl font-bold text-indigo-600">{estadisticas.aptitudesEvaluadas?.length || 0}</div>
                        <div className="text-xs text-gray-600">Aptitudes</div>
                      </div>
                    </div>

                    <div className="mt-3 text-xs text-gray-500 text-center">
                      <strong>Aptitudes evaluadas:</strong> {estadisticas.aptitudesEvaluadas?.join(', ') || 'N/A'}
                    </div>
                  </div>



                  {/* Detalles expandidos */}
                  {isExpanded && (
                    <CardBody className="bg-gray-50">
                      <div className="space-y-4">
                        <h4 className="text-lg font-semibold text-gray-800 mb-4">
                          📊 Resultados Detallados por Test
                        </h4>

                        {/* Tabla de resultados */}
                        <div className="overflow-x-auto">
                          <table className="w-full bg-white rounded-lg shadow">
                            <thead className="bg-gradient-to-r from-indigo-500 to-purple-600">
                              <tr>
                                <th className="px-4 py-3 text-left text-xs font-bold text-white uppercase tracking-wider">
                                  <div className="flex items-center">
                                    <i className="fas fa-clipboard-list mr-2"></i>
                                    Test
                                  </div>
                                </th>
                                <th className="px-4 py-3 text-center text-xs font-bold text-white uppercase tracking-wider">
                                  <div className="flex items-center justify-center">
                                    <i className="fas fa-bullseye mr-2"></i>
                                    Puntaje PD
                                  </div>
                                </th>
                                <th className="px-4 py-3 text-center text-xs font-bold text-white uppercase tracking-wider">
                                  <div className="flex items-center justify-center">
                                    <i className="fas fa-chart-line mr-2"></i>
                                    Puntaje PC
                                  </div>
                                </th>
                                <th className="px-4 py-3 text-center text-xs font-bold text-white uppercase tracking-wider">
                                  <div className="flex items-center justify-center">
                                    <i className="fas fa-medal mr-2"></i>
                                    Nivel
                                  </div>
                                </th>
                                <th className="px-4 py-3 text-center text-xs font-bold text-white uppercase tracking-wider">
                                  <div className="flex items-center justify-center">
                                    <i className="fas fa-exclamation-triangle mr-2"></i>
                                    Errores
                                  </div>
                                </th>
                                <th className="px-4 py-3 text-center text-xs font-bold text-white uppercase tracking-wider">
                                  <div className="flex items-center justify-center">
                                    <i className="fas fa-clock mr-2"></i>
                                    Tiempo
                                  </div>
                                </th>
                              </tr>
                            </thead>
                            <tbody className="divide-y divide-gray-200">
                              {resultados.map((resultado, idx) => {
                                const interpretacion = getInterpretacionColor(resultado.percentil);
                                const nivelAptitudinal = getNivelAptitudinal(resultado.percentil);
                                return (
                                  <tr key={resultado.id} className="hover:bg-gray-50">
                                    <td className="px-4 py-3">
                                      <div className="flex items-center">
                                        <div className={`w-8 h-8 rounded-full flex items-center justify-center mr-3 ${
                                          resultado.aptitudes.codigo === 'E' ? 'bg-purple-100 text-purple-600' :
                                          resultado.aptitudes.codigo === 'A' ? 'bg-red-100 text-red-600' :
                                          resultado.aptitudes.codigo === 'O' ? 'bg-green-100 text-green-600' :
                                          resultado.aptitudes.codigo === 'V' ? 'bg-blue-100 text-blue-600' :
                                          resultado.aptitudes.codigo === 'N' ? 'bg-indigo-100 text-indigo-600' :
                                          resultado.aptitudes.codigo === 'R' ? 'bg-orange-100 text-orange-600' :
                                          resultado.aptitudes.codigo === 'M' ? 'bg-gray-100 text-gray-600' :
                                          'bg-gray-100 text-gray-600'
                                        }`}>
                                          <span className="text-sm font-bold">{resultado.aptitudes.codigo}</span>
                                        </div>
                                        <div>
                                          <div className="text-sm font-medium text-gray-900">{resultado.aptitudes.codigo}</div>
                                          <div className="text-xs text-gray-500">{resultado.aptitudes.nombre}</div>
                                        </div>
                                      </div>
                                    </td>
                                    <td className="px-4 py-3 text-center">
                                      <span className="text-lg font-bold text-orange-600">{resultado.puntaje_directo}</span>
                                    </td>
                                    <td className="px-4 py-3 text-center">
                                      <span className="text-lg font-bold text-blue-600">{resultado.percentil}</span>
                                    </td>
                                    <td className="px-4 py-3 text-center">
                                      <span className={`px-3 py-1 rounded-full text-xs font-bold ${nivelAptitudinal.bg} ${nivelAptitudinal.text}`}>
                                        {nivelAptitudinal.nivel}
                                      </span>
                                    </td>
                                    <td className="px-4 py-3 text-center">
                                      <span className="text-sm font-medium text-gray-600">{resultado.errores || 0}</span>
                                    </td>
                                    <td className="px-4 py-3 text-center">
                                      <span className="text-sm text-gray-600">{formatTiempo(resultado.tiempo_segundos)}</span>
                                    </td>
                                  </tr>
                                );
                              })}
                            </tbody>
                          </table>
                        </div>
                      </div>
                    </CardBody>
                  )}
                </Card>
              );
            })}
          </div>

          <div className="mt-6 p-4 bg-blue-50 border border-blue-200 rounded-lg">
            <h3 className="font-semibold text-blue-800 mb-2">
              📊 Resumen de Informes Faltantes Generados:
            </h3>
            <div className="grid grid-cols-1 md:grid-cols-5 gap-4 text-sm">
              <div className="text-center">
                <div className="text-2xl font-bold text-green-600">{informesFaltantes.length}</div>
                <div className="text-green-700">Informes Generados</div>
              </div>
              <div className="text-center">
                <div className="text-2xl font-bold text-blue-600">
                  {informesFaltantes.reduce((sum, i) => sum + (i.metadatos?.total_resultados || 0), 0)}
                </div>
                <div className="text-blue-700">Total Resultados</div>
              </div>
              <div className="text-center">
                <div className="text-2xl font-bold text-purple-600">100%</div>
                <div className="text-purple-700">Datos Reales</div>
              </div>
              <div className="text-center">
                <div className="text-2xl font-bold text-orange-600">v3.0</div>
                <div className="text-orange-700">Versión</div>
              </div>
              <div className="text-center">
                <div className="text-2xl font-bold text-red-600">✅</div>
                <div className="text-red-700">Completado</div>
              </div>
            </div>
          </div>

          <div className="mt-4 p-4 bg-yellow-50 border border-yellow-200 rounded-lg">
            <p className="text-yellow-800 font-semibold">
              🎯 INFORMES LISTOS PARA USO:
            </p>
            <div className="text-sm text-yellow-700 mt-1 space-y-1">
              <p>• <strong>Mariana Sanabria Rueda:</strong> 6 tests, PC promedio 99, 6 aptitudes altas</p>
              <p>• <strong>Ana Sofia Rueda Acevedo:</strong> 7 tests, PC promedio 44, 1 aptitud baja</p>
              <p>• <strong>Camila Vargas Vargas:</strong> 5 tests, PC promedio 46, rendimiento normal</p>
              <p>• <strong>Henry Rueda:</strong> 2 tests, PC promedio 92, aptitudes destacadas</p>
              <p>• <strong>María González:</strong> 3 tests, PC promedio 37, evaluación parcial</p>
            </div>
          </div>
        </CardBody>
      </Card>

      {/* Visor de informes */}
      {informeViendose && (
        <InformeViewer
          informeId={informeViendose}
          onClose={() => setInformeViendose(null)}
        />
      )}

      {/* Visor de gráficos */}
      {graficoViendose && (
        <GraficoResultados
          paciente={graficoViendose.pacientes}
          resultados={graficoViendose.resultados}
          estadisticas={graficoViendose.estadisticas}
          onClose={() => setGraficoViendose(null)}
        />
      )}
    </>
  );
};

export default InformesFaltantesGenerados;
