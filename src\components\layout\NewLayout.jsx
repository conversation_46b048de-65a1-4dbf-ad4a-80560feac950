import React from 'react';
import { NavLink, Outlet } from 'react-router-dom';

const navigation = [
  { name: 'Dashboard', path: '/admin/dashboard', icon: 'home', key: 'dashboard' },
  { name: '<PERSON><PERSON><PERSON>', path: '/admin/patients', icon: 'users', key: 'patients' },
  { name: '<PERSON><PERSON><PERSON><PERSON>', path: '/admin/reports', icon: 'chart-bar', key: 'reports' },
  { name: 'Panel Admin', path: '/admin/administration', icon: 'shield-alt', key: 'administration' },
];

const NewLayout = () => {
  return (
    <div className="flex h-screen bg-gray-100">
      <div className="flex flex-col w-64 bg-gray-800">
        <div className="flex items-center justify-center h-16 bg-gray-900">
          <span className="text-white font-bold uppercase">Bat-7 Demo</span>
        </div>
        <div className="flex flex-col flex-1 overflow-y-auto">
          <nav className="flex-1 px-2 py-4 bg-gray-800">
            {navigation.map((item) => (
              <NavLink
                key={item.key}
                to={item.path}
                className={({ isActive }) =>
                  `flex items-center px-4 py-2 mt-2 text-gray-100 hover:bg-gray-700 ${isActive ? 'bg-gray-700' : ''}`
                }
              >
                <i className={`fas fa-${item.icon} w-6 h-6 mr-3`} />
                {item.name}
              </NavLink>
            ))}
          </nav>
        </div>
      </div>
      <div className="flex flex-col flex-1 overflow-y-auto">
        <main className="p-4">
          <Outlet />
        </main>
      </div>
    </div>
  );
};

export default NewLayout;