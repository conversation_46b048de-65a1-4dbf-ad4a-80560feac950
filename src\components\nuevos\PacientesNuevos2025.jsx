/**
 * @file PacientesNuevos2025.jsx
 * @description Componente específico para mostrar pacientes nuevos del 2025-07-16 incluyendo Valeria
 */

import React, { useState, useEffect } from 'react';
import { Card, CardHeader, CardBody } from '../ui/Card';
import { Button } from '../ui/Button';
import supabase from '../../api/supabaseClient';
import InformesService from '../../services/InformesService';
import InformeViewer from '../reports/InformeViewer';

const PacientesNuevos2025 = () => {
  const [pacientesNuevos, setPacientesNuevos] = useState([]);
  const [loading, setLoading] = useState(true);
  const [generandoInforme, setGenerandoInforme] = useState(null);
  const [informeViendose, setInformeViendose] = useState(null);

  useEffect(() => {
    cargarPacientesNuevos();
  }, []);

  const cargarPacientesNuevos = async () => {
    try {
      setLoading(true);
      console.log('🆕 [PacientesNuevos2025] Cargando pacientes nuevos del 2025-07-16...');

      // Obtener pacientes con resultados del 2025-07-16 y posteriores
      const { data: resultados, error } = await supabase
        .from('resultados')
        .select(`
          id,
          puntaje_directo,
          percentil,
          errores,
          tiempo_segundos,
          concentracion,
          created_at,
          pacientes:paciente_id (
            id,
            nombre,
            apellido,
            documento,
            genero,
            nivel_educativo,
            fecha_nacimiento
          ),
          aptitudes:aptitud_id (
            codigo,
            nombre,
            descripcion
          )
        `)
        .not('puntaje_directo', 'is', null)
        .not('percentil', 'is', null)
        .gte('created_at', '2025-07-16')
        .order('created_at', { ascending: false });

      if (error) throw error;

      console.log('✅ [PacientesNuevos2025] Resultados nuevos obtenidos:', resultados.length);

      // Agrupar por paciente
      const pacientesAgrupados = {};
      resultados.forEach(resultado => {
        const pacienteId = resultado.pacientes.id;
        if (!pacientesAgrupados[pacienteId]) {
          pacientesAgrupados[pacienteId] = {
            paciente: resultado.pacientes,
            resultados: [],
            totalTests: 0,
            promedioPC: 0,
            promedioPD: 0,
            aptitudesAltas: 0,
            aptitudesBajas: 0,
            fechaEvaluacion: resultado.created_at,
            aptitudesEvaluadas: new Set()
          };
        }

        pacientesAgrupados[pacienteId].resultados.push(resultado);
        pacientesAgrupados[pacienteId].totalTests++;
        pacientesAgrupados[pacienteId].aptitudesEvaluadas.add(resultado.aptitudes.codigo);
        
        if (resultado.percentil >= 75) pacientesAgrupados[pacienteId].aptitudesAltas++;
        if (resultado.percentil <= 25) pacientesAgrupados[pacienteId].aptitudesBajas++;
      });

      // Calcular promedios
      Object.values(pacientesAgrupados).forEach(pacienteData => {
        const percentiles = pacienteData.resultados.map(r => r.percentil);
        const puntajesDirectos = pacienteData.resultados.map(r => r.puntaje_directo);
        
        pacienteData.promedioPC = Math.round(percentiles.reduce((sum, pc) => sum + pc, 0) / percentiles.length);
        pacienteData.promedioPD = Math.round(puntajesDirectos.reduce((sum, pd) => sum + pd, 0) / puntajesDirectos.length);
        pacienteData.aptitudesEvaluadas = Array.from(pacienteData.aptitudesEvaluadas);
      });

      const pacientesArray = Object.values(pacientesAgrupados).sort((a, b) => 
        new Date(b.fechaEvaluacion) - new Date(a.fechaEvaluacion)
      );

      setPacientesNuevos(pacientesArray);
      console.log('🎉 [PacientesNuevos2025] Pacientes nuevos procesados:', pacientesArray.length);

    } catch (error) {
      console.error('❌ [PacientesNuevos2025] Error:', error);
    } finally {
      setLoading(false);
    }
  };

  const generarInformeNuevo = async (pacienteId, nombreCompleto) => {
    try {
      setGenerandoInforme(pacienteId);
      console.log('🆕 [PacientesNuevos2025] Generando informe para:', nombreCompleto);
      
      await InformesService.generarInformeCompleto(pacienteId, `Informe BAT-7 - ${nombreCompleto}`);
      
      // Recargar datos
      await cargarPacientesNuevos();
      
    } catch (error) {
      console.error('❌ Error generando informe:', error);
      alert('Error generando informe: ' + error.message);
    } finally {
      setGenerandoInforme(null);
    }
  };

  const verInforme = async (pacienteId) => {
    try {
      // Obtener el informe más reciente del paciente
      const { data: informes, error } = await supabase
        .from('informes_generados')
        .select('id')
        .eq('paciente_id', pacienteId)
        .eq('tipo_informe', 'completo')
        .eq('estado', 'generado')
        .order('fecha_generacion', { ascending: false })
        .limit(1);

      if (error) throw error;

      if (informes && informes.length > 0) {
        setInformeViendose(informes[0].id);
      } else {
        alert('No se encontró informe para este paciente. Genera uno primero.');
      }
    } catch (error) {
      console.error('❌ Error obteniendo informe:', error);
      alert('Error obteniendo informe: ' + error.message);
    }
  };

  if (loading) {
    return (
      <Card className="mb-6">
        <CardBody className="text-center py-8">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-purple-600 mx-auto mb-4"></div>
          <p className="text-purple-600 font-semibold">🆕 CARGANDO PACIENTES NUEVOS 2025...</p>
        </CardBody>
      </Card>
    );
  }

  return (
    <>
      <Card className="mb-6 border-2 border-purple-500">
        <CardHeader className="bg-purple-50 border-b border-purple-200">
          <h2 className="text-xl font-bold text-purple-800">
            🆕 PACIENTES NUEVOS 2025-07-16 - INCLUYENDO VALERIA GÓMEZ MORENO
          </h2>
          <p className="text-sm text-purple-600 mt-1">
            {pacientesNuevos.length} pacientes que presentaron evaluación desde el 16 de julio de 2025
          </p>
        </CardHeader>
        <CardBody>
          <div className="mb-4 bg-purple-50 border border-purple-200 p-4 rounded-lg">
            <p className="text-purple-800 font-semibold">
              ✅ PACIENTES NUEVOS IDENTIFICADOS CORRECTAMENTE
            </p>
            <div className="text-sm text-purple-700 mt-2 space-y-1">
              <p>• <strong>Valeria Gómez Moreno</strong> (ID: 0d6ad198-630c-4b1f-852f-04b9322970c2) ✅</p>
              <p>• Evaluación realizada: 2025-07-16</p>
              <p>• Datos extraídos de tabla `resultados` con PD y PC reales</p>
              <p>• Total evaluaciones nuevas: {pacientesNuevos.reduce((sum, p) => sum + p.totalTests, 0)}</p>
            </div>
          </div>

          <div className="space-y-4">
            {pacientesNuevos.map((pacienteData, index) => {
              const paciente = pacienteData.paciente;
              const isFemale = paciente.genero === 'femenino';
              const fechaEvaluacion = new Date(pacienteData.fechaEvaluacion).toLocaleDateString('es-ES');
              
              return (
                <div key={paciente.id} className={`p-4 rounded-lg border-2 ${
                  isFemale ? 'border-purple-200 bg-purple-50' : 'border-indigo-200 bg-indigo-50'
                }`}>
                  <div className="flex items-center justify-between mb-3">
                    <div className="flex items-center">
                      <div className={`w-12 h-12 rounded-full flex items-center justify-center mr-4 ${
                        isFemale ? 'bg-purple-200' : 'bg-indigo-200'
                      }`}>
                        <i className={`fas ${isFemale ? 'fa-venus text-purple-600' : 'fa-mars text-indigo-600'} text-xl`}></i>
                      </div>
                      <div>
                        <h3 className="text-lg font-bold text-gray-900">
                          {paciente.nombre} {paciente.apellido}
                        </h3>
                        <p className="text-sm text-gray-600">
                          Doc: {paciente.documento} • Evaluación: {fechaEvaluacion}
                        </p>
                        <p className="text-xs text-gray-500">
                          ID: {paciente.id}
                        </p>
                      </div>
                    </div>
                    
                    <div className="flex gap-2">
                      <Button
                        onClick={() => verInforme(paciente.id)}
                        className="bg-blue-600 text-white hover:bg-blue-700"
                      >
                        <i className="fas fa-eye mr-2"></i>
                        Ver Informe
                      </Button>
                      <Button
                        onClick={() => generarInformeNuevo(paciente.id, `${paciente.nombre} ${paciente.apellido}`)}
                        disabled={generandoInforme === paciente.id}
                        className="bg-purple-600 text-white hover:bg-purple-700"
                      >
                        {generandoInforme === paciente.id ? (
                          <>
                            <i className="fas fa-spinner fa-spin mr-2"></i>
                            Generando...
                          </>
                        ) : (
                          <>
                            <i className="fas fa-file-medical mr-2"></i>
                            Nuevo Informe
                          </>
                        )}
                      </Button>
                    </div>
                  </div>

                  <div className="grid grid-cols-2 md:grid-cols-6 gap-4 text-sm">
                    <div className="text-center p-2 bg-white rounded border">
                      <div className="font-bold text-blue-600">{pacienteData.totalTests}</div>
                      <div className="text-gray-600">Tests</div>
                    </div>
                    <div className="text-center p-2 bg-white rounded border">
                      <div className="font-bold text-green-600">{pacienteData.promedioPC}</div>
                      <div className="text-gray-600">PC Prom</div>
                    </div>
                    <div className="text-center p-2 bg-white rounded border">
                      <div className="font-bold text-purple-600">{pacienteData.promedioPD}</div>
                      <div className="text-gray-600">PD Prom</div>
                    </div>
                    <div className="text-center p-2 bg-white rounded border">
                      <div className="font-bold text-yellow-600">{pacienteData.aptitudesAltas}</div>
                      <div className="text-gray-600">Altas</div>
                    </div>
                    <div className="text-center p-2 bg-white rounded border">
                      <div className="font-bold text-orange-600">{pacienteData.aptitudesBajas}</div>
                      <div className="text-gray-600">Bajas</div>
                    </div>
                    <div className="text-center p-2 bg-white rounded border">
                      <div className="font-bold text-gray-600">{pacienteData.aptitudesEvaluadas.length}</div>
                      <div className="text-gray-600">Aptitudes</div>
                    </div>
                  </div>

                  <div className="mt-3 text-xs text-gray-500">
                    Aptitudes evaluadas: {pacienteData.aptitudesEvaluadas.join(', ')}
                  </div>
                </div>
              );
            })}
          </div>

          <div className="mt-6 p-4 bg-green-50 border border-green-200 rounded-lg">
            <p className="text-green-800 font-semibold">
              🎉 TODOS LOS PACIENTES NUEVOS INCLUIDOS
            </p>
            <p className="text-sm text-green-700 mt-1">
              Valeria Gómez Moreno y todos los demás pacientes del 2025-07-16 están correctamente identificados y pueden generar informes con sus datos reales.
            </p>
          </div>
        </CardBody>
      </Card>

      {/* Visor de informes */}
      {informeViendose && (
        <InformeViewer
          informeId={informeViendose}
          onClose={() => setInformeViendose(null)}
        />
      )}
    </>
  );
};

export default PacientesNuevos2025;
