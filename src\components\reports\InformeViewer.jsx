import React, { useState, useEffect } from 'react';
import { Card, CardHeader, CardBody } from '../ui/Card';
import { Button } from '../ui/Button';
import InformesService from '../../services/InformesService';
import { InterpretacionCualitativaService } from '../../services/interpretacionCualitativaService';

const InformeViewer = ({ informeId, onClose }) => {
  const [informe, setInforme] = useState(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);

  useEffect(() => {
    if (informeId) {
      cargarInforme();
    }
  }, [informeId]);

  const cargarInforme = async () => {
    try {
      setLoading(true);
      const informeData = await InformesService.obtenerInforme(informeId);
      setInforme(informeData);
      setError(null);
    } catch (error) {
      console.error('Error cargando informe:', error);
      setError(error.message);
    } finally {
      setLoading(false);
    }
  };

  if (loading) {
    return (
      <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
        <div className="bg-white p-8 rounded-lg shadow-xl">
          <div className="flex items-center space-x-3">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
            <span className="text-lg font-medium text-gray-700">Cargando informe...</span>
          </div>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
        <div className="bg-white p-8 rounded-lg shadow-xl max-w-md">
          <div className="text-center">
            <i className="fas fa-exclamation-triangle text-4xl text-red-500 mb-4"></i>
            <h3 className="text-lg font-semibold text-gray-900 mb-2">Error al cargar informe</h3>
            <p className="text-gray-600 mb-4">{error}</p>
            <div className="flex space-x-3 justify-center">
              <Button onClick={cargarInforme} className="bg-blue-600 text-white">
                <i className="fas fa-redo mr-2"></i>
                Reintentar
              </Button>
              <Button onClick={onClose} className="bg-gray-500 text-white">
                <i className="fas fa-times mr-2"></i>
                Cerrar
              </Button>
            </div>
          </div>
        </div>
      </div>
    );
  }

  if (!informe) {
    return (
      <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
        <div className="bg-white p-8 rounded-lg shadow-xl">
          <div className="text-center">
            <i className="fas fa-file-alt text-4xl text-gray-400 mb-4"></i>
            <p className="text-gray-600">No se encontró el informe solicitado</p>
            <Button onClick={onClose} className="mt-4 bg-gray-500 text-white">
              Cerrar
            </Button>
          </div>
        </div>
      </div>
    );
  }

  const { paciente, resultados, estadisticas } = informe;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
      <div className="bg-white rounded-lg shadow-2xl max-w-6xl w-full max-h-[90vh] overflow-hidden">
        {/* Header del Modal */}
        <div className="bg-gradient-to-r from-blue-600 to-purple-700 text-white p-6 flex items-center justify-between">
          <div className="flex items-center">
            <i className="fas fa-file-medical-alt text-2xl mr-3"></i>
            <div>
              <h2 className="text-xl font-bold">Informe Psicométrico BAT-7</h2>
              <p className="text-blue-100 text-sm">
                {paciente?.nombre} {paciente?.apellido} - {paciente?.documento}
              </p>
            </div>
          </div>
          <Button 
            onClick={onClose}
            className="bg-white bg-opacity-20 text-white hover:bg-opacity-30 border border-white border-opacity-30"
          >
            <i className="fas fa-times"></i>
          </Button>
        </div>

        {/* Contenido del Informe */}
        <div className="overflow-y-auto max-h-[calc(90vh-120px)] p-6">
          
          {/* Información del Paciente */}
          <Card className="mb-6">
            <CardHeader className="bg-gray-50 border-b">
              <h3 className="text-lg font-semibold text-gray-800 flex items-center">
                <i className="fas fa-user mr-2 text-blue-600"></i>
                Información del Paciente
              </h3>
            </CardHeader>
            <CardBody>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <span className="font-medium text-gray-700">Nombre:</span>
                  <span className="ml-2 text-gray-900">{paciente?.nombre} {paciente?.apellido}</span>
                </div>
                <div>
                  <span className="font-medium text-gray-700">Documento:</span>
                  <span className="ml-2 text-gray-900">{paciente?.documento}</span>
                </div>
                <div>
                  <span className="font-medium text-gray-700">Género:</span>
                  <span className="ml-2 text-gray-900">{paciente?.genero}</span>
                </div>
                <div>
                  <span className="font-medium text-gray-700">Fecha de Nacimiento:</span>
                  <span className="ml-2 text-gray-900">
                    {paciente?.fecha_nacimiento ? new Date(paciente.fecha_nacimiento).toLocaleDateString('es-ES') : 'N/A'}
                  </span>
                </div>
              </div>
            </CardBody>
          </Card>

          {/* Resumen General */}
          <Card className="mb-6">
            <CardHeader className="bg-blue-50 border-b">
              <h3 className="text-lg font-semibold text-blue-800 flex items-center">
                <i className="fas fa-chart-pie mr-2"></i>
                Resumen General
              </h3>
            </CardHeader>
            <CardBody>
              <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
                <div className="text-center p-4 bg-blue-50 rounded-lg">
                  <div className="text-2xl font-bold text-blue-600">{estadisticas?.tests_completados || resultados?.length || 0}</div>
                  <div className="text-sm text-blue-700">Tests Completados</div>
                </div>
                <div className="text-center p-4 bg-green-50 rounded-lg">
                  <div className="text-2xl font-bold text-green-600">{estadisticas?.percentil_promedio || 'N/A'}</div>
                  <div className="text-sm text-green-700">Percentil Promedio</div>
                </div>
                <div className="text-center p-4 bg-yellow-50 rounded-lg">
                  <div className="text-2xl font-bold text-yellow-600">{estadisticas?.aptitudes_altas || 0}</div>
                  <div className="text-sm text-yellow-700">Aptitudes Altas</div>
                </div>
                <div className="text-center p-4 bg-orange-50 rounded-lg">
                  <div className="text-2xl font-bold text-orange-600">{estadisticas?.aptitudes_bajas || 0}</div>
                  <div className="text-sm text-orange-700">A Reforzar</div>
                </div>
              </div>
            </CardBody>
          </Card>

          {/* Gráfico de Barras Profesional */}
          <Card className="mb-6">
            <CardHeader className="bg-blue-50 border-b border-blue-200">
              <h2 className="text-xl font-semibold text-blue-800 flex items-center">
                <i className="fas fa-chart-bar mr-2 text-blue-600"></i>
                📊 Resultados Detallados por Aptitud
              </h2>
            </CardHeader>
            <CardBody className="p-0">
              <div className="overflow-hidden">
                {/* Header de la tabla */}
                <div className="bg-gray-800 text-white px-6 py-4">
                  <div className="grid grid-cols-12 gap-4 items-center font-semibold text-sm">
                    <div className="col-span-1 text-center">S</div>
                    <div className="col-span-4">APTITUDES EVALUADAS</div>
                    <div className="col-span-1 text-center">PD</div>
                    <div className="col-span-1 text-center">PC</div>
                    <div className="col-span-5">PERFIL DE LAS APTITUDES</div>
                  </div>
                </div>

                {/* Filas de datos */}
                <div className="divide-y divide-gray-200">
                  {resultados && resultados.map((resultado, index) => {
                    const aptitudCodigo = resultado.aptitud?.codigo || 'N/A';
                    const percentil = resultado.percentil || 0;
                    const puntajeDirecto = resultado.puntaje_directo || 0;
                    const nombreAptitud = resultado.aptitud?.nombre || 'Aptitud Desconocida';

                    // Colores del círculo por aptitud
                    const coloresCirculo = {
                      'E': 'bg-teal-500',
                      'A': 'bg-red-500',
                      'O': 'bg-green-500',
                      'V': 'bg-blue-500',
                      'N': 'bg-indigo-500',
                      'R': 'bg-orange-500',
                      'M': 'bg-gray-500'
                    };
                    const colorCirculo = coloresCirculo[aptitudCodigo] || 'bg-gray-500';

                    // Color de la barra según la nueva escala de niveles
                    let colorBarra = 'bg-red-500'; // Muy bajo (1-2)
                    if (percentil >= 98) {
                      colorBarra = 'bg-purple-600'; // Muy alto (98-99)
                    } else if (percentil >= 85) {
                      colorBarra = 'bg-green-500'; // Alto (85-97)
                    } else if (percentil >= 70) {
                      colorBarra = 'bg-blue-500'; // Medio-alto (70-84)
                    } else if (percentil >= 31) {
                      colorBarra = 'bg-gray-500'; // Medio (31-69)
                    } else if (percentil >= 16) {
                      colorBarra = 'bg-yellow-500'; // Medio-bajo (16-30)
                    } else if (percentil >= 3) {
                      colorBarra = 'bg-orange-500'; // Bajo (3-15)
                    }

                    // Ancho de la barra (máximo 100%)
                    const anchoBarra = Math.max((percentil / 100) * 100, 2);

                    return (
                      <div key={index} className="px-6 py-4 hover:bg-gray-50 transition-colors">
                        <div className="grid grid-cols-12 gap-4 items-center">
                          {/* Círculo con letra */}
                          <div className="col-span-1 flex justify-center">
                            <div className={`w-8 h-8 rounded-full ${colorCirculo} flex items-center justify-center`}>
                              <span className="text-white font-bold text-sm">{aptitudCodigo}</span>
                            </div>
                          </div>

                          {/* Nombre de la aptitud */}
                          <div className="col-span-4">
                            <span className="font-medium text-gray-900">{nombreAptitud}</span>
                          </div>

                          {/* Puntaje Directo */}
                          <div className="col-span-1 text-center">
                            <span className="font-bold text-gray-800">{puntajeDirecto}</span>
                          </div>

                          {/* Percentil */}
                          <div className="col-span-1 text-center">
                            <span className="font-bold text-gray-800">{percentil}</span>
                          </div>

                          {/* Barra de progreso */}
                          <div className="col-span-5">
                            <div className="relative">
                              {/* Fondo de la barra */}
                              <div className="w-full h-6 bg-gray-200 rounded-full overflow-hidden">
                                {/* Barra de progreso */}
                                <div
                                  className={`h-full ${colorBarra} rounded-full transition-all duration-1000 ease-out flex items-center justify-end pr-2`}
                                  style={{ width: `${anchoBarra}%` }}
                                >
                                  <span className="text-white text-xs font-bold">
                                    {percentil}
                                  </span>
                                </div>
                              </div>

                              {/* Líneas de referencia */}
                              <div className="absolute top-0 left-0 w-full h-6 pointer-events-none">
                                {/* Línea 25% */}
                                <div className="absolute left-1/4 top-0 w-px h-full bg-gray-400 opacity-50"></div>
                                {/* Línea 50% */}
                                <div className="absolute left-1/2 top-0 w-px h-full bg-gray-400 opacity-50"></div>
                                {/* Línea 75% */}
                                <div className="absolute left-3/4 top-0 w-px h-full bg-gray-400 opacity-50"></div>
                              </div>
                            </div>
                          </div>
                        </div>
                      </div>
                    );
                  })}
                </div>

                {/* Leyenda de colores actualizada */}
                <div className="bg-gray-50 px-6 py-4 border-t">
                  <div className="grid grid-cols-2 md:grid-cols-4 lg:grid-cols-7 gap-4 text-xs">
                    <div className="flex items-center">
                      <div className="w-4 h-4 bg-red-500 rounded mr-2"></div>
                      <span className="text-gray-700">Muy bajo (1-2)</span>
                    </div>
                    <div className="flex items-center">
                      <div className="w-4 h-4 bg-orange-500 rounded mr-2"></div>
                      <span className="text-gray-700">Bajo (3-15)</span>
                    </div>
                    <div className="flex items-center">
                      <div className="w-4 h-4 bg-yellow-500 rounded mr-2"></div>
                      <span className="text-gray-700">Medio-bajo (16-30)</span>
                    </div>
                    <div className="flex items-center">
                      <div className="w-4 h-4 bg-gray-500 rounded mr-2"></div>
                      <span className="text-gray-700">Medio (31-69)</span>
                    </div>
                    <div className="flex items-center">
                      <div className="w-4 h-4 bg-blue-500 rounded mr-2"></div>
                      <span className="text-gray-700">Medio-alto (70-84)</span>
                    </div>
                    <div className="flex items-center">
                      <div className="w-4 h-4 bg-green-500 rounded mr-2"></div>
                      <span className="text-gray-700">Alto (85-97)</span>
                    </div>
                    <div className="flex items-center">
                      <div className="w-4 h-4 bg-purple-600 rounded mr-2"></div>
                      <span className="text-gray-700">Muy alto (98-99)</span>
                    </div>
                  </div>
                </div>
              </div>
            </CardBody>
          </Card>

          {/* Análisis Cualitativo Personalizado */}
          {resultados && resultados.length > 0 && (
            <Card className="mb-6">
              <CardHeader className="bg-gradient-to-r from-indigo-600 to-purple-700 border-b">
                <h2 className="text-xl font-semibold text-white flex items-center">
                  <i className="fas fa-brain mr-3 text-yellow-300"></i>
                  💡 Análisis Cualitativo Personalizado
                </h2>
                <p className="text-indigo-100 text-sm mt-1">
                  Interpretación específica basada en las aptitudes evaluadas
                </p>
              </CardHeader>
              <CardBody className="bg-gradient-to-br from-gray-50 to-blue-50">
                {(() => {
                  const interpretacionPersonalizada = InterpretacionCualitativaService.generarInterpretacionPersonalizada(resultados, paciente);

                  return (
                    <div className="space-y-8">
                      {/* Resumen General Personalizado */}
                      <div className="bg-white border-l-4 border-indigo-500 p-6 rounded-r-lg shadow-sm">
                        <h4 className="font-bold text-indigo-800 mb-4 flex items-center text-lg">
                          <i className="fas fa-user-chart mr-3 text-indigo-600"></i>
                          Perfil Cognitivo General
                        </h4>
                        <div className="bg-indigo-50 p-4 rounded-lg mb-4">
                          <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-4">
                            <div className="text-center">
                              <div className="text-2xl font-bold text-indigo-600">
                                {interpretacionPersonalizada.resumenGeneral.percentilPromedio}
                              </div>
                              <div className="text-sm text-indigo-700">Percentil Promedio</div>
                            </div>
                            <div className="text-center">
                              <div className="text-2xl font-bold text-purple-600">
                                {interpretacionPersonalizada.resumenGeneral.nivelGeneral}
                              </div>
                              <div className="text-sm text-purple-700">Nivel General</div>
                            </div>
                            <div className="text-center">
                              <div className="text-2xl font-bold text-blue-600">
                                {interpretacionPersonalizada.resumenGeneral.totalAptitudes}
                              </div>
                              <div className="text-sm text-blue-700">Aptitudes Evaluadas</div>
                            </div>
                          </div>
                        </div>
                        <p className="text-gray-700 leading-relaxed text-justify">
                          {interpretacionPersonalizada.resumenGeneral.descripcion}
                        </p>
                      </div>

                      {/* Interpretaciones por Aptitud */}
                      <div className="space-y-6">
                        <h4 className="font-bold text-gray-800 mb-4 flex items-center text-lg border-b border-gray-200 pb-2">
                          <i className="fas fa-microscope mr-3 text-blue-600"></i>
                          Análisis Detallado por Aptitud
                        </h4>

                        {interpretacionPersonalizada.aptitudesEspecificas.map((aptitud, index) => {
                          const nivelColor = {
                            'Muy Alto': 'border-purple-500 bg-purple-50',
                            'Alto': 'border-green-500 bg-green-50',
                            'Medio-Alto': 'border-blue-500 bg-blue-50',
                            'Medio': 'border-gray-500 bg-gray-50',
                            'Medio-Bajo': 'border-yellow-500 bg-yellow-50',
                            'Bajo': 'border-orange-500 bg-orange-50',
                            'Muy Bajo': 'border-red-500 bg-red-50'
                          }[aptitud.nivel] || 'border-gray-300 bg-gray-50';

                          const iconoAptitud = {
                            'V': 'fa-comments',
                            'E': 'fa-cube',
                            'A': 'fa-eye',
                            'R': 'fa-puzzle-piece',
                            'N': 'fa-calculator',
                            'M': 'fa-cogs',
                            'O': 'fa-spell-check'
                          }[aptitud.codigo] || 'fa-clipboard-list';

                          return (
                            <div key={index} className={`border-l-4 ${nivelColor} p-6 rounded-r-lg shadow-sm`}>
                              <div className="flex items-center justify-between mb-4">
                                <h5 className="font-bold text-gray-800 flex items-center text-lg">
                                  <i className={`fas ${iconoAptitud} mr-3 text-blue-600`}></i>
                                  {aptitud.nombre}
                                </h5>
                                <div className="flex items-center space-x-3">
                                  <span className="text-sm font-medium text-gray-600">PC: {aptitud.percentil}</span>
                                  <span className={`px-3 py-1 rounded-full text-xs font-bold ${
                                    aptitud.nivel === 'Muy Alto' ? 'bg-purple-100 text-purple-800' :
                                    aptitud.nivel === 'Alto' ? 'bg-green-100 text-green-800' :
                                    aptitud.nivel === 'Medio-Alto' ? 'bg-blue-100 text-blue-800' :
                                    aptitud.nivel === 'Medio' ? 'bg-gray-100 text-gray-800' :
                                    aptitud.nivel === 'Medio-Bajo' ? 'bg-yellow-100 text-yellow-800' :
                                    aptitud.nivel === 'Bajo' ? 'bg-orange-100 text-orange-800' :
                                    'bg-red-100 text-red-800'
                                  }`}>
                                    {aptitud.nivel}
                                  </span>
                                </div>
                              </div>

                              <p className="text-sm text-gray-600 mb-4 italic">
                                {aptitud.descripcion}
                              </p>

                              <div className="space-y-4">
                                <div className="bg-white p-4 rounded-lg">
                                  <h6 className="font-semibold text-gray-700 mb-2 flex items-center">
                                    <i className="fas fa-chart-line mr-2 text-blue-500"></i>
                                    Rendimiento
                                  </h6>
                                  <p className="text-gray-700 text-sm leading-relaxed">
                                    {aptitud.interpretacion.rendimiento}
                                  </p>
                                </div>

                                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                                  <div className="bg-white p-4 rounded-lg">
                                    <h6 className="font-semibold text-gray-700 mb-2 flex items-center">
                                      <i className="fas fa-graduation-cap mr-2 text-green-500"></i>
                                      Implicaciones Académicas
                                    </h6>
                                    <p className="text-gray-700 text-sm leading-relaxed">
                                      {aptitud.interpretacion.academico}
                                    </p>
                                  </div>

                                  <div className="bg-white p-4 rounded-lg">
                                    <h6 className="font-semibold text-gray-700 mb-2 flex items-center">
                                      <i className="fas fa-briefcase mr-2 text-purple-500"></i>
                                      Orientación Vocacional
                                    </h6>
                                    <p className="text-gray-700 text-sm leading-relaxed">
                                      {aptitud.interpretacion.vocacional}
                                    </p>
                                  </div>
                                </div>
                              </div>
                            </div>
                          );
                        })}
                      </div>

                      {/* Recomendaciones Personalizadas */}
                      <div className="bg-white border-l-4 border-green-500 p-6 rounded-r-lg shadow-sm">
                        <h4 className="font-bold text-green-800 mb-4 flex items-center text-lg">
                          <i className="fas fa-lightbulb mr-3 text-yellow-500"></i>
                          Recomendaciones Personalizadas
                        </h4>

                        {interpretacionPersonalizada.recomendaciones.fortalezas.length > 0 && (
                          <div className="mb-6">
                            <h5 className="font-semibold text-green-700 mb-3 flex items-center">
                              <i className="fas fa-star mr-2 text-green-600"></i>
                              Potenciar Fortalezas
                            </h5>
                            <div className="space-y-2">
                              {interpretacionPersonalizada.recomendaciones.fortalezas.map((fortaleza, idx) => (
                                <div key={idx} className="bg-green-50 p-3 rounded-lg border border-green-200">
                                  <p className="text-green-800 text-sm">
                                    <strong>{fortaleza.aptitud}:</strong> {fortaleza.recomendacion}
                                  </p>
                                </div>
                              ))}
                            </div>
                          </div>
                        )}

                        {interpretacionPersonalizada.recomendaciones.areasDeDesarrollo.length > 0 && (
                          <div className="mb-6">
                            <h5 className="font-semibold text-orange-700 mb-3 flex items-center">
                              <i className="fas fa-arrow-up mr-2 text-orange-600"></i>
                              Áreas de Desarrollo
                            </h5>
                            <div className="space-y-2">
                              {interpretacionPersonalizada.recomendaciones.areasDeDesarrollo.map((area, idx) => (
                                <div key={idx} className="bg-orange-50 p-3 rounded-lg border border-orange-200">
                                  <p className="text-orange-800 text-sm">
                                    <strong>{area.aptitud}:</strong> {area.recomendacion}
                                  </p>
                                </div>
                              ))}
                            </div>
                          </div>
                        )}

                        <div>
                          <h5 className="font-semibold text-blue-700 mb-3 flex items-center">
                            <i className="fas fa-compass mr-2 text-blue-600"></i>
                            Estrategias Generales
                          </h5>
                          <div className="bg-blue-50 p-4 rounded-lg border border-blue-200">
                            <ul className="space-y-2">
                              {interpretacionPersonalizada.recomendaciones.estrategiasGenerales.map((estrategia, idx) => (
                                <li key={idx} className="text-blue-800 text-sm flex items-start">
                                  <i className="fas fa-check-circle mr-2 text-blue-600 mt-0.5 flex-shrink-0"></i>
                                  {estrategia}
                                </li>
                              ))}
                            </ul>
                          </div>
                        </div>
                      </div>
                    </div>
                  );
                })()}
              </CardBody>
            </Card>
          )}

          {/* Información del Informe */}
          <Card>
            <CardHeader className="bg-gray-50 border-b">
              <h3 className="text-lg font-semibold text-gray-800">
                <i className="fas fa-info-circle mr-2 text-gray-600"></i>
                Información del Informe
              </h3>
            </CardHeader>
            <CardBody>
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4 text-sm">
                <div>
                  <span className="font-medium text-gray-700">Tipo de Informe:</span>
                  <span className="ml-2 text-gray-900">BAT-7 Completo</span>
                </div>
                <div>
                  <span className="font-medium text-gray-700">Estado:</span>
                  <span className="ml-2 text-green-600 font-medium">Completado</span>
                </div>
                <div>
                  <span className="font-medium text-gray-700">Fecha de Generación:</span>
                  <span className="ml-2 text-gray-900">{new Date().toLocaleDateString('es-ES')}</span>
                </div>
              </div>
            </CardBody>
          </Card>
        </div>
      </div>
    </div>
  );
};

export default InformeViewer;
