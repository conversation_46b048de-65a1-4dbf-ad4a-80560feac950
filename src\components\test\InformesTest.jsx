/**
 * @file InformesTest.jsx
 * @description Componente de prueba para verificar la funcionalidad de informes
 */

import React, { useState } from 'react';
import { Card, CardHeader, CardBody } from '../ui/Card';
import { Button } from '../ui/Button';
import InformesService from '../../services/InformesService';

const InformesTest = () => {
  const [loading, setLoading] = useState(false);
  const [resultado, setResultado] = useState('');

  // IDs de pacientes para pruebas
  const PACIENTES_TEST = {
    ANA_SOFIA: '41836cc4-94a6-4d9a-b0f7-b7eec7c2f5a0',
    MARIANA: '65c27c22-b667-42b0-9151-2068b3f655da'
  };

  const [pacienteSeleccionado, setPacienteSeleccionado] = useState(PACIENTES_TEST.ANA_SOFIA);

  const probarGenerarInforme = async () => {
    try {
      setLoading(true);
      setResultado('Generando informe...');

      console.log('🧪 [InformesTest] Iniciando prueba de generación de informe');
      console.log('🧪 [InformesTest] Paciente ID:', pacienteSeleccionado);

      const nombrePaciente = pacienteSeleccionado === PACIENTES_TEST.ANA_SOFIA ? 'Ana Sofia' : 'Mariana';

      const informeId = await InformesService.generarInformeCompleto(
        pacienteSeleccionado,
        `Informe de Prueba - ${nombrePaciente}`,
        'Este es un informe generado para probar la funcionalidad con interpretación cualitativa'
      );

      console.log('🧪 [InformesTest] Informe generado con ID:', informeId);
      setResultado(`✅ Informe generado exitosamente con ID: ${informeId}`);
    } catch (error) {
      console.error('🧪 [InformesTest] Error:', error);
      setResultado(`❌ Error: ${error.message || JSON.stringify(error)}`);
    } finally {
      setLoading(false);
    }
  };

  const probarObtenerInformes = async () => {
    try {
      setLoading(true);
      setResultado('Obteniendo informes...');

      console.log('🧪 [InformesTest] Obteniendo informes para:', pacienteSeleccionado);
      const informes = await InformesService.obtenerInformesPaciente(pacienteSeleccionado);
      console.log('🧪 [InformesTest] Informes obtenidos:', informes);

      setResultado(`✅ Informes obtenidos: ${informes.length}\n${JSON.stringify(informes, null, 2)}`);
    } catch (error) {
      console.error('🧪 [InformesTest] Error:', error);
      setResultado(`❌ Error: ${error.message || JSON.stringify(error)}`);
    } finally {
      setLoading(false);
    }
  };

  const verInformePrueba = async () => {
    try {
      setLoading(true);
      setResultado('Obteniendo primer informe...');

      const informes = await InformesService.obtenerInformesPaciente(pacienteSeleccionado);
      console.log('🧪 [InformesTest] Informes encontrados:', informes.length);

      if (informes.length > 0) {
        console.log('🧪 [InformesTest] Primer informe ID:', informes[0].id);
        const primerInforme = await InformesService.obtenerInforme(informes[0].id);
        console.log('🧪 [InformesTest] Contenido del informe:', primerInforme);

        const resultadosCount = primerInforme.contenido?.resultados?.length || 0;
        const pacienteNombre = primerInforme.contenido?.paciente?.nombre || 'N/A';

        setResultado(`✅ Informe obtenido exitosamente:
Título: ${primerInforme.titulo}
Tipo: ${primerInforme.tipo_informe}
Paciente: ${pacienteNombre}
Resultados: ${resultadosCount} tests
Estado: ${primerInforme.estado}
Fecha: ${new Date(primerInforme.fecha_generacion).toLocaleDateString('es-ES')}`);
      } else {
        setResultado('❌ No hay informes disponibles para este paciente');
      }
    } catch (error) {
      console.error('🧪 [InformesTest] Error:', error);
      setResultado(`❌ Error: ${error.message || JSON.stringify(error)}`);
    } finally {
      setLoading(false);
    }
  };

  return (
    <Card className="m-4">
      <CardHeader>
        <h3 className="text-lg font-semibold">🧪 Prueba de Funcionalidad de Informes</h3>
      </CardHeader>
      <CardBody>
        <div className="space-y-4">
          {/* Selector de paciente */}
          <div className="mb-4">
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Seleccionar Paciente para Pruebas:
            </label>
            <select
              value={pacienteSeleccionado}
              onChange={(e) => setPacienteSeleccionado(e.target.value)}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
            >
              <option value={PACIENTES_TEST.ANA_SOFIA}>Ana Sofia Rueda Acevedo (Percentiles variados)</option>
              <option value={PACIENTES_TEST.MARIANA}>Mariana Sanabria Rueda (Percentiles altos)</option>
            </select>
          </div>

          <div className="flex gap-4">
            <Button
              onClick={probarGenerarInforme}
              disabled={loading}
              className="bg-blue-600 text-white hover:bg-blue-700"
            >
              {loading ? 'Procesando...' : 'Generar Informe de Prueba'}
            </Button>
            
            <Button
              onClick={probarObtenerInformes}
              disabled={loading}
              className="bg-green-600 text-white hover:bg-green-700"
            >
              {loading ? 'Procesando...' : 'Obtener Informes'}
            </Button>

            <Button
              onClick={verInformePrueba}
              disabled={loading}
              className="bg-purple-600 text-white hover:bg-purple-700"
            >
              {loading ? 'Procesando...' : 'Ver Primer Informe'}
            </Button>
          </div>
          
          {resultado && (
            <div className="mt-4 p-4 bg-gray-100 rounded-lg">
              <h4 className="font-semibold mb-2">Resultado:</h4>
              <pre className="text-sm whitespace-pre-wrap">{resultado}</pre>
            </div>
          )}
        </div>
      </CardBody>
    </Card>
  );
};

export default InformesTest;
