import React from 'react';

/**
 * Componente de título simplificado para BAT-7
 * Sin efectos, solo colores planos
 */
const AnimatedTitle = ({ className = "" }) => {
  const containerStyle = {
    display: 'inline-block'
  };

  const titleStyle = {
    fontWeight: 800,
    letterSpacing: '1px'
  };

  return (
    <div className={className} style={containerStyle}>
      <h1 className="text-2xl font-bold md:text-xl sm:text-lg" style={titleStyle}>
        <span className="text-red-600">BAT-7</span>
        <span className="text-gray-600 mx-2">-</span>
        <span className="text-blue-600">Batería de Aptitudes</span>
      </h1>
    </div>
  );
};

export default AnimatedTitle;
