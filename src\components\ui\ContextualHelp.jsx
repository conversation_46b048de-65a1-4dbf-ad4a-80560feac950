import React, { useState, useRef, useEffect } from 'react';
import { FaQuestionCircle, FaInfoCircle, FaLightbulb, FaTimes, FaBook } from 'react-icons/fa';
import { motion, AnimatePresence } from 'framer-motion';

/**
 * Tooltip avanzado con posicionamiento inteligente
 */
export const SmartTooltip = ({ 
  children, 
  content, 
  position = "auto",
  trigger = "hover",
  className = "",
  maxWidth = "300px"
}) => {
  const [isVisible, setIsVisible] = useState(false);
  const [calculatedPosition, setCalculatedPosition] = useState(position);
  const triggerRef = useRef(null);
  const tooltipRef = useRef(null);

  const calculatePosition = () => {
    if (!triggerRef.current || position !== "auto") return;

    const triggerRect = triggerRef.current.getBoundingClientRect();
    const viewportWidth = window.innerWidth;
    const viewportHeight = window.innerHeight;

    // Determinar la mejor posición basada en el espacio disponible
    let bestPosition = "top";

    if (triggerRect.top < 100) bestPosition = "bottom";
    if (triggerRect.bottom > viewportHeight - 100) bestPosition = "top";
    if (triggerRect.left < 100) bestPosition = "right";
    if (triggerRect.right > viewportWidth - 100) bestPosition = "left";

    setCalculatedPosition(bestPosition);
  };

  const showTooltip = () => {
    calculatePosition();
    setIsVisible(true);
  };

  const hideTooltip = () => {
    setIsVisible(false);
  };

  const handleTrigger = () => {
    if (trigger === "click") {
      isVisible ? hideTooltip() : showTooltip();
    }
  };

  const positionClasses = {
    top: "bottom-full left-1/2 transform -translate-x-1/2 mb-2",
    bottom: "top-full left-1/2 transform -translate-x-1/2 mt-2",
    left: "right-full top-1/2 transform -translate-y-1/2 mr-2",
    right: "left-full top-1/2 transform -translate-y-1/2 ml-2"
  };

  const arrowClasses = {
    top: "top-full left-1/2 transform -translate-x-1/2 border-l-transparent border-r-transparent border-b-transparent border-t-gray-800",
    bottom: "bottom-full left-1/2 transform -translate-x-1/2 border-l-transparent border-r-transparent border-t-transparent border-b-gray-800",
    left: "left-full top-1/2 transform -translate-y-1/2 border-t-transparent border-b-transparent border-r-transparent border-l-gray-800",
    right: "right-full top-1/2 transform -translate-y-1/2 border-t-transparent border-b-transparent border-l-transparent border-r-gray-800"
  };

  return (
    <div className={`relative inline-block ${className}`}>
      <div
        ref={triggerRef}
        onMouseEnter={trigger === "hover" ? showTooltip : undefined}
        onMouseLeave={trigger === "hover" ? hideTooltip : undefined}
        onClick={trigger === "click" ? handleTrigger : undefined}
        className="cursor-help"
      >
        {children}
      </div>

      <AnimatePresence>
        {isVisible && (
          <motion.div
            ref={tooltipRef}
            className={`absolute z-50 ${positionClasses[calculatedPosition]}`}
            initial={{ opacity: 0, scale: 0.9 }}
            animate={{ opacity: 1, scale: 1 }}
            exit={{ opacity: 0, scale: 0.9 }}
            transition={{ duration: 0.2 }}
            style={{ maxWidth }}
          >
            <div className="bg-gray-800 text-white text-sm rounded-lg px-3 py-2 shadow-lg">
              {content}
              <div className={`absolute w-0 h-0 border-4 ${arrowClasses[calculatedPosition]}`} />
            </div>
          </motion.div>
        )}
      </AnimatePresence>
    </div>
  );
};

/**
 * Icono de ayuda con tooltip
 */
export const HelpIcon = ({ 
  content, 
  size = "sm",
  color = "gray",
  position = "auto"
}) => {
  const sizeClasses = {
    xs: "text-xs",
    sm: "text-sm",
    md: "text-base",
    lg: "text-lg"
  };

  const colorClasses = {
    gray: "text-gray-400 hover:text-gray-600",
    blue: "text-blue-400 hover:text-blue-600",
    green: "text-green-400 hover:text-green-600"
  };

  return (
    <SmartTooltip content={content} position={position}>
      <FaQuestionCircle className={`${sizeClasses[size]} ${colorClasses[color]} transition-colors cursor-help`} />
    </SmartTooltip>
  );
};

/**
 * Panel de ayuda contextual expandible
 */
export const ContextualHelpPanel = ({ 
  title,
  sections,
  isOpen,
  onClose,
  className = ""
}) => {
  const [activeSection, setActiveSection] = useState(0);

  return (
    <AnimatePresence>
      {isOpen && (
        <>
          {/* Backdrop */}
          <motion.div
            className="fixed inset-0 bg-black bg-opacity-50 z-40"
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            onClick={onClose}
          />

          {/* Panel */}
          <motion.div
            className={`fixed right-0 top-0 h-full w-96 bg-white shadow-2xl z-50 overflow-hidden ${className}`}
            initial={{ x: "100%" }}
            animate={{ x: 0 }}
            exit={{ x: "100%" }}
            transition={{ type: "spring", damping: 25, stiffness: 200 }}
          >
            {/* Header */}
            <div className="bg-blue-600 text-white p-4">
              <div className="flex items-center justify-between">
                <div className="flex items-center">
                  <FaBook className="mr-2" />
                  <h3 className="text-lg font-semibold">{title}</h3>
                </div>
                <button
                  onClick={onClose}
                  className="text-white hover:text-gray-200 transition-colors"
                >
                  <FaTimes />
                </button>
              </div>
            </div>

            {/* Navigation */}
            <div className="border-b border-gray-200">
              <nav className="flex overflow-x-auto">
                {sections.map((section, index) => (
                  <button
                    key={index}
                    onClick={() => setActiveSection(index)}
                    className={`px-4 py-3 text-sm font-medium whitespace-nowrap border-b-2 transition-colors ${
                      activeSection === index
                        ? 'border-blue-500 text-blue-600'
                        : 'border-transparent text-gray-500 hover:text-gray-700'
                    }`}
                  >
                    {section.title}
                  </button>
                ))}
              </nav>
            </div>

            {/* Content */}
            <div className="flex-1 overflow-y-auto p-4">
              <AnimatePresence mode="wait">
                <motion.div
                  key={activeSection}
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  exit={{ opacity: 0, y: -20 }}
                  transition={{ duration: 0.2 }}
                >
                  {sections[activeSection]?.content}
                </motion.div>
              </AnimatePresence>
            </div>
          </motion.div>
        </>
      )}
    </AnimatePresence>
  );
};

/**
 * Guía interactiva paso a paso
 */
export const InteractiveGuide = ({ 
  steps,
  isActive,
  onComplete,
  onSkip
}) => {
  const [currentStep, setCurrentStep] = useState(0);
  const [highlightedElement, setHighlightedElement] = useState(null);

  useEffect(() => {
    if (isActive && steps[currentStep]?.selector) {
      const element = document.querySelector(steps[currentStep].selector);
      setHighlightedElement(element);
      
      if (element) {
        element.scrollIntoView({ behavior: 'smooth', block: 'center' });
      }
    }
  }, [currentStep, isActive, steps]);

  const nextStep = () => {
    if (currentStep < steps.length - 1) {
      setCurrentStep(currentStep + 1);
    } else {
      onComplete();
    }
  };

  const prevStep = () => {
    if (currentStep > 0) {
      setCurrentStep(currentStep - 1);
    }
  };

  if (!isActive) return null;

  const currentStepData = steps[currentStep];

  return (
    <>
      {/* Overlay */}
      <div className="fixed inset-0 bg-black bg-opacity-50 z-50" />
      
      {/* Highlight */}
      {highlightedElement && (
        <div
          className="fixed border-4 border-blue-500 rounded-lg pointer-events-none z-50"
          style={{
            top: highlightedElement.offsetTop - 4,
            left: highlightedElement.offsetLeft - 4,
            width: highlightedElement.offsetWidth + 8,
            height: highlightedElement.offsetHeight + 8
          }}
        />
      )}

      {/* Guide popup */}
      <motion.div
        className="fixed z-50 bg-white rounded-lg shadow-2xl p-6 max-w-md"
        style={{
          top: highlightedElement ? highlightedElement.offsetTop + highlightedElement.offsetHeight + 10 : '50%',
          left: highlightedElement ? highlightedElement.offsetLeft : '50%',
          transform: highlightedElement ? 'none' : 'translate(-50%, -50%)'
        }}
        initial={{ opacity: 0, scale: 0.9 }}
        animate={{ opacity: 1, scale: 1 }}
        transition={{ duration: 0.3 }}
      >
        <div className="flex items-start mb-4">
          <FaLightbulb className="text-yellow-500 text-xl mr-3 mt-1" />
          <div>
            <h4 className="font-semibold text-gray-900 mb-2">
              Paso {currentStep + 1} de {steps.length}
            </h4>
            <h3 className="text-lg font-bold text-gray-900 mb-2">
              {currentStepData.title}
            </h3>
            <p className="text-gray-600">
              {currentStepData.description}
            </p>
          </div>
        </div>

        {/* Progress bar */}
        <div className="w-full bg-gray-200 rounded-full h-2 mb-4">
          <div
            className="bg-blue-600 h-2 rounded-full transition-all duration-300"
            style={{ width: `${((currentStep + 1) / steps.length) * 100}%` }}
          />
        </div>

        {/* Actions */}
        <div className="flex justify-between">
          <div>
            {currentStep > 0 && (
              <button
                onClick={prevStep}
                className="px-4 py-2 text-gray-600 hover:text-gray-800 transition-colors"
              >
                Anterior
              </button>
            )}
          </div>
          
          <div className="flex space-x-2">
            <button
              onClick={onSkip}
              className="px-4 py-2 text-gray-600 hover:text-gray-800 transition-colors"
            >
              Saltar
            </button>
            <button
              onClick={nextStep}
              className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
            >
              {currentStep === steps.length - 1 ? 'Finalizar' : 'Siguiente'}
            </button>
          </div>
        </div>
      </motion.div>
    </>
  );
};

/**
 * Contenido de ayuda para diferentes secciones del dashboard
 */
export const dashboardHelpContent = {
  filters: {
    title: "Sistema de Filtros",
    sections: [
      {
        title: "Básico",
        content: (
          <div className="space-y-4">
            <h4 className="font-semibold">Filtros Básicos</h4>
            <p className="text-sm text-gray-600">
              Utiliza los filtros para segmentar los datos según tus necesidades específicas.
            </p>
            <ul className="text-sm text-gray-600 space-y-1">
              <li>• <strong>Fecha:</strong> Filtra por rango de fechas de evaluación</li>
              <li>• <strong>Institución:</strong> Selecciona una institución específica</li>
              <li>• <strong>Nivel:</strong> Escolar (E), Medio (M) o Superior (S)</li>
            </ul>
          </div>
        )
      },
      {
        title: "Avanzado",
        content: (
          <div className="space-y-4">
            <h4 className="font-semibold">Filtros Avanzados</h4>
            <p className="text-sm text-gray-600">
              Combina múltiples filtros para análisis más específicos.
            </p>
            <ul className="text-sm text-gray-600 space-y-1">
              <li>• <strong>Rango de edad:</strong> Define edades mínima y máxima</li>
              <li>• <strong>Psicólogo:</strong> Filtra por profesional evaluador</li>
              <li>• <strong>Paciente específico:</strong> Análisis individual</li>
            </ul>
          </div>
        )
      }
    ]
  },
  
  charts: {
    title: "Interpretación de Gráficos",
    sections: [
      {
        title: "Gráfico Radar",
        content: (
          <div className="space-y-4">
            <h4 className="font-semibold">Gráfico de Radar (Araña)</h4>
            <p className="text-sm text-gray-600">
              Muestra el perfil aptitudinal completo en un formato visual intuitivo.
            </p>
            <ul className="text-sm text-gray-600 space-y-1">
              <li>• Cada eje representa una aptitud del BAT-7</li>
              <li>• Los valores van del centro (bajo) al exterior (alto)</li>
              <li>• El área sombreada muestra el perfil general</li>
            </ul>
          </div>
        )
      },
      {
        title: "Gráfico de Barras",
        content: (
          <div className="space-y-4">
            <h4 className="font-semibold">Gráfico de Barras Comparativo</h4>
            <p className="text-sm text-gray-600">
              Permite comparar puntuaciones entre diferentes grupos o individuos.
            </p>
            <ul className="text-sm text-gray-600 space-y-1">
              <li>• Altura de barras = nivel de rendimiento</li>
              <li>• Colores indican rangos de percentiles</li>
              <li>• Facilita identificación de fortalezas/debilidades</li>
            </ul>
          </div>
        )
      }
    ]
  }
};

export default {
  SmartTooltip,
  HelpIcon,
  ContextualHelpPanel,
  InteractiveGuide,
  dashboardHelpContent
};
