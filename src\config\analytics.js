/**
 * Configuración principal del sistema de analytics mejorado
 * Centraliza todas las configuraciones del módulo analytics
 * Incluye configuraciones de rendimiento, cache y optimizaciones
 */

import { TIME_RANGES, CHART_TYPES, EXPORT_FORMATS } from '../utils/analytics/constants.js';
import { CHART_THEMES } from '../utils/analytics/chartThemes.js';

// Configuración general del sistema analytics
export const ANALYTICS_CONFIG = {
  // Configuración de la aplicación
  app: {
    name: 'BAT-7 Analytics Dashboard',
    version: '1.0.0',
    description: 'Sistema de análisis avanzado para evaluaciones psicométricas BAT-7'
  },

  // Configuración de datos por defecto
  defaults: {
    timeRange: TIME_RANGES.LAST_30_DAYS,
    chartType: CHART_TYPES.LINE,
    theme: 'light',
    pageSize: 20,
    refreshInterval: 300000, // 5 minutos
    autoRefresh: false
  },

  // Configuración de características habilitadas
  features: {
    advancedAnalytics: true,
    patientProgress: true,
    institutionalMetrics: true,
    comparativeAnalysis: true,
    scheduledReports: true,
    customDashboards: true,
    exportFunctionality: true,
    realTimeUpdates: false, // Para implementación futura
    predictiveAnalytics: false // Para implementación futura
  },

  // Configuración de límites del sistema
  limits: {
    maxDataPoints: 10000,
    maxExportRecords: 50000,
    maxCustomWidgets: 20,
    maxScheduledReports: 10,
    maxFilterCombinations: 100,
    chartAnimationDuration: 750
  },

  // Configuración de rendimiento
  performance: {
    enableVirtualization: true,
    lazyLoadCharts: true,
    cacheEnabled: true,
    cacheTTL: 300000, // 5 minutos
    debounceDelay: 300,
    throttleDelay: 100
  },

  // Configuración de UI/UX
  ui: {
    theme: CHART_THEMES.light,
    animations: {
      enabled: true,
      duration: 300,
      easing: 'ease-in-out'
    },
    responsive: {
      breakpoints: {
        mobile: 768,
        tablet: 1024,
        desktop: 1280
      }
    },
    accessibility: {
      enabled: true,
      highContrast: false,
      screenReaderSupport: true,
      keyboardNavigation: true
    }
  },

  // Configuración de exportación
  export: {
    enabledFormats: [
      EXPORT_FORMATS.PDF,
      EXPORT_FORMATS.EXCEL,
      EXPORT_FORMATS.CSV,
      EXPORT_FORMATS.PNG
    ],
    defaultFormat: EXPORT_FORMATS.PDF,
    quality: {
      image: 0.9,
      pdf: 'high'
    },
    compression: {
      enabled: true,
      level: 6
    }
  },

  // Configuración de notificaciones
  notifications: {
    enabled: true,
    position: 'top-right',
    duration: 5000,
    types: {
      success: true,
      error: true,
      warning: true,
      info: true
    }
  },

  // Configuración de logging
  logging: {
    enabled: true,
    level: 'info', // 'debug', 'info', 'warn', 'error'
    console: true,
    remote: false, // Para implementación futura
    maxLogEntries: 1000
  },

  // Configuración de seguridad
  security: {
    enableCSRFProtection: true,
    sanitizeInputs: true,
    validatePermissions: true,
    auditTrail: true
  }
};

// Configuración específica por entorno
export const ENVIRONMENT_CONFIG = {
  development: {
    ...ANALYTICS_CONFIG,
    logging: {
      ...ANALYTICS_CONFIG.logging,
      level: 'debug',
      console: true
    },
    performance: {
      ...ANALYTICS_CONFIG.performance,
      cacheEnabled: false
    }
  },

  production: {
    ...ANALYTICS_CONFIG,
    logging: {
      ...ANALYTICS_CONFIG.logging,
      level: 'error',
      console: false
    },
    performance: {
      ...ANALYTICS_CONFIG.performance,
      cacheEnabled: true
    }
  },

  test: {
    ...ANALYTICS_CONFIG,
    features: {
      ...ANALYTICS_CONFIG.features,
      scheduledReports: false,
      realTimeUpdates: false
    },
    notifications: {
      ...ANALYTICS_CONFIG.notifications,
      enabled: false
    }
  }
};

// Función para obtener configuración según el entorno
export const getConfig = (environment = 'development') => {
  return ENVIRONMENT_CONFIG[environment] || ENVIRONMENT_CONFIG.development;
};

// Configuración de rutas del sistema analytics
export const ANALYTICS_ROUTES = {
  dashboard: '/admin/dashboard',
  analytics: '/admin/analytics',
  patientProgress: '/admin/analytics/patient-progress',
  institutionalMetrics: '/admin/analytics/institutional',
  comparativeAnalysis: '/admin/analytics/comparative',
  scheduledReports: '/admin/analytics/reports',
  customDashboard: '/admin/analytics/custom',
  systemMetrics: '/admin/analytics/system',
  export: '/admin/analytics/export'
};

// Configuración de API endpoints
export const API_ENDPOINTS = {
  analytics: '/api/analytics',
  institutionalMetrics: '/api/analytics/institutional',
  patientProgress: '/api/analytics/patient-progress',
  comparativeAnalysis: '/api/analytics/comparative',
  systemMetrics: '/api/analytics/system',
  export: '/api/analytics/export',
  scheduledReports: '/api/analytics/scheduled-reports'
};

// Configuración de permisos
export const PERMISSIONS = {
  VIEW_ANALYTICS: 'analytics:view',
  VIEW_INSTITUTIONAL_METRICS: 'analytics:institutional:view',
  VIEW_PATIENT_PROGRESS: 'analytics:patient:view',
  EXPORT_DATA: 'analytics:export',
  MANAGE_SCHEDULED_REPORTS: 'analytics:reports:manage',
  VIEW_SYSTEM_METRICS: 'analytics:system:view',
  CUSTOMIZE_DASHBOARD: 'analytics:dashboard:customize'
};

export default {
  ANALYTICS_CONFIG,
  ENVIRONMENT_CONFIG,
  getConfig,
  ANALYTICS_ROUTES,
  API_ENDPOINTS,
  PERMISSIONS
};