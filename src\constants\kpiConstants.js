/**
 * @file kpiConstants.js
 * @description Constants for KPI status handling and configuration
 */

import { 
  CheckCircleIcon,
  ExclamationTriangleIcon,
  XCircleIcon
} from '@heroicons/react/24/outline';

export const KPI_STATUS_TYPES = {
  EXCELLENT: 'excellent',
  GOOD: 'good', 
  WARNING: 'warning',
  CRITICAL: 'critical',
  ERROR: 'error'
};

export const KPI_STATUS_CONFIG = {
  [KPI_STATUS_TYPES.EXCELLENT]: {
    textColor: 'text-green-600',
    bgColor: 'bg-green-50',
    borderColor: 'border-green-200',
    cardClasses: 'text-green-600 bg-green-50 border-green-200',
    chartColor: 'bg-green-500',
    icon: <CheckCircleIcon className="h-5 w-5" />
  },
  [KPI_STATUS_TYPES.GOOD]: {
    textColor: 'text-blue-600',
    bgColor: 'bg-blue-50', 
    borderColor: 'border-blue-200',
    cardClasses: 'text-blue-600 bg-blue-50 border-blue-200',
    chartColor: 'bg-blue-500',
    icon: <CheckCircleIcon className="h-5 w-5" />
  },
  [KPI_STATUS_TYPES.WARNING]: {
    textColor: 'text-yellow-600',
    bgColor: 'bg-yellow-50',
    borderColor: 'border-yellow-200', 
    cardClasses: 'text-yellow-600 bg-yellow-50 border-yellow-200',
    chartColor: 'bg-yellow-500',
    icon: <ExclamationTriangleIcon className="h-5 w-5" />
  },
  [KPI_STATUS_TYPES.CRITICAL]: {
    textColor: 'text-red-600',
    bgColor: 'bg-red-50',
    borderColor: 'border-red-200',
    cardClasses: 'text-red-600 bg-red-50 border-red-200', 
    chartColor: 'bg-red-500',
    icon: <XCircleIcon className="h-5 w-5" />
  },
  [KPI_STATUS_TYPES.ERROR]: {
    textColor: 'text-gray-600',
    bgColor: 'bg-gray-50',
    borderColor: 'border-gray-200',
    cardClasses: 'text-gray-600 bg-gray-50 border-gray-200',
    chartColor: 'bg-gray-500', 
    icon: <XCircleIcon className="h-5 w-5" />
  },
  default: {
    textColor: 'text-gray-600',
    bgColor: 'bg-gray-50',
    borderColor: 'border-gray-200',
    cardClasses: 'text-gray-600 bg-gray-50 border-gray-200',
    chartColor: 'bg-gray-500',
    icon: null
  }
};

export const KPI_THRESHOLDS = {
  EXCELLENT_THRESHOLD: 1.05, // 5% above target
  WARNING_THRESHOLD: 0.9,    // 90% of target
  CRITICAL_THRESHOLD: 0.8    // 80% of target
};

export const ALERT_SEVERITY_LEVELS = {
  HIGH: 'high',
  MEDIUM: 'medium', 
  LOW: 'low'
};

export const ALERT_SEVERITY_CONFIG = {
  [ALERT_SEVERITY_LEVELS.HIGH]: {
    bgColor: 'bg-red-100',
    textColor: 'text-red-800',
    label: 'Alta'
  },
  [ALERT_SEVERITY_LEVELS.MEDIUM]: {
    bgColor: 'bg-yellow-100', 
    textColor: 'text-yellow-800',
    label: 'Media'
  },
  [ALERT_SEVERITY_LEVELS.LOW]: {
    bgColor: 'bg-blue-100',
    textColor: 'text-blue-800', 
    label: 'Baja'
  }
};