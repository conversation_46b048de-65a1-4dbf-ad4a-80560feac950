/**
 * @file thresholds.js
 * @description Centralized thresholds and constants for the BAT-7 system
 */

export const PERFORMANCE_THRESHOLDS = {
  EXCELLENT: 90,
  GOOD: 75,
  AVERAGE: 60,
  NEEDS_IMPROVEMENT: 50,
  CRITICAL: 40
};

export const COVERAGE_THRESHOLDS = {
  EXCELLENT: 90,
  GOOD: 80,
  AVERA<PERSON>: 70,
  NEEDS_IMPROVEMENT: 60,
  CRITICAL: 50
};

export const VARIABILITY_THRESHOLDS = {
  LOW: 15,
  MODERATE: 20,
  HIGH: 25,
  VERY_HIGH: 30
};

export const KPI_TARGETS = {
  RENDIMIENTO_GENERAL: 75,
  TASA_FINALIZACION: 90,
  PRODUCTIVIDAD_SEMANAL: 10,
  CONSISTENCIA_MAX: 20,
  COBERTURA_APTITUDES: 7
};

export const GRADE_THRESHOLDS = {
  A_PLUS: 90,
  A: 80,
  B_PLUS: 70,
  B: 60,
  C_PLUS: 50
};

export const ALERT_DEADLINES = {
  CRITICAL: '30 días',
  HIGH: '60 días',
  MEDIUM: '90 días',
  LOW: '120 días'
};