/**
 * @file trendAnalysisConfig.js
 * @description Configuration constants for trend analysis
 */

export const TREND_ANALYSIS_CONFIG = {
  // Thresholds for trend significance
  TREND_THRESHOLDS: {
    SLOPE_SIGNIFICANT: 1.0,
    SLOPE_MODERATE: 0.5,
    R_SQUARED_SIGNIFICANT: 0.5,
    R_SQUARED_MODERATE: 0.3,
    MIN_SAMPLE_SIZE: 5
  },

  // Change point detection
  CHANGE_POINT: {
    MIN_WINDOW_SIZE: 3,
    SIGNIFICANT_CHANGE: 10, // percentiles
    MAJOR_CHANGE: 15 // percentiles
  },

  // Seasonality analysis
  SEASONALITY: {
    MIN_PERIODS_FOR_ANALYSIS: 12,
    STANDARD_DEVIATION_MULTIPLIER: 1.0
  },

  // Forecasting
  FORECASTING: {
    DEFAULT_PERIODS: 3,
    CONFIDENCE_INTERVAL_WIDTH: 5,
    MIN_PERCENTILE: 0,
    MAX_PERCENTILE: 100
  },

  // Default analysis configuration
  DEFAULT_CONFIG: {
    period: 'monthly',
    timeRange: 12,
    aptitudes: 'all',
    groupBy: null,
    includeForecasting: true
  }
};