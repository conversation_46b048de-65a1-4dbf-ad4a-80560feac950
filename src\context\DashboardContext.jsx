import React, { createContext, useState, useEffect, useCallback, useMemo } from 'react';
import { toast } from 'react-toastify';
import dashboardService from '../services/dashboardService';

// Crear el contexto
export const DashboardContext = createContext();

// Proveedor del contexto
export const DashboardProvider = ({ children }) => {
  const [filters, setFilters] = useState({
    dateRange: {
      startDate: null,
      endDate: null,
    },
    institutions: [],
    psychologists: [],
    testLevel: 'all', // 'E', 'M', 'S'
    populations: [],
  });
  const [data, setData] = useState(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);
  const [lastUpdated, setLastUpdated] = useState(null);

  const fetchData = useCallback(async () => {
    setLoading(true);
    setError(null);
    try {
      const result = await dashboardService.fetchDashboardData(filters);
      setData(result);
      setLastUpdated(new Date());
      toast.success('Datos del dashboard actualizados.');
    } catch (err) {
      const errorMessage = err.message || 'Ocurrió un error desconocido.';
      setError(errorMessage);
      toast.error(`Error al obtener datos: ${errorMessage}`);
    } finally {
      setLoading(false);
    }
  }, [filters]); // Depende de los filtros

  // Efecto para cargar datos cuando los filtros cambian
  useEffect(() => {
    fetchData();
  }, [fetchData]);

  const applyFilters = (newFilters) => {
    setFilters(prevFilters => ({ ...prevFilters, ...newFilters }));
  };

  const refetch = () => {
    fetchData();
  };

  // Usar useMemo para evitar re-renders innecesarios en los consumidores del contexto
  const value = useMemo(() => ({
    filters,
    applyFilters,
    data,
    loading,
    error,
    lastUpdated,
    refetch,
  }), [filters, data, loading, error, lastUpdated]);

  return (
    <DashboardContext.Provider value={value}>
      {children}
    </DashboardContext.Provider>
  );
};