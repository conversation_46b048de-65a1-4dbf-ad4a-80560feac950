import React, { createContext, useContext, useState } from 'react';

// Crear el contexto
const PatientContext = createContext();

// Hook personalizado para usar el contexto
export const usePatient = () => {
  const context = useContext(PatientContext);
  if (!context) {
    throw new Error('usePatient debe ser usado dentro de un PatientProvider');
  }
  return context;
};

// Proveedor del contexto
export const PatientProvider = ({ children }) => {
  const [selectedPatient, setSelectedPatient] = useState(null);
  const [currentTest, setCurrentTest] = useState(null);

  const selectPatient = (patient) => {
    setSelectedPatient(patient);
  };

  const clearPatient = () => {
    setSelectedPatient(null);
    setCurrentTest(null);
  };

  const setTestInProgress = (testInfo) => {
    setCurrentTest(testInfo);
  };

  const clearTest = () => {
    setCurrentTest(null);
  };

  const value = {
    selectedPatient,
    currentTest,
    selectPatient,
    clearPatient,
    setTestInProgress,
    clearTest
  };

  return (
    <PatientContext.Provider value={value}>
      {children}
    </PatientContext.Provider>
  );
};

export default PatientContext;
