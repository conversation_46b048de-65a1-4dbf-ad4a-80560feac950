/**
 * @file useAdvancedFilters.js
 * @description Hook avanzado para manejo de filtros con validaciones y optimizaciones
 */

import { useState, useEffect, useCallback, useMemo } from 'react';
import { debounce } from 'lodash';

export const useAdvancedFilters = (initialFilters = {}, onFiltersChange) => {
  const [filters, setFilters] = useState(initialFilters);
  const [validationErrors, setValidationErrors] = useState({});
  const [isApplying, setIsApplying] = useState(false);
  const [filterHistory, setFilterHistory] = useState([initialFilters]);
  const [currentHistoryIndex, setCurrentHistoryIndex] = useState(0);

  // Validaciones de filtros
  const validateFilters = useCallback((filterData) => {
    const errors = {};

    // Validar rango de fechas
    if (filterData.fechaInicio && filterData.fechaFin) {
      const inicio = new Date(filterData.fechaInicio);
      const fin = new Date(filterData.fechaFin);
      
      if (inicio > fin) {
        errors.fechaFin = 'La fecha de fin debe ser posterior a la fecha de inicio';
      }
      
      if (inicio > new Date()) {
        errors.fechaInicio = 'La fecha de inicio no puede ser futura';
      }
    }

    // Validar rango de edad
    if (filterData.rangoEdad) {
      const { min, max } = filterData.rangoEdad;
      if (min && max && parseInt(min) > parseInt(max)) {
        errors.rangoEdad = 'La edad mínima debe ser menor que la máxima';
      }
      if (min && (parseInt(min) < 0 || parseInt(min) > 120)) {
        errors.rangoEdadMin = 'La edad mínima debe estar entre 0 y 120 años';
      }
      if (max && (parseInt(max) < 0 || parseInt(max) > 120)) {
        errors.rangoEdadMax = 'La edad máxima debe estar entre 0 y 120 años';
      }
    }

    // Validar combinaciones lógicas
    if (filterData.nivelAplicacion === 'E' && filterData.rangoEdad?.min && parseInt(filterData.rangoEdad.min) > 18) {
      errors.combinacion = 'El nivel Escolar (E) no es apropiado para edades mayores a 18 años';
    }

    return errors;
  }, []);

  // Aplicar filtros con debounce para optimizar rendimiento
  const debouncedApplyFilters = useMemo(
    () => debounce(async (newFilters) => {
      setIsApplying(true);
      
      try {
        // Validar filtros antes de aplicar
        const errors = validateFilters(newFilters);
        setValidationErrors(errors);
        
        // Solo aplicar si no hay errores
        if (Object.keys(errors).length === 0) {
          await onFiltersChange(newFilters);
          
          // Agregar al historial
          setFilterHistory(prev => {
            const newHistory = prev.slice(0, currentHistoryIndex + 1);
            newHistory.push(newFilters);
            return newHistory;
          });
          setCurrentHistoryIndex(prev => prev + 1);
        }
      } catch (error) {
        console.error('Error aplicando filtros:', error);
      } finally {
        setIsApplying(false);
      }
    }, 500),
    [onFiltersChange, validateFilters, currentHistoryIndex]
  );

  // Actualizar filtro individual
  const updateFilter = useCallback((key, value) => {
    const newFilters = { ...filters, [key]: value };
    setFilters(newFilters);
    debouncedApplyFilters(newFilters);
  }, [filters, debouncedApplyFilters]);

  // Actualizar múltiples filtros
  const updateMultipleFilters = useCallback((updates) => {
    const newFilters = { ...filters, ...updates };
    setFilters(newFilters);
    debouncedApplyFilters(newFilters);
  }, [filters, debouncedApplyFilters]);

  // Limpiar filtros
  const clearFilters = useCallback(() => {
    const clearedFilters = {
      fechaInicio: null,
      fechaFin: null,
      institucion: null,
      nivelAplicacion: null,
      categoriaEvaluado: null,
      rangoEdad: { min: null, max: null },
      cursoGrado: null,
      psicologo: null,
      pacienteEspecifico: null
    };
    setFilters(clearedFilters);
    setValidationErrors({});
    debouncedApplyFilters(clearedFilters);
  }, [debouncedApplyFilters]);

  // Aplicar filtros predefinidos
  const applyPreset = useCallback((presetName) => {
    let presetFilters = {};
    
    switch (presetName) {
      case 'ultimoMes':
        const lastMonth = new Date();
        lastMonth.setMonth(lastMonth.getMonth() - 1);
        presetFilters = {
          ...filters,
          fechaInicio: lastMonth.toISOString().split('T')[0],
          fechaFin: new Date().toISOString().split('T')[0]
        };
        break;
        
      case 'escolares':
        presetFilters = {
          ...filters,
          nivelAplicacion: 'E',
          categoriaEvaluado: 'Escolares',
          rangoEdad: { min: 6, max: 18 }
        };
        break;
        
      case 'adultos':
        presetFilters = {
          ...filters,
          nivelAplicacion: 'S',
          categoriaEvaluado: 'Adultos',
          rangoEdad: { min: 18, max: null }
        };
        break;
        
      case 'riesgo':
        presetFilters = {
          ...filters,
          // Filtros específicos para identificar casos de riesgo
          // Se implementaría según criterios específicos
        };
        break;
        
      default:
        presetFilters = filters;
    }
    
    setFilters(presetFilters);
    debouncedApplyFilters(presetFilters);
  }, [filters, debouncedApplyFilters]);

  // Navegación en historial
  const goBackInHistory = useCallback(() => {
    if (currentHistoryIndex > 0) {
      const newIndex = currentHistoryIndex - 1;
      const historicalFilters = filterHistory[newIndex];
      setCurrentHistoryIndex(newIndex);
      setFilters(historicalFilters);
      debouncedApplyFilters(historicalFilters);
    }
  }, [currentHistoryIndex, filterHistory, debouncedApplyFilters]);

  const goForwardInHistory = useCallback(() => {
    if (currentHistoryIndex < filterHistory.length - 1) {
      const newIndex = currentHistoryIndex + 1;
      const historicalFilters = filterHistory[newIndex];
      setCurrentHistoryIndex(newIndex);
      setFilters(historicalFilters);
      debouncedApplyFilters(historicalFilters);
    }
  }, [currentHistoryIndex, filterHistory, debouncedApplyFilters]);

  // Guardar/cargar configuraciones de filtros
  const saveFilterConfiguration = useCallback((name) => {
    const savedConfigs = JSON.parse(localStorage.getItem('bat7_filter_configs') || '{}');
    savedConfigs[name] = filters;
    localStorage.setItem('bat7_filter_configs', JSON.stringify(savedConfigs));
  }, [filters]);

  const loadFilterConfiguration = useCallback((name) => {
    const savedConfigs = JSON.parse(localStorage.getItem('bat7_filter_configs') || '{}');
    if (savedConfigs[name]) {
      setFilters(savedConfigs[name]);
      debouncedApplyFilters(savedConfigs[name]);
    }
  }, [debouncedApplyFilters]);

  const getSavedConfigurations = useCallback(() => {
    return Object.keys(JSON.parse(localStorage.getItem('bat7_filter_configs') || '{}'));
  }, []);

  // Estadísticas de filtros aplicados
  const filterStats = useMemo(() => {
    const activeFilters = Object.entries(filters).filter(([key, value]) => {
      if (value === null || value === undefined || value === '') return false;
      if (typeof value === 'object' && value.min === null && value.max === null) return false;
      return true;
    });

    return {
      totalActive: activeFilters.length,
      hasDateRange: !!(filters.fechaInicio || filters.fechaFin),
      hasAgeRange: !!(filters.rangoEdad?.min || filters.rangoEdad?.max),
      hasSpecificPatient: !!filters.pacienteEspecifico,
      isIndividualAnalysis: !!filters.pacienteEspecifico
    };
  }, [filters]);

  // Cleanup del debounce al desmontar
  useEffect(() => {
    return () => {
      debouncedApplyFilters.cancel();
    };
  }, [debouncedApplyFilters]);

  return {
    // Estado
    filters,
    validationErrors,
    isApplying,
    filterStats,
    
    // Historial
    canGoBack: currentHistoryIndex > 0,
    canGoForward: currentHistoryIndex < filterHistory.length - 1,
    
    // Acciones básicas
    updateFilter,
    updateMultipleFilters,
    clearFilters,
    
    // Presets
    applyPreset,
    
    // Historial
    goBackInHistory,
    goForwardInHistory,
    
    // Configuraciones guardadas
    saveFilterConfiguration,
    loadFilterConfiguration,
    getSavedConfigurations,
    
    // Utilidades
    validateFilters
  };
};

export default useAdvancedFilters;
