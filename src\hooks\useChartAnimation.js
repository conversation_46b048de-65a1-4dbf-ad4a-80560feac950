/**
 * @file useChartAnimation.js
 * @description Custom hook for managing chart animations
 */

import { useState, useEffect, useMemo } from 'react';

export const useChartAnimation = (data, dependencies = []) => {
  const [animationKey, setAnimationKey] = useState(0);

  // Create a stable hash of the data to detect meaningful changes
  const dataHash = useMemo(() => {
    if (!data || !Array.isArray(data)) return 'empty';
    
    return data
      .map(item => `${item.nivel}-${item.total_estudiantes}-${item.porcentaje}`)
      .join('|');
  }, [data]);

  useEffect(() => {
    setAnimationKey(prev => prev + 1);
  }, [dataHash, ...dependencies]);

  return animationKey;
};