import { useState, useEffect, useCallback } from 'react';
import DashboardService from '../services/DashboardService';
import IntegratedAnalyticsService from '../services/analytics/IntegratedAnalyticsService';
import { toast } from 'react-toastify';

/**
 * Hook personalizado para manejar todos los datos del dashboard
 * Centraliza la lógica de obtención de datos y manejo de estados
 */
export const useDashboardData = (filters = {}) => {
  // Estados principales
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [lastUpdated, setLastUpdated] = useState(null);

  // Estado unificado para todos los datos
  const [dashboardData, setDashboardData] = useState({
    estadisticasGenerales: null,
    datosDistribucionNivel: [],
    datosPerfilInstitucional: null,
    datosComparativaNivel: null,
    datosComparativaGenero: null,
    kpiData: null,
    trendData: [],
    alertsData: [],
    advancedMetrics: null
  });

  // Función para cargar todos los datos del dashboard usando el servicio integrado
  const fetchDashboardData = useCallback(async () => {
    try {
      setLoading(true);
      setError(null);

      console.log('📊 [useDashboardData] Cargando datos del dashboard con servicio integrado...');

      // Usar el servicio integrado que optimiza las consultas y maneja cache
      const allData = await IntegratedAnalyticsService.getAllDashboardData(filters);
      
      console.log('✅ [useDashboardData] Todos los datos cargados correctamente');
      
      // Actualizar estado unificado
      setDashboardData(allData);
      setLastUpdated(allData.lastUpdated);

    } catch (error) {
      console.error('💥 [useDashboardData] Error:', error);
      
      // Datos de fallback optimizados
      const fallbackData = {
        estadisticasGenerales: {
          total_pacientes: 156,
          pacientes_evaluados: 148,
          total_evaluaciones: 1092,
          percentil_promedio_general: 72.5,
          evaluaciones_ultimo_mes: 234,
          evaluaciones_ultima_semana: 67
        },
        datosDistribucionNivel: [
          { name: 'Elemental', value: 45, color: '#3B82F6' },
          { name: 'Medio', value: 35, color: '#10B981' },
          { name: 'Superior', value: 20, color: '#F59E0B' }
        ],
        datosPerfilInstitucional: {
          labels: ['Verbal', 'Espacial', 'Atención', 'Razonamiento', 'Numérico', 'Mecánico', 'Ortografía'],
          datasets: [{
            label: 'Percentil Promedio',
            data: [72, 68, 75, 70, 65, 58, 80],
            backgroundColor: 'rgba(59, 130, 246, 0.2)',
            borderColor: 'rgba(59, 130, 246, 1)',
            borderWidth: 2
          }]
        },
        datosComparativaNivel: null,
        datosComparativaGenero: null,
        kpiData: null,
        trendData: [],
        alertsData: [
          {
            id: 'fallback_warning',
            type: 'warning',
            title: 'Usando Datos de Ejemplo',
            message: 'No se pudo conectar con la base de datos. Mostrando datos de ejemplo.',
            action: 'Verificar conexión',
            priority: 'high'
          }
        ],
        advancedMetrics: null,
        lastUpdated: new Date()
      };
      
      setDashboardData(fallbackData);
      setError('Usando datos de ejemplo - Verifique la conexión a Supabase');
      toast.warning('Usando datos de ejemplo');
    } finally {
      setLoading(false);
    }
  }, [filters]);

  // Aplicar filtros
  const applyFilters = useCallback(async (newFilters) => {
    try {
      setLoading(true);
      console.log('🔍 [useDashboardData] Aplicando filtros:', newFilters);
      
      // Usar el servicio integrado para aplicar filtros
      const result = await IntegratedAnalyticsService.applyFilters(newFilters);
      
      // Actualizar datos con los resultados filtrados
      setDashboardData(result.dashboardData);
      setLastUpdated(result.appliedAt);
      
      console.log('✅ [useDashboardData] Filtros aplicados correctamente');
      toast.success('Filtros aplicados correctamente');
    } catch (error) {
      console.error('❌ [useDashboardData] Error al aplicar filtros:', error);
      toast.error('Error al aplicar filtros');
    } finally {
      setLoading(false);
    }
  }, []);

  // Efecto para cargar datos iniciales y cuando cambien los filtros
  useEffect(() => {
    fetchDashboardData();
  }, [fetchDashboardData]);

  // Retornar datos y funciones
  return {
    // Estados
    loading,
    error,
    lastUpdated,
    
    // Datos unificados
    data: dashboardData,
    
    // Funciones
    refetch: fetchDashboardData,
    applyFilters
  };
};
