import { toast } from 'react-toastify';

export const useDashboardExport = (dashboardData) => {
  const exportAsPDF = async () => {
    try {
      toast.info('Exportando a PDF...');
      const { jsPDF } = await import('jspdf');
      const doc = new jsPDF();
      const data = dashboardData;

      doc.setFontSize(20);
      doc.text('Dashboard Ejecutivo BAT-7', 20, 30);

      doc.setFontSize(12);
      doc.text(`Fecha: ${new Date().toLocaleDateString()}`, 20, 50);
      doc.text(`Hora: ${new Date().toLocaleTimeString()}`, 20, 60);

      if (data.estadisticasGenerales) {
        doc.setFontSize(16);
        doc.text('Estadísticas Generales', 20, 80);

        doc.setFontSize(12);
        let yPos = 100;
        doc.text(`Total de Pacientes: ${data.estadisticasGenerales.totalPacientes || 0}`, 20, yPos);
        yPos += 10;
        doc.text(`Total de Evaluaciones: ${data.estadisticasGenerales.totalEvaluaciones || 0}`, 20, yPos);
        yPos += 10;
        doc.text(`Promedio General: ${data.estadisticasGenerales.promedioGeneral || 0}`, 20, yPos);
        yPos += 10;
        doc.text(`Instituciones Activas: ${data.estadisticasGenerales.institucionesActivas || 0}`, 20, yPos);
      }

      if (data.kpiData) {
        doc.setFontSize(16);
        doc.text('KPIs Críticos', 20, 150);

        doc.setFontSize(12);
        let yPos = 170;
        Object.entries(data.kpiData).forEach(([key, kpi]) => {
          if (yPos > 250) {
            doc.addPage();
            yPos = 30;
          }
          doc.text(`${kpi.title || key}: ${kpi.current || 0}${kpi.unit || ''}`, 20, yPos);
          yPos += 10;
        });
      }

      doc.save(`dashboard-ejecutivo-${new Date().toISOString().split('T')[0]}.pdf`);
      toast.success('Exportación a PDF completada');
    } catch (error) {
      console.error('Error en exportación a PDF:', error);
      toast.error(`Error en exportación a PDF: ${error.message}`);
    }
  };

  const exportAsExcel = async () => {
    try {
      toast.info('Exportando a Excel...');
      const XLSX = await import('xlsx');
      const wb = XLSX.utils.book_new();
      const data = dashboardData;

      const wsData = [
        ['Dashboard Ejecutivo BAT-7'],
        ['Fecha', new Date().toLocaleDateString()],
        [''],
        ['Estadísticas Generales'],
        ['Total Pacientes', data.estadisticasGenerales?.totalPacientes || 0],
        ['Total Evaluaciones', data.estadisticasGenerales?.totalEvaluaciones || 0],
        ['Promedio General', data.estadisticasGenerales?.promedioGeneral || 0],
        ['Instituciones Activas', data.estadisticasGenerales?.institucionesActivas || 0]
      ];

      const ws = XLSX.utils.aoa_to_sheet(wsData);
      XLSX.utils.book_append_sheet(wb, ws, 'Dashboard');

      XLSX.writeFile(wb, `dashboard-ejecutivo-${new Date().toISOString().split('T')[0]}.xlsx`);
      toast.success('Exportación a Excel completada');
    } catch (error) {
      console.error('Error en exportación a Excel:', error);
      toast.error(`Error en exportación a Excel: ${error.message}`);
    }
  };

  return { exportAsPDF, exportAsExcel };
};