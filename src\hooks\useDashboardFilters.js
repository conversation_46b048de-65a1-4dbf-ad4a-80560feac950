import { useState, useEffect, useCallback } from 'react';
import supabase from '../api/supabaseClient';

/**
 * Hook personalizado para manejar filtros del Dashboard
 * Proporciona filtrado por fecha, institución, nivel educativo, etc.
 */
export const useDashboardFilters = () => {
  const [filters, setFilters] = useState({
    dateRange: {
      start: null,
      end: null
    },
    institution: null,
    educationalLevel: null,
    gender: null,
    aptitude: null
  });

  const [availableOptions, setAvailableOptions] = useState({
    institutions: [],
    educationalLevels: [],
    genders: [],
    aptitudes: []
  });

  const [loading, setLoading] = useState(false);

  // Cargar opciones disponibles para filtros
  const loadFilterOptions = useCallback(async () => {
    try {
      setLoading(true);
      
      // Obtener instituciones únicas
      const { data: institutions } = await supabase
        .from('pacientes')
        .select('institucion_id')
        .not('institucion_id', 'is', null);

      // Obtener niveles educativos únicos
      const { data: levels } = await supabase
        .from('pacientes')
        .select('nivel_educativo')
        .not('nivel_educativo', 'is', null);

      // Obtener géneros únicos
      const { data: genders } = await supabase
        .from('pacientes')
        .select('genero')
        .not('genero', 'is', null);

      // Obtener aptitudes
      const { data: aptitudes } = await supabase
        .from('aptitudes')
        .select('id, codigo, nombre')
        .order('codigo');

      setAvailableOptions({
        institutions: [...new Set(institutions?.map(i => i.institucion_id) || [])],
        educationalLevels: [...new Set(levels?.map(l => l.nivel_educativo) || [])],
        genders: [...new Set(genders?.map(g => g.genero) || [])],
        aptitudes: aptitudes || []
      });

    } catch (error) {
      console.error('Error al cargar opciones de filtro:', error);
    } finally {
      setLoading(false);
    }
  }, []);

  useEffect(() => {
    loadFilterOptions();
  }, [loadFilterOptions]);

  // Actualizar filtro específico
  const updateFilter = useCallback((filterName, value) => {
    setFilters(prev => ({
      ...prev,
      [filterName]: value
    }));
  }, []);

  // Actualizar rango de fechas
  const updateDateRange = useCallback((start, end) => {
    setFilters(prev => ({
      ...prev,
      dateRange: { start, end }
    }));
  }, []);

  // Limpiar todos los filtros
  const clearFilters = useCallback(() => {
    setFilters({
      dateRange: {
        start: null,
        end: null
      },
      institution: null,
      educationalLevel: null,
      gender: null,
      aptitude: null
    });
  }, []);

  // Generar condiciones SQL para Supabase basadas en filtros activos
  const getSupabaseFilters = useCallback(() => {
    const conditions = [];
    
    if (filters.dateRange.start && filters.dateRange.end) {
      conditions.push({
        column: 'created_at',
        operator: 'gte',
        value: filters.dateRange.start
      });
      conditions.push({
        column: 'created_at',
        operator: 'lte',
        value: filters.dateRange.end
      });
    }

    if (filters.institution) {
      conditions.push({
        column: 'institucion_id',
        operator: 'eq',
        value: filters.institution
      });
    }

    if (filters.educationalLevel) {
      conditions.push({
        column: 'nivel_educativo',
        operator: 'eq',
        value: filters.educationalLevel
      });
    }

    if (filters.gender) {
      conditions.push({
        column: 'genero',
        operator: 'eq',
        value: filters.gender
      });
    }

    return conditions;
  }, [filters]);

  // Aplicar filtros a una consulta de Supabase
  const applyFiltersToQuery = useCallback((query) => {
    const conditions = getSupabaseFilters();
    
    let filteredQuery = query;
    
    conditions.forEach(condition => {
      switch (condition.operator) {
        case 'eq':
          filteredQuery = filteredQuery.eq(condition.column, condition.value);
          break;
        case 'gte':
          filteredQuery = filteredQuery.gte(condition.column, condition.value);
          break;
        case 'lte':
          filteredQuery = filteredQuery.lte(condition.column, condition.value);
          break;
        case 'in':
          filteredQuery = filteredQuery.in(condition.column, condition.value);
          break;
        default:
          break;
      }
    });

    return filteredQuery;
  }, [getSupabaseFilters]);

  // Verificar si hay filtros activos
  const hasActiveFilters = useCallback(() => {
    return (
      (filters.dateRange.start && filters.dateRange.end) ||
      filters.institution ||
      filters.educationalLevel ||
      filters.gender ||
      filters.aptitude
    );
  }, [filters]);

  // Obtener resumen de filtros activos
  const getActiveFiltersCount = useCallback(() => {
    let count = 0;
    
    if (filters.dateRange.start && filters.dateRange.end) count++;
    if (filters.institution) count++;
    if (filters.educationalLevel) count++;
    if (filters.gender) count++;
    if (filters.aptitude) count++;
    
    return count;
  }, [filters]);

  // Obtener descripción de filtros activos
  const getActiveFiltersDescription = useCallback(() => {
    const descriptions = [];
    
    if (filters.dateRange.start && filters.dateRange.end) {
      descriptions.push(`Fecha: ${filters.dateRange.start} - ${filters.dateRange.end}`);
    }
    
    if (filters.institution) {
      descriptions.push(`Institución: ${filters.institution}`);
    }
    
    if (filters.educationalLevel) {
      const levelNames = {
        'E': 'Elemental',
        'M': 'Medio',
        'S': 'Superior'
      };
      descriptions.push(`Nivel: ${levelNames[filters.educationalLevel] || filters.educationalLevel}`);
    }
    
    if (filters.gender) {
      const genderNames = {
        'M': 'Masculino',
        'F': 'Femenino'
      };
      descriptions.push(`Género: ${genderNames[filters.gender] || filters.gender}`);
    }
    
    if (filters.aptitude) {
      const aptitude = availableOptions.aptitudes.find(a => a.id === filters.aptitude);
      descriptions.push(`Aptitud: ${aptitude?.codigo || filters.aptitude}`);
    }
    
    return descriptions;
  }, [filters, availableOptions]);

  // Presets de filtros comunes
  const applyPreset = useCallback((presetName) => {
    const now = new Date();
    const presets = {
      'last-30-days': {
        dateRange: {
          start: new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000).toISOString().split('T')[0],
          end: now.toISOString().split('T')[0]
        }
      },
      'last-7-days': {
        dateRange: {
          start: new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000).toISOString().split('T')[0],
          end: now.toISOString().split('T')[0]
        }
      },
      'this-month': {
        dateRange: {
          start: new Date(now.getFullYear(), now.getMonth(), 1).toISOString().split('T')[0],
          end: now.toISOString().split('T')[0]
        }
      },
      'elementary-only': {
        educationalLevel: 'E'
      },
      'middle-only': {
        educationalLevel: 'M'
      },
      'high-only': {
        educationalLevel: 'S'
      }
    };

    const preset = presets[presetName];
    if (preset) {
      setFilters(prev => ({
        ...prev,
        ...preset
      }));
    }
  }, []);

  return {
    filters,
    availableOptions,
    loading,
    updateFilter,
    updateDateRange,
    clearFilters,
    applyFiltersToQuery,
    hasActiveFilters,
    getActiveFiltersCount,
    getActiveFiltersDescription,
    applyPreset,
    loadFilterOptions
  };
};

export default useDashboardFilters;
