/**
 * @file useDistributionData.js
 * @description Custom hook for processing and validating distribution data
 */

import { useMemo } from 'react';

const DEFAULT_COLORS = ['#3B82F6', '#10B981', '#F59E0B', '#EF4444', '#8B5CF6'];

export const useDistributionData = (rawData, colors = DEFAULT_COLORS) => {
  const validateNumericValue = (rawValue) => {
    if (typeof rawValue === 'number') return rawValue;
    if (typeof rawValue === 'string') return parseFloat(rawValue);
    return 0;
  };

  const createSafeDistributionItem = (item, index) => {
    const numericValue = validateNumericValue(item?.value);
    const safeValue = (isNaN(numericValue) || numericValue <= 0 || !isFinite(numericValue)) 
      ? 1 
      : Math.round(numericValue);
    const safeName = (item?.name?.trim()) || `Categoría ${index + 1}`;
    const safeColor = item?.color || colors[index % colors.length];
    
    return { name: safeName, value: safeValue, color: safeColor };
  };

  const getFallbackData = (reason = 'Sin datos') => {
    if (process.env.NODE_ENV === 'development') {
      console.warn(`⚠️ ${reason}, usando datos de fallback`);
    }
    return [{ name: reason, value: 1, color: '#9CA3AF' }];
  };

  const processedData = useMemo(() => {
    if (!rawData || !Array.isArray(rawData)) {
      return {
        data: getFallbackData('No hay datos de distribución válidos'),
        hasValidData: false,
        totalItems: 0
      };
    }

    const processed = rawData
      .map(createSafeDistributionItem)
      .filter(item => item.name && item.value > 0 && isFinite(item.value));

    if (processed.length === 0) {
      return {
        data: getFallbackData('Todos los datos fueron filtrados'),
        hasValidData: false,
        totalItems: 0
      };
    }

    const totalItems = processed.reduce((sum, item) => sum + item.value, 0);

    if (process.env.NODE_ENV === 'development') {
      console.log('✅ Datos de distribución procesados:', processed);
    }

    return {
      data: processed,
      hasValidData: true,
      totalItems
    };
  }, [rawData, colors]);

  return processedData;
};