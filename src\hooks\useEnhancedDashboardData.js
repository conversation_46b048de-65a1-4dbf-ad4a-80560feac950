/**
 * @file useEnhancedDashboardData.js
 * @description Hook mejorado para gestionar datos del dashboard con conexión real
 * Maneja automáticamente datos reales vs simulados, filtros y estados
 */

import { useState, useEffect, useCallback, useMemo } from 'react';
import DashboardService from '../services/DashboardService';
import { useRealDataConnection } from './useRealDataConnection';
import supabase from '../api/supabaseClient';

export const useEnhancedDashboardData = (initialFilters = {}) => {
  // Estados principales
  const [data, setData] = useState(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [filters, setFilters] = useState(initialFilters);
  const [lastUpdate, setLastUpdate] = useState(null);

  // Conexión a datos reales
  const { isRealDataAvailable, connectionStatus } = useRealDataConnection();

  // Detectar si es vista individual
  const isIndividualView = useMemo(() => {
    return filters.pacienteEspecifico && filters.pacienteEspecifico !== '';
  }, [filters.pacienteEspecifico]);

  /**
   * Obtener datos reales desde Supabase
   */
  const fetchRealData = useCallback(async (currentFilters) => {
    try {
      console.log('📊 [EnhancedDashboard] Obteniendo datos reales con filtros:', currentFilters);

      if (isIndividualView) {
        // Datos individuales específicos
        const { data: patientData, error } = await supabase
          .from('pacientes')
          .select(`
            *,
            evaluaciones (
              *,
              resultados (*)
            )
          `)
          .eq('id', currentFilters.pacienteEspecifico)
          .single();

        if (error) throw error;

        return {
          type: 'individual',
          patient: patientData,
          evaluations: patientData.evaluaciones || [],
          lastUpdate: new Date().toISOString()
        };

      } else {
        // Datos agregados del dashboard - USAR DASHBOARDSERVICE DIRECTAMENTE
        console.log('🔄 [EnhancedDashboard] Delegando a DashboardService para datos agregados...');
        const dashboardData = await DashboardService.fetchDashboardData(currentFilters);

        return {
          type: 'aggregate',
          dashboardData,
          lastUpdate: dashboardData.lastUpdate || new Date().toISOString()
        };
      }

    } catch (error) {
      console.error('❌ [EnhancedDashboard] Error obteniendo datos reales:', error);
      throw error;
    }
  }, [isIndividualView]);

  /**
   * Procesar datos reales al formato esperado por las vistas
   */
  const processRealData = useCallback((rawData) => {
    if (rawData.type === 'individual') {
      // Procesar datos individuales
      return {
        type: 'individual',
        patient: rawData.patient,
        evaluations: rawData.evaluations,
        // Agregar procesamiento específico para vista individual
        puntuaciones: extractScoresFromEvaluations(rawData.evaluations),
        interpretaciones: generateInterpretations(rawData.evaluations),
        lastUpdate: rawData.lastUpdate
      };

    } else {
      // Procesar datos agregados - YA VIENEN PROCESADOS DE DASHBOARDSERVICE
      return {
        type: 'aggregate',
        ...rawData.dashboardData, // Spread todos los datos del DashboardService
        lastUpdate: rawData.lastUpdate
      };
    }
  }, []);

  /**
   * Cargar datos (reales o simulados)
   */
  const loadData = useCallback(async (currentFilters = filters) => {
    setLoading(true);
    setError(null);

    try {
      let result;

      if (isRealDataAvailable) {
        // Intentar obtener datos reales
        try {
          const rawData = await fetchRealData(currentFilters);
          result = processRealData(rawData);
          console.log('✅ [EnhancedDashboard] Datos reales cargados exitosamente');
        } catch (realDataError) {
          console.warn('⚠️ [EnhancedDashboard] Error con datos reales, usando simulados:', realDataError);
          result = await DashboardService.fetchDashboardData(currentFilters);
        }
      } else {
        // Usar datos simulados
        result = await DashboardService.fetchDashboardData(currentFilters);
        console.log('📊 [EnhancedDashboard] Usando datos simulados');
      }

      setData(result);
      setLastUpdate(new Date().toISOString());

    } catch (error) {
      console.error('❌ [EnhancedDashboard] Error cargando datos:', error);
      setError(error);
      
      // Fallback final a datos básicos
      setData({
        type: 'error',
        message: 'No se pudieron cargar los datos',
        estadisticasGenerales: { total_pacientes: 0, total_evaluaciones: 0 },
        kpiData: { averageScore: 0, completionRate: 0 },
        lastUpdate: new Date().toISOString()
      });
    } finally {
      setLoading(false);
    }
  }, [filters, isRealDataAvailable, fetchRealData, processRealData]);

  /**
   * Aplicar filtros
   */
  const applyFilters = useCallback((newFilters) => {
    console.log('🔍 [EnhancedDashboard] Aplicando filtros:', newFilters);
    setFilters(prev => ({ ...prev, ...newFilters }));
  }, []);

  /**
   * Refrescar datos
   */
  const refreshData = useCallback(() => {
    loadData(filters);
  }, [loadData, filters]);

  // Cargar datos iniciales y cuando cambien los filtros
  useEffect(() => {
    loadData();
  }, [loadData]);

  // Auto-refresh cada 5 minutos si hay datos reales
  useEffect(() => {
    if (!isRealDataAvailable) return;

    const interval = setInterval(() => {
      console.log('🔄 [EnhancedDashboard] Auto-refresh de datos');
      refreshData();
    }, 5 * 60 * 1000); // 5 minutos

    return () => clearInterval(interval);
  }, [isRealDataAvailable, refreshData]);

  return {
    // Datos
    data,
    loading,
    error,
    filters,
    lastUpdate,
    
    // Estados
    isRealDataAvailable,
    connectionStatus,
    isIndividualView,
    
    // Acciones
    applyFilters,
    refreshData,
    loadData
  };
};

// Funciones auxiliares para procesar datos reales
const extractScoresFromEvaluations = (evaluations) => {
  // Implementar extracción de puntuaciones desde evaluaciones reales
  return {};
};

const generateInterpretations = (evaluations) => {
  // Implementar generación de interpretaciones desde evaluaciones reales
  return {};
};

// FUNCIONES DEPRECADAS - ELIMINADAS PORQUE AHORA SE USAN LAS DE DASHBOARDSERVICE

export default useEnhancedDashboardData;
