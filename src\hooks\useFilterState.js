/**
 * @file useFilterState.js
 * @description Custom hook for managing filter state with optimizations
 */

import { useState, useCallback, useMemo } from 'react';

const initialFilters = {
  dateRange: { start: null, end: null },
  institution: null,
  educationalLevel: null,
  gender: null,
  aptitude: null,
  nivelAplicacion: null,
  categoriaEvaluado: null,
  rangoEdad: { min: null, max: null },
  cursoGrado: null,
  psicologo: null,
  pacienteEspecifico: null
};

export const useFilterState = (onFiltersChange) => {
  const [localFilters, setLocalFilters] = useState(initialFilters);

  // Memoized helper functions
  const updateFilter = useCallback((key, value) => {
    const newFilters = { ...localFilters, [key]: value };
    setLocalFilters(newFilters);
    if (onFiltersChange) {
      onFiltersChange(newFilters);
    }
  }, [localFilters, onFiltersChange]);

  const updateDateRange = useCallback((start, end) => {
    const newFilters = {
      ...localFilters,
      dateRange: { start, end }
    };
    setLocalFilters(newFilters);
    if (onFiltersChange) {
      onFiltersChange(newFilters);
    }
  }, [localFilters, onFiltersChange]);

  const clearFilters = useCallback(() => {
    setLocalFilters(initialFilters);
    if (onFiltersChange) {
      onFiltersChange(initialFilters);
    }
  }, [onFiltersChange]);

  // Memoized calculations
  const hasActiveFilters = useMemo(() => {
    return (
      (localFilters.dateRange.start && localFilters.dateRange.end) ||
      localFilters.institution ||
      localFilters.educationalLevel ||
      localFilters.gender ||
      localFilters.aptitude ||
      localFilters.nivelAplicacion ||
      localFilters.categoriaEvaluado ||
      (localFilters.rangoEdad.min || localFilters.rangoEdad.max) ||
      localFilters.cursoGrado ||
      localFilters.psicologo ||
      localFilters.pacienteEspecifico
    );
  }, [localFilters]);

  const activeFiltersCount = useMemo(() => {
    let count = 0;
    if (localFilters.dateRange.start && localFilters.dateRange.end) count++;
    if (localFilters.institution) count++;
    if (localFilters.educationalLevel) count++;
    if (localFilters.gender) count++;
    if (localFilters.aptitude) count++;
    if (localFilters.nivelAplicacion) count++;
    if (localFilters.categoriaEvaluado) count++;
    if (localFilters.rangoEdad.min || localFilters.rangoEdad.max) count++;
    if (localFilters.cursoGrado) count++;
    if (localFilters.psicologo) count++;
    if (localFilters.pacienteEspecifico) count++;
    return count;
  }, [localFilters]);

  return {
    localFilters,
    updateFilter,
    updateDateRange,
    clearFilters,
    hasActiveFilters,
    activeFiltersCount
  };
};