/**
 * @file useKPIData.js
 * @description Custom hook for managing KPI data loading and state
 */

import { useState, useEffect, useCallback, useMemo } from 'react';
import CriticalKPIService from '../services/analytics/CriticalKPIService.js';

export const useKPIData = (filters = {}) => {
  const [state, setState] = useState({
    kpis: {},
    chartData: null,
    alerts: [],
    loading: true,
    error: null,
    lastUpdate: null
  });

  const loadKPIData = useCallback(async () => {
    setState(prev => ({ ...prev, loading: true, error: null }));
    
    try {
      const [kpiData, chartData, alertsData] = await Promise.all([
        CriticalKPIService.getCriticalKPIs(),
        CriticalKPIService.getKPIChartData(),
        CriticalKPIService.getKPIAlerts()
      ]);

      setState({
        kpis: kpiData,
        chartData,
        alerts: alertsData,
        loading: false,
        error: null,
        lastUpdate: new Date()
      });
    } catch (err) {
      setState(prev => ({
        ...prev,
        loading: false,
        error: err.message
      }));
      console.error('Error cargando KPIs:', err);
    }
  }, []);

  // Reload data when filters change
  useEffect(() => {
    loadKPIData();
  }, [loadKPIData, filters]);

  // Memoized computed values
  const kpiStats = useMemo(() => {
    const kpiArray = Object.values(state.kpis);
    return {
      total: kpiArray.length,
      critical: kpiArray.filter(kpi => kpi.critical).length,
      byStatus: {
        excellent: kpiArray.filter(kpi => kpi.status === 'excellent').length,
        good: kpiArray.filter(kpi => kpi.status === 'good').length,
        warning: kpiArray.filter(kpi => kpi.status === 'warning').length,
        critical: kpiArray.filter(kpi => kpi.status === 'critical').length,
        error: kpiArray.filter(kpi => kpi.status === 'error').length
      }
    };
  }, [state.kpis]);

  const hasAlerts = useMemo(() => state.alerts.length > 0, [state.alerts]);

  const criticalAlerts = useMemo(() => 
    state.alerts.filter(alert => alert.severity === 'high'), 
    [state.alerts]
  );

  return {
    ...state,
    kpiStats,
    hasAlerts,
    criticalAlerts,
    refetch: loadKPIData
  };
};