/**
 * @file usePatientManagement.js
 * @description Custom hook for patient management operations
 */

import { useState, useEffect } from 'react';
import { toast } from 'react-toastify';
import supabase from '../api/supabaseClient';

export const usePatientManagement = () => {
  const [patients, setPatients] = useState([]);
  const [institutions, setInstitutions] = useState([]);
  const [loading, setLoading] = useState(false);

  // Load institutions
  const loadInstitutions = async () => {
    try {
      const { data, error } = await supabase
        .from('instituciones')
        .select('*')
        .order('nombre', { ascending: true });

      if (error) throw error;
      setInstitutions(data || []);
      console.log('✅ Instituciones cargadas:', data?.length || 0);
    } catch (error) {
      console.error('❌ Error al cargar instituciones:', error);
      toast.error('Error al cargar las instituciones');
    }
  };

  // Load patients
  const loadPatients = async () => {
    setLoading(true);
    try {
      const { data, error } = await supabase
        .from('pacientes')
        .select('*')
        .order('created_at', { ascending: false });

      if (error) throw error;
      setPatients(data || []);
      console.log('✅ Pacientes cargados:', data?.length || 0);
    } catch (error) {
      console.error('❌ Error al cargar pacientes:', error);
      toast.error('Error al cargar los pacientes');
    } finally {
      setLoading(false);
    }
  };

  // Create patient
  const createPatient = async (patientData) => {
    try {
      setLoading(true);

      // Validations
      if (!patientData.nombre || !patientData.apellido || !patientData.email) {
        toast.error('Por favor complete los campos obligatorios');
        return false;
      }

      const institutionId = patientData.institucion_id || (institutions.length > 0 ? institutions[0].id : '');
      if (!institutionId) {
        toast.error('Debe seleccionar una institución');
        return false;
      }

      const dataToInsert = {
        ...patientData,
        institucion_id: institutionId,
        created_at: new Date().toISOString()
      };

      const { data, error } = await supabase
        .from('pacientes')
        .insert([dataToInsert])
        .select();

      if (error) throw error;

      console.log('✅ Paciente creado exitosamente:', data);
      toast.success('Paciente creado correctamente');
      await loadPatients();
      return true;
    } catch (error) {
      console.error('💥 Error creating patient:', error);
      toast.error(`Error al crear el paciente: ${error.message}`);
      return false;
    } finally {
      setLoading(false);
    }
  };

  // Delete patient
  const deletePatient = async (patient) => {
    if (!window.confirm(`¿Está seguro de eliminar al paciente ${patient.nombre} ${patient.apellido}?`)) {
      return false;
    }

    try {
      setLoading(true);
      const { error } = await supabase
        .from('pacientes')
        .delete()
        .eq('id', patient.id);

      if (error) throw error;
      
      toast.success('Paciente eliminado correctamente');
      await loadPatients();
      return true;
    } catch (error) {
      console.error('Error deleting patient:', error);
      toast.error('Error al eliminar el paciente');
      return false;
    } finally {
      setLoading(false);
    }
  };

  // Initialize data on mount
  useEffect(() => {
    loadInstitutions();
    loadPatients();
  }, []);

  return {
    patients,
    institutions,
    loading,
    loadPatients,
    createPatient,
    deletePatient,
    getDefaultInstitutionId: () => institutions.length > 0 ? institutions[0].id : ''
  };
};