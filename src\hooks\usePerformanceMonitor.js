import { useEffect, useRef, useCallback, useState } from 'react';

/**
 * Hook personalizado para monitorear el rendimiento de componentes
 * Útil para identificar cuellos de botella y optimizar la experiencia del usuario
 */
export const usePerformanceMonitor = (componentName = 'Unknown Component') => {
  const [metrics, setMetrics] = useState({
    renderCount: 0,
    averageRenderTime: 0,
    lastRenderTime: 0,
    totalRenderTime: 0,
    slowRenders: 0
  });

  const renderStartTime = useRef(null);
  const renderTimes = useRef([]);
  const mountTime = useRef(Date.now());

  // Iniciar medición de render
  const startRender = useCallback(() => {
    renderStartTime.current = performance.now();
  }, []);

  // Finalizar medición de render
  const endRender = useCallback(() => {
    if (renderStartTime.current) {
      const renderTime = performance.now() - renderStartTime.current;
      renderTimes.current.push(renderTime);

      // Mantener solo las últimas 50 mediciones
      if (renderTimes.current.length > 50) {
        renderTimes.current.shift();
      }

      const totalTime = renderTimes.current.reduce((sum, time) => sum + time, 0);
      const averageTime = totalTime / renderTimes.current.length;
      const slowRenders = renderTimes.current.filter(time => time > 16).length; // > 16ms es considerado lento

      setMetrics(prev => ({
        renderCount: prev.renderCount + 1,
        averageRenderTime: Math.round(averageTime * 100) / 100,
        lastRenderTime: Math.round(renderTime * 100) / 100,
        totalRenderTime: Math.round(totalTime * 100) / 100,
        slowRenders
      }));

      // Log warning para renders lentos
      if (renderTime > 16) {
        console.warn(`🐌 Render lento detectado en ${componentName}: ${renderTime.toFixed(2)}ms`);
      }

      renderStartTime.current = null;
    }
  }, [componentName]);

  // Medir tiempo de montaje del componente
  useEffect(() => {
    const mountDuration = Date.now() - mountTime.current;
    console.log(`⚡ ${componentName} montado en ${mountDuration}ms`);

    return () => {
      const totalLifetime = Date.now() - mountTime.current;
      console.log(`🔄 ${componentName} desmontado después de ${totalLifetime}ms de vida`);
    };
  }, [componentName]);

  // Obtener métricas de rendimiento
  const getPerformanceReport = useCallback(() => {
    const report = {
      component: componentName,
      ...metrics,
      lifetime: Date.now() - mountTime.current,
      performanceScore: calculatePerformanceScore(metrics),
      recommendations: generateRecommendations(metrics)
    };

    return report;
  }, [componentName, metrics]);

  // Calcular puntuación de rendimiento (0-100)
  const calculatePerformanceScore = useCallback((metrics) => {
    if (metrics.renderCount === 0) return 100;

    let score = 100;
    
    // Penalizar renders lentos
    const slowRenderRatio = metrics.slowRenders / metrics.renderCount;
    score -= slowRenderRatio * 30;

    // Penalizar tiempo promedio alto
    if (metrics.averageRenderTime > 16) {
      score -= Math.min(30, (metrics.averageRenderTime - 16) * 2);
    }

    // Penalizar muchos re-renders
    if (metrics.renderCount > 100) {
      score -= Math.min(20, (metrics.renderCount - 100) * 0.1);
    }

    return Math.max(0, Math.round(score));
  }, []);

  // Generar recomendaciones de optimización
  const generateRecommendations = useCallback((metrics) => {
    const recommendations = [];

    if (metrics.slowRenders > metrics.renderCount * 0.2) {
      recommendations.push('Considera usar React.memo() para evitar re-renders innecesarios');
    }

    if (metrics.averageRenderTime > 16) {
      recommendations.push('El tiempo de render promedio es alto. Considera dividir el componente o usar lazy loading');
    }

    if (metrics.renderCount > 50) {
      recommendations.push('Alto número de re-renders. Revisa las dependencias de useEffect y useState');
    }

    if (recommendations.length === 0) {
      recommendations.push('¡Excelente rendimiento! No se detectaron problemas');
    }

    return recommendations;
  }, []);

  // Log métricas en desarrollo
  useEffect(() => {
    if (process.env.NODE_ENV === 'development' && metrics.renderCount > 0) {
      const report = getPerformanceReport();
      if (metrics.renderCount % 10 === 0) { // Log cada 10 renders
        console.group(`📊 Reporte de rendimiento: ${componentName}`);
        console.log('Métricas:', report);
        console.log('Recomendaciones:', report.recommendations);
        console.groupEnd();
      }
    }
  }, [metrics.renderCount, componentName, getPerformanceReport]);

  return {
    metrics,
    startRender,
    endRender,
    getPerformanceReport
  };
};

/**
 * Hook para monitorear el uso de memoria de un componente
 */
export const useMemoryMonitor = (componentName = 'Unknown Component') => {
  const [memoryUsage, setMemoryUsage] = useState({
    usedJSHeapSize: 0,
    totalJSHeapSize: 0,
    jsHeapSizeLimit: 0
  });

  const updateMemoryUsage = useCallback(() => {
    if (performance.memory) {
      setMemoryUsage({
        usedJSHeapSize: Math.round(performance.memory.usedJSHeapSize / 1024 / 1024), // MB
        totalJSHeapSize: Math.round(performance.memory.totalJSHeapSize / 1024 / 1024), // MB
        jsHeapSizeLimit: Math.round(performance.memory.jsHeapSizeLimit / 1024 / 1024) // MB
      });
    }
  }, []);

  useEffect(() => {
    updateMemoryUsage();
    
    const interval = setInterval(updateMemoryUsage, 5000); // Actualizar cada 5 segundos
    
    return () => clearInterval(interval);
  }, [updateMemoryUsage]);

  // Detectar posibles memory leaks
  useEffect(() => {
    if (memoryUsage.usedJSHeapSize > 0) {
      const memoryUsagePercent = (memoryUsage.usedJSHeapSize / memoryUsage.jsHeapSizeLimit) * 100;
      
      if (memoryUsagePercent > 80) {
        console.warn(`🚨 Alto uso de memoria detectado en ${componentName}: ${memoryUsagePercent.toFixed(1)}%`);
      }
    }
  }, [memoryUsage, componentName]);

  return {
    memoryUsage,
    updateMemoryUsage
  };
};

/**
 * Hook para monitorear el tiempo de carga de datos
 */
export const useDataLoadingMonitor = () => {
  const [loadingMetrics, setLoadingMetrics] = useState({
    totalRequests: 0,
    successfulRequests: 0,
    failedRequests: 0,
    averageLoadTime: 0,
    slowRequests: 0
  });

  const loadingTimes = useRef([]);

  const trackDataLoading = useCallback(async (loadingFunction, requestName = 'Unknown Request') => {
    const startTime = performance.now();
    
    try {
      const result = await loadingFunction();
      const loadTime = performance.now() - startTime;
      
      loadingTimes.current.push(loadTime);
      
      // Mantener solo las últimas 20 mediciones
      if (loadingTimes.current.length > 20) {
        loadingTimes.current.shift();
      }

      const averageTime = loadingTimes.current.reduce((sum, time) => sum + time, 0) / loadingTimes.current.length;
      const slowRequests = loadingTimes.current.filter(time => time > 2000).length; // > 2s es considerado lento

      setLoadingMetrics(prev => ({
        totalRequests: prev.totalRequests + 1,
        successfulRequests: prev.successfulRequests + 1,
        failedRequests: prev.failedRequests,
        averageLoadTime: Math.round(averageTime),
        slowRequests
      }));

      if (loadTime > 2000) {
        console.warn(`🐌 Carga lenta detectada en ${requestName}: ${loadTime.toFixed(0)}ms`);
      }

      return result;
    } catch (error) {
      setLoadingMetrics(prev => ({
        ...prev,
        totalRequests: prev.totalRequests + 1,
        failedRequests: prev.failedRequests + 1
      }));

      console.error(`❌ Error en carga de datos ${requestName}:`, error);
      throw error;
    }
  }, []);

  return {
    loadingMetrics,
    trackDataLoading
  };
};

export default {
  usePerformanceMonitor,
  useMemoryMonitor,
  useDataLoadingMonitor
};