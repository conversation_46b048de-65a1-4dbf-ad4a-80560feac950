/**
 * @file useRealDataConnection.js
 * @description Hook para conectar con datos reales de Supabase cuando la base de datos esté lista
 * Proporciona una interfaz unificada para datos simulados y reales
 */

import { useState, useEffect, useCallback } from 'react';
import supabase from '../api/supabaseClient';

/**
 * Hook para gestionar la conexión con datos reales
 * Detecta automáticamente si las tablas existen y cambia entre datos simulados y reales
 */
export const useRealDataConnection = () => {
  const [isRealDataAvailable, setIsRealDataAvailable] = useState(false);
  const [connectionStatus, setConnectionStatus] = useState('checking');
  const [availableTables, setAvailableTables] = useState([]);

  // Verificar disponibilidad de datos reales
  const checkDataAvailability = useCallback(async () => {
    try {
      setConnectionStatus('checking');
      
      // Verificar tablas principales
      const tablesToCheck = ['pacientes', 'evaluaciones', 'resultados', 'usuarios'];
      const tableStatus = {};
      
      for (const table of tablesToCheck) {
        try {
          const { data, error } = await supabase
            .from(table)
            .select('*')
            .limit(1);
          
          tableStatus[table] = !error;
        } catch (e) {
          tableStatus[table] = false;
        }
      }

      const availableTablesList = Object.entries(tableStatus)
        .filter(([_, available]) => available)
        .map(([table, _]) => table);

      setAvailableTables(availableTablesList);
      
      // Considerar datos reales disponibles si al menos pacientes y usuarios existen
      const hasMinimumTables = tableStatus.pacientes && tableStatus.usuarios;
      setIsRealDataAvailable(hasMinimumTables);
      setConnectionStatus(hasMinimumTables ? 'connected' : 'simulated');

      console.log('📊 [RealDataConnection] Estado de tablas:', tableStatus);
      console.log('📊 [RealDataConnection] Usando datos:', hasMinimumTables ? 'reales' : 'simulados');

    } catch (error) {
      console.error('❌ [RealDataConnection] Error verificando disponibilidad:', error);
      setIsRealDataAvailable(false);
      setConnectionStatus('error');
    }
  }, []);

  useEffect(() => {
    checkDataAvailability();
  }, [checkDataAvailability]);

  return {
    isRealDataAvailable,
    connectionStatus,
    availableTables,
    recheckConnection: checkDataAvailability
  };
};

/**
 * Hook para obtener datos de pacientes (reales o simulados)
 */
export const usePatientData = (patientId = null) => {
  const [data, setData] = useState(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);
  const { isRealDataAvailable } = useRealDataConnection();

  const fetchPatientData = useCallback(async (id) => {
    if (!id) return;

    setLoading(true);
    setError(null);

    try {
      if (isRealDataAvailable) {
        // Obtener datos reales de Supabase
        const { data: patientData, error: patientError } = await supabase
          .from('pacientes')
          .select(`
            *,
            evaluaciones (
              *,
              resultados (*)
            )
          `)
          .eq('id', id)
          .single();

        if (patientError) throw patientError;

        // Transformar datos al formato esperado
        const transformedData = transformRealDataToExpectedFormat(patientData);
        setData(transformedData);

      } else {
        // Usar datos simulados
        const simulatedData = generateSimulatedPatientData(id);
        setData(simulatedData);
      }

    } catch (err) {
      console.error('❌ [PatientData] Error obteniendo datos:', err);
      setError(err);
      
      // Fallback a datos simulados en caso de error
      const simulatedData = generateSimulatedPatientData(id);
      setData(simulatedData);
    } finally {
      setLoading(false);
    }
  }, [isRealDataAvailable]);

  useEffect(() => {
    if (patientId) {
      fetchPatientData(patientId);
    }
  }, [patientId, fetchPatientData]);

  return { data, loading, error, refetch: () => fetchPatientData(patientId) };
};

/**
 * Hook para obtener lista de pacientes disponibles
 */
export const usePatientsList = () => {
  const [patients, setPatients] = useState([]);
  const [loading, setLoading] = useState(false);
  const { isRealDataAvailable } = useRealDataConnection();

  const fetchPatients = useCallback(async () => {
    setLoading(true);

    try {
      if (isRealDataAvailable) {
        try {
          const { data, error } = await supabase
            .from('pacientes')
            .select('id, nombre, apellido, documento, genero')
            .order('apellido', { ascending: true });

          if (error) {
            console.warn('⚠️ [PatientsList] Error de Supabase, usando datos simulados:', error);
            throw error;
          }
          setPatients(data || []);
        } catch (supabaseError) {
          // Si hay error con la base de datos, usar datos simulados
          console.log('📊 [PatientsList] Fallback a datos simulados debido a error de BD');
          const simulatedPatients = generateSimulatedPatientsList();
          setPatients(simulatedPatients);
        }

      } else {
        // Generar lista simulada
        const simulatedPatients = generateSimulatedPatientsList();
        setPatients(simulatedPatients);
      }

    } catch (error) {
      console.error('❌ [PatientsList] Error obteniendo pacientes:', error);
      
      // Fallback a datos simulados
      const simulatedPatients = generateSimulatedPatientsList();
      setPatients(simulatedPatients);
    } finally {
      setLoading(false);
    }
  }, [isRealDataAvailable]);

  useEffect(() => {
    fetchPatients();
  }, [fetchPatients]);

  return { patients, loading, refetch: fetchPatients };
};

/**
 * Hook para obtener lista de psicólogos disponibles
 */
export const usePsychologistsList = () => {
  const [psychologists, setPsychologists] = useState([]);
  const [loading, setLoading] = useState(false);
  const { isRealDataAvailable } = useRealDataConnection();

  const fetchPsychologists = useCallback(async () => {
    setLoading(true);

    try {
      if (isRealDataAvailable) {
        try {
          const { data, error } = await supabase
            .from('usuarios')
            .select('id, nombre, apellido')
            .eq('rol', 'psicologo')
            .order('apellido', { ascending: true });

          if (error) {
            console.warn('⚠️ [PsychologistsList] Error de Supabase, usando datos simulados:', error);
            throw error;
          }
          setPsychologists(data || []);
        } catch (supabaseError) {
          // Si hay error con la base de datos, usar datos simulados
          console.log('📊 [PsychologistsList] Fallback a datos simulados debido a error de BD');
          const simulatedPsychologists = [
            { id: '1', nombre: 'Dr. Juan', apellido: 'Pérez' },
            { id: '2', nombre: 'Dra. María', apellido: 'González' },
            { id: '3', nombre: 'Dr. Carlos', apellido: 'Rodríguez' }
          ];
          setPsychologists(simulatedPsychologists);
        }

      } else {
        // Generar lista simulada
        const simulatedPsychologists = [
          { id: '1', nombre: 'Dr. Juan', apellido: 'Pérez', email: '<EMAIL>' },
          { id: '2', nombre: 'Dra. María', apellido: 'González', email: '<EMAIL>' },
          { id: '3', nombre: 'Dr. Carlos', apellido: 'Rodríguez', email: '<EMAIL>' }
        ];
        setPsychologists(simulatedPsychologists);
      }

    } catch (error) {
      console.error('❌ [PsychologistsList] Error obteniendo psicólogos:', error);
      setPsychologists([]);
    } finally {
      setLoading(false);
    }
  }, [isRealDataAvailable]);

  useEffect(() => {
    fetchPsychologists();
  }, [fetchPsychologists]);

  return { psychologists, loading, refetch: fetchPsychologists };
};

// Funciones auxiliares

/**
 * Transformar datos reales de Supabase al formato esperado por los componentes
 */
const transformRealDataToExpectedFormat = (realData) => {
  // Transformar evaluaciones y resultados
  const puntuaciones = {};
  const factores = { g: { pc: 0, nivel: 'Medio' }, Gf: { pc: 0, nivel: 'Medio' }, Gc: { pc: 0, nivel: 'Medio' } };

  if (realData.evaluaciones && realData.evaluaciones.length > 0) {
    const ultimaEvaluacion = realData.evaluaciones[realData.evaluaciones.length - 1];

    if (ultimaEvaluacion.resultados) {
      ultimaEvaluacion.resultados.forEach(resultado => {
        if (resultado.area_evaluada && resultado.puntuacion_percentil) {
          puntuaciones[resultado.area_evaluada] = {
            pd: resultado.puntuacion_directa || 0,
            pc: resultado.puntuacion_percentil,
            nivel: getNivelFromPercentile(resultado.puntuacion_percentil)
          };
        }
      });
    }

    // Calcular factores generales
    const percentiles = Object.values(puntuaciones).map(p => p.pc);
    if (percentiles.length > 0) {
      const promedio = percentiles.reduce((sum, pc) => sum + pc, 0) / percentiles.length;
      factores.g = { pc: Math.round(promedio), nivel: getNivelFromPercentile(promedio) };
      factores.Gf = { pc: Math.round(promedio * 0.95), nivel: getNivelFromPercentile(promedio * 0.95) };
      factores.Gc = { pc: Math.round(promedio * 1.05), nivel: getNivelFromPercentile(promedio * 1.05) };
    }
  }

  // Determinar estilo atencional
  const atencion = puntuaciones.A?.pc || 50;
  const concentracion = puntuaciones.CON?.pc || puntuaciones.A?.pc || 50;
  const estiloAtencional = determineAttentionStyle(atencion, concentracion);

  return {
    id: realData.id,
    nombre: realData.nombre,
    apellido: realData.apellido,
    documento: realData.documento,
    edad: calculateAge(realData.fecha_nacimiento) || realData.edad,
    genero: realData.genero,
    curso: realData.curso || realData.nivel_educativo,
    institucion: realData.institucion?.nombre || realData.institucion,
    fechaEvaluacion: realData.evaluaciones?.[0]?.fecha_evaluacion || new Date().toISOString().split('T')[0],
    psicologo: realData.evaluaciones?.[0]?.psicologo?.nombre || 'No asignado',
    nivelBat7: realData.nivel_bat7 || 'M',
    puntuaciones,
    factores,
    estiloAtencional
  };
};

/**
 * Obtener nivel aptitudinal basado en percentil
 */
const getNivelFromPercentile = (percentil) => {
  if (percentil >= 90) return 'Muy Alto';
  if (percentil >= 75) return 'Alto';
  if (percentil >= 60) return 'Medio-Alto';
  if (percentil >= 40) return 'Medio';
  if (percentil >= 25) return 'Medio-Bajo';
  if (percentil >= 10) return 'Bajo';
  return 'Muy Bajo';
};

/**
 * Calcular edad desde fecha de nacimiento
 */
const calculateAge = (fechaNacimiento) => {
  if (!fechaNacimiento) return null;
  const today = new Date();
  const birthDate = new Date(fechaNacimiento);
  let age = today.getFullYear() - birthDate.getFullYear();
  const monthDiff = today.getMonth() - birthDate.getMonth();
  if (monthDiff < 0 || (monthDiff === 0 && today.getDate() < birthDate.getDate())) {
    age--;
  }
  return age;
};

/**
 * Determinar estilo atencional basado en puntuaciones
 */
const determineAttentionStyle = (atencion, concentracion) => {
  const x = atencion - 50;
  const y = concentracion - 50;

  if (x >= 0 && y >= 0) {
    return {
      cuadrante: 'Focalizado-Sostenido',
      descripcion: 'Excelente capacidad para mantener la atención en tareas específicas durante períodos prolongados'
    };
  } else if (x < 0 && y >= 0) {
    return {
      cuadrante: 'Difuso-Sostenido',
      descripcion: 'Capacidad para mantener la atención durante tiempo prolongado pero con enfoque amplio'
    };
  } else if (x < 0 && y < 0) {
    return {
      cuadrante: 'Difuso-Variable',
      descripcion: 'Atención amplia pero variable en el tiempo, requiere cambios frecuentes de actividad'
    };
  } else {
    return {
      cuadrante: 'Focalizado-Variable',
      descripcion: 'Capacidad para enfocar intensamente pero por períodos cortos'
    };
  }
};

/**
 * Generar datos simulados de un paciente
 */
const generateSimulatedPatientData = (id) => {
  return {
    id: id,
    nombre: 'María',
    apellido: 'González',
    documento: '12345678',
    edad: 16,
    genero: 'Femenino',
    curso: '2-secundaria',
    institucion: 'Colegio San José',
    fechaEvaluacion: '2024-01-15',
    psicologo: 'Dr. Juan Pérez',
    nivelBat7: 'M',
    puntuaciones: {
      V: { pd: 45, pc: 78, nivel: 'Alto' },
      E: { pd: 38, pc: 65, nivel: 'Medio' },
      A: { pd: 52, pc: 85, nivel: 'Alto' },
      R: { pd: 41, pc: 72, nivel: 'Medio-Alto' },
      N: { pd: 35, pc: 58, nivel: 'Medio' },
      M: { pd: 29, pc: 45, nivel: 'Medio-Bajo' },
      O: { pd: 48, pc: 82, nivel: 'Alto' }
    },
    factores: {
      g: { pc: 73, nivel: 'Medio-Alto' },
      Gf: { pc: 69, nivel: 'Medio' },
      Gc: { pc: 77, nivel: 'Alto' }
    },
    estiloAtencional: {
      cuadrante: 'Focalizado-Sostenido',
      descripcion: 'Capacidad para mantener la atención en tareas específicas'
    }
  };
};

/**
 * Generar lista simulada de pacientes
 */
const generateSimulatedPatientsList = () => {
  return [
    { id: '1', nombre: 'María', apellido: 'González', documento: '12345678', edad: 16, genero: 'Femenino' },
    { id: '2', nombre: 'Juan', apellido: 'Pérez', documento: '87654321', edad: 15, genero: 'Masculino' },
    { id: '3', nombre: 'Ana', apellido: 'Rodríguez', documento: '11223344', edad: 17, genero: 'Femenino' },
    { id: '4', nombre: 'Carlos', apellido: 'López', documento: '44332211', edad: 16, genero: 'Masculino' },
    { id: '5', nombre: 'Sofía', apellido: 'Martínez', documento: '55667788', edad: 15, genero: 'Femenino' }
  ];
};
