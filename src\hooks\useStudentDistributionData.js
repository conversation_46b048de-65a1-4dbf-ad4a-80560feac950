/**
 * @file useStudentDistributionData.js
 * @description Custom hook for processing student distribution data
 */

import { useMemo } from 'react';
import { validateStudentData, calculatePieSegments } from '../utils/chartUtils.js';

export const useStudentDistributionData = (rawData) => {
  const validatedData = useMemo(() => {
    return validateStudentData(rawData);
  }, [rawData]);

  const segments = useMemo(() => {
    return calculatePieSegments(validatedData);
  }, [validatedData]);

  const totalStudents = useMemo(() => {
    return validatedData.reduce((sum, item) => sum + (parseInt(item.total_estudiantes) || 0), 0);
  }, [validatedData]);

  const hasData = validatedData.length > 0;

  return {
    validatedData,
    segments,
    totalStudents,
    hasData
  };
};