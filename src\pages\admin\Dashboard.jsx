import React, { useState, useEffect } from 'react';
import PageHeader from '../../components/ui/PageHeader';
import FilterPanel from '../../components/dashboard/FilterPanel';
import DashboardToolbar from '../../components/dashboard/DashboardToolbar';
import DashboardViewSelector from '../../components/dashboard/DashboardViewSelector';
import QuickAccess from '../../components/admin/QuickAccess';
// Hook personalizado para datos del dashboard
// import useEnhancedDashboardData from '../../hooks/useEnhancedDashboardData';
import DashboardService from '../../services/DashboardService';
import { useDashboardExport } from '../../hooks/useDashboardExport';
// Vistas del dashboard
import {
  ExecutiveView,
  KpisView,
  GeneralView,
  TrendsView,
  StatisticalView,
  ExportView
} from '../../components/dashboard/views';
import EnhancedIndividualView from '../../components/dashboard/views/EnhancedIndividualView';
import ComparisonView from '../../components/dashboard/views/ComparisonView';
import DependencyStatus from '../../components/dashboard/DependencyStatus';
import ConnectionTestPanel from '../../components/dashboard/ConnectionTestPanel';
import AdvancedFilterPanel from '../../components/dashboard/AdvancedFilterPanel';
import DiagnosticPanel from '../../components/admin/DiagnosticPanel';
import { AnimatedView, AnimatedCard, PulseLoader } from '../../components/animations/DashboardAnimations';
import { HelpIcon, ContextualHelpPanel, dashboardHelpContent } from '../../components/ui/ContextualHelp';
import SyncTestView from '../../components/dashboard/views/enhanced/SyncTestView';
import {
  FaChartPie,
  FaChartLine,
  FaTachometerAlt,
  FaProjectDiagram,
  FaBalanceScale,
  FaDownload,
  FaShare,
  FaRedo,
  FaFilter,
  FaClock,
  FaUser
} from 'react-icons/fa';
import { toast } from 'react-toastify';

/**
 * Dashboard Ejecutivo Refactorizado
 * Componente orquestador que maneja navegación y datos
 * Las vistas individuales están separadas en componentes específicos
 */
const Dashboard = () => {
  // Estados para UI
  const [selectedView, setSelectedView] = useState('executive');
  const [showFilters, setShowFilters] = useState(false);
  const [showAdvancedFilters, setShowAdvancedFilters] = useState(false);
  const [showHelp, setShowHelp] = useState(false);
  const [filters, setFilters] = useState({});

  // Estados para datos del dashboard
  const [data, setData] = useState(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [lastUpdate, setLastUpdate] = useState(null);para 

  // Cargar datos usando DashboardService directamente
  useEffect(() => {
    const loadDashboardData = async () => {
      try {
        setLoading(true);
        setError(null);
        console.log('🔄 [Dashboard] Cargando datos con DashboardService...');

        const dashboardData = await DashboardService.fetchDashboardData(filters);
        setData(dashboardData);
        setLastUpdate(dashboardData.lastUpdate);

        console.log('✅ [Dashboard] Datos cargados exitosamente:', dashboardData);
      } catch (err) {
        console.error('❌ [Dashboard] Error cargando datos:', err);
        setError(err);
      } finally {
        setLoading(false);
      }
    };

    loadDashboardData();
  }, [filters]);

  // Funciones auxiliares
  const refreshData = () => {
    setData(null);
    setLoading(true);
  };

  const applyFilters = (newFilters) => {
    setFilters(newFilters);
  };
  const { exportAsPDF, exportAsExcel } = useDashboardExport(data);

  // Configuración de vistas del dashboard
  const dashboardViews = [
    {
      key: 'sync-test',
      label: '🔧 Pruebas Sync',
      description: 'Verificar sincronización de datos',
      icon: FaProjectDiagram
    },
    {
      key: 'diagnostics',
      label: '🏥 Diagnóstico',
      description: 'Diagnóstico y corrección de problemas críticos',
      icon: FaProjectDiagram
    },
    {
      key: 'executive',
      label: 'Resumen Ejecutivo',
      description: 'Vista estratégica con hallazgos clave',
      icon: FaChartPie
    },
    {
      key: 'kpis',
      label: 'KPIs Críticos',
      description: 'Indicadores clave de rendimiento',
      icon: FaTachometerAlt
    },
    {
      key: 'general',
      label: 'Visión General',
      description: 'Estadísticas principales y gráficos',
      icon: FaChartLine
    },
    {
      key: 'trends',
      label: 'Análisis de Tendencias',
      description: 'Evolución temporal de métricas',
      icon: FaChartLine
    },
    {
      key: 'comparative',
      label: 'Análisis Comparativo',
      description: 'Benchmarking y comparaciones',
      icon: FaBalanceScale
    },
    {
      key: 'statistical',
      label: 'Análisis Estadístico',
      description: 'Medidas de tendencia y dispersión',
      icon: FaProjectDiagram
    },
    {
      key: 'export',
      label: 'Exportación',
      description: 'Reportes y presentaciones',
      icon: FaDownload
    },
    {
      key: 'individual',
      label: 'Informe Individual',
      description: 'Análisis detallado de un evaluado',
      icon: FaUser
    },
    {
      key: 'dependencies',
      label: 'Estado del Sistema',
      description: 'Dependencias y configuración',
      icon: FaDownload
    }
  ];

  // Manejadores de eventos
  const handleFiltersChange = (newFilters) => {
    setFilters(newFilters);
    applyFilters(newFilters);
    setShowFilters(false);
  };

  const handleShareDashboard = () => {
    const url = window.location.href;
    navigator.clipboard.writeText(url).then(() => {
      toast.success('URL copiada al portapapeles');
    }).catch(() => {
      toast.error('Error al copiar URL');
    });
  };

  const handleAdvancedFiltersToggle = () => {
    setShowAdvancedFilters(!showAdvancedFilters);
    setShowFilters(false); // Cerrar filtros básicos si están abiertos
  };

  const handleHelpToggle = () => {
    setShowHelp(!showHelp);
  };

  const handleExport = (format) => {
    if (format === 'pdf') {
      exportAsPDF();
    } else if (format === 'excel') {
      exportAsExcel();
    } else {
      toast.warn(`Formato de exportación '${format}' no soportado.`);
    }
  };

  const handleExportComplete = (result) => {
    if (result.success) {
      toast.success('Exportación completada exitosamente');
    } else {
      toast.error('Error en la exportación');
    }
  };

  // Renderizar vista actual
  const renderCurrentView = () => {
    const {
      estadisticasGenerales,
      alertsData,
      kpiData,
      trendData,
      datosDistribucionNivel,
      datosPerfilInstitucional,
    } = data || {};

    // Debug: verificar datos extraídos
    console.log('🎯 [Dashboard] Datos extraídos para vistas:', {
      estadisticasGenerales: !!estadisticasGenerales,
      alertsData: !!alertsData,
      kpiData: !!kpiData,
      trendData: !!trendData,
      comparisonData: !!data?.comparisonData,
      distributionData: !!data?.distributionData,
      selectedView
    });

    // Debug detallado de kpiData
    if (selectedView === 'kpis') {
      console.log('🎯 [Dashboard] KPI Data detallado:', kpiData);
      console.log('🎯 [Dashboard] Alerts Data detallado:', alertsData);
    }

    switch (selectedView) {
      case 'sync-test':
        return <SyncTestView />;
      case 'diagnostics':
        return <DiagnosticPanel />;
      case 'executive':
        return (
          <ExecutiveView
            loading={loading}
            estadisticasGenerales={estadisticasGenerales}
            alertsData={alertsData}
            kpiData={kpiData}
            trendData={trendData}
          />
        );
      case 'kpis':
        return <KpisView loading={loading} kpiData={kpiData} alertsData={alertsData} />;
      case 'general':
        return (
          <GeneralView
            loading={loading}
            estadisticasGenerales={estadisticasGenerales}
            datosDistribucionNivel={datosDistribucionNivel}
            datosPerfilInstitucional={datosPerfilInstitucional}
            data={data}
          />
        );
      case 'trends':
        return <TrendsView loading={loading} trendData={trendData} />;
      case 'comparative':
        return <ComparisonView loading={loading} comparisonData={data?.comparisonData} />;
      case 'statistical':
        return <StatisticalView loading={loading} data={data} filters={filters} />;
      case 'export':
        return (
          <ExportView
            loading={loading}
            estadisticasGenerales={estadisticasGenerales}
            onExport={handleExport}
            onExportComplete={handleExportComplete}
          />
        );
      case 'individual':
        return <EnhancedIndividualView
          data={data}
          loading={loading}
          filters={currentFilters}
          onFiltersChange={applyFilters}
          isRealDataAvailable={isRealDataAvailable}
          connectionStatus={connectionStatus}
        />;
      case 'dependencies':
        return (
          <div className="space-y-6">
            <ConnectionTestPanel
              isRealDataAvailable={isRealDataAvailable}
              connectionStatus={connectionStatus}
            />
            <DependencyStatus />
          </div>
        );
      default:
        return (
          <GeneralView
            loading={loading}
            estadisticasGenerales={estadisticasGenerales}
            datosDistribucionNivel={datosDistribucionNivel}
            datosPerfilInstitucional={datosPerfilInstitucional}
            data={data}
          />
        );
    }
  };

  return (
    <div className="min-h-screen bg-gray-50">
      <PageHeader
        title="📊 Dashboard Ejecutivo BAT-7"
        subtitle="Análisis integral de datos psicométricos"
        actions={
          <div className="flex items-center space-x-2">
            <HelpIcon
              content="Accede a la ayuda contextual para aprender a usar el dashboard"
              position="bottom"
            />
            <button
              onClick={handleHelpToggle}
              className="px-3 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors text-sm"
            >
              Ayuda
            </button>
          </div>
        }
      />

      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <DashboardToolbar
          lastUpdated={lastUpdate}
          loading={loading}
          onRefresh={refreshData}
          onShare={handleShareDashboard}
          onShowFilters={() => setShowFilters(true)}
        />

        {/* Acceso rápido a nuevas funcionalidades */}
        <QuickAccess />

        {/* Indicador de estado de conexión */}
        {isRealDataAvailable && (
          <div className="mb-4 p-3 bg-green-50 border border-green-200 rounded-lg">
            <div className="flex items-center text-sm text-green-700">
              <div className="w-2 h-2 bg-green-500 rounded-full mr-2 animate-pulse"></div>
              <span className="font-medium">Conectado a base de datos real</span>
              <span className="ml-2 text-green-600">({connectionStatus})</span>
            </div>
          </div>
        )}

        {!isRealDataAvailable && (
          <div className="mb-4 p-3 bg-yellow-50 border border-yellow-200 rounded-lg">
            <div className="flex items-center text-sm text-yellow-700">
              <div className="w-2 h-2 bg-yellow-500 rounded-full mr-2"></div>
              <span className="font-medium">Usando datos simulados</span>
              <span className="ml-2 text-yellow-600">({connectionStatus})</span>
            </div>
          </div>
        )}

        <DashboardViewSelector
          views={dashboardViews}
          selectedView={selectedView}
          onSelectView={setSelectedView}
        />

        {/* Contenido de la vista actual */}
        <div className="mb-8">
          {error && (
            <div className="mb-6 bg-yellow-50 border-l-4 border-yellow-400 p-4 rounded">
              <div className="flex">
                <div className="flex-shrink-0">
                  <i className="fas fa-exclamation-triangle text-yellow-400"></i>
                </div>
                <div className="ml-3">
                  <p className="text-sm text-yellow-700">
                    <strong>Advertencia:</strong> {error}
                  </p>
                </div>
              </div>
            </div>
          )}

          <AnimatedView>
            {loading ? (
              <div className="flex items-center justify-center h-64">
                <PulseLoader size="lg" color="blue" />
              </div>
            ) : (
              renderCurrentView()
            )}
          </AnimatedView>
        </div>

        {/* Panel de filtros básicos */}
        <FilterPanel
          isOpen={showFilters}
          onClose={() => setShowFilters(false)}
          onFiltersChange={handleFiltersChange}
        />

        {/* Panel de filtros avanzados */}
        <AdvancedFilterPanel
          isOpen={showAdvancedFilters}
          onClose={() => setShowAdvancedFilters(false)}
          onFiltersChange={handleFiltersChange}
          initialFilters={filters}
        />

        {/* Panel de ayuda contextual */}
        <ContextualHelpPanel
          title="Ayuda del Dashboard"
          sections={dashboardHelpContent.filters.sections}
          isOpen={showHelp}
          onClose={() => setShowHelp(false)}
        />
      </div>
    </div>
  );
};

export default Dashboard;
