import React, { useState, useEffect, useCallback } from 'react';
import { Card, CardHeader, CardBody } from '../../components/ui/Card';
import PageHeader from '../../components/ui/PageHeader';
import { <PERSON><PERSON><PERSON> } from '../../components/charts/PieChart';
import { <PERSON><PERSON><PERSON> } from '../../components/charts/BarChart';
// Import components for dashboard
import KPICard from '../../components/dashboard/KPICard';
import AlertSystem from '../../components/dashboard/AlertSystem';
import TrendChart from '../../components/dashboard/TrendChart';
import FilterPanel from '../../components/dashboard/FilterPanel';
import ExecutiveExport from '../../components/dashboard/ExecutiveExport';
// Import these components conditionally to avoid 500 errors
// import PredictiveAnalysis from '../../components/dashboard/PredictiveAnalysis';
// import StatisticalAnalysis from '../../components/dashboard/StatisticalAnalysis';
import ExecutiveSummary from '../../components/dashboard/ExecutiveSummary';
import {
  FaChart<PERSON>ie,
  FaUsers,
  FaUserCheck,
  FaClipboardList,
  FaChartLine,
  FaCalendarAlt,
  FaClock,
  FaDownload,
  FaShare,
  FaRedo,
  FaLayerGroup,
  FaBrain,
  FaProjectDiagram,
  FaTachometerAlt,
  FaExclamationTriangle,
  FaArrowUp,
  FaGraduationCap,
  FaAward,
  FaFilter,
  FaBalanceScale
} from 'react-icons/fa';
import supabase from '../../api/supabaseClient';
import DashboardService from '../../services/dashboardService';
import { toast } from 'react-toastify';

/**
 * Dashboard Ejecutivo Completo con análisis avanzado de datos BAT-7
 * Incluye análisis tradicional + KPIs ejecutivos + análisis predictivo + exportación
 */
const Dashboard = () => {
  // Estados principales
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [estadisticasGenerales, setEstadisticasGenerales] = useState(null);
  const [selectedView, setSelectedView] = useState('executive');
  const [lastUpdated, setLastUpdated] = useState(null);
  const [filters, setFilters] = useState({});
  const [showFilters, setShowFilters] = useState(false);

  // Estados para datos dinámicos
  const [datosDistribucionNivel, setDatosDistribucionNivel] = useState([]);
  const [datosPerfilInstitucional, setDatosPerfilInstitucional] = useState(null);
  const [datosComparativaNivel, setDatosComparativaNivel] = useState(null);
  const [datosComparativaGenero, setDatosComparativaGenero] = useState(null);
  const [kpiData, setKpiData] = useState(null);
  const [trendData, setTrendData] = useState([]);

  // Sistema de alertas
  const alertsData = [
    {
      id: 1,
      type: 'critical',
      title: 'Aptitud Mecánica Bajo Rendimiento',
      message: '15 estudiantes presentan percentiles críticos (<25). Requiere intervención inmediata.',
      action: 'Ver plan de acción'
    },
    {
      id: 2,
      type: 'warning',
      title: 'Tendencia Descendente Detectada',
      message: 'Aptitud espacial muestra declive del 3% en las últimas 4 semanas.',
      action: 'Analizar causas'
    },
    {
      id: 3,
      type: 'success',
      title: 'Meta Alcanzada',
      message: 'Superado el objetivo de 70% en rendimiento general institucional.',
      action: 'Ver detalles'
    }
  ];

  // Configuración de vistas del dashboard ejecutivo (AMPLIADO)
  const dashboardViews = [
    {
      id: 'executive',
      name: 'Resumen Ejecutivo',
      icon: 'fas fa-tachometer-alt',
      description: 'Vista estratégica con storytelling y hallazgos clave'
    },
    {
      id: 'kpis',
      name: 'KPIs Críticos',
      icon: 'fas fa-chart-line',
      description: 'Métricas clave de rendimiento con semáforos'
    },
    {
      id: 'general',
      name: 'Visión General',
      icon: 'fas fa-chart-pie',
      description: 'Resumen tradicional y estadísticas principales'
    },
    {
      id: 'trends',
      name: 'Análisis de Tendencias',
      icon: 'fas fa-trending-up',
      description: 'Evolución temporal y proyecciones'
    },
    {
      id: 'export',
      name: 'Exportación',
      icon: 'fas fa-download',
      description: 'Reportes ejecutivos y presentaciones'
    }
  ];

  // Cargar datos del dashboard
  const fetchDashboardData = useCallback(async () => {
    try {
      setLoading(true);
      setError(null);

      console.log('📊 [Executive Dashboard] Cargando datos...');

      // Cargar todos los datos en paralelo
      const [
        estadisticas,
        distribucionNivel,
        perfilInstitucional,
        comparativaNivel,
        comparativaGenero,
        kpis,
        tendencias
      ] = await Promise.all([
        DashboardService.getEstadisticasGenerales(),
        DashboardService.getDistribucionNivel(),
        DashboardService.getPerfilInstitucional(),
        DashboardService.getComparativaNivel(),
        DashboardService.getComparativaGenero(),
        DashboardService.getKPIData(),
        DashboardService.getTrendData()
      ]);

      console.log('✅ [Executive Dashboard] Todos los datos cargados');

      // Actualizar todos los estados
      setEstadisticasGenerales(estadisticas);
      setDatosDistribucionNivel(distribucionNivel);
      setDatosPerfilInstitucional(perfilInstitucional);
      setDatosComparativaNivel(comparativaNivel);
      setDatosComparativaGenero(comparativaGenero);
      setKpiData(kpis);
      setTrendData(tendencias);
      setLastUpdated(new Date());

    } catch (error) {
      console.error('💥 [Executive Dashboard] Error general:', error);

      // Datos de fallback para todos los componentes
      setEstadisticasGenerales({
        total_pacientes: 156,
        pacientes_evaluados: 148,
        total_evaluaciones: 1092,
        percentil_promedio_general: 72.5,
        evaluaciones_ultimo_mes: 234,
        evaluaciones_ultima_semana: 67
      });

      setDatosDistribucionNivel([
        { name: 'Elemental', value: 45, color: '#3B82F6' },
        { name: 'Medio', value: 35, color: '#10B981' },
        { name: 'Superior', value: 20, color: '#F59E0B' }
      ]);

      setDatosPerfilInstitucional({
        labels: ['Verbal', 'Espacial', 'Atención', 'Razonamiento', 'Numérico', 'Mecánico', 'Ortografía'],
        datasets: [{
          label: 'Percentil Promedio',
          data: [72, 68, 75, 70, 65, 58, 80],
          backgroundColor: 'rgba(59, 130, 246, 0.2)',
          borderColor: 'rgba(59, 130, 246, 1)',
          borderWidth: 2
        }]
      });

      setError('Error al conectar con la base de datos - Usando datos de ejemplo');
      toast.warning('Usando datos de ejemplo - Verifique la conexión a Supabase');
    } finally {
      setLoading(false);
    }
  }, []);

  useEffect(() => {
    fetchDashboardData();
  }, [fetchDashboardData]);

  // Manejar cambios de filtros
  const handleFiltersChange = async (newFilters) => {
    try {
      setFilters(newFilters);
      console.log('🔍 Filtros aplicados:', newFilters);

      // Recargar datos con filtros aplicados
      setLoading(true);
      const filteredData = await DashboardService.applyFilters(newFilters);

      // Procesar datos filtrados y actualizar estados
      // (Aquí se procesarían los datos filtrados para actualizar los gráficos)
      console.log('✅ Datos filtrados obtenidos:', filteredData);

      toast.success('Filtros aplicados correctamente');
    } catch (error) {
      console.error('❌ Error al aplicar filtros:', error);
      toast.error('Error al aplicar filtros');
    } finally {
      setLoading(false);
    }
  };

  // Manejar exportación
  const handleExport = async (type, data) => {
    console.log(`📄 Exportando en formato: ${type}`);

    switch (type) {
      case 'pdf':
        toast.success('Generando reporte ejecutivo PDF...');
        setTimeout(() => {
          toast.success('Reporte PDF generado exitosamente');
        }, 2000);
        break;
      case 'excel':
        toast.success('Exportando datos a Excel...');
        break;
      case 'powerpoint':
        toast.success('Generando presentación PowerPoint...');
        break;
      case 'email':
        toast.success('Enviando reporte por email...');
        break;
      default:
        toast.info(`Exportación ${type} en desarrollo`);
    }
  };

  const handleExportComplete = async (format) => {
    toast.info(`Exportación en formato ${format.toUpperCase()} - Próximamente`);
  };

  const handleShareDashboard = () => {
    const shareURL = window.location.href;
    navigator.clipboard.writeText(shareURL);
    toast.success('URL del dashboard copiada al portapapeles');
  };

  // Componente de estadísticas generales
  const EstadisticasGenerales = ({ data, loading }) => {
    if (loading) {
      return (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 mb-8">
          {[...Array(6)].map((_, i) => (
            <Card key={i} className="animate-pulse">
              <CardBody className="p-6">
                <div className="h-4 bg-gray-200 rounded w-3/4 mb-4"></div>
                <div className="h-8 bg-gray-200 rounded w-1/2 mb-2"></div>
                <div className="h-3 bg-gray-200 rounded w-2/3"></div>
              </CardBody>
            </Card>
          ))}
        </div>
      );
    }

    return (
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 mb-8">
        {/* Total Pacientes */}
        <Card className="hover:shadow-lg transition-shadow duration-200">
          <CardHeader className="bg-gradient-to-r from-blue-500 to-blue-600 text-white">
            <h3 className="text-lg font-semibold flex items-center justify-center">
              <FaUsers className="mr-2" />
              Total Pacientes
            </h3>
          </CardHeader>
          <CardBody className="p-6 text-center">
            <div className="text-3xl font-bold text-blue-600 mb-2">
              {data?.total_pacientes || 0}
            </div>
            <p className="text-gray-600">Pacientes registrados</p>
          </CardBody>
        </Card>

        {/* Pacientes Evaluados */}
        <Card className="hover:shadow-lg transition-shadow duration-200">
          <CardHeader className="bg-gradient-to-r from-green-500 to-green-600 text-white">
            <h3 className="text-lg font-semibold flex items-center justify-center">
              <FaUserCheck className="mr-2" />
              Pacientes Evaluados
            </h3>
          </CardHeader>
          <CardBody className="p-6 text-center">
            <div className="text-3xl font-bold text-green-600 mb-2">
              {data?.pacientes_evaluados || 0}
            </div>
            <p className="text-gray-600">Con al menos 1 test</p>
          </CardBody>
        </Card>

        {/* Total Evaluaciones */}
        <Card className="hover:shadow-lg transition-shadow duration-200">
          <CardHeader className="bg-gradient-to-r from-purple-500 to-purple-600 text-white">
            <h3 className="text-lg font-semibold flex items-center justify-center">
              <FaClipboardList className="mr-2" />
              Total Evaluaciones
            </h3>
          </CardHeader>
          <CardBody className="p-6 text-center">
            <div className="text-3xl font-bold text-purple-600 mb-2">
              {data?.total_evaluaciones || 0}
            </div>
            <p className="text-gray-600">Tests completados</p>
          </CardBody>
        </Card>

        {/* Percentil Promedio */}
        <Card className="hover:shadow-lg transition-shadow duration-200">
          <CardHeader className="bg-gradient-to-r from-orange-500 to-orange-600 text-white">
            <h3 className="text-lg font-semibold flex items-center justify-center">
              <FaChartLine className="mr-2" />
              Percentil Promedio
            </h3>
          </CardHeader>
          <CardBody className="p-6 text-center">
            <div className="text-3xl font-bold text-orange-600 mb-2">
              {data?.percentil_promedio_general || 0}
            </div>
            <p className="text-gray-600">Rendimiento general</p>
          </CardBody>
        </Card>

        {/* Último Mes */}
        <Card className="hover:shadow-lg transition-shadow duration-200">
          <CardHeader className="bg-gradient-to-r from-indigo-500 to-indigo-600 text-white">
            <h3 className="text-lg font-semibold flex items-center justify-center">
              <FaCalendarAlt className="mr-2" />
              Último Mes
            </h3>
          </CardHeader>
          <CardBody className="p-6 text-center">
            <div className="text-3xl font-bold text-indigo-600 mb-2">
              {data?.evaluaciones_ultimo_mes || 0}
            </div>
            <p className="text-gray-600">Evaluaciones realizadas</p>
          </CardBody>
        </Card>

        {/* Última Semana */}
        <Card className="hover:shadow-lg transition-shadow duration-200">
          <CardHeader className="bg-gradient-to-r from-pink-500 to-pink-600 text-white">
            <h3 className="text-lg font-semibold flex items-center justify-center">
              <FaClock className="mr-2" />
              Última Semana
            </h3>
          </CardHeader>
          <CardBody className="p-6 text-center">
            <div className="text-3xl font-bold text-pink-600 mb-2">
              {data?.evaluaciones_ultima_semana || 0}
            </div>
            <p className="text-gray-600">Evaluaciones recientes</p>
          </CardBody>
        </Card>
      </div>
    );
  };

  // Renderizar contenido según la vista seleccionada
  const renderDashboardContent = () => {
    switch (selectedView) {
      case 'executive':
        return (
          <div className="space-y-6">
            <div className="bg-gradient-to-r from-blue-600 to-purple-600 text-white rounded-lg shadow-lg p-8 text-center">
              <h1 className="text-3xl font-bold mb-4">Dashboard Ejecutivo - BAT-7</h1>
              <p className="text-blue-100">
                Vista ejecutiva del sistema de evaluación psicométrica
              </p>
            </div>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
              <div className="bg-white rounded-lg shadow-lg p-6 text-center">
                <h3 className="text-lg font-semibold mb-4 text-center">Total Pacientes</h3>
                <div className="text-4xl font-bold text-blue-600 mb-2">
                  {estadisticasGenerales?.total_pacientes || 0}
                </div>
              </div>
              <div className="bg-white rounded-lg shadow-lg p-6 text-center">
                <h3 className="text-lg font-semibold mb-4 text-center">Evaluaciones</h3>
                <div className="text-4xl font-bold text-green-600 mb-2">
                  {estadisticasGenerales?.total_evaluaciones || 0}
                </div>
              </div>
              <div className="bg-white rounded-lg shadow-lg p-6 text-center">
                <h3 className="text-lg font-semibold mb-4 text-center">Percentil Promedio</h3>
                <div className="text-4xl font-bold text-orange-600 mb-2">
                  {estadisticasGenerales?.percentil_promedio_general || 0}
                </div>
              </div>
            </div>
          </div>
        );

      case 'kpis':
        return (
          <div className="space-y-6">
            {/* KPIs Críticos */}
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
              {kpiData && (
                <>
                  <KPICard
                    title="Rendimiento General"
                    value={kpiData.rendimientoGeneral?.current || 0}
                    previousValue={kpiData.rendimientoGeneral?.previous || 0}
                    target={kpiData.rendimientoGeneral?.target || 0}
                    unit=" percentil"
                    icon={FaTachometerAlt}
                    color="blue"
                    format="decimal"
                  />
                  <KPICard
                    title="Tasa de Completitud"
                    value={kpiData.tasaCompletitud?.current || 0}
                    previousValue={kpiData.tasaCompletitud?.previous || 0}
                    target={kpiData.tasaCompletitud?.target || 0}
                    unit="%"
                    icon={FaAward}
                    color="green"
                    format="number"
                  />
                  <KPICard
                    title="Estudiantes en Riesgo"
                    value={kpiData.estudiantesRiesgo?.current || 0}
                    previousValue={kpiData.estudiantesRiesgo?.previous || 0}
                    target={kpiData.estudiantesRiesgo?.target || 0}
                    unit=" estudiantes"
                    icon={FaExclamationTriangle}
                    color="red"
                    format="number"
                    alert={kpiData.estudiantesRiesgo?.current > kpiData.estudiantesRiesgo?.target ?
                      "Supera el objetivo establecido" : null}
                  />
                </>
              )}
              {kpiData && (
                <>
                  <KPICard
                    title="Mejora Mensual"
                    value={kpiData.mejoraMensual?.current || 0}
                    previousValue={kpiData.mejoraMensual?.previous || 0}
                    target={kpiData.mejoraMensual?.target || 0}
                    unit="%"
                    icon={FaArrowUp}
                    color="purple"
                    format="decimal"
                  />
                  <KPICard
                    title="Satisfacción Docente"
                    value={kpiData.satisfaccionDocente?.current || 0}
                    previousValue={kpiData.satisfaccionDocente?.previous || 0}
                    target={kpiData.satisfaccionDocente?.target || 0}
                    unit="%"
                    icon={FaGraduationCap}
                    color="indigo"
                    format="number"
                  />
                  <KPICard
                    title="ROI Educativo"
                    value={kpiData.roiEducativo?.current || 0}
                    previousValue={kpiData.roiEducativo?.previous || 0}
                    target={kpiData.roiEducativo?.target || 0}
                    unit="x"
                    icon={FaChartLine}
                    color="orange"
                    format="decimal"
                  />
                </>
              )}
            </div>

            {/* Sistema de Alertas */}
            <AlertSystem alerts={alertsData} />
          </div>
        );

      case 'trends':
        return (
          <div className="space-y-6">
            {trendData && trendData.length > 0 ? (
              <>
                <TrendChart
                  data={trendData}
                  title="Evolución de Métricas Clave - Últimos 6 Meses"
                  height={400}
                  colors={['#3B82F6', '#10B981', '#F59E0B']}
                />

                <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                  <TrendChart
                    data={trendData.map(d => ({ name: d.name || '', rendimiento: d.rendimiento || 0 }))}
                    title="Tendencia de Rendimiento"
                    type="area"
                    height={300}
                    colors={['#3B82F6']}
                  />
                  <TrendChart
                    data={trendData.map(d => ({ name: d.name || '', participacion: d.participacion || 0 }))}
                    title="Evolución de Participación"
                    type="area"
                    height={300}
                    colors={['#10B981']}
                  />
                </div>
              </>
            ) : (
              <div className="text-center py-12 bg-white rounded-lg shadow-md">
                <p className="text-gray-500">No hay datos de tendencias disponibles</p>
              </div>
            )}
          </div>
        );

      case 'export':
        return <ExecutiveExport data={estadisticasGenerales} onExport={handleExport} />;

      case 'general':
        return (
          <div className="space-y-6">
            {/* Estadísticas generales con el nuevo diseño */}
            <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
              {/* Total Pacientes */}
              <div className="bg-white rounded-lg shadow-md overflow-hidden">
                <div className="bg-blue-500 text-white p-4 flex items-center justify-center">
                  <FaUsers className="mr-2" />
                  <h3 className="text-lg font-semibold">Total Pacientes</h3>
                </div>
                <div className="p-6 text-center">
                  <div className="text-4xl font-bold text-blue-600 mb-2">
                    {estadisticasGenerales?.total_pacientes || 0}
                  </div>
                  <p className="text-gray-600">Pacientes registrados</p>
                </div>
              </div>
              
              {/* Pacientes Evaluados */}
              <div className="bg-white rounded-lg shadow-md overflow-hidden">
                <div className="bg-green-500 text-white p-4 flex items-center justify-center">
                  <FaUserCheck className="mr-2" />
                  <h3 className="text-lg font-semibold">Pacientes Evaluados</h3>
                </div>
                <div className="p-6 text-center">
                  <div className="text-4xl font-bold text-green-600 mb-2">
                    {estadisticasGenerales?.pacientes_evaluados || 0}
                  </div>
                  <p className="text-gray-600">Con al menos 1 test</p>
                </div>
              </div>
              
              {/* Total Evaluaciones */}
              <div className="bg-white rounded-lg shadow-md overflow-hidden">
                <div className="bg-purple-500 text-white p-4 flex items-center justify-center">
                  <FaClipboardList className="mr-2" />
                  <h3 className="text-lg font-semibold">Total Evaluaciones</h3>
                </div>
                <div className="p-6 text-center">
                  <div className="text-4xl font-bold text-purple-600 mb-2">
                    {estadisticasGenerales?.total_evaluaciones || 0}
                  </div>
                  <p className="text-gray-600">Tests completados</p>
                </div>
              </div>
            </div>
          </div>
        );

      default:
        return (
          <div className="text-center py-12 bg-white rounded-lg shadow-md">
            <p className="text-gray-500">Seleccione una vista del dashboard</p>
          </div>
        );
    }
  };

  return (
    <div>
      {/* Header Section with Standardized Style */}
      <PageHeader
        title="Dashboard Ejecutivo"
        subtitle="Análisis avanzado de datos BAT-7"
        icon={FaTachometerAlt}
      />

      <div className="container mx-auto py-6">
        {/* Barra de acciones */}
        <div className="mb-6 bg-white rounded-lg shadow-md p-4">
          <div className="flex flex-col md:flex-row md:items-center md:justify-between gap-4">
            {/* Selector de vistas */}
            <div className="flex flex-wrap gap-2">
              {dashboardViews.map(view => (
                <button
                  key={view.id}
                  onClick={() => setSelectedView(view.id)}
                  className={`px-3 py-2 rounded-lg text-sm font-medium transition-colors ${
                    selectedView === view.id
                      ? 'bg-blue-600 text-white'
                      : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
                  }`}
                  title={view.description}
                >
                  <i className={`${view.icon} mr-1`}></i>
                  {view.name}
                </button>
              ))}
            </div>

            {/* Acciones secundarias */}
            <div className="flex items-center gap-2">
              <button
                onClick={() => setShowFilters(!showFilters)}
                className="px-3 py-2 bg-gray-100 text-gray-700 rounded-lg hover:bg-gray-200 transition-colors"
                title="Filtrar datos"
              >
                <FaFilter className="inline-block mr-1" />
                Filtros
              </button>
              
              <button
                onClick={fetchDashboardData}
                className="px-3 py-2 bg-gray-100 text-gray-700 rounded-lg hover:bg-gray-200 transition-colors"
                title="Actualizar datos"
              >
                <FaRedo className="inline-block mr-1" />
                Actualizar
              </button>
              
              <button
                onClick={handleShareDashboard}
                className="px-3 py-2 bg-gray-100 text-gray-700 rounded-lg hover:bg-gray-200 transition-colors"
                title="Compartir dashboard"
              >
                <FaShare className="inline-block mr-1" />
                Compartir
              </button>
            </div>
          </div>
          
          {/* Panel de filtros (condicional) */}
          {showFilters && (
            <div className="mt-4 pt-4 border-t border-gray-200">
              <FilterPanel onApplyFilters={handleFiltersChange} />
            </div>
          )}
        </div>

        {/* Indicador de última actualización */}
        {lastUpdated && (
          <div className="mb-4 text-right text-sm text-gray-500">
            Última actualización: {lastUpdated.toLocaleString()}
          </div>
        )}

        {/* Contenido principal */}
        {loading ? (
          <div className="py-16 text-center">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-500 mx-auto mb-4"></div>
            <p className="text-gray-500">Cargando dashboard...</p>
          </div>
        ) : error ? (
          <div className="py-16 text-center">
            <div className="bg-red-100 text-red-700 p-4 rounded-lg inline-block mb-4">
              <FaExclamationTriangle className="text-2xl inline-block mr-2" />
              {error}
            </div>
            <button
              onClick={fetchDashboardData}
              className="mt-4 px-4 py-2 bg-blue-500 text-white rounded-lg hover:bg-blue-600"
            >
              <FaRedo className="inline-block mr-2" />
              Reintentar
            </button>
          </div>
        ) : (
          renderDashboardContent()
        )}
      </div>
    </div>
  );
};

export default Dashboard;