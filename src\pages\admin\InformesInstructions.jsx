/**
 * @file InformesInstructions.jsx
 * @description Página de instrucciones para administradores sobre las nuevas funcionalidades de informes
 */

import React from 'react';
import { Link } from 'react-router-dom';
import { Card, CardHeader, CardBody } from '../../components/ui/Card';
import PageHeader from '../../components/ui/PageHeader';
import { FaRocket } from 'react-icons/fa';

const InformesInstructions = () => {
  return (
    <div className="min-h-screen bg-gray-50">
      <PageHeader
        title="🚀 Nuevas Funcionalidades de Informes"
        subtitle="Guía completa para administradores sobre la generación manual de informes"
        icon={FaRocket}
      />

      <div className="max-w-6xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        
        {/* Acceso rápido */}
        <Card className="mb-8 border-l-4 border-blue-500">
          <CardHeader className="bg-blue-50">
            <h2 className="text-xl font-semibold text-blue-800">
              🎯 Acceso Rápido a las Nuevas Funcionalidades
            </h2>
          </CardHeader>
          <CardBody>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <Link
                to="/admin/results"
                className="block p-4 bg-blue-50 border-2 border-blue-200 rounded-lg hover:bg-blue-100 transition-colors"
              >
                <div className="text-center">
                  <i className="fas fa-file-medical-alt text-3xl text-blue-600 mb-2"></i>
                  <h3 className="font-semibold text-blue-800">Resultados + Informes</h3>
                  <p className="text-sm text-blue-600">Nueva página con generación de informes</p>
                </div>
              </Link>
              
              <Link
                to="/student/results"
                className="block p-4 bg-green-50 border-2 border-green-200 rounded-lg hover:bg-green-100 transition-colors"
              >
                <div className="text-center">
                  <i className="fas fa-graduation-cap text-3xl text-green-600 mb-2"></i>
                  <h3 className="font-semibold text-green-800">Vista de Estudiantes</h3>
                  <p className="text-sm text-green-600">Acceso directo a resultados de estudiantes</p>
                </div>
              </Link>
              
              <Link
                to="/dashboard"
                className="block p-4 bg-purple-50 border-2 border-purple-200 rounded-lg hover:bg-purple-100 transition-colors"
              >
                <div className="text-center">
                  <i className="fas fa-tachometer-alt text-3xl text-purple-600 mb-2"></i>
                  <h3 className="font-semibold text-purple-800">Dashboard</h3>
                  <p className="text-sm text-purple-600">Panel de control principal</p>
                </div>
              </Link>
            </div>
          </CardBody>
        </Card>

        {/* Instrucciones paso a paso */}
        <Card className="mb-8">
          <CardHeader className="bg-green-50">
            <h2 className="text-xl font-semibold text-green-800">
              📋 Instrucciones Paso a Paso
            </h2>
          </CardHeader>
          <CardBody>
            <div className="space-y-6">
              
              <div className="flex items-start">
                <div className="flex-shrink-0 w-8 h-8 bg-blue-500 text-white rounded-full flex items-center justify-center font-bold mr-4">
                  1
                </div>
                <div>
                  <h3 className="font-semibold text-gray-800 mb-2">Acceder a la Nueva Página de Resultados</h3>
                  <p className="text-gray-600 mb-2">
                    Navega a <code className="bg-gray-100 px-2 py-1 rounded">/admin/results</code> o usa el menú lateral:
                  </p>
                  <ul className="list-disc list-inside text-gray-600 space-y-1 ml-4">
                    <li>Busca "🆕 Resultados + Informes" en el menú lateral</li>
                    <li>O haz clic en el enlace de acceso rápido arriba</li>
                  </ul>
                </div>
              </div>

              <div className="flex items-start">
                <div className="flex-shrink-0 w-8 h-8 bg-green-500 text-white rounded-full flex items-center justify-center font-bold mr-4">
                  2
                </div>
                <div>
                  <h3 className="font-semibold text-gray-800 mb-2">Encontrar la Sección de Informes</h3>
                  <p className="text-gray-600 mb-2">
                    En la página de resultados, busca la sección "Informes Generados" que aparece debajo de cada estudiante:
                  </p>
                  <ul className="list-disc list-inside text-gray-600 space-y-1 ml-4">
                    <li>Cada tarjeta de estudiante tiene su propia sección de informes</li>
                    <li>Verás un botón "Generar Informe" en cada sección</li>
                    <li>Los informes archivados aparecen listados debajo</li>
                  </ul>
                </div>
              </div>

              <div className="flex items-start">
                <div className="flex-shrink-0 w-8 h-8 bg-yellow-500 text-white rounded-full flex items-center justify-center font-bold mr-4">
                  3
                </div>
                <div>
                  <h3 className="font-semibold text-gray-800 mb-2">Generar un Informe Manual</h3>
                  <p className="text-gray-600 mb-2">
                    Para crear un nuevo informe:
                  </p>
                  <ul className="list-disc list-inside text-gray-600 space-y-1 ml-4">
                    <li>Haz clic en "Generar Informe"</li>
                    <li>Selecciona el tipo: "Informe Completo" o "Informe Individual"</li>
                    <li>Si eliges individual, selecciona el test específico</li>
                    <li>Opcionalmente, agrega un título y descripción personalizados</li>
                    <li>Haz clic en "Generar Informe"</li>
                  </ul>
                </div>
              </div>

              <div className="flex items-start">
                <div className="flex-shrink-0 w-8 h-8 bg-purple-500 text-white rounded-full flex items-center justify-center font-bold mr-4">
                  4
                </div>
                <div>
                  <h3 className="font-semibold text-gray-800 mb-2">Visualizar Informes Generados</h3>
                  <p className="text-gray-600 mb-2">
                    Para ver un informe generado:
                  </p>
                  <ul className="list-disc list-inside text-gray-600 space-y-1 ml-4">
                    <li>Busca el informe en la lista "Informes Archivados"</li>
                    <li>Haz clic en el botón azul "👁️" (Ver)</li>
                    <li>Se abrirá un modal con el contenido completo del informe</li>
                    <li>Puedes imprimir el informe desde el modal</li>
                  </ul>
                </div>
              </div>

              <div className="flex items-start">
                <div className="flex-shrink-0 w-8 h-8 bg-red-500 text-white rounded-full flex items-center justify-center font-bold mr-4">
                  5
                </div>
                <div>
                  <h3 className="font-semibold text-gray-800 mb-2">Probar con el Componente de Prueba</h3>
                  <p className="text-gray-600 mb-2">
                    Al final de la página encontrarás un componente de prueba:
                  </p>
                  <ul className="list-disc list-inside text-gray-600 space-y-1 ml-4">
                    <li>Busca "🧪 Prueba de Funcionalidad de Informes"</li>
                    <li>Haz clic en "Generar Informe de Prueba" para crear un informe de Ana Sofia</li>
                    <li>Haz clic en "Obtener Informes" para ver la lista de informes</li>
                    <li>Verifica que todo funciona correctamente</li>
                  </ul>
                </div>
              </div>
            </div>
          </CardBody>
        </Card>

        {/* Características principales */}
        <Card className="mb-8">
          <CardHeader className="bg-yellow-50">
            <h2 className="text-xl font-semibold text-yellow-800">
              ⭐ Características Principales
            </h2>
          </CardHeader>
          <CardBody>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div>
                <h3 className="font-semibold text-gray-800 mb-3">🔄 Generación de Informes</h3>
                <ul className="space-y-2 text-gray-600">
                  <li className="flex items-start">
                    <i className="fas fa-check-circle text-green-500 mr-2 mt-1"></i>
                    <span>Informes completos con todos los resultados del estudiante</span>
                  </li>
                  <li className="flex items-start">
                    <i className="fas fa-check-circle text-green-500 mr-2 mt-1"></i>
                    <span>Informes individuales para tests específicos</span>
                  </li>
                  <li className="flex items-start">
                    <i className="fas fa-check-circle text-green-500 mr-2 mt-1"></i>
                    <span>Títulos y descripciones personalizables</span>
                  </li>
                  <li className="flex items-start">
                    <i className="fas fa-check-circle text-green-500 mr-2 mt-1"></i>
                    <span>Metadatos automáticos y auditoría</span>
                  </li>
                </ul>
              </div>
              
              <div>
                <h3 className="font-semibold text-gray-800 mb-3">📁 Gestión de Informes</h3>
                <ul className="space-y-2 text-gray-600">
                  <li className="flex items-start">
                    <i className="fas fa-check-circle text-green-500 mr-2 mt-1"></i>
                    <span>Lista de informes archivados por estudiante</span>
                  </li>
                  <li className="flex items-start">
                    <i className="fas fa-check-circle text-green-500 mr-2 mt-1"></i>
                    <span>Estados: generado, archivado, eliminado</span>
                  </li>
                  <li className="flex items-start">
                    <i className="fas fa-check-circle text-green-500 mr-2 mt-1"></i>
                    <span>Información del creador y fecha</span>
                  </li>
                  <li className="flex items-start">
                    <i className="fas fa-check-circle text-green-500 mr-2 mt-1"></i>
                    <span>Acciones: ver, archivar, eliminar</span>
                  </li>
                </ul>
              </div>
            </div>
          </CardBody>
        </Card>

        {/* Seguridad */}
        <Card className="mb-8">
          <CardHeader className="bg-red-50">
            <h2 className="text-xl font-semibold text-red-800">
              🔒 Seguridad y Permisos
            </h2>
          </CardHeader>
          <CardBody>
            <div className="space-y-4">
              <div className="flex items-start">
                <i className="fas fa-shield-alt text-red-500 text-xl mr-3 mt-1"></i>
                <div>
                  <h3 className="font-semibold text-gray-800 mb-2">Control de Acceso Basado en Roles</h3>
                  <ul className="space-y-1 text-gray-600">
                    <li><strong>Administradores:</strong> Acceso completo a todos los informes</li>
                    <li><strong>Psicólogos:</strong> Solo informes de sus pacientes asignados</li>
                    <li><strong>Auditoría:</strong> Registro de quién generó cada informe</li>
                  </ul>
                </div>
              </div>
            </div>
          </CardBody>
        </Card>

        {/* Soporte */}
        <Card>
          <CardHeader className="bg-gray-50">
            <h2 className="text-xl font-semibold text-gray-800">
              🆘 Soporte y Resolución de Problemas
            </h2>
          </CardHeader>
          <CardBody>
            <div className="space-y-4">
              <div>
                <h3 className="font-semibold text-gray-800 mb-2">Si no puedes ver las nuevas funcionalidades:</h3>
                <ul className="space-y-1 text-gray-600 ml-4">
                  <li>1. Verifica que estés logueado como administrador</li>
                  <li>2. Navega directamente a <code className="bg-gray-100 px-2 py-1 rounded">/admin/results</code></li>
                  <li>3. Busca "🆕 Resultados + Informes" en el menú lateral</li>
                  <li>4. Actualiza la página (F5) si es necesario</li>
                </ul>
              </div>
              
              <div>
                <h3 className="font-semibold text-gray-800 mb-2">Si hay errores al generar informes:</h3>
                <ul className="space-y-1 text-gray-600 ml-4">
                  <li>1. Verifica que el estudiante tenga resultados de tests</li>
                  <li>2. Revisa la consola del navegador (F12) para errores</li>
                  <li>3. Usa el componente de prueba para verificar la funcionalidad</li>
                </ul>
              </div>
            </div>
          </CardBody>
        </Card>
      </div>
    </div>
  );
};

export default InformesInstructions;
