import React, { useState, useEffect } from 'react';
import { useNoAuth } from '../../context/NoAuthContext';
import { toast } from 'react-toastify';
import {
  FaUsers, FaSearch, FaPlus, FaTrash, FaEdit, FaSpinner
} from 'react-icons/fa';
import supabase from '../../api/supabaseClient';
import PageHeader from '../../components/ui/PageHeader';

/**
 * Página de Gestión de Pacientes - BAT-7
 * Diseño moderno que coincide con el panel de administración
 */
const Patients = () => {
  const { isAdmin, isPsicologo, loading: authLoading } = useNoAuth();
  
  // Estado principal
  const [patients, setPatients] = useState([]);
  const [institutions, setInstitutions] = useState([]);
  const [loading, setLoading] = useState(false);
  const [searchTerm, setSearchTerm] = useState('');

  // Control de acceso
  if (authLoading) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gray-50">
        <div className="flex items-center">
          <FaSpinner className="animate-spin text-blue-500 text-2xl mr-3" />
          <span className="text-gray-600">Cargando...</span>
        </div>
      </div>
    );
  }

  if (!isAdmin && !isPsicologo) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gray-50">
        <div className="text-center p-8 bg-white rounded-lg shadow-lg max-w-md">
          <div className="w-16 h-16 bg-red-100 rounded-full flex items-center justify-center mx-auto mb-4">
            <FaUsers className="text-red-600 text-2xl" />
          </div>
          <h2 className="text-2xl font-bold text-red-600 mb-4">Acceso Denegado</h2>
          <p className="text-gray-600 mb-6">
            Solo los administradores y psicólogos pueden gestionar pacientes.
          </p>
          <button 
            onClick={() => window.history.back()}
            className="px-4 py-2 bg-gray-500 text-white rounded-lg hover:bg-gray-600 transition-colors"
          >
            Volver
          </button>
        </div>
      </div>
    );
  }

  // Cargar instituciones
  const loadInstitutions = async () => {
    try {
      const { data, error } = await supabase
        .from('instituciones')
        .select('*')
        .order('nombre', { ascending: true });

      if (error) throw error;
      setInstitutions(data || []);

      console.log('✅ Instituciones cargadas:', data?.length || 0);

    } catch (error) {
      console.error('❌ Error al cargar instituciones:', error);
      toast.error('Error al cargar las instituciones');
    }
  };

  // Cargar pacientes
  const loadPatients = async () => {
    setLoading(true);
    try {
      const { data, error } = await supabase
        .from('pacientes')
        .select('*')
        .order('created_at', { ascending: false });

      if (error) throw error;
      setPatients(data || []);
      
      // Log de información de conexión
      console.log('✅ Pacientes cargados:', data?.length || 0);
      
    } catch (error) {
      console.error('❌ Error al cargar pacientes:', error);
      toast.error('Error al cargar los pacientes');
    } finally {
      setLoading(false);
    }
  };

  // Cargar datos al montar el componente
  useEffect(() => {
    loadInstitutions();
    loadPatients();
    
    // Verificar conexión con Supabase
    const testConnection = async () => {
      try {
        console.log('🔍 Verificando conexión con Supabase...');
        
        // Contar pacientes
        const { count: patientsCount } = await supabase
          .from('pacientes')
          .select('*', { count: 'exact', head: true });
        
        // Contar resultados
        const { count: resultsCount } = await supabase
          .from('resultados')
          .select('*', { count: 'exact', head: true });
        
        console.log(`�  Total de pacientes: ${patientsCount || 0}`);
        console.log(`📋 Total de resultados: ${resultsCount || 0}`);
        
      } catch (error) {
        console.error('💥 Error de conexión:', error);
      }
    };
    
    testConnection();
  }, []);

  // Filtrar pacientes
  const filteredPatients = patients.filter(patient => {
    if (!searchTerm) return true;
    
    const searchLower = searchTerm.toLowerCase();
    return (
      patient.nombre?.toLowerCase().includes(searchLower) ||
      patient.apellido?.toLowerCase().includes(searchLower) ||
      patient.documento?.toLowerCase().includes(searchLower) ||
      patient.email?.toLowerCase().includes(searchLower)
    );
  });

  // Estados para el modal de creación
  const [showCreateModal, setShowCreateModal] = useState(false);
  const [newPatient, setNewPatient] = useState({
    nombre: '',
    apellido: '',
    email: '',
    telefono: '',
    fecha_nacimiento: '',
    genero: '',
    nivel_educativo: '',
    institucion_id: ''
  });

  // Función para obtener el ID de la primera institución disponible
  const getDefaultInstitutionId = () => {
    return institutions.length > 0 ? institutions[0].id : '';
  };

  // Funciones CRUD
  const handleCreate = () => {
    setShowCreateModal(true);
  };

  const handleSaveNewPatient = async () => {
    try {
      setLoading(true);

      console.log('🔍 Datos del nuevo paciente:', newPatient);

      // Validaciones básicas
      if (!newPatient.nombre || !newPatient.apellido || !newPatient.email) {
        toast.error('Por favor complete los campos obligatorios');
        return;
      }

      // Asegurar que hay una institución seleccionada
      const institutionId = newPatient.institucion_id || getDefaultInstitutionId();
      console.log('🏢 Institution ID seleccionado:', institutionId);

      if (!institutionId) {
        toast.error('Debe seleccionar una institución');
        return;
      }

      // Preparar datos para insertar
      const dataToInsert = {
        ...newPatient,
        institucion_id: institutionId,
        created_at: new Date().toISOString()
      };

      console.log('📝 Datos a insertar:', dataToInsert);

      const { data, error } = await supabase
        .from('pacientes')
        .insert([dataToInsert])
        .select();

      if (error) {
        console.error('❌ Error de Supabase:', error);
        throw error;
      }

      console.log('✅ Paciente creado exitosamente:', data);
      toast.success('Paciente creado correctamente');
      setShowCreateModal(false);
      setNewPatient({
        nombre: '',
        apellido: '',
        email: '',
        telefono: '',
        fecha_nacimiento: '',
        genero: '',
        nivel_educativo: '',
        institucion_id: ''
      });
      loadPatients();
    } catch (error) {
      console.error('💥 Error creating patient:', error);
      toast.error(`Error al crear el paciente: ${error.message}`);
    } finally {
      setLoading(false);
    }
  };

  const handleEdit = (patient) => {
    toast.info(`Editar paciente: ${patient.nombre} ${patient.apellido}`);
  };

  const handleDelete = async (patient) => {
    if (!window.confirm(`¿Está seguro de eliminar al paciente ${patient.nombre} ${patient.apellido}?`)) {
      return;
    }

    try {
      setLoading(true);
      const { error } = await supabase
        .from('pacientes')
        .delete()
        .eq('id', patient.id);

      if (error) throw error;
      
      toast.success('Paciente eliminado correctamente');
      loadPatients();
    } catch (error) {
      console.error('Error deleting patient:', error);
      toast.error('Error al eliminar el paciente');
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header Section with Standardized Style */}
      <PageHeader
        title="Gestión de Pacientes"
        subtitle="Administra la información y el historial de tus pacientes registrados en la plataforma"
        icon={FaUsers}
      />



      {/* Main Content */}
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6">
        {/* Section Header */}
        <div className="mb-6">
          <div className="flex items-center justify-between">
            <div>
              <h2 className="text-2xl font-bold text-gray-900">Gestión de Pacientes</h2>
              <p className="text-gray-600 mt-1">
                Administre los pacientes registrados en el sistema ({filteredPatients.length} pacientes)
              </p>
            </div>
            <div className="flex items-center space-x-3">
              {/* Search */}
              <div className="relative">
                <FaSearch className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" />
                <input
                  type="text"
                  placeholder="Buscar paciente..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                />
              </div>
              
              {/* Add New Button */}
              {isAdmin && (
                <button
                  onClick={handleCreate}
                  className="flex items-center px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
                >
                  <FaPlus className="mr-2" />
                  Nuevo Paciente
                </button>
              )}
            </div>
          </div>
        </div>

        {/* Data Table */}
        <div className="bg-white shadow-sm rounded-lg overflow-hidden">
          <div className="overflow-x-auto">
            <table className="min-w-full divide-y divide-gray-200">
              <thead className="bg-blue-600 text-white">
                <tr>
                  <th className="px-6 py-3 text-left text-xs font-medium uppercase tracking-wider">
                    Nombre Completo
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium uppercase tracking-wider">
                    Email
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium uppercase tracking-wider">
                    Documento
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium uppercase tracking-wider">
                    Género
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium uppercase tracking-wider">
                    Fecha de Nacimiento
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium uppercase tracking-wider">
                    Nivel Educativo
                  </th>
                  {isAdmin && (
                    <th className="px-6 py-3 text-left text-xs font-medium uppercase tracking-wider">
                      Acciones
                    </th>
                  )}
                </tr>
              </thead>
              
              <tbody className="bg-white divide-y divide-gray-200">
                {loading ? (
                  <tr>
                    <td colSpan={isAdmin ? 7 : 6} className="px-6 py-12 text-center">
                      <div className="flex items-center justify-center">
                        <FaSpinner className="animate-spin text-blue-500 text-2xl mr-3" />
                        <span className="text-gray-600">Cargando pacientes...</span>
                      </div>
                    </td>
                  </tr>
                ) : filteredPatients.length === 0 ? (
                  <tr>
                    <td colSpan={isAdmin ? 7 : 6} className="px-6 py-12 text-center text-gray-500">
                      {patients.length === 0 ? 'No hay pacientes registrados' : 'No se encontraron pacientes que coincidan con la búsqueda'}
                    </td>
                  </tr>
                ) : (
                  filteredPatients.map((patient) => (
                    <tr key={patient.id} className="hover:bg-gray-50">
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="flex items-center">
                          <div className={`w-8 h-8 rounded-full flex items-center justify-center mr-3 ${
                            patient.genero === 'femenino' ? 'bg-pink-500' :
                            patient.genero === 'masculino' ? 'bg-blue-500' :
                            'bg-gray-500'
                          }`}>
                            <span className="text-white text-sm font-medium">
                              {patient.nombre?.charAt(0)?.toUpperCase()}
                            </span>
                          </div>
                          <div>
                            <div className="text-sm font-medium text-gray-900">
                              {patient.nombre} {patient.apellido}
                            </div>
                            <div className="text-sm text-gray-500">
                              {patient.telefono && `Tel: ${patient.telefono}`}
                            </div>
                          </div>
                        </div>
                      </td>
                      
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                        {patient.email || '-'}
                      </td>
                      
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                        <span className="px-2 py-1 text-xs font-medium bg-blue-100 text-blue-800 rounded-full">
                          {patient.documento || '-'}
                        </span>
                      </td>
                      
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                        <span className={`px-2 py-1 text-xs font-medium rounded-full ${
                          patient.genero === 'masculino' ? 'bg-blue-100 text-blue-800' :
                          patient.genero === 'femenino' ? 'bg-pink-100 text-pink-800' :
                          'bg-gray-100 text-gray-800'
                        }`}>
                          {patient.genero ? patient.genero.charAt(0).toUpperCase() + patient.genero.slice(1) : '-'}
                        </span>
                      </td>
                      
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                        {patient.fecha_nacimiento ? 
                          new Date(patient.fecha_nacimiento).toLocaleDateString('es-ES') : 
                          '-'
                        }
                      </td>
                      
                      <td className="px-6 py-4 text-sm text-gray-900 max-w-xs truncate">
                        {patient.nivel_educativo || '-'}
                      </td>
                      
                      {isAdmin && (
                        <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                          <div className="flex space-x-2">
                            <button
                              onClick={() => handleEdit(patient)}
                              className="text-blue-600 hover:text-blue-900 transition-colors"
                              title="Editar paciente"
                            >
                              <FaEdit />
                            </button>
                            <button
                              onClick={() => handleDelete(patient)}
                              className="text-red-600 hover:text-red-900 transition-colors"
                              title="Eliminar paciente"
                            >
                              <FaTrash />
                            </button>
                          </div>
                        </td>
                      )}
                    </tr>
                  ))
                )}
              </tbody>
            </table>
          </div>
        </div>

        {/* Footer */}
        <div className="mt-8 text-center text-sm text-gray-500">
          © 2025 Sistema de Gestión Psicológica - Panel de Administración
        </div>
      </div>

      {/* Modal de creación de paciente */}
      {showCreateModal && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg p-6 w-full max-w-md mx-4">
            <h3 className="text-lg font-semibold text-gray-800 mb-4">Crear Nuevo Paciente</h3>

            <div className="space-y-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">Nombre *</label>
                <input
                  type="text"
                  value={newPatient.nombre}
                  onChange={(e) => setNewPatient({...newPatient, nombre: e.target.value})}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                  placeholder="Nombre del paciente"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">Apellido *</label>
                <input
                  type="text"
                  value={newPatient.apellido}
                  onChange={(e) => setNewPatient({...newPatient, apellido: e.target.value})}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                  placeholder="Apellido del paciente"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">Email *</label>
                <input
                  type="email"
                  value={newPatient.email}
                  onChange={(e) => setNewPatient({...newPatient, email: e.target.value})}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                  placeholder="<EMAIL>"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">Teléfono</label>
                <input
                  type="tel"
                  value={newPatient.telefono}
                  onChange={(e) => setNewPatient({...newPatient, telefono: e.target.value})}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                  placeholder="Número de teléfono"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">Fecha de Nacimiento</label>
                <input
                  type="date"
                  value={newPatient.fecha_nacimiento}
                  onChange={(e) => setNewPatient({...newPatient, fecha_nacimiento: e.target.value})}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">Género</label>
                <select
                  value={newPatient.genero}
                  onChange={(e) => setNewPatient({...newPatient, genero: e.target.value})}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                >
                  <option value="">Seleccionar género</option>
                  <option value="masculino">Masculino</option>
                  <option value="femenino">Femenino</option>
                  <option value="otro">Otro</option>
                </select>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">Nivel Educativo</label>
                <select
                  value={newPatient.nivel_educativo}
                  onChange={(e) => setNewPatient({...newPatient, nivel_educativo: e.target.value})}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                >
                  <option value="">Seleccionar nivel</option>
                  <option value="E">Elemental</option>
                  <option value="M">Medio</option>
                  <option value="S">Superior</option>
                </select>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">Institución *</label>
                <select
                  value={newPatient.institucion_id}
                  onChange={(e) => setNewPatient({...newPatient, institucion_id: e.target.value})}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                >
                  <option value="">Seleccionar institución</option>
                  {institutions.map((institution) => (
                    <option key={institution.id} value={institution.id}>
                      {institution.nombre}
                    </option>
                  ))}
                </select>
              </div>
            </div>

            <div className="flex justify-end space-x-3 mt-6">
              <button
                onClick={() => setShowCreateModal(false)}
                className="px-4 py-2 text-gray-600 border border-gray-300 rounded-lg hover:bg-gray-50 transition-colors"
              >
                Cancelar
              </button>
              <button
                onClick={handleSaveNewPatient}
                disabled={loading}
                className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors disabled:opacity-50"
              >
                {loading ? 'Guardando...' : 'Crear Paciente'}
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default Patients;
