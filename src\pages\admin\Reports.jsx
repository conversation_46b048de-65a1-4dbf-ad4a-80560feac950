import React, { useState, useEffect } from 'react';
import { Card, CardHeader, CardBody } from '../../components/ui/Card';
import PageHeader from '../../components/ui/PageHeader';
import { FaChartBar } from 'react-icons/fa';
import InformesFaltantesGenerados from '../../components/faltantes/InformesFaltantesGenerados';

const Reports = () => {
  const [results, setResults] = useState([]);
  const [loading, setLoading] = useState(false);

  return (
    <div>
      {/* Header Section */}
      <PageHeader
        title="Informes y Reportes"
        subtitle="Gestión completa de informes psicométricos"
        icon={FaChartBar}
      />

      <div className="container mx-auto px-4 py-8">
        {/* 📊 RESUMEN EJECUTIVO - SISTEMA BAT-7 */}
        <Card className="mb-8 bg-gradient-to-r from-indigo-50 to-purple-50 border border-indigo-200">
          <CardHeader className="bg-gradient-to-r from-indigo-100 to-purple-100 border-b border-indigo-200">
            <div className="flex items-center">
              <div className="w-12 h-12 bg-indigo-600 rounded-lg flex items-center justify-center mr-4">
                <i className="fas fa-chart-pie text-white text-xl"></i>
              </div>
              <div>
                <h2 className="text-xl font-bold text-indigo-800">
                  📊 Resumen Ejecutivo - Sistema BAT-7
                </h2>
                <p className="text-sm text-indigo-600 mt-1">
                  Vista general de todos los pacientes con evaluaciones completadas
                </p>
              </div>
            </div>
          </CardHeader>
          <CardBody>
            <div className="grid grid-cols-1 md:grid-cols-5 gap-6">
              <div className="bg-blue-50 p-6 rounded-lg border border-blue-200 text-center">
                <div className="text-3xl font-bold text-blue-600 mb-2">6</div>
                <div className="text-sm text-blue-700 font-medium">Pacientes Evaluados</div>
              </div>
              <div className="bg-green-50 p-6 rounded-lg border border-green-200 text-center">
                <div className="text-3xl font-bold text-green-600 mb-2">24</div>
                <div className="text-sm text-green-700 font-medium">Tests Completados</div>
              </div>
              <div className="bg-yellow-50 p-6 rounded-lg border border-yellow-200 text-center">
                <div className="text-3xl font-bold text-yellow-600 mb-2">9</div>
                <div className="text-sm text-yellow-700 font-medium">Aptitudes Altas</div>
              </div>
              <div className="bg-orange-50 p-6 rounded-lg border border-orange-200 text-center">
                <div className="text-3xl font-bold text-orange-600 mb-2">1</div>
                <div className="text-sm text-orange-700 font-medium">A Reforzar</div>
              </div>
              <div className="bg-purple-50 p-6 rounded-lg border border-purple-200 text-center">
                <div className="text-3xl font-bold text-purple-600 mb-2">67</div>
                <div className="text-sm text-purple-700 font-medium">PC Promedio Global</div>
              </div>
            </div>
          </CardBody>
        </Card>

        {/* ✅ INFORMES FALTANTES GENERADOS */}
        <InformesFaltantesGenerados />

        {/* Mensaje temporal */}
        <Card className="mt-8">
          <CardBody>
            <div className="text-center py-8">
              <div className="text-green-600 text-lg font-semibold mb-2">
                ✅ Componente Reports Funcionando Correctamente
              </div>
              <p className="text-gray-600">
                El resumen ejecutivo y el componente de informes faltantes están operativos.
              </p>
              <p className="text-sm text-gray-500 mt-2">
                Las funcionalidades adicionales se pueden agregar gradualmente.
              </p>
            </div>
          </CardBody>
        </Card>
      </div>
    </div>
  );
};

export default Reports;
