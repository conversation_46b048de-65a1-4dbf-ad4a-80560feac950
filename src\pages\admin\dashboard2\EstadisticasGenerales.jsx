import React from 'react';
import { Card, CardBody } from '../../../components/ui/Card';
import { FaArrowUp, FaArrowDown, FaMinus } from 'react-icons/fa';

/**
 * Widget de estadísticas generales del dashboard
 * Muestra métricas clave en formato de tarjetas con indicadores de tendencia
 */
const EstadisticasGenerales = ({ data, loading, previousData }) => {
  const estadisticas = [
    {
      titulo: 'Total Pacientes',
      valor: data?.total_pacientes || 0,
      icono: 'fas fa-users',
      color: 'blue',
      descripcion: 'Pacientes registrados'
    },
    {
      titulo: 'Pacientes Evaluados',
      valor: data?.pacientes_evaluados || 0,
      icono: 'fas fa-user-check',
      color: 'green',
      descripcion: 'Con al menos 1 test'
    },
    {
      titulo: 'Total Evaluaciones',
      valor: data?.total_evaluaciones || 0,
      icono: 'fas fa-clipboard-list',
      color: 'purple',
      descripcion: 'Tests completados'
    },
    {
      titulo: 'Percentil Promedio',
      valor: data?.percentil_promedio_general || 0,
      icono: 'fas fa-chart-line',
      color: 'orange',
      descripcion: 'Rendimiento general',
      formato: 'decimal'
    },
    {
      titulo: 'Evaluaciones (30 días)',
      valor: data?.evaluaciones_ultimo_mes || 0,
      icono: 'fas fa-calendar-alt',
      color: 'indigo',
      descripcion: 'Último mes'
    },
    {
      titulo: 'Evaluaciones (7 días)',
      valor: data?.evaluaciones_ultima_semana || 0,
      icono: 'fas fa-clock',
      color: 'pink',
      descripcion: 'Última semana'
    }
  ];

  const getColorClasses = (color) => {
    const colors = {
      blue: 'bg-blue-500 text-blue-100',
      green: 'bg-green-500 text-green-100',
      purple: 'bg-purple-500 text-purple-100',
      orange: 'bg-orange-500 text-orange-100',
      indigo: 'bg-indigo-500 text-indigo-100',
      pink: 'bg-pink-500 text-pink-100'
    };
    return colors[color] || colors.blue;
  };

  const formatValue = (value, formato) => {
    if (loading) return '...';
    if (formato === 'decimal') {
      return typeof value === 'number' ? value.toFixed(1) : '0.0';
    }
    return typeof value === 'number' ? value.toLocaleString() : '0';
  };

  // Calcular tendencias comparando con datos anteriores
  const getTrend = (current, previous, key) => {
    if (!previousData || !current || !previous) return null;

    const currentVal = current[key] || 0;
    const previousVal = previous[key] || 0;

    if (currentVal > previousVal) return 'up';
    if (currentVal < previousVal) return 'down';
    return 'same';
  };

  const getTrendIcon = (trend) => {
    switch (trend) {
      case 'up': return <FaArrowUp className="text-green-500" />;
      case 'down': return <FaArrowDown className="text-red-500" />;
      case 'same': return <FaMinus className="text-gray-400" />;
      default: return null;
    }
  };

  if (loading) {
    return (
      <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-6 gap-4 mb-6">
        {Array.from({ length: 6 }).map((_, index) => (
          <Card key={index} className="animate-pulse">
            <CardBody className="p-4">
              <div className="flex items-center">
                <div className="w-12 h-12 bg-gray-200 rounded-lg mr-3"></div>
                <div className="flex-1">
                  <div className="h-4 bg-gray-200 rounded mb-2"></div>
                  <div className="h-6 bg-gray-200 rounded"></div>
                </div>
              </div>
            </CardBody>
          </Card>
        ))}
      </div>
    );
  }

  return (
    <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-6 gap-4 mb-6">
      {estadisticas.map((stat, index) => {
        const trend = getTrend(data, previousData, stat.key || stat.titulo.toLowerCase().replace(/\s+/g, '_'));
        return (
          <Card key={index} className="shadow-lg">
            <CardBody className="p-4">
              <div className="flex items-center">
                <div className={`w-12 h-12 rounded-lg flex items-center justify-center mr-3 ${getColorClasses(stat.color)} shadow-md`}>
                  <i className={`${stat.icono} text-lg`}></i>
                </div>
                <div className="flex-1 min-w-0">
                  <div className="flex items-center justify-between">
                    <p className="text-sm font-medium text-gray-600 truncate">
                      {stat.titulo}
                    </p>
                    {trend && (
                      <div className="ml-2" title={`Tendencia: ${trend === 'up' ? 'Aumentó' : trend === 'down' ? 'Disminuyó' : 'Sin cambios'}`}>
                        {getTrendIcon(trend)}
                      </div>
                    )}
                  </div>
                  <p className="text-2xl font-bold text-gray-900">
                    {formatValue(stat.valor, stat.formato)}
                  </p>
                  <p className="text-xs text-gray-500 truncate">
                    {stat.descripcion}
                  </p>
                </div>
              </div>
            </CardBody>
          </Card>
        );
      })}
    </div>
  );
};

export default EstadisticasGenerales;
