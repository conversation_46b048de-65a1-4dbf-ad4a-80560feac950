import React, { useState, useEffect, useCallback } from 'react';
import { Card, CardHeader, CardBody } from '../../../components/ui/Card';
import supabase from '../../../api/supabaseClient';
import { toast } from 'react-toastify';
import { FaRedo, FaExclamationTriangle } from 'react-icons/fa';

/**
 * Widget: Distribución de Estudiantes por Nivel
 * Gráfico de torta mostrando la distribución de estudiantes por nivel educativo
 */
const EstudiantesPorNivel = () => {
  const [data, setData] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [animationKey, setAnimationKey] = useState(0);

  const fetchData = useCallback(async () => {
    try {
      setLoading(true);
      setError(null);

      console.log('📊 [EstudiantesPorNivel] Cargando datos...');

      const { data: result, error } = await supabase
        .from('dashboard_estudiantes_por_nivel')
        .select('*');

      if (error) {
        console.error('❌ [EstudiantesPorNivel] Error:', error);
        setError('Error al cargar la distribución por nivel');
        toast.error('Error al cargar la distribución por nivel');
        return;
      }

      console.log('✅ [EstudiantesPorNivel] Datos cargados:', result);
      setData(result || []);
      setAnimationKey(prev => prev + 1); // Trigger re-animation

    } catch (error) {
      console.error('💥 [EstudiantesPorNivel] Error general:', error);
      setError('Error al conectar con la base de datos');
      toast.error('Error al conectar con la base de datos');
    } finally {
      setLoading(false);
    }
  }, []);

  useEffect(() => {
    fetchData();

    // Configurar sincronización en tiempo real
    const subscription = supabase
      .channel('estudiantes-nivel-changes')
      .on(
        'postgres_changes',
        {
          event: '*',
          schema: 'public',
          table: 'pacientes',
        },
        (payload) => {
          console.log('🔄 [EstudiantesPorNivel] Cambio detectado:', payload);
          setTimeout(() => {
            fetchData();
          }, 1000);
        }
      )
      .subscribe();

    return () => {
      subscription.unsubscribe();
    };
  }, [fetchData]);

  // Colores para cada nivel
  const getColorForNivel = (nivel) => {
    const colors = {
      'E': { bg: 'bg-blue-500', text: 'text-blue-700', border: 'border-blue-500' },
      'M': { bg: 'bg-green-500', text: 'text-green-700', border: 'border-green-500' },
      'S': { bg: 'bg-purple-500', text: 'text-purple-700', border: 'border-purple-500' }
    };
    return colors[nivel] || { bg: 'bg-gray-500', text: 'text-gray-700', border: 'border-gray-500' };
  };

  // Crear gráfico de torta animado con CSS
  const createPieChart = () => {
    if (!data.length) return null;

    let cumulativePercentage = 0;
    const segments = data.map((item, index) => {
      const startAngle = cumulativePercentage * 3.6; // Convertir porcentaje a grados
      const endAngle = (cumulativePercentage + item.porcentaje) * 3.6;
      cumulativePercentage += item.porcentaje;

      const colors = getColorForNivel(item.nivel);

      return {
        ...item,
        startAngle,
        endAngle,
        colors
      };
    });

    return (
      <div className="relative w-48 h-48 mx-auto">
        <svg
          viewBox="0 0 200 200"
          className="w-full h-full transform -rotate-90"
          key={animationKey} // Force re-render for animation
        >
          {segments.map((segment, index) => {
            const radius = 80;
            const centerX = 100;
            const centerY = 100;

            const startAngleRad = (segment.startAngle * Math.PI) / 180;
            const endAngleRad = (segment.endAngle * Math.PI) / 180;

            const x1 = centerX + radius * Math.cos(startAngleRad);
            const y1 = centerY + radius * Math.sin(startAngleRad);
            const x2 = centerX + radius * Math.cos(endAngleRad);
            const y2 = centerY + radius * Math.sin(endAngleRad);

            const largeArcFlag = segment.porcentaje > 50 ? 1 : 0;

            const pathData = [
              `M ${centerX} ${centerY}`,
              `L ${x1} ${y1}`,
              `A ${radius} ${radius} 0 ${largeArcFlag} 1 ${x2} ${y2}`,
              'Z'
            ].join(' ');

            const strokeColors = {
              'E': '#3b82f6',
              'M': '#10b981',
              'S': '#8b5cf6'
            };

            return (
              <path
                key={`${index}-${animationKey}`}
                d={pathData}
                fill={strokeColors[segment.nivel] || '#6b7280'}
                stroke="white"
                strokeWidth="2"
                className="hover:opacity-80 transition-all duration-300 hover:scale-105 cursor-pointer"
                style={{
                  animation: `fadeInScale 0.6s ease-out ${index * 0.1}s both`,
                  transformOrigin: `${centerX}px ${centerY}px`
                }}
                title={`${segment.nivel_nombre}: ${segment.total_estudiantes} estudiantes (${segment.porcentaje}%)`}
              />
            );
          })}
        </svg>

        {/* Centro del gráfico */}
        <div className="absolute inset-0 flex items-center justify-center">
          <div className="bg-white rounded-full w-16 h-16 flex items-center justify-center shadow-lg border-2 border-gray-100">
            <div className="text-center">
              <div className="text-lg font-bold text-gray-800 animate-pulse">
                {data.reduce((sum, item) => sum + item.total_estudiantes, 0)}
              </div>
              <div className="text-xs text-gray-600">Total</div>
            </div>
          </div>
        </div>
      </div>
    );
  };

  if (loading) {
    return (
      <Card>
        <CardHeader className="bg-gradient-to-r from-blue-500 to-blue-600 text-white">
          <h3 className="text-lg font-semibold">
            <i className="fas fa-chart-pie mr-2 animate-spin"></i>
            Distribución por Nivel Educativo
          </h3>
        </CardHeader>
        <CardBody>
          <div className="animate-pulse">
            <div className="w-48 h-48 bg-gradient-to-r from-blue-200 to-purple-200 rounded-full mx-auto mb-4 animate-pulse"></div>
            <div className="space-y-3">
              {Array.from({ length: 3 }).map((_, i) => (
                <div key={i} className="flex items-center animate-pulse" style={{ animationDelay: `${i * 0.1}s` }}>
                  <div className="w-4 h-4 bg-gray-300 rounded mr-3"></div>
                  <div className="h-4 bg-gray-300 rounded flex-1"></div>
                  <div className="w-12 h-4 bg-gray-300 rounded ml-2"></div>
                </div>
              ))}
            </div>
          </div>
        </CardBody>
      </Card>
    );
  }

  if (error) {
    return (
      <Card>
        <CardHeader className="bg-gradient-to-r from-red-500 to-red-600 text-white">
          <h3 className="text-lg font-semibold">
            <i className="fas fa-chart-pie mr-2"></i>
            Distribución por Nivel Educativo
          </h3>
        </CardHeader>
        <CardBody>
          <div className="text-center py-8">
            <FaExclamationTriangle className="text-red-500 text-4xl mx-auto mb-4" />
            <p className="text-gray-600 mb-4">{error}</p>
            <button
              onClick={fetchData}
              className="bg-blue-500 hover:bg-blue-600 text-white px-4 py-2 rounded-lg font-medium transition-colors duration-200 flex items-center mx-auto"
            >
              <FaRedo className="mr-2" />
              Reintentar
            </button>
          </div>
        </CardBody>
      </Card>
    );
  }

  return (
    <Card>
      <CardHeader className="bg-gradient-to-r from-blue-500 to-blue-600 text-white">
        <h3 className="text-lg font-semibold">
          <i className="fas fa-chart-pie mr-2"></i>
          Distribución por Nivel Educativo
        </h3>
      </CardHeader>
      <CardBody>
        {data.length > 0 ? (
          <>
            {/* Gráfico de torta */}
            <div className="mb-6">
              {createPieChart()}
            </div>

            {/* Leyenda */}
            <div className="space-y-3">
              {data.map((item, index) => {
                const colors = getColorForNivel(item.nivel);
                return (
                  <div key={index} className="flex items-center justify-between">
                    <div className="flex items-center">
                      <div className={`w-4 h-4 rounded mr-3 ${colors.bg}`}></div>
                      <span className="font-medium text-gray-700">
                        {item.nivel_nombre}
                      </span>
                    </div>
                    <div className="text-right">
                      <div className="font-bold text-gray-900">
                        {item.total_estudiantes}
                      </div>
                      <div className="text-sm text-gray-500">
                        {item.porcentaje}%
                      </div>
                    </div>
                  </div>
                );
              })}
            </div>

            {/* Interpretación */}
            <div className="mt-6 p-4 bg-blue-50 rounded-lg">
              <h4 className="font-medium text-blue-800 mb-2">
                <i className="fas fa-lightbulb mr-2"></i>
                Interpretación
              </h4>
              <p className="text-sm text-blue-700">
                Esta distribución muestra la composición de la población evaluada. 
                Una distribución equilibrada indica una cobertura adecuada en todos los niveles educativos.
              </p>
            </div>
          </>
        ) : (
          <div className="text-center py-8 text-gray-500">
            <i className="fas fa-chart-pie text-4xl mb-4"></i>
            <p>No hay datos disponibles</p>
            <p className="text-sm mt-2">Asegúrese de que los pacientes tengan nivel asignado</p>
          </div>
        )}
      </CardBody>
    </Card>
  );
};

export default EstudiantesPorNivel;
