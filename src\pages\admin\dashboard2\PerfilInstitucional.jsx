import React, { useState, useEffect, useCallback } from 'react';
import { Card, CardHeader, CardBody } from '../../../components/ui/Card';
import supabase from '../../../api/supabaseClient';
import { toast } from 'react-toastify';
import { FaRedo, FaExclamationTriangle, FaInfoCircle } from 'react-icons/fa';

/**
 * Widget: Perfil de Aptitud Institucional
 * Gráfico de radar mostrando el percentil promedio para cada aptitud
 */
const PerfilInstitucional = () => {
  const [data, setData] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [hoveredPoint, setHoveredPoint] = useState(null);

  const fetchData = useCallback(async () => {
    try {
      setLoading(true);
      setError(null);

      console.log('📊 [PerfilInstitucional] Cargando datos...');

      const { data: result, error } = await supabase
        .from('dashboard_perfil_institucional')
        .select('*')
        .order('codigo');

      if (error) {
        console.error('❌ [PerfilInstitucional] Error:', error);
        setError('Error al cargar el perfil institucional');
        toast.error('Error al cargar el perfil institucional');
        return;
      }

      console.log('✅ [PerfilInstitucional] Datos cargados:', result);
      setData(result || []);

    } catch (error) {
      console.error('💥 [PerfilInstitucional] Error general:', error);
      setError('Error al conectar con la base de datos');
      toast.error('Error al conectar con la base de datos');
    } finally {
      setLoading(false);
    }
  }, []);

  useEffect(() => {
    fetchData();

    // Configurar sincronización en tiempo real
    const subscription = supabase
      .channel('perfil-institucional-changes')
      .on(
        'postgres_changes',
        {
          event: '*',
          schema: 'public',
          table: 'resultados',
        },
        (payload) => {
          console.log('🔄 [PerfilInstitucional] Cambio detectado:', payload);
          setTimeout(() => {
            fetchData();
          }, 1000);
        }
      )
      .subscribe();

    return () => {
      subscription.unsubscribe();
    };
  }, [fetchData]);

  // Crear gráfico de radar con SVG
  const createRadarChart = () => {
    if (!data.length) return null;

    const size = 200;
    const center = size / 2;
    const maxRadius = 80;
    const levels = 5; // 5 niveles (0, 25, 50, 75, 100)

    // Calcular puntos para cada aptitud
    const angleStep = (2 * Math.PI) / data.length;
    const points = data.map((item, index) => {
      const angle = index * angleStep - Math.PI / 2; // Empezar desde arriba
      const value = item.percentil_promedio || 0;
      const radius = (value / 100) * maxRadius;
      
      return {
        x: center + radius * Math.cos(angle),
        y: center + radius * Math.sin(angle),
        labelX: center + (maxRadius + 20) * Math.cos(angle),
        labelY: center + (maxRadius + 20) * Math.sin(angle),
        value: value,
        codigo: item.codigo,
        aptitud: item.aptitud_nombre
      };
    });

    // Crear líneas de nivel (círculos concéntricos)
    const levelLines = Array.from({ length: levels }, (_, i) => {
      const radius = ((i + 1) * maxRadius) / levels;
      const value = ((i + 1) * 100) / levels;
      return { radius, value };
    });

    // Crear líneas de eje
    const axisLines = data.map((_, index) => {
      const angle = index * angleStep - Math.PI / 2;
      return {
        x1: center,
        y1: center,
        x2: center + maxRadius * Math.cos(angle),
        y2: center + maxRadius * Math.sin(angle)
      };
    });

    // Crear path del polígono
    const polygonPath = points.map((point, index) => 
      `${index === 0 ? 'M' : 'L'} ${point.x} ${point.y}`
    ).join(' ') + ' Z';

    return (
      <div className="relative">
        <svg viewBox={`0 0 ${size} ${size}`} className="w-full h-64">
          {/* Líneas de nivel (círculos) */}
          {levelLines.map((level, index) => (
            <circle
              key={index}
              cx={center}
              cy={center}
              r={level.radius}
              fill="none"
              stroke="#e5e7eb"
              strokeWidth="1"
              strokeDasharray={index === levels - 1 ? "none" : "2,2"}
            />
          ))}

          {/* Líneas de eje */}
          {axisLines.map((line, index) => (
            <line
              key={index}
              x1={line.x1}
              y1={line.y1}
              x2={line.x2}
              y2={line.y2}
              stroke="#d1d5db"
              strokeWidth="1"
            />
          ))}

          {/* Polígono de datos */}
          <path
            d={polygonPath}
            fill="rgba(59, 130, 246, 0.2)"
            stroke="#3b82f6"
            strokeWidth="2"
          />

          {/* Puntos de datos */}
          {points.map((point, index) => (
            <circle
              key={index}
              cx={point.x}
              cy={point.y}
              r={hoveredPoint === index ? "6" : "4"}
              fill="#3b82f6"
              stroke="white"
              strokeWidth="2"
              className="transition-all duration-200 cursor-pointer hover:fill-blue-700"
              onMouseEnter={() => setHoveredPoint(index)}
              onMouseLeave={() => setHoveredPoint(null)}
              aria-label={`${point.codigo}: ${point.value.toFixed(1)} percentil`}
            />
          ))}

          {/* Tooltip para punto hover */}
          {hoveredPoint !== null && (
            <g>
              <rect
                x={points[hoveredPoint].x - 30}
                y={points[hoveredPoint].y - 35}
                width="60"
                height="25"
                fill="rgba(0, 0, 0, 0.8)"
                rx="4"
                className="animate-fadeInScale"
              />
              <text
                x={points[hoveredPoint].x}
                y={points[hoveredPoint].y - 20}
                textAnchor="middle"
                className="text-xs fill-white font-medium"
              >
                {points[hoveredPoint].codigo}: {points[hoveredPoint].value.toFixed(1)}
              </text>
            </g>
          )}

          {/* Etiquetas de aptitudes */}
          {points.map((point, index) => (
            <text
              key={index}
              x={point.labelX}
              y={point.labelY}
              textAnchor="middle"
              dominantBaseline="middle"
              className="text-sm font-medium fill-gray-700"
            >
              {point.codigo}
            </text>
          ))}

          {/* Etiquetas de valores en los círculos */}
          {levelLines.map((level, index) => (
            <text
              key={index}
              x={center + level.radius + 5}
              y={center}
              textAnchor="start"
              dominantBaseline="middle"
              className="text-xs fill-gray-500"
            >
              {level.value}
            </text>
          ))}
        </svg>
      </div>
    );
  };

  // Determinar fortalezas y debilidades
  const getAnalysis = () => {
    if (!data.length) return null;

    const sortedData = [...data].sort((a, b) => (b.percentil_promedio || 0) - (a.percentil_promedio || 0));
    const fortalezas = sortedData.slice(0, 2);
    const debilidades = sortedData.slice(-2);

    return { fortalezas, debilidades };
  };

  if (loading) {
    return (
      <Card>
        <CardHeader className="bg-gradient-to-r from-green-500 to-green-600 text-white">
          <h3 className="text-lg font-semibold">
            <i className="fas fa-radar-chart mr-2"></i>
            Perfil Institucional
          </h3>
        </CardHeader>
        <CardBody>
          <div className="animate-pulse">
            <div className="w-full h-64 bg-gray-200 rounded mb-4"></div>
            <div className="space-y-2">
              {Array.from({ length: 4 }).map((_, i) => (
                <div key={i} className="h-4 bg-gray-200 rounded"></div>
              ))}
            </div>
          </div>
        </CardBody>
      </Card>
    );
  }

  const analysis = getAnalysis();

  return (
    <Card>
      <CardHeader className="bg-gradient-to-r from-green-500 to-green-600 text-white">
        <h3 className="text-lg font-semibold">
          <i className="fas fa-chart-area mr-2"></i>
          Perfil de Aptitud Institucional
        </h3>
      </CardHeader>
      <CardBody>
        {data.length > 0 ? (
          <>
            {/* Gráfico de radar */}
            <div className="mb-6">
              {createRadarChart()}
            </div>

            {/* Tabla de datos */}
            <div className="mb-6">
              <h4 className="font-medium text-gray-800 mb-3">Percentiles Promedio por Aptitud</h4>
              <div className="grid grid-cols-1 sm:grid-cols-2 gap-2 text-sm">
                {data.map((item, index) => (
                  <div key={index} className="flex justify-between items-center p-2 bg-gray-50 rounded">
                    <span className="font-medium">{item.codigo} - {item.aptitud_nombre}</span>
                    <span className="font-bold text-blue-600">
                      {item.percentil_promedio?.toFixed(1) || '0.0'}
                    </span>
                  </div>
                ))}
              </div>
            </div>

            {/* Análisis de fortalezas y debilidades */}
            {analysis && (
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="p-4 bg-green-50 rounded-lg">
                  <h4 className="font-medium text-green-800 mb-2">
                    <i className="fas fa-arrow-up mr-2"></i>
                    Fortalezas Institucionales
                  </h4>
                  {analysis.fortalezas.map((item, index) => (
                    <div key={index} className="text-sm text-green-700">
                      <strong>{item.codigo}</strong>: {item.percentil_promedio?.toFixed(1)}
                    </div>
                  ))}
                </div>

                <div className="p-4 bg-orange-50 rounded-lg">
                  <h4 className="font-medium text-orange-800 mb-2">
                    <i className="fas fa-arrow-down mr-2"></i>
                    Áreas de Oportunidad
                  </h4>
                  {analysis.debilidades.map((item, index) => (
                    <div key={index} className="text-sm text-orange-700">
                      <strong>{item.codigo}</strong>: {item.percentil_promedio?.toFixed(1)}
                    </div>
                  ))}
                </div>
              </div>
            )}

            {/* Interpretación */}
            <div className="mt-6 p-4 bg-blue-50 rounded-lg">
              <h4 className="font-medium text-blue-800 mb-2">
                <i className="fas fa-lightbulb mr-2"></i>
                Interpretación
              </h4>
              <p className="text-sm text-blue-700">
                Este perfil muestra las fortalezas y debilidades institucionales. 
                Los valores cercanos al centro (percentil bajo) indican áreas que requieren atención, 
                mientras que los valores hacia el exterior representan fortalezas institucionales.
              </p>
            </div>
          </>
        ) : (
          <div className="text-center py-8 text-gray-500">
            <i className="fas fa-chart-area text-4xl mb-4"></i>
            <p>No hay datos disponibles</p>
            <p className="text-sm mt-2">Asegúrese de que existan resultados de evaluaciones</p>
          </div>
        )}
      </CardBody>
    </Card>
  );
};

export default PerfilInstitucional;
