import React, { useState, useEffect } from 'react';
import { Card, CardHeader, CardBody } from '../../../components/ui/Card';
import supabase from '../../../api/supabaseClient';
import { toast } from 'react-toastify';

/**
 * Widget: Perfil de Aptitud por Nivel Escolar
 * Compara el rendimiento promedio entre niveles educativos
 */
const PerfilPorNivel = () => {
  const [data, setData] = useState([]);
  const [loading, setLoading] = useState(true);
  const [selectedAptitud, setSelectedAptitud] = useState('all');

  useEffect(() => {
    const fetchData = async () => {
      try {
        const { data: result, error } = await supabase
          .from('dashboard_perfil_por_nivel')
          .select('*')
          .order('nivel')
          .order('codigo');

        if (error) {
          console.error('Error al cargar perfil por nivel:', error);
          toast.error('Error al cargar el perfil por nivel');
          return;
        }

        setData(result || []);
      } catch (error) {
        console.error('Error general:', error);
        toast.error('Error al conectar con la base de datos');
      } finally {
        setLoading(false);
      }
    };

    fetchData();
  }, []);

  // Obtener aptitudes únicas
  const aptitudes = [...new Set(data.map(item => item.codigo))];

  // Filtrar datos según aptitud seleccionada
  const filteredData = selectedAptitud === 'all' 
    ? data 
    : data.filter(item => item.codigo === selectedAptitud);

  // Agrupar por nivel
  const dataByLevel = filteredData.reduce((acc, item) => {
    if (!acc[item.nivel]) {
      acc[item.nivel] = [];
    }
    acc[item.nivel].push(item);
    return acc;
  }, {});

  // Crear gráfico de barras comparativo
  const createBarChart = () => {
    if (!filteredData.length) return null;

    const niveles = ['E', 'M', 'S'];
    const maxValue = Math.max(...filteredData.map(item => item.percentil_promedio || 0));
    const chartHeight = 200;

    if (selectedAptitud === 'all') {
      // Mostrar todas las aptitudes agrupadas por nivel
      return (
        <div className="space-y-4">
          {niveles.map(nivel => {
            const nivelData = dataByLevel[nivel] || [];
            const nivelNombre = nivel === 'E' ? 'Elemental' : nivel === 'M' ? 'Medio' : 'Superior';
            
            return (
              <div key={nivel} className="border rounded-lg p-4">
                <h4 className="font-medium text-gray-800 mb-3">Nivel {nivelNombre}</h4>
                <div className="grid grid-cols-7 gap-2">
                  {aptitudes.map(codigo => {
                    const aptitudData = nivelData.find(item => item.codigo === codigo);
                    const value = aptitudData?.percentil_promedio || 0;
                    const height = (value / 100) * 60; // Altura máxima 60px
                    
                    return (
                      <div key={codigo} className="text-center">
                        <div className="h-16 flex items-end justify-center mb-1">
                          <div 
                            className="bg-blue-500 rounded-t w-8 transition-all duration-300 hover:bg-blue-600"
                            style={{ height: `${height}px` }}
                            title={`${codigo}: ${value.toFixed(1)}`}
                          ></div>
                        </div>
                        <div className="text-xs font-medium text-gray-600">{codigo}</div>
                        <div className="text-xs text-gray-500">{value.toFixed(1)}</div>
                      </div>
                    );
                  })}
                </div>
              </div>
            );
          })}
        </div>
      );
    } else {
      // Mostrar comparación de una aptitud específica entre niveles
      const aptitudData = filteredData.filter(item => item.codigo === selectedAptitud);
      
      return (
        <div className="flex justify-center items-end space-x-8 h-48">
          {niveles.map(nivel => {
            const item = aptitudData.find(d => d.nivel === nivel);
            const value = item?.percentil_promedio || 0;
            const height = (value / 100) * 150; // Altura máxima 150px
            const nivelNombre = nivel === 'E' ? 'Elemental' : nivel === 'M' ? 'Medio' : 'Superior';
            
            const getColorForNivel = (nivel) => {
              const colors = {
                'E': 'bg-blue-500 hover:bg-blue-600',
                'M': 'bg-green-500 hover:bg-green-600',
                'S': 'bg-purple-500 hover:bg-purple-600'
              };
              return colors[nivel] || 'bg-gray-500';
            };
            
            return (
              <div key={nivel} className="text-center">
                <div className="h-40 flex items-end justify-center mb-2">
                  <div 
                    className={`rounded-t w-16 transition-all duration-300 ${getColorForNivel(nivel)}`}
                    style={{ height: `${height}px` }}
                    title={`${nivelNombre}: ${value.toFixed(1)}`}
                  ></div>
                </div>
                <div className="font-medium text-gray-700">{nivelNombre}</div>
                <div className="text-sm text-gray-500">{value.toFixed(1)}</div>
                <div className="text-xs text-gray-400">
                  {item?.total_evaluaciones || 0} eval.
                </div>
              </div>
            );
          })}
        </div>
      );
    }
  };

  if (loading) {
    return (
      <Card>
        <CardHeader className="bg-gradient-to-r from-purple-500 to-purple-600 text-white">
          <h3 className="text-lg font-semibold">
            <i className="fas fa-layer-group mr-2"></i>
            Perfil por Nivel Educativo
          </h3>
        </CardHeader>
        <CardBody>
          <div className="animate-pulse">
            <div className="h-8 bg-gray-200 rounded mb-4"></div>
            <div className="h-48 bg-gray-200 rounded"></div>
          </div>
        </CardBody>
      </Card>
    );
  }

  return (
    <Card>
      <CardHeader className="bg-gradient-to-r from-purple-500 to-purple-600 text-white">
        <h3 className="text-lg font-semibold">
          <i className="fas fa-layer-group mr-2"></i>
          Perfil por Nivel Educativo
        </h3>
      </CardHeader>
      <CardBody>
        {data.length > 0 ? (
          <>
            {/* Selector de aptitud */}
            <div className="mb-6">
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Seleccionar Aptitud:
              </label>
              <select
                value={selectedAptitud}
                onChange={(e) => setSelectedAptitud(e.target.value)}
                className="w-full md:w-auto px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-purple-500"
              >
                <option value="all">Todas las Aptitudes</option>
                {aptitudes.map(codigo => (
                  <option key={codigo} value={codigo}>
                    Aptitud {codigo}
                  </option>
                ))}
              </select>
            </div>

            {/* Gráfico */}
            <div className="mb-6">
              {createBarChart()}
            </div>

            {/* Interpretación */}
            <div className="p-4 bg-purple-50 rounded-lg">
              <h4 className="font-medium text-purple-800 mb-2">
                <i className="fas fa-lightbulb mr-2"></i>
                Interpretación
              </h4>
              <p className="text-sm text-purple-700">
                {selectedAptitud === 'all' 
                  ? 'Compare el perfil de aptitudes entre niveles educativos. Una progresión ascendente indica desarrollo adecuado.'
                  : `Evolución de la aptitud ${selectedAptitud} a través de los niveles. Observe si hay progresión o estancamiento.`
                }
              </p>
            </div>
          </>
        ) : (
          <div className="text-center py-8 text-gray-500">
            <i className="fas fa-layer-group text-4xl mb-4"></i>
            <p>No hay datos disponibles</p>
            <p className="text-sm mt-2">Asegúrese de que existan resultados por nivel</p>
          </div>
        )}
      </CardBody>
    </Card>
  );
};

export default PerfilPorNivel;
