import React, { useContext } from 'react';
import { Card, CardHeader, CardBody } from '../../ui/Card';
import { DashboardContext } from '../../../context/DashboardContext';

const ComparativeGender = () => {
  const { data, loading, filters } = useContext(DashboardContext);
  const comparativaGeneroData = data?.comparativaGeneroData || [];
  const selectedNivel = filters.testLevel;

  const niveles = [...new Set(comparativaGeneroData.map(item => item.nivel))];
  const aptitudes = [...new Set(comparativaGeneroData.map(item => item.codigo))];
  
  const filteredData = selectedNivel === 'all'
    ? comparativaGeneroData
    : comparativaGeneroData.filter(item => item.nivel === selectedNivel);

  if (loading) {
    return (
      <Card>
        <CardHeader className="bg-gradient-to-r from-pink-500 to-pink-600 text-white">
          <h3 className="text-lg font-semibold">
            <i className="fas fa-venus-mars mr-2"></i>
            Comparativa por Género
          </h3>
        </CardHeader>
        <CardBody>
          <div className="animate-pulse h-64 bg-gray-200 rounded"></div>
        </CardBody>
      </Card>
    );
  }

  return (
    <Card>
      <CardHeader className="bg-gradient-to-r from-pink-500 to-pink-600 text-white">
        <h3 className="text-lg font-semibold">
          <i className="fas fa-venus-mars mr-2"></i>
          Comparativa por Género
        </h3>
      </CardHeader>
      <CardBody>
        {comparativaGeneroData.length > 0 ? (
          <>
            {/* El filtro de nivel ahora se maneja en FilterPanel.jsx */}

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              {aptitudes.map(codigo => {
                const aptitudData = filteredData.filter(item => item.codigo === codigo);
                const masculino = aptitudData.find(item => item.genero === 'M');
                const femenino = aptitudData.find(item => item.genero === 'F');
                
                return (
                  <div key={codigo} className="border rounded-lg p-4">
                    <h4 className="font-medium text-gray-800 mb-3 text-center">
                      Aptitud {codigo}
                    </h4>
                    <div className="flex justify-between items-center">
                      <div className="text-center">
                        <div className="w-16 h-16 bg-blue-500 rounded-full flex items-center justify-center text-white font-bold mb-2">
                          <i className="fas fa-mars"></i>
                        </div>
                        <div className="text-sm font-medium">Masculino</div>
                        <div className="text-lg font-bold text-blue-600">
                          {masculino?.percentil_promedio?.toFixed(1) || 'N/A'}
                        </div>
                        <div className="text-xs text-gray-500">
                          {masculino?.total_evaluaciones || 0} eval.
                        </div>
                      </div>
                      
                      <div className="text-center">
                        <div className="w-16 h-16 bg-pink-500 rounded-full flex items-center justify-center text-white font-bold mb-2">
                          <i className="fas fa-venus"></i>
                        </div>
                        <div className="text-sm font-medium">Femenino</div>
                        <div className="text-lg font-bold text-pink-600">
                          {femenino?.percentil_promedio?.toFixed(1) || 'N/A'}
                        </div>
                        <div className="text-xs text-gray-500">
                          {femenino?.total_evaluaciones || 0} eval.
                        </div>
                      </div>
                    </div>
                    
                    {masculino && femenino && (
                      <div className="mt-3 text-center">
                        <div className="text-xs text-gray-600">
                          Diferencia: {Math.abs((masculino.percentil_promedio || 0) - (femenino.percentil_promedio || 0)).toFixed(1)} puntos
                        </div>
                      </div>
                    )}
                  </div>
                );
              })}
            </div>

            <div className="mt-6 p-4 bg-pink-50 rounded-lg">
              <h4 className="font-medium text-pink-800 mb-2">
                <i className="fas fa-balance-scale mr-2"></i>
                Interpretación
              </h4>
              <p className="text-sm text-pink-700">
                Identifica posibles brechas de género en el rendimiento. 
                Use esta información para garantizar equidad educativa, no para reforzar estereotipos.
              </p>
            </div>
          </>
        ) : (
          <div className="text-center py-8 text-gray-500">
            <i className="fas fa-venus-mars text-4xl mb-4"></i>
            <p>No hay datos de comparativa por género</p>
          </div>
        )}
      </CardBody>
    </Card>
  );
};
 
export default ComparativeGender;
