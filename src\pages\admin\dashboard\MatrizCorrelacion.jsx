import React, { useState, useEffect } from 'react';
import { Card, CardHeader, CardBody } from '../../../components/ui/Card';
import supabase from '../../../api/supabaseClient';

const MatrizCorrelacion = () => {
  const [data, setData] = useState([]);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    const fetchData = async () => {
      try {
        const { data: result, error } = await supabase
          .from('dashboard_correlacion_aptitudes')
          .select('*');
        setData(result || []);
      } catch (error) {
        console.error('Error:', error);
      } finally {
        setLoading(false);
      }
    };
    fetchData();
  }, []);

  const aptitudes = ['V', 'E', 'A', 'R', 'N', 'M', 'O'];
  
  const getCorrelation = (apt1, apt2) => {
    if (apt1 === apt2) return 1;
    const correlation = data.find(item => 
      (item.aptitud_1 === apt1 && item.aptitud_2 === apt2) ||
      (item.aptitud_1 === apt2 && item.aptitud_2 === apt1)
    );
    return correlation?.correlacion || 0;
  };

  const getColorForCorrelation = (value) => {
    const absValue = Math.abs(value);
    if (absValue >= 0.7) return value > 0 ? 'bg-green-600' : 'bg-red-600';
    if (absValue >= 0.5) return value > 0 ? 'bg-green-400' : 'bg-red-400';
    if (absValue >= 0.3) return value > 0 ? 'bg-green-200' : 'bg-red-200';
    return 'bg-gray-100';
  };

  const getTextColorForCorrelation = (value) => {
    const absValue = Math.abs(value);
    return absValue >= 0.5 ? 'text-white' : 'text-gray-800';
  };

  if (loading) {
    return (
      <Card>
        <CardHeader className="bg-gradient-to-r from-indigo-500 to-indigo-600 text-white">
          <h3 className="text-lg font-semibold">
            <i className="fas fa-project-diagram mr-2"></i>
            Matriz de Correlación
          </h3>
        </CardHeader>
        <CardBody>
          <div className="animate-pulse h-64 bg-gray-200 rounded"></div>
        </CardBody>
      </Card>
    );
  }

  return (
    <Card>
      <CardHeader className="bg-gradient-to-r from-indigo-500 to-indigo-600 text-white">
        <h3 className="text-lg font-semibold">
          <i className="fas fa-project-diagram mr-2"></i>
          Matriz de Correlación entre Aptitudes
        </h3>
      </CardHeader>
      <CardBody>
        {data.length > 0 ? (
          <>
            <div className="overflow-x-auto mb-6">
              <table className="w-full">
                <thead>
                  <tr>
                    <th className="w-12 h-12"></th>
                    {aptitudes.map(apt => (
                      <th key={apt} className="w-12 h-12 text-center font-bold text-gray-700">
                        {apt}
                      </th>
                    ))}
                  </tr>
                </thead>
                <tbody>
                  {aptitudes.map(apt1 => (
                    <tr key={apt1}>
                      <td className="w-12 h-12 text-center font-bold text-gray-700 bg-gray-50">
                        {apt1}
                      </td>
                      {aptitudes.map(apt2 => {
                        const correlation = getCorrelation(apt1, apt2);
                        return (
                          <td 
                            key={apt2} 
                            className={`w-12 h-12 text-center text-xs font-medium border ${getColorForCorrelation(correlation)} ${getTextColorForCorrelation(correlation)}`}
                            title={`Correlación ${apt1}-${apt2}: ${correlation.toFixed(3)}`}
                          >
                            {correlation.toFixed(2)}
                          </td>
                        );
                      })}
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>

            {/* Leyenda */}
            <div className="grid grid-cols-2 md:grid-cols-4 gap-2 mb-6 text-sm">
              <div className="flex items-center">
                <div className="w-4 h-4 bg-green-600 rounded mr-2"></div>
                <span>Correlación fuerte positiva (≥0.7)</span>
              </div>
              <div className="flex items-center">
                <div className="w-4 h-4 bg-green-400 rounded mr-2"></div>
                <span>Correlación moderada positiva (0.5-0.7)</span>
              </div>
              <div className="flex items-center">
                <div className="w-4 h-4 bg-red-400 rounded mr-2"></div>
                <span>Correlación moderada negativa (-0.5 a -0.7)</span>
              </div>
              <div className="flex items-center">
                <div className="w-4 h-4 bg-gray-100 border rounded mr-2"></div>
                <span>Correlación débil (&lt;0.3)</span>
              </div>
            </div>

            {/* Correlaciones destacadas */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="p-4 bg-green-50 rounded-lg">
                <h4 className="font-medium text-green-800 mb-2">
                  <i className="fas fa-link mr-2"></i>
                  Correlaciones Más Fuertes
                </h4>
                {data
                  .filter(item => Math.abs(item.correlacion) >= 0.5)
                  .sort((a, b) => Math.abs(b.correlacion) - Math.abs(a.correlacion))
                  .slice(0, 3)
                  .map((item, index) => (
                    <div key={index} className="text-sm text-green-700">
                      <strong>{item.aptitud_1}-{item.aptitud_2}</strong>: {item.correlacion.toFixed(3)}
                    </div>
                  ))}
              </div>

              <div className="p-4 bg-blue-50 rounded-lg">
                <h4 className="font-medium text-blue-800 mb-2">
                  <i className="fas fa-unlink mr-2"></i>
                  Aptitudes Independientes
                </h4>
                {data
                  .filter(item => Math.abs(item.correlacion) < 0.2)
                  .slice(0, 3)
                  .map((item, index) => (
                    <div key={index} className="text-sm text-blue-700">
                      <strong>{item.aptitud_1}-{item.aptitud_2}</strong>: {item.correlacion.toFixed(3)}
                    </div>
                  ))}
              </div>
            </div>

            <div className="mt-6 p-4 bg-indigo-50 rounded-lg">
              <h4 className="font-medium text-indigo-800 mb-2">
                <i className="fas fa-lightbulb mr-2"></i>
                Interpretación
              </h4>
              <p className="text-sm text-indigo-700">
                Las correlaciones fuertes indican aptitudes que tienden a desarrollarse juntas. 
                Las correlaciones débiles sugieren habilidades independientes que requieren enfoques pedagógicos específicos.
              </p>
            </div>
          </>
        ) : (
          <div className="text-center py-8 text-gray-500">
            <i className="fas fa-project-diagram text-4xl mb-4"></i>
            <p>No hay datos de correlación disponibles</p>
          </div>
        )}
      </CardBody>
    </Card>
  );
};

export default MatrizCorrelacion;
