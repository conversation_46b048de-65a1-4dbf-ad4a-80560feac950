import React, { useState, useEffect } from 'react';
import { Link } from 'react-router-dom';
import { Card, CardHeader, CardBody } from '../../components/ui/Card';
import { Button } from '../../components/ui/Button';
import { useToast } from '../../hooks/useToast';
import supabase from '../../api/supabaseClient';
import { BaremosService } from '../../services/baremosService';
import PageHeader from '../../components/ui/PageHeader';
import ExportToPDF from '../../components/export/ExportToPDF';
import { FaChartLine } from 'react-icons/fa';
import InformesManager from '../../components/reports/InformesManager';
import InformesTest from '../../components/test/InformesTest';

const Results = () => {
  const [results, setResults] = useState([]);
  const [loading, setLoading] = useState(true);
  const [currentPage, setCurrentPage] = useState(1);
  const [itemsPerPage] = useState(5); // 5 pacientes por página
  const [searchTerm, setSearchTerm] = useState('');
  const { showToast } = useToast();

  useEffect(() => {
    const fetchResults = async () => {
      try {
        setLoading(true);

        // Primero obtener TODOS los pacientes
        const { data: pacientes, error: pacientesError } = await supabase
          .from('pacientes')
          .select(`
            id,
            nombre,
            apellido,
            documento,
            genero,
            created_at
          `)
          .order('nombre', { ascending: true });

        if (pacientesError) {
          console.error('Error al cargar pacientes:', pacientesError);
          showToast('Error al cargar los pacientes', 'error');
          return;
        }

        console.log('📊 [Results] Pacientes obtenidos:', pacientes?.length || 0);
        console.log('📊 [Results] Lista de pacientes:', pacientes?.map(p => `${p.nombre} ${p.apellido}`) || []);

        // Buscar específicamente a Ana Sofia
        const anaSofia = pacientes?.find(p =>
          p.nombre.toLowerCase().includes('ana') &&
          p.apellido.toLowerCase().includes('rueda')
        );
        console.log('🔍 [Results] Ana Sofia encontrada:', anaSofia ? `${anaSofia.nombre} ${anaSofia.apellido} (ID: ${anaSofia.id})` : 'NO ENCONTRADA');

        // Luego obtener todos los resultados
        const { data: resultados, error: resultadosError } = await supabase
          .from('resultados')
          .select(`
            id,
            paciente_id,
            puntaje_directo,
            percentil,
            errores,
            tiempo_segundos,
            concentracion,
            created_at,
            aptitudes:aptitud_id (
              codigo,
              nombre,
              descripcion
            )
          `)
          .order('created_at', { ascending: false });

        if (resultadosError) {
          console.error('Error al cargar resultados:', resultadosError);
          showToast('Error al cargar los resultados', 'error');
          return;
        }

        console.log('📊 [Results] Resultados obtenidos:', resultados?.length || 0);
        console.log('📊 [Results] Resultados por paciente:', resultados?.reduce((acc, r) => {
          acc[r.paciente_id] = (acc[r.paciente_id] || 0) + 1;
          return acc;
        }, {}) || {});

        // Verificar resultados de Ana Sofia si la encontramos
        if (anaSofia) {
          const resultadosAnaSofia = resultados?.filter(r => r.paciente_id === anaSofia.id) || [];
          console.log(`🔍 [Results] Resultados de Ana Sofia (${anaSofia.id}):`, resultadosAnaSofia.length);
          resultadosAnaSofia.forEach(r => {
            console.log(`   - ${r.aptitudes?.codigo || 'N/A'}: PD=${r.puntaje_directo}, PC=${r.percentil || 'N/A'}`);
          });
        }

        // Agrupar resultados por paciente, incluyendo pacientes sin resultados
        const groupedByPatient = pacientes.map(paciente => {
          const pacienteResultados = resultados.filter(resultado => resultado.paciente_id === paciente.id);

          const resultadosProcesados = pacienteResultados.map(resultado => {
            const interpretacion = resultado.percentil
              ? BaremosService.obtenerInterpretacionPC(resultado.percentil)
              : { nivel: 'Pendiente', color: 'text-gray-600', bg: 'bg-gray-100' };

            return {
              id: resultado.id,
              test: resultado.aptitudes?.codigo || 'N/A',
              testName: resultado.aptitudes?.nombre || 'Test Desconocido',
              puntajePD: resultado.puntaje_directo || 0,
              puntajePC: resultado.percentil || 'N/A',
              errores: resultado.errores || 0,
              tiempo: resultado.tiempo_segundos ? `${Math.round(resultado.tiempo_segundos / 60)}:${String(resultado.tiempo_segundos % 60).padStart(2, '0')}` : 'N/A',
              concentracion: resultado.concentracion ? `${resultado.concentracion.toFixed(1)}%` : 'N/A',
              fecha: new Date(resultado.created_at).toLocaleDateString('es-ES'),
              interpretacion: interpretacion.nivel,
              interpretacionColor: interpretacion.color,
              interpretacionBg: interpretacion.bg
            };
          });

          // Determinar la fecha de última evaluación
          const fechaUltimaEvaluacion = pacienteResultados.length > 0
            ? pacienteResultados[0].created_at
            : paciente.created_at;

          return {
            paciente: paciente,
            resultados: resultadosProcesados,
            fechaUltimaEvaluacion: fechaUltimaEvaluacion,
            tieneResultados: pacienteResultados.length > 0
          };
        });

        // Ordenar: primero los que tienen resultados, luego por fecha más reciente
        const processedResults = groupedByPatient.sort((a, b) => {
          // Priorizar pacientes con resultados
          if (a.tieneResultados && !b.tieneResultados) return -1;
          if (!a.tieneResultados && b.tieneResultados) return 1;

          // Si ambos tienen o no tienen resultados, ordenar por fecha
          return new Date(b.fechaUltimaEvaluacion) - new Date(a.fechaUltimaEvaluacion);
        });

        setResults(processedResults);

        console.log('📊 [Results] Pacientes procesados para mostrar:', processedResults.length);
        console.log('📊 [Results] Lista final:', processedResults.map(p => `${p.paciente.nombre} ${p.paciente.apellido} (${p.resultados.length} resultados)`));

        // Verificar si Ana Sofia está en los resultados finales
        const anaSofiaFinal = processedResults.find(p =>
          p.paciente.nombre.toLowerCase().includes('ana') &&
          p.paciente.apellido.toLowerCase().includes('rueda')
        );
        console.log('🔍 [Results] Ana Sofia en resultados finales:', anaSofiaFinal ? 'SÍ' : 'NO');

        setLoading(false);
      } catch (error) {
        console.error('Error al cargar resultados:', error);
        showToast('Error al cargar los resultados', 'error');
        setLoading(false);
      }
    };

    // Cargar datos iniciales
    fetchResults();

    // Configurar suscripción en tiempo real para cambios en resultados
    const resultsSubscription = supabase
      .channel('resultados-changes')
      .on(
        'postgres_changes',
        {
          event: '*', // Escuchar INSERT, UPDATE, DELETE
          schema: 'public',
          table: 'resultados',
        },
        (payload) => {
          console.log('🔄 [Results] Cambio en tiempo real detectado:', payload);
          // Recargar todos los datos cuando hay cambios
          fetchResults();
        }
      )
      .subscribe();

    // Suscripción para cambios en pacientes
    const patientsSubscription = supabase
      .channel('pacientes-changes')
      .on(
        'postgres_changes',
        {
          event: '*',
          schema: 'public',
          table: 'pacientes',
        },
        (payload) => {
          console.log('🔄 [Results] Cambio en pacientes detectado:', payload);
          fetchResults();
        }
      )
      .subscribe();

    console.log('📡 [Results] Suscripciones en tiempo real activadas');

    // Limpieza de suscripciones
    return () => {
      console.log('📡 [Results] Removiendo suscripciones');
      supabase.removeChannel(resultsSubscription);
      supabase.removeChannel(patientsSubscription);
    };
  }, [showToast]);

  // Filtrar y paginar resultados
  const filteredResults = results.filter(patientGroup => {
    if (!searchTerm) return true;
    const patient = patientGroup.paciente;
    return (
      patient.nombre.toLowerCase().includes(searchTerm.toLowerCase()) ||
      patient.apellido.toLowerCase().includes(searchTerm.toLowerCase()) ||
      (patient.documento && patient.documento.toLowerCase().includes(searchTerm.toLowerCase()))
    );
  });

  const totalPages = Math.ceil(filteredResults.length / itemsPerPage);
  const startIndex = (currentPage - 1) * itemsPerPage;
  const paginatedResults = filteredResults.slice(startIndex, startIndex + itemsPerPage);

  // Función para cambiar página con validación mejorada
  const handlePageChange = (page) => {
    // Validar que la página esté en el rango válido
    if (page < 1 || page > totalPages) return;

    setCurrentPage(page);
    window.scrollTo({ top: 0, behavior: 'smooth' });
  };

  // Efecto para ajustar la página actual cuando cambian los filtros
  useEffect(() => {
    // Si la página actual es mayor que el total de páginas después del filtrado, ir a la última página
    if (currentPage > totalPages && totalPages > 0) {
      setCurrentPage(totalPages);
    }
    // Si no hay resultados filtrados, ir a la página 1
    else if (filteredResults.length === 0 && currentPage !== 1) {
      setCurrentPage(1);
    }
  }, [filteredResults.length, totalPages, currentPage]);

  const getTestIcon = (testCode) => {
    const icons = {
      'V': 'fas fa-comments',
      'E': 'fas fa-cube',
      'A': 'fas fa-eye',
      'R': 'fas fa-puzzle-piece',
      'N': 'fas fa-calculator',
      'M': 'fas fa-cogs',
      'O': 'fas fa-spell-check'
    };
    return icons[testCode] || 'fas fa-clipboard-list';
  };

  const getTestColor = (testCode) => {
    const colors = {
      'V': 'text-blue-600',
      'E': 'text-indigo-600',
      'A': 'text-red-600',
      'R': 'text-amber-600',
      'N': 'text-teal-600',
      'M': 'text-slate-600',
      'O': 'text-green-600'
    };
    return colors[testCode] || 'text-gray-600';
  };

  return (
    <div>
      {/* Header Section with Standardized Style */}
      <PageHeader
        title="Resultados de Tests"
        subtitle={`${results.length} estudiante${results.length !== 1 ? 's' : ''} evaluado${results.length !== 1 ? 's' : ''} • ${results.reduce((total, student) => total + student.resultados.length, 0)} tests completados`}
        icon={FaChartLine}
      />

      <div className="container mx-auto py-6">

      {loading ? (
        <div className="py-16 text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-500 mx-auto mb-4"></div>
          <p className="text-gray-500">Cargando resultados...</p>
        </div>
      ) : (
        <>
          {/* Resumen ejecutivo */}
          <div className="mb-6 bg-gradient-to-r from-blue-50 to-indigo-50 rounded-lg p-6 border border-blue-200">
            <div className="flex items-center justify-between mb-4">
              <h2 className="text-xl font-bold text-gray-800">
                <i className="fas fa-chart-pie mr-2 text-blue-600"></i>
                Resumen de Evaluaciones BAT-7
              </h2>
              <div className="text-sm text-gray-600">
                Última actualización: {new Date().toLocaleDateString('es-ES')}
              </div>
            </div>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 mb-4">
              <div className="bg-white rounded-lg p-4 shadow-sm border border-blue-100">
                <div className="flex items-center">
                  <div className="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center mr-3">
                    <i className="fas fa-users text-blue-600 text-xl"></i>
                  </div>
                  <div>
                    <p className="text-sm text-gray-600">Estudiantes Evaluados</p>
                    <p className="text-2xl font-bold text-blue-600">{results.length}</p>
                  </div>
                </div>
              </div>
              <div className="bg-white rounded-lg p-4 shadow-sm border border-green-100">
                <div className="flex items-center">
                  <div className="w-12 h-12 bg-green-100 rounded-lg flex items-center justify-center mr-3">
                    <i className="fas fa-clipboard-check text-green-600 text-xl"></i>
                  </div>
                  <div>
                    <p className="text-sm text-gray-600">Tests Completados</p>
                    <p className="text-2xl font-bold text-green-600">{results.reduce((total, student) => total + student.resultados.length, 0)}</p>
                  </div>
                </div>
              </div>
              <div className="bg-white rounded-lg p-4 shadow-sm border border-purple-100">
                <div className="flex items-center">
                  <div className="w-12 h-12 bg-purple-100 rounded-lg flex items-center justify-center mr-3">
                    <i className="fas fa-chart-line text-purple-600 text-xl"></i>
                  </div>
                  <div>
                    <p className="text-sm text-gray-600">Promedio por Estudiante</p>
                    <p className="text-2xl font-bold text-purple-600">{results.length > 0 ? Math.round(results.reduce((total, student) => total + student.resultados.length, 0) / results.length * 10) / 10 : 0}</p>
                  </div>
                </div>
              </div>
              <div className="bg-white rounded-lg p-4 shadow-sm border border-orange-100">
                <div className="flex items-center">
                  <div className="w-12 h-12 bg-orange-100 rounded-lg flex items-center justify-center mr-3">
                    <i className="fas fa-battery-full text-orange-600 text-xl"></i>
                  </div>
                  <div>
                    <p className="text-sm text-gray-600">Baterías Completas</p>
                    <p className="text-2xl font-bold text-orange-600">{results.filter(student => student.resultados.length >= 7).length}</p>
                  </div>
                </div>
              </div>
            </div>
            <div className="bg-white rounded-lg p-4 border border-blue-100">
              <p className="text-sm text-gray-700">
                <i className="fas fa-info-circle mr-2 text-blue-500"></i>
                <strong>Explicación:</strong> Se muestran {results.length} estudiantes únicos que han completado un total de {results.reduce((total, student) => total + student.resultados.length, 0)} tests individuales.
                Cada estudiante puede tomar múltiples tests de la batería BAT-7 (Verbal, Espacial, Atención, etc.).
                Una batería completa consta de 7 tests diferentes.
              </p>
            </div>
          </div>

          {/* Estadísticas generales */}
          <div className="mb-6 grid grid-cols-1 md:grid-cols-4 gap-4" style={{display: 'none'}}>
            <div className="bg-gradient-to-r from-blue-500 to-blue-600 rounded-lg p-4 text-white">
              <div className="flex items-center">
                <i className="fas fa-users text-2xl mr-3"></i>
                <div>
                  <p className="text-blue-100 text-sm">Estudiantes Evaluados</p>
                  <p className="text-2xl font-bold">{results.length}</p>
                </div>
              </div>
            </div>
            <div className="bg-gradient-to-r from-green-500 to-green-600 rounded-lg p-4 text-white">
              <div className="flex items-center">
                <i className="fas fa-clipboard-check text-2xl mr-3"></i>
                <div>
                  <p className="text-green-100 text-sm">Tests Completados</p>
                  <p className="text-2xl font-bold">{results.reduce((total, student) => total + student.resultados.length, 0)}</p>
                </div>
              </div>
            </div>
            <div className="bg-gradient-to-r from-purple-500 to-purple-600 rounded-lg p-4 text-white">
              <div className="flex items-center">
                <i className="fas fa-chart-line text-2xl mr-3"></i>
                <div>
                  <p className="text-purple-100 text-sm">Promedio por Estudiante</p>
                  <p className="text-2xl font-bold">{results.length > 0 ? Math.round(results.reduce((total, student) => total + student.resultados.length, 0) / results.length * 10) / 10 : 0}</p>
                </div>
              </div>
            </div>
            <div className="bg-gradient-to-r from-orange-500 to-orange-600 rounded-lg p-4 text-white">
              <div className="flex items-center">
                <i className="fas fa-battery-full text-2xl mr-3"></i>
                <div>
                  <p className="text-orange-100 text-sm">Baterías Completas</p>
                  <p className="text-2xl font-bold">{results.filter(student => student.resultados.length >= 7).length}</p>
                </div>
              </div>
            </div>
          </div>

          {/* Barra de búsqueda y filtros */}
          <div className="mb-6 bg-white rounded-lg shadow-md p-4">
            <div className="flex flex-col md:flex-row md:items-center md:justify-between gap-4">
              {/* Campo de búsqueda */}
              <div className="relative flex-1 max-w-md">
                <input
                  type="text"
                  placeholder="Buscar paciente por nombre, apellido o documento..."
                  value={searchTerm}
                  onChange={(e) => {
                    setSearchTerm(e.target.value);
                    setCurrentPage(1); // Resetear a primera página al buscar
                  }}
                  className="w-full px-4 py-2 pl-10 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                />
                <i className="fas fa-search absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400"></i>
              </div>

              {/* Estadísticas y acciones */}
              <div className="flex items-center gap-4 text-sm">
                <div className="flex items-center gap-4 text-gray-600">
                  <span className="bg-blue-100 text-blue-800 px-3 py-1 rounded-full">
                    <i className="fas fa-users mr-1"></i>
                    {filteredResults.length} estudiante{filteredResults.length !== 1 ? 's' : ''}
                  </span>
                  <span className="bg-green-100 text-green-800 px-3 py-1 rounded-full">
                    <i className="fas fa-clipboard-check mr-1"></i>
                    {filteredResults.reduce((total, student) => total + student.resultados.length, 0)} tests
                  </span>
                  <span className="bg-purple-100 text-purple-800 px-3 py-1 rounded-full">
                    <i className="fas fa-chart-line mr-1"></i>
                    {Math.round(filteredResults.reduce((total, student) => total + student.resultados.length, 0) / filteredResults.length * 10) / 10} promedio/estudiante
                  </span>
                  <span className="bg-green-100 text-green-800 px-3 py-1 rounded-full">
                    <i className="fas fa-chart-line mr-1"></i>
                    {results.reduce((total, p) => total + p.resultados.length, 0)} resultado{results.reduce((total, p) => total + p.resultados.length, 0) !== 1 ? 's' : ''}
                  </span>
                </div>

                {/* Botón de exportación */}
                <ExportToPDF
                  data={filteredResults}
                  filename={`resultados-bat7-${new Date().toISOString().split('T')[0]}`}
                  disabled={loading || filteredResults.length === 0}
                />
              </div>
            </div>
          </div>

          {filteredResults.length === 0 ? (
            <Card>
              <CardBody>
                <div className="py-8 text-center">
                  <i className="fas fa-clipboard-list text-4xl text-gray-300 mb-4"></i>
                  <p className="text-gray-500">No hay resultados de tests disponibles.</p>
                  <p className="text-sm text-gray-400 mt-2">
                    Los resultados aparecerán aquí una vez que se completen los tests.
                  </p>
                </div>
              </CardBody>
            </Card>
          ) : (
            <>
              {/* Resultados paginados */}
              <div className="space-y-6">
                {paginatedResults.map((patientGroup, index) => (
                <Card key={patientGroup.paciente.id} className="overflow-hidden shadow-lg border border-blue-200">
                  <CardHeader className="bg-gradient-to-r from-blue-500 to-blue-600 border-b border-blue-300">
                    <div className="flex items-center justify-between">
                      <div className="flex items-center">
                        <div className={`w-14 h-14 rounded-full flex items-center justify-center text-white text-xl font-bold mr-4 shadow-lg ${
                          patientGroup.paciente?.genero === 'masculino' ? 'bg-white bg-opacity-20 border-2 border-white border-opacity-30' : 'bg-white bg-opacity-20 border-2 border-white border-opacity-30'
                        }`}>
                          <i className={`fas ${patientGroup.paciente?.genero === 'masculino' ? 'fa-mars text-blue-100' : 'fa-venus text-pink-200'}`}></i>
                        </div>
                        <div>
                          <h3 className="text-xl font-bold text-white">
                            {patientGroup.paciente?.nombre} {patientGroup.paciente?.apellido}
                          </h3>
                          <p className="text-blue-100 text-sm">
                            <i className="fas fa-clipboard-check mr-1"></i>
                            {patientGroup.resultados.length} test{patientGroup.resultados.length !== 1 ? 's' : ''} completado{patientGroup.resultados.length !== 1 ? 's' : ''}
                          </p>
                        </div>
                      </div>
                      <div className="flex items-center space-x-4">
                        <div className="text-right">
                          <p className="text-blue-100 text-sm">Última evaluación</p>
                          <p className="text-white font-semibold">
                            {new Date(patientGroup.fechaUltimaEvaluacion).toLocaleDateString('es-ES')}
                          </p>
                        </div>
                        <Button
                          as={Link}
                          to={`/student/informe-completo/${patientGroup.paciente.id}`}
                          className="bg-white text-blue-600 hover:bg-blue-50 border-white shadow-lg"
                          size="sm"
                        >
                          <i className="fas fa-file-alt mr-2"></i>
                          Ver Informe Completo
                        </Button>
                      </div>
                    </div>
                  </CardHeader>
                  <CardBody className="p-0">
                    <div className="overflow-x-auto">
                      <table className="w-full">
                        <thead className="bg-blue-50 border-b border-blue-200">
                          <tr>
                            <th className="px-4 py-3 text-center text-xs font-medium text-blue-700 uppercase tracking-wider">
                              Test
                            </th>
                            <th className="px-4 py-3 text-center text-xs font-medium text-blue-700 uppercase tracking-wider">
                              Puntaje PD
                            </th>
                            <th className="px-4 py-3 text-center text-xs font-medium text-blue-700 uppercase tracking-wider">
                              Puntaje PC
                            </th>
                            <th className="px-4 py-3 text-center text-xs font-medium text-blue-700 uppercase tracking-wider">
                              Errores
                            </th>
                            <th className="px-4 py-3 text-center text-xs font-medium text-blue-700 uppercase tracking-wider">
                              Tiempo
                            </th>
                            <th className="px-4 py-3 text-center text-xs font-medium text-blue-700 uppercase tracking-wider">
                              Fecha Test
                            </th>
                            <th className="px-4 py-3 text-center text-xs font-medium text-blue-700 uppercase tracking-wider">
                              Acciones
                            </th>
                          </tr>
                        </thead>
                        <tbody className="bg-white divide-y divide-gray-200">
                          {patientGroup.resultados.length > 0 ? (
                            patientGroup.resultados.map((result, resultIndex) => (
                              <tr key={result.id} className={resultIndex % 2 === 0 ? 'bg-white' : 'bg-gray-50'}>
                                <td className="px-4 py-4 text-center">
                                  <div className="flex items-center justify-center">
                                    <div className={`w-8 h-8 rounded-full bg-gray-100 flex items-center justify-center mr-2 ${getTestColor(result.test)}`}>
                                      <i className={getTestIcon(result.test)}></i>
                                    </div>
                                    <div className="text-left">
                                      <div className="text-sm font-medium text-gray-900">{result.test}</div>
                                      <div className="text-xs text-gray-500">{result.testName}</div>
                                    </div>
                                  </div>
                                </td>
                                <td className="px-4 py-4 text-center">
                                  <span className="text-lg font-bold text-orange-600 bg-orange-100 px-3 py-1 rounded-full">
                                    {result.puntajePD}
                                  </span>
                                </td>
                                <td className="px-4 py-4 text-center">
                                  {result.puntajePC !== 'N/A' ? (
                                    <div className="flex flex-col items-center">
                                      <span className="text-lg font-bold text-blue-600 bg-blue-100 px-3 py-1 rounded-full mb-1">
                                        {result.puntajePC}
                                      </span>
                                      <span className={`text-xs px-2 py-1 rounded-full ${result.interpretacionBg} ${result.interpretacionColor}`}>
                                        {result.interpretacion}
                                      </span>
                                    </div>
                                  ) : (
                                    <span className="text-gray-400 text-sm">Pendiente</span>
                                  )}
                                </td>
                                <td className="px-4 py-4 text-center">
                                  <span className="text-sm font-medium text-gray-700">{result.errores}</span>
                                </td>
                                <td className="px-4 py-4 text-center">
                                  <span className="text-sm font-medium text-gray-700">{result.tiempo}</span>
                                </td>
                                <td className="px-4 py-4 text-center">
                                  <span className="text-sm text-gray-500">{result.fecha}</span>
                                </td>
                                <td className="px-4 py-4 text-center">
                                  <Button
                                    as={Link}
                                    to={`/student/report/${result.id}`}
                                    size="sm"
                                    className="bg-blue-500 text-white hover:bg-blue-600"
                                  >
                                    <i className="fas fa-eye mr-1"></i>
                                    Ver
                                  </Button>
                                </td>
                              </tr>
                            ))
                          ) : (
                            <tr>
                              <td colSpan="7" className="px-4 py-8 text-center">
                                <div className="flex flex-col items-center justify-center text-gray-500">
                                  <i className="fas fa-clipboard-list text-4xl mb-3 text-gray-300"></i>
                                  <p className="text-lg font-medium">Sin evaluaciones realizadas</p>
                                  <p className="text-sm">Este paciente aún no ha completado ningún test</p>
                                </div>
                              </td>
                            </tr>
                          )}
                        </tbody>
                      </table>
                    </div>
                  </CardBody>
                </Card>

                {/* Componente de gestión de informes para cada estudiante */}
                <InformesManager
                  paciente={patientGroup.paciente}
                  resultados={patientGroup.resultados}
                  onInformeGenerado={(informeId) => {
                    console.log('Informe generado:', informeId);
                    toast.success('Informe generado y archivado exitosamente');
                  }}
                />
              ))}
              </div>

              {/* Controles de paginación mejorados */}
              {totalPages > 1 && (
                <div className="mt-8 flex flex-col sm:flex-row items-center justify-between gap-4 bg-white rounded-lg shadow-md p-4">
                  <div className="text-sm text-gray-600">
                    Mostrando <span className="font-semibold text-blue-600">{startIndex + 1}</span> - <span className="font-semibold text-blue-600">{Math.min(startIndex + itemsPerPage, filteredResults.length)}</span> de <span className="font-semibold text-blue-600">{filteredResults.length}</span> estudiantes
                    {searchTerm && (
                      <span className="ml-2 text-xs bg-yellow-100 text-yellow-800 px-2 py-1 rounded-full">
                        <i className="fas fa-search mr-1"></i>
                        Filtrado
                      </span>
                    )}
                  </div>

                  <div className="flex items-center gap-2">
                    {/* Botón primera página */}
                    {currentPage > 2 && totalPages > 5 && (
                      <>
                        <button
                          onClick={() => handlePageChange(1)}
                          className="px-3 py-2 rounded-lg text-sm font-medium bg-gray-100 text-gray-700 hover:bg-gray-200 transition-colors"
                        >
                          1
                        </button>
                        {currentPage > 3 && <span className="px-2 text-gray-400">...</span>}
                      </>
                    )}

                    {/* Botón anterior */}
                    <button
                      onClick={() => handlePageChange(currentPage - 1)}
                      disabled={currentPage === 1}
                      className={`px-3 py-2 rounded-lg text-sm font-medium transition-colors ${
                        currentPage === 1
                          ? 'bg-gray-100 text-gray-400 cursor-not-allowed'
                          : 'bg-blue-500 text-white hover:bg-blue-600'
                      }`}
                      title="Página anterior"
                    >
                      <i className="fas fa-chevron-left mr-1"></i>
                      Anterior
                    </button>

                    {/* Números de página con lógica inteligente */}
                    <div className="flex gap-1">
                      {(() => {
                        const pages = [];
                        const maxVisiblePages = 5;

                        if (totalPages <= maxVisiblePages) {
                          // Mostrar todas las páginas si son pocas
                          for (let i = 1; i <= totalPages; i++) {
                            pages.push(i);
                          }
                        } else {
                          // Lógica para páginas con elipsis
                          const start = Math.max(1, currentPage - 2);
                          const end = Math.min(totalPages, currentPage + 2);

                          for (let i = start; i <= end; i++) {
                            pages.push(i);
                          }
                        }

                        return pages.map(page => (
                          <button
                            key={page}
                            onClick={() => handlePageChange(page)}
                            className={`px-3 py-2 rounded-lg text-sm font-medium transition-colors ${
                              currentPage === page
                                ? 'bg-blue-500 text-white shadow-md'
                                : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
                            }`}
                            title={`Ir a página ${page}`}
                          >
                            {page}
                          </button>
                        ));
                      })()}
                    </div>

                    {/* Botón siguiente */}
                    <button
                      onClick={() => handlePageChange(currentPage + 1)}
                      disabled={currentPage === totalPages}
                      className={`px-3 py-2 rounded-lg text-sm font-medium transition-colors ${
                        currentPage === totalPages
                          ? 'bg-gray-100 text-gray-400 cursor-not-allowed'
                          : 'bg-blue-500 text-white hover:bg-blue-600'
                      }`}
                      title="Página siguiente"
                    >
                      Siguiente
                      <i className="fas fa-chevron-right ml-1"></i>
                    </button>

                    {/* Botón última página */}
                    {currentPage < totalPages - 1 && totalPages > 5 && (
                      <>
                        {currentPage < totalPages - 2 && <span className="px-2 text-gray-400">...</span>}
                        <button
                          onClick={() => handlePageChange(totalPages)}
                          className="px-3 py-2 rounded-lg text-sm font-medium bg-gray-100 text-gray-700 hover:bg-gray-200 transition-colors"
                        >
                          {totalPages}
                        </button>
                      </>
                    )}
                  </div>
                </div>
              )}

              {/* Información adicional cuando no hay paginación */}
              {totalPages <= 1 && filteredResults.length > 0 && (
                <div className="mt-4 text-center text-sm text-gray-500">
                  <i className="fas fa-info-circle mr-1"></i>
                  Mostrando todos los {filteredResults.length} estudiante{filteredResults.length !== 1 ? 's' : ''} disponible{filteredResults.length !== 1 ? 's' : ''}
                </div>
              )}
            </>
          )}
        </>
      )}

      {/* Componente de prueba temporal */}
      <InformesTest />
      </div>
    </div>
  );
};

export default Results;