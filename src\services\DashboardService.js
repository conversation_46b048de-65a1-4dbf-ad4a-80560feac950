/**
 * @file DashboardService.js
 * @description Servicio para obtener y procesar los datos del dashboard desde Supabase.
 * Este servicio centraliza todas las llamadas a las vistas especializadas del dashboard
 * y realiza cualquier pre-procesamiento necesario antes de entregar los datos a la UI.
 */

import supabase from '../api/supabaseClient.js';

// --- Funciones de Simulación de Datos (Fallback) ---

const generarEstadisticasGenerales = () => {
  const total_pacientes = 125 + Math.floor(Math.random() * 50);
  const total_evaluaciones = 42 + Math.floor(Math.random() * 20);
  return {
    total_pacientes,
    pacientes_evaluados: Math.floor(total_pacientes * 0.8),
    total_evaluaciones,
    percentil_promedio_general: parseFloat((75.5 + (Math.random() - 0.5) * 10).toFixed(2)),
    evaluaciones_ultimo_mes: Math.floor(total_evaluaciones * 0.3),
    evaluaciones_ultima_semana: Math.floor(total_evaluaciones * 0.1),
    total_psicologos: 8 + Math.floor(Math.random() * 5),
    ultima_actualizacion: new Date().toISOString()
  };
};

const generarDistribucionNivel = () => {
  return [
    { name: 'Masculino', value: 65, color: '#3B82F6' },
    { name: 'Femenino', value: 58, color: '#10B981' },
    { name: 'Otro', value: 2, color: '#F59E0B' }
  ];
};

const generarPerfilInstitucional = () => {
  const data = [
    75 + Math.random() * 20, // Verbal
    70 + Math.random() * 25, // Espacial
    80 + Math.random() * 15, // Atención
    72 + Math.random() * 23, // Razonamiento
    68 + Math.random() * 27, // Numérico
    65 + Math.random() * 30, // Mecánico
    85 + Math.random() * 10  // Ortografía
  ].map(val => parseFloat(val.toFixed(2)));

  return {
    labels: ['Verbal', 'Espacial', 'Atención', 'Razonamiento', 'Numérico', 'Mecánico', 'Ortografía'],
    datasets: [{
      label: 'Percentil Promedio',
      data: data,
      backgroundColor: 'rgba(59, 130, 246, 0.2)',
      borderColor: 'rgba(59, 130, 246, 1)',
      borderWidth: 2,
      pointBackgroundColor: 'rgba(59, 130, 246, 1)',
      pointBorderColor: '#fff',
      pointHoverBackgroundColor: '#fff',
      pointHoverBorderColor: 'rgba(59, 130, 246, 1)'
    }]
  };
};

const generarTendencias = () => {
  const tendencias = [];
  const fechaActual = new Date();
  for (let i = 5; i >= 0; i--) {
    const fecha = new Date(fechaActual);
    fecha.setMonth(fecha.getMonth() - i);
    tendencias.push({
      fecha: fecha.toISOString().slice(0, 7), // Formato YYYY-MM
      name: fecha.toLocaleDateString('es-ES', { month: 'short', year: 'numeric' }),
      promedio: parseFloat((70 + Math.random() * 15).toFixed(2)),
      evaluaciones: Math.floor(Math.random() * 50) + 20,
      rendimiento: parseFloat((70 + Math.random() * 15).toFixed(2)),
      participacion: Math.floor(Math.random() * 20) + 80,
      satisfaccion: parseFloat((4.0 + Math.random() * 1).toFixed(1))
    });
  }
  return tendencias;
};

// --- Objeto del Servicio ---

const DashboardService = {
  /**
   * Obtiene todas las piezas de datos para el dashboard desde Supabase.
   * Usa las vistas especializadas del dashboard para datos en tiempo real.
   */
  async fetchDashboardData(filters = {}) {
    console.log('📊 [DashboardService] Obteniendo datos REALES del dashboard con filtros:', filters);
    try {
      // Obtener estadísticas generales desde la vista especializada
      const { data: estadisticasData, error: estadisticasError } = await supabase
        .from('dashboard_estadisticas_generales')
        .select('*')
        .single();

      if (estadisticasError) {
        console.warn('⚠️ Error obteniendo estadísticas generales:', estadisticasError);
        throw estadisticasError;
      }

      // Obtener perfil institucional desde la vista especializada
      const { data: perfilData, error: perfilError } = await supabase
        .from('dashboard_perfil_institucional')
        .select('*')
        .order('codigo');

      if (perfilError) {
        console.warn('⚠️ Error obteniendo perfil institucional:', perfilError);
        throw perfilError;
      }

      // Obtener distribución por nivel
      const { data: distribucionData, error: distribucionError } = await supabase
        .from('dashboard_estudiantes_por_nivel')
        .select('*');

      if (distribucionError) {
        console.warn('⚠️ Error obteniendo distribución por nivel:', distribucionError);
      }

      // Procesar datos para el formato esperado por la UI
      const estadisticasGenerales = {
        total_pacientes: estadisticasData.total_pacientes,
        pacientes_evaluados: estadisticasData.pacientes_evaluados,
        total_evaluaciones: estadisticasData.total_evaluaciones,
        percentil_promedio_general: parseFloat(estadisticasData.percentil_promedio_general),
        evaluaciones_ultimo_mes: estadisticasData.evaluaciones_ultimo_mes,
        evaluaciones_ultima_semana: estadisticasData.evaluaciones_ultima_semana,
        ultima_actualizacion: new Date().toISOString()
      };

      // Debug: verificar datos antes de procesar KPIs
      console.log('🔍 [DashboardService] Datos estadísticas:', estadisticasData);
      console.log('🔍 [DashboardService] Datos perfil:', perfilData);
      // Calcular KPIs desde datos reales
      const topAptitude = perfilData.reduce((max, apt) =>
        parseFloat(apt.percentil_promedio) > parseFloat(max.percentil_promedio) ? apt : max
      );
      const bottomAptitude = perfilData.reduce((min, apt) =>
        parseFloat(apt.percentil_promedio) < parseFloat(min.percentil_promedio) ? apt : min
      );

      const kpiData = {
        averageScore: parseFloat(estadisticasData.percentil_promedio_general),
        completionRate: estadisticasData.pacientes_evaluados > 0 ?
          (estadisticasData.pacientes_evaluados / estadisticasData.total_pacientes * 100) : 0,
        topAptitude: {
          name: topAptitude.aptitud_nombre,
          score: parseFloat(topAptitude.percentil_promedio)
        },
        bottomAptitude: {
          name: bottomAptitude.aptitud_nombre,
          score: parseFloat(bottomAptitude.percentil_promedio)
        },
      };

      // Procesar perfil institucional para gráficos
      const datosPerfilInstitucional = {
        labels: perfilData.map(apt => apt.aptitud_nombre),
        datasets: [{
          label: 'Percentil Promedio',
          data: perfilData.map(apt => parseFloat(apt.percentil_promedio)),
          backgroundColor: 'rgba(59, 130, 246, 0.2)',
          borderColor: 'rgba(59, 130, 246, 1)',
          borderWidth: 2,
          pointBackgroundColor: 'rgba(59, 130, 246, 1)',
          pointBorderColor: '#fff',
          pointHoverBackgroundColor: '#fff',
          pointHoverBorderColor: 'rgba(59, 130, 246, 1)'
        }]
      };

      // Procesar distribución por nivel
      const datosDistribucionNivel = distribucionData ? distribucionData.map((nivel, index) => ({
        name: nivel.nivel_nombre,
        value: nivel.total_estudiantes,
        color: ['#3B82F6', '#10B981', '#F59E0B', '#EF4444'][index % 4]
      })) : [];

      // Generar alertas basadas en datos reales
      const alertsData = [];

      // Alerta si hay aptitudes con rendimiento bajo
      const aptitudesBajas = perfilData.filter(apt => parseFloat(apt.percentil_promedio) < 50);
      if (aptitudesBajas.length > 0) {
        alertsData.push({
          id: 1,
          type: 'warning',
          message: `${aptitudesBajas.length} aptitud(es) con rendimiento por debajo del percentil 50: ${aptitudesBajas.map(a => a.aptitud_nombre).join(', ')}`
        });
      }

      // Alerta de evaluaciones recientes
      if (estadisticasData.evaluaciones_ultima_semana > 0) {
        alertsData.push({
          id: 2,
          type: 'info',
          message: `Se han completado ${estadisticasData.evaluaciones_ultima_semana} evaluaciones esta semana`
        });
      }

      const processedData = {
        estadisticasGenerales,
        kpiData,
        datosDistribucionNivel,
        datosPerfilInstitucional,
        trendData: generarTendencias(), // Temporal hasta implementar vista de tendencias
        alertsData,
        comparisonData: generarDatosComparativos(),
        distributionData: datosDistribucionNivel,
        lastUpdate: new Date().toISOString(),
        isRealData: true,
        syncStatus: 'synced'
      };

      console.log('✅ [DashboardService] Datos REALES cargados exitosamente:', processedData);
      return processedData;

    } catch (error) {
      console.error("❌ [DashboardService] Error obteniendo datos reales, usando fallback:", error);

      // Fallback a datos simulados si hay error
      const estadisticasGenerales = generarEstadisticasGenerales();
      return {
        estadisticasGenerales,
        kpiData: {
          averageScore: estadisticasGenerales.percentil_promedio_general,
          completionRate: 95.4,
          topAptitude: { name: 'Atención', score: 81.5 },
          bottomAptitude: { name: 'Mecánico', score: 67.9 },
        },
        datosDistribucionNivel: generarDistribucionNivel(),
        datosPerfilInstitucional: generarPerfilInstitucional(),
        trendData: generarTendencias(),
        alertsData: [
          { id: 1, type: 'error', message: 'Error conectando con base de datos. Mostrando datos simulados.' }
        ],
        lastUpdate: new Date().toISOString(),
        isRealData: false
      };
    }
  },

  /**
   * Obtener datos para análisis de tendencias (versión simplificada)
   */
  async getTrendsData() {
    console.log('📊 [DashboardService] Obteniendo datos de tendencias...');
    try {
      // Usar consultas simples sin joins complejos
      const { data: resultados, error: resultadosError } = await supabase
        .from('resultados')
        .select('percentil, aptitud_id, evaluacion_id');

      if (resultadosError) {
        console.warn('⚠️ Error obteniendo resultados:', resultadosError);
        throw resultadosError;
      }

      const { data: evaluaciones, error: evaluacionesError } = await supabase
        .from('evaluaciones')
        .select('id, fecha_fin')
        .eq('estado', 'completada');

      if (evaluacionesError) {
        console.warn('⚠️ Error obteniendo evaluaciones:', evaluacionesError);
        throw evaluacionesError;
      }

      const { data: aptitudes, error: aptitudesError } = await supabase
        .from('aptitudes')
        .select('id, codigo, nombre');

      if (aptitudesError) {
        console.warn('⚠️ Error obteniendo aptitudes:', aptitudesError);
        throw aptitudesError;
      }

      // Crear mapas para joins manuales
      const evaluacionesMap = new Map(evaluaciones.map(e => [e.id, e]));
      const aptitudesMap = new Map(aptitudes.map(a => [a.id, a]));

      // Procesar datos
      const tendenciasPorMes = {};
      resultados.forEach(resultado => {
        const evaluacion = evaluacionesMap.get(resultado.evaluacion_id);
        const aptitud = aptitudesMap.get(resultado.aptitud_id);

        if (evaluacion && aptitud && evaluacion.fecha_fin) {
          const fecha = new Date(evaluacion.fecha_fin);
          const mesKey = `${fecha.getFullYear()}-${String(fecha.getMonth() + 1).padStart(2, '0')}`;
          const aptitudKey = aptitud.codigo;

          if (!tendenciasPorMes[mesKey]) {
            tendenciasPorMes[mesKey] = {};
          }
          if (!tendenciasPorMes[mesKey][aptitudKey]) {
            tendenciasPorMes[mesKey][aptitudKey] = [];
          }
          tendenciasPorMes[mesKey][aptitudKey].push(resultado.percentil);
        }
      });

      // Calcular promedios por mes
      const tendenciasFinales = Object.entries(tendenciasPorMes).map(([mes, aptitudes]) => {
        const promediosAptitudes = {};
        Object.entries(aptitudes).forEach(([aptitud, percentiles]) => {
          promediosAptitudes[aptitud] = percentiles.reduce((sum, p) => sum + p, 0) / percentiles.length;
        });

        const fechaObj = new Date(mes + '-01');
        return {
          fecha: mes,
          name: fechaObj.toLocaleDateString('es-ES', { month: 'short', year: 'numeric' }),
          rendimiento: Object.values(promediosAptitudes).reduce((sum, val) => sum + val, 0) / Object.values(promediosAptitudes).length || 0,
          participacion: Math.floor(Math.random() * 20) + 80, // Temporal
          satisfaccion: parseFloat((4.0 + Math.random() * 1).toFixed(1)), // Temporal
          ...promediosAptitudes
        };
      }).sort((a, b) => a.fecha.localeCompare(b.fecha));

      console.log('✅ [DashboardService] Tendencias procesadas:', tendenciasFinales);
      return tendenciasFinales;

    } catch (error) {
      console.error('❌ Error obteniendo tendencias:', error);
      return generarTendencias(); // Fallback
    }
  },

  /**
   * Obtener datos para análisis estadístico interactivo
   */
  async getStatisticalAnalysisData() {
    console.log('📈 [DashboardService] Obteniendo datos para análisis estadístico...');
    try {
      // Usar consultas simples sin joins complejos
      const { data: resultados, error: resultadosError } = await supabase
        .from('resultados')
        .select('aptitud_id, puntaje_directo, percentil, evaluacion_id');

      if (resultadosError) {
        console.warn('⚠️ Error obteniendo resultados:', resultadosError);
        throw resultadosError;
      }

      const { data: evaluaciones, error: evaluacionesError } = await supabase
        .from('evaluaciones')
        .select('id, paciente_id, fecha_fin')
        .eq('estado', 'completada');

      if (evaluacionesError) {
        console.warn('⚠️ Error obteniendo evaluaciones:', evaluacionesError);
        throw evaluacionesError;
      }

      const { data: pacientes, error: pacientesError } = await supabase
        .from('pacientes')
        .select('id, genero, fecha_nacimiento');

      if (pacientesError) {
        console.warn('⚠️ Error obteniendo pacientes:', pacientesError);
        throw pacientesError;
      }

      const { data: aptitudes, error: aptitudesError } = await supabase
        .from('aptitudes')
        .select('id, codigo, nombre');

      if (aptitudesError) {
        console.warn('⚠️ Error obteniendo aptitudes:', aptitudesError);
        throw aptitudesError;
      }

      // Crear mapas para joins manuales
      const evaluacionesMap = new Map(evaluaciones.map(e => [e.id, e]));
      const pacientesMap = new Map(pacientes.map(p => [p.id, p]));
      const aptitudesMap = new Map(aptitudes.map(a => [a.id, a]));

      // Procesar datos para análisis estadístico
      const datosProcessados = resultados.map(resultado => {
        const evaluacion = evaluacionesMap.get(resultado.evaluacion_id);
        const paciente = evaluacion ? pacientesMap.get(evaluacion.paciente_id) : null;
        const aptitud = aptitudesMap.get(resultado.aptitud_id);

        if (evaluacion && paciente && aptitud) {
          return {
            aptitud_codigo: aptitud.codigo,
            aptitud_nombre: aptitud.nombre,
            puntaje_directo: resultado.puntaje_directo,
            percentil: resultado.percentil,
            genero: paciente.genero,
            edad: new Date().getFullYear() - new Date(paciente.fecha_nacimiento).getFullYear(),
            fecha_evaluacion: evaluacion.fecha_fin
          };
        }
        return null;
      }).filter(Boolean);

      return datosProcessados;

    } catch (error) {
      console.error('❌ Error obteniendo datos estadísticos:', error);
      return this.generateSimulatedStatisticalData();
    }
  },

  /**
   * Generar datos estadísticos simulados para fallback
   */
  generateSimulatedStatisticalData() {
    const aptitudes = ['V', 'E', 'A', 'R', 'N', 'M', 'O'];
    const aptitudesNombres = {
      'V': 'Aptitud Verbal',
      'E': 'Aptitud Espacial',
      'A': 'Atención',
      'R': 'Razonamiento',
      'N': 'Aptitud Numérica',
      'M': 'Aptitud Mecánica',
      'O': 'Ortografía'
    };

    const datos = [];
    for (let i = 0; i < 50; i++) {
      const aptitud = aptitudes[Math.floor(Math.random() * aptitudes.length)];
      datos.push({
        aptitud_codigo: aptitud,
        aptitud_nombre: aptitudesNombres[aptitud],
        puntaje_directo: Math.floor(Math.random() * 40) + 10,
        percentil: Math.floor(Math.random() * 100) + 1,
        genero: Math.random() > 0.5 ? 'masculino' : 'femenino',
        edad: Math.floor(Math.random() * 10) + 10,
        fecha_evaluacion: new Date(Date.now() - Math.random() * 30 * 24 * 60 * 60 * 1000).toISOString()
      });
    }
    return datos;
  },

  /**
   * Aplicar filtros (actualmente vuelve a llamar a fetchDashboardData)
   */
  async applyFilters(filters) {
    console.log('🔍 [DashboardService] Aplicando filtros:', filters);
    return await this.fetchDashboardData(filters);
  }
};

export default DashboardService;
