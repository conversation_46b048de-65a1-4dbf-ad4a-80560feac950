/**
 * @file DataSyncService.js
 * @description Servicio para sincronización avanzada de datos con Supabase
 * Utiliza el MCP de Supabase para operaciones de sincronización más robustas
 */

import supabase from '../api/supabaseClient.js';

const DataSyncService = {
  /**
   * Verifica la conectividad con la base de datos
   */
  async checkConnection() {
    try {
      const { data, error } = await supabase
        .from('aptitudes')
        .select('count')
        .limit(1);
      
      return { connected: !error, error };
    } catch (error) {
      return { connected: false, error: error.message };
    }
  },

  /**
   * Sincroniza y valida las vistas del dashboard
   */
  async validateDashboardViews() {
    const views = [
      'dashboard_estadisticas_generales',
      'dashboard_perfil_institucional', 
      'dashboard_estudiantes_por_nivel',
      'dashboard_comparativa_genero',
      'dashboard_correlacion_aptitudes',
      'dashboard_distribucion_rendimiento',
      'dashboard_perfil_por_nivel'
    ];

    const results = {};
    
    for (const view of views) {
      try {
        const { data, error } = await supabase
          .from(view)
          .select('*')
          .limit(1);
        
        results[view] = {
          exists: !error,
          hasData: data && data.length > 0,
          error: error?.message
        };
      } catch (error) {
        results[view] = {
          exists: false,
          hasData: false,
          error: error.message
        };
      }
    }
    
    return results;
  },

  /**
   * Obtiene estadísticas de sincronización
   */
  async getSyncStats() {
    try {
      const [
        { data: pacientes },
        { data: evaluaciones },
        { data: resultados },
        { data: aptitudes }
      ] = await Promise.all([
        supabase.from('pacientes').select('id', { count: 'exact' }),
        supabase.from('evaluaciones').select('id', { count: 'exact' }),
        supabase.from('resultados').select('id', { count: 'exact' }),
        supabase.from('aptitudes').select('id', { count: 'exact' })
      ]);

      return {
        totalPacientes: pacientes?.length || 0,
        totalEvaluaciones: evaluaciones?.length || 0,
        totalResultados: resultados?.length || 0,
        totalAptitudes: aptitudes?.length || 0,
        lastSync: new Date().toISOString()
      };
    } catch (error) {
      console.error('Error obteniendo estadísticas de sync:', error);
      return null;
    }
  },

  /**
   * Fuerza la actualización de las vistas materializadas (si existen)
   */
  async refreshMaterializedViews() {
    try {
      // Verificar si existen vistas materializadas
      const { data: materializedViews } = await supabase
        .rpc('get_materialized_views');
      
      if (materializedViews && materializedViews.length > 0) {
        const refreshPromises = materializedViews
          .filter(view => view.startsWith('dashboard_'))
          .map(view => supabase.rpc('refresh_materialized_view', { view_name: view }));
        
        await Promise.all(refreshPromises);
        return { success: true, refreshed: materializedViews.length };
      }
      
      return { success: true, refreshed: 0, message: 'No materialized views found' };
    } catch (error) {
      console.warn('Error refreshing materialized views:', error);
      return { success: false, error: error.message };
    }
  },

  /**
   * Ejecuta una sincronización completa del dashboard
   */
  async fullDashboardSync() {
    console.log('🔄 Iniciando sincronización completa del dashboard...');
    
    const syncResult = {
      timestamp: new Date().toISOString(),
      connection: null,
      views: null,
      stats: null,
      materialized: null,
      success: false
    };

    try {
      // 1. Verificar conexión
      syncResult.connection = await this.checkConnection();
      if (!syncResult.connection.connected) {
        throw new Error('No se pudo conectar a la base de datos');
      }

      // 2. Validar vistas
      syncResult.views = await this.validateDashboardViews();
      
      // 3. Obtener estadísticas
      syncResult.stats = await this.getSyncStats();
      
      // 4. Refrescar vistas materializadas
      syncResult.materialized = await this.refreshMaterializedViews();
      
      syncResult.success = true;
      console.log('✅ Sincronización completa exitosa');
      
    } catch (error) {
      console.error('❌ Error en sincronización completa:', error);
      syncResult.error = error.message;
    }

    return syncResult;
  },

  /**
   * Monitorea la salud de los datos del dashboard
   */
  async healthCheck() {
    const health = {
      timestamp: new Date().toISOString(),
      overall: 'healthy',
      issues: [],
      recommendations: []
    };

    try {
      // Verificar datos básicos
      const stats = await this.getSyncStats();
      if (!stats) {
        health.issues.push('No se pudieron obtener estadísticas básicas');
        health.overall = 'unhealthy';
      }

      // Verificar vistas del dashboard
      const views = await this.validateDashboardViews();
      const failedViews = Object.entries(views)
        .filter(([_, status]) => !status.exists || !status.hasData)
        .map(([view, _]) => view);

      if (failedViews.length > 0) {
        health.issues.push(`Vistas con problemas: ${failedViews.join(', ')}`);
        health.overall = failedViews.length > 3 ? 'unhealthy' : 'degraded';
      }

      // Recomendaciones
      if (stats && stats.totalResultados === 0) {
        health.recommendations.push('No hay resultados de evaluaciones. Considere ejecutar algunas evaluaciones de prueba.');
      }

      if (failedViews.length > 0) {
        health.recommendations.push('Ejecute una sincronización completa para reparar las vistas.');
      }

    } catch (error) {
      health.overall = 'unhealthy';
      health.issues.push(`Error en health check: ${error.message}`);
    }

    return health;
  }
};

export default DataSyncService;