/**
 * @file DigitalSignatureService.js
 * @description Servicio para implementar firmas digitales en PDFs de informes BAT-7
 * Incluye firma de psicólogo, timestamp, y validación de integridad
 */

// import CryptoJS from 'crypto-js'; // Comentado temporalmente hasta instalar dependencia

// Implementación temporal de hash sin dependencias externas
const createHash = (data) => {
  let hash = 0;
  if (data.length === 0) return hash.toString();
  for (let i = 0; i < data.length; i++) {
    const char = data.charCodeAt(i);
    hash = ((hash << 5) - hash) + char;
    hash = hash & hash; // Convert to 32bit integer
  }
  return Math.abs(hash).toString(16);
};

class DigitalSignatureService {
  constructor() {
    this.signatures = new Map();
    this.certificates = new Map();
    this.signatureHistory = [];
  }

  /**
   * Registrar certificado de psicólogo
   */
  registerPsychologistCertificate(psychologistId, certificateData) {
    const certificate = {
      id: psychologistId,
      nombre: certificateData.nombre,
      apellido: certificateData.apellido,
      numeroLicencia: certificateData.numeroLicencia,
      institucion: certificateData.institucion,
      email: certificateData.email,
      fechaExpedicion: certificateData.fechaExpedicion,
      fechaVencimiento: certificateData.fechaVencimiento,
      autoridad: certificateData.autoridad || 'Colegio de Psicólogos',
      publicKey: this.generatePublicKey(psychologistId),
      privateKey: this.generatePrivateKey(psychologistId),
      isValid: true,
      createdAt: new Date().toISOString()
    };

    this.certificates.set(psychologistId, certificate);
    console.log(`🔐 [DigitalSignature] Certificado registrado para: ${certificate.nombre} ${certificate.apellido}`);
    
    return certificate;
  }

  /**
   * Crear firma digital para un informe
   */
  async createDigitalSignature(reportData, psychologistId, signatureOptions = {}) {
    try {
      const certificate = this.certificates.get(psychologistId);
      if (!certificate) {
        throw new Error('Certificado de psicólogo no encontrado');
      }

      if (!this.isCertificateValid(certificate)) {
        throw new Error('Certificado de psicólogo expirado o inválido');
      }

      const {
        includeTimestamp = true,
        includeLocation = true,
        signatureReason = 'Validación profesional de informe psicológico',
        contactInfo = certificate.email
      } = signatureOptions;

      // Generar hash del contenido del informe
      const reportHash = this.generateReportHash(reportData);
      
      // Crear timestamp
      const timestamp = new Date().toISOString();
      
      // Crear datos de la firma
      const signatureData = {
        reportId: reportData.id || `report_${Date.now()}`,
        reportHash,
        psychologistId,
        psychologistName: `${certificate.nombre} ${certificate.apellido}`,
        licenseNumber: certificate.numeroLicencia,
        institution: certificate.institucion,
        timestamp,
        reason: signatureReason,
        location: includeLocation ? this.getSignatureLocation() : null,
        contactInfo,
        version: '1.0'
      };

      // Generar firma criptográfica
      const digitalSignature = this.generateCryptographicSignature(signatureData, certificate.privateKey);
      
      // Crear objeto de firma completo
      const signature = {
        ...signatureData,
        signature: digitalSignature,
        certificateFingerprint: this.generateCertificateFingerprint(certificate),
        signatureId: this.generateSignatureId(),
        isValid: true
      };

      // Almacenar firma
      this.signatures.set(signature.signatureId, signature);
      this.signatureHistory.push({
        signatureId: signature.signatureId,
        reportId: signature.reportId,
        psychologistId,
        timestamp,
        action: 'created'
      });

      console.log(`✅ [DigitalSignature] Firma digital creada: ${signature.signatureId}`);
      return signature;

    } catch (error) {
      console.error('❌ [DigitalSignature] Error creando firma digital:', error);
      throw error;
    }
  }

  /**
   * Validar firma digital
   */
  validateDigitalSignature(signatureId, reportData) {
    try {
      const signature = this.signatures.get(signatureId);
      if (!signature) {
        return {
          isValid: false,
          error: 'Firma no encontrada',
          details: null
        };
      }

      const certificate = this.certificates.get(signature.psychologistId);
      if (!certificate) {
        return {
          isValid: false,
          error: 'Certificado no encontrado',
          details: signature
        };
      }

      // Verificar integridad del documento
      const currentHash = this.generateReportHash(reportData);
      const hashMatch = currentHash === signature.reportHash;

      // Verificar firma criptográfica
      const signatureValid = this.verifyCryptographicSignature(
        signature,
        certificate.publicKey
      );

      // Verificar validez del certificado
      const certificateValid = this.isCertificateValid(certificate);

      // Verificar timestamp (no debe ser futuro)
      const timestampValid = new Date(signature.timestamp) <= new Date();

      const isValid = hashMatch && signatureValid && certificateValid && timestampValid;

      return {
        isValid,
        details: {
          hashMatch,
          signatureValid,
          certificateValid,
          timestampValid,
          signatureInfo: signature,
          certificateInfo: certificate
        },
        error: isValid ? null : 'Firma inválida o documento modificado'
      };

    } catch (error) {
      console.error('❌ [DigitalSignature] Error validando firma:', error);
      return {
        isValid: false,
        error: error.message,
        details: null
      };
    }
  }

  /**
   * Generar representación visual de la firma para PDF
   */
  generateSignatureVisual(signature, options = {}) {
    const {
      width = 200,
      height = 80,
      includeQR = true,
      includeLogo = false
    } = options;

    const certificate = this.certificates.get(signature.psychologistId);
    
    return {
      type: 'signature_block',
      width,
      height,
      content: {
        header: 'FIRMA DIGITAL VERIFICADA',
        psychologist: `${certificate.nombre} ${certificate.apellido}`,
        license: `Lic. ${certificate.numeroLicencia}`,
        institution: certificate.institucion,
        timestamp: new Date(signature.timestamp).toLocaleString('es-ES'),
        signatureId: signature.signatureId.substring(0, 8).toUpperCase(),
        qrCode: includeQR ? this.generateSignatureQR(signature.signatureId) : null,
        validationUrl: `https://bat7.verify.com/signature/${signature.signatureId}`
      },
      style: {
        border: '2px solid #059669',
        backgroundColor: '#f0fdf4',
        textColor: '#065f46',
        fontSize: 10,
        fontFamily: 'Arial'
      }
    };
  }

  /**
   * Generar hash del contenido del informe
   */
  generateReportHash(reportData) {
    const contentString = JSON.stringify({
      patientId: reportData.id,
      nombre: reportData.nombre,
      apellido: reportData.apellido,
      documento: reportData.documento,
      puntuaciones: reportData.puntuaciones,
      fechaEvaluacion: reportData.fechaEvaluacion
    });
    
    return createHash(contentString);
  }

  /**
   * Generar firma criptográfica
   */
  generateCryptographicSignature(data, privateKey) {
    const dataString = JSON.stringify(data);
    return createHash(dataString + privateKey);
  }

  /**
   * Verificar firma criptográfica
   */
  verifyCryptographicSignature(signatureData, publicKey) {
    try {
      const { signature, ...dataToVerify } = signatureData;
      const expectedSignature = this.generateCryptographicSignature(dataToVerify, publicKey);
      return signature === expectedSignature;
    } catch (error) {
      return false;
    }
  }

  /**
   * Generar claves públicas y privadas (simulado)
   */
  generatePublicKey(psychologistId) {
    return createHash(`public_${psychologistId}_${Date.now()}`);
  }

  generatePrivateKey(psychologistId) {
    return createHash(`private_${psychologistId}_${Date.now()}`);
  }

  /**
   * Generar fingerprint del certificado
   */
  generateCertificateFingerprint(certificate) {
    const certString = `${certificate.id}_${certificate.numeroLicencia}_${certificate.publicKey}`;
    return createHash(certString).substring(0, 16).toUpperCase();
  }

  /**
   * Generar ID único de firma
   */
  generateSignatureId() {
    return createHash(`signature_${Date.now()}_${Math.random()}`).substring(0, 16);
  }

  /**
   * Verificar validez del certificado
   */
  isCertificateValid(certificate) {
    if (!certificate.isValid) return false;
    
    const now = new Date();
    const expiration = new Date(certificate.fechaVencimiento);
    
    return now <= expiration;
  }

  /**
   * Obtener ubicación de la firma (simulado)
   */
  getSignatureLocation() {
    return 'Sistema BAT-7, Evaluación Psicológica Digital';
  }

  /**
   * Generar código QR para validación de firma
   */
  generateSignatureQR(signatureId) {
    // En implementación real, se usaría una librería de QR
    return {
      data: `https://bat7.verify.com/signature/${signatureId}`,
      size: 60,
      format: 'base64'
    };
  }

  /**
   * Exportar certificados y firmas para backup
   */
  exportSignatureData() {
    return {
      certificates: Object.fromEntries(this.certificates),
      signatures: Object.fromEntries(this.signatures),
      history: this.signatureHistory,
      exportedAt: new Date().toISOString()
    };
  }

  /**
   * Importar certificados y firmas desde backup
   */
  importSignatureData(data) {
    try {
      if (data.certificates) {
        this.certificates = new Map(Object.entries(data.certificates));
      }
      if (data.signatures) {
        this.signatures = new Map(Object.entries(data.signatures));
      }
      if (data.history) {
        this.signatureHistory = data.history;
      }
      
      console.log('✅ [DigitalSignature] Datos de firma importados exitosamente');
      return true;
    } catch (error) {
      console.error('❌ [DigitalSignature] Error importando datos de firma:', error);
      return false;
    }
  }

  /**
   * Revocar certificado
   */
  revokeCertificate(psychologistId, reason = 'Revocado por administrador') {
    const certificate = this.certificates.get(psychologistId);
    if (certificate) {
      certificate.isValid = false;
      certificate.revocationDate = new Date().toISOString();
      certificate.revocationReason = reason;
      
      console.log(`🚫 [DigitalSignature] Certificado revocado: ${psychologistId}`);
      return true;
    }
    return false;
  }

  /**
   * Obtener historial de firmas
   */
  getSignatureHistory(filters = {}) {
    let history = [...this.signatureHistory];
    
    if (filters.psychologistId) {
      history = history.filter(h => h.psychologistId === filters.psychologistId);
    }
    
    if (filters.dateFrom) {
      history = history.filter(h => new Date(h.timestamp) >= new Date(filters.dateFrom));
    }
    
    if (filters.dateTo) {
      history = history.filter(h => new Date(h.timestamp) <= new Date(filters.dateTo));
    }
    
    return history.sort((a, b) => new Date(b.timestamp) - new Date(a.timestamp));
  }
}

// Instancia singleton
const digitalSignatureService = new DigitalSignatureService();

// Registrar certificados de ejemplo
digitalSignatureService.registerPsychologistCertificate('psi_001', {
  nombre: 'Dr. Juan',
  apellido: 'Pérez',
  numeroLicencia: 'PSI-2024-001',
  institucion: 'Colegio de Psicólogos',
  email: '<EMAIL>',
  fechaExpedicion: '2024-01-01',
  fechaVencimiento: '2026-12-31',
  autoridad: 'Colegio Nacional de Psicólogos'
});

digitalSignatureService.registerPsychologistCertificate('psi_002', {
  nombre: 'Dra. María',
  apellido: 'González',
  numeroLicencia: 'PSI-2024-002',
  institucion: 'Centro de Evaluación Psicológica',
  email: '<EMAIL>',
  fechaExpedicion: '2024-01-01',
  fechaVencimiento: '2026-12-31',
  autoridad: 'Colegio Nacional de Psicólogos'
});

export default digitalSignatureService;
