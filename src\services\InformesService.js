/**
 * @file InformesService.js
 * @description Servicio para manejar la generación y gestión de informes manuales
 */

import supabase from '../api/supabaseClient';
import { toast } from 'react-toastify';

class InformesService {
  /**
   * Generar informe completo para un paciente
   */
  async generarInformeCompleto(pacienteId, titulo = null, descripcion = null) {
    try {
      console.log('🚨 [InformesService] MÉTODO DIRECTO - Generando informe para paciente:', pacienteId);

      // Usar la función RPC que SÍ funciona
      const { data, error } = await supabase.rpc('generar_informe_directo', {
        p_paciente_id: pacienteId,
        p_titulo: titulo || `Informe BAT-7 - ${new Date().toLocaleDateString('es-ES')}`
      });

      if (error) {
        console.error('❌ [InformesService] Error con función directa:', error);
        throw error;
      }

      console.log('✅ [InformesService] Informe generado exitosamente con ID:', data);
      toast.success('Informe generado exitosamente con datos reales');
      return data; // Retorna el ID del informe generado
    } catch (error) {
      console.error('❌ [InformesService] Error generando informe:', error);
      toast.error('Error al generar el informe: ' + error.message);
      throw error;
    }
  }

  /**
   * Generar informe individual para un resultado específico
   */
  async generarInformeIndividual(resultadoId, titulo = null, descripcion = null) {
    try {
      console.log('📄 [InformesService] Generando informe individual para resultado:', resultadoId);

      // Obtener datos del resultado específico
      const { data: resultado, error: resultadoError } = await supabase
        .from('resultados')
        .select(`
          *,
          pacientes:paciente_id (
            id, nombre, apellido, documento, fecha_nacimiento, 
            sexo, nivel_educativo, institucion
          ),
          aptitudes:aptitud_id (
            codigo, nombre, descripcion
          )
        `)
        .eq('id', resultadoId)
        .single();

      if (resultadoError) throw resultadoError;

      if (!resultado) {
        throw new Error('Resultado no encontrado');
      }

      // Construir contenido del informe individual
      const contenidoInforme = {
        tipo: 'individual',
        paciente: resultado.pacientes,
        resultado: {
          id: resultado.id,
          puntaje_directo: resultado.puntaje_directo,
          percentil: resultado.percentil,
          errores: resultado.errores,
          tiempo_segundos: resultado.tiempo_segundos,
          concentracion: resultado.concentracion,
          fecha_evaluacion: resultado.created_at,
          aptitud: resultado.aptitudes
        },
        interpretacion: this.obtenerInterpretacion(resultado.percentil),
        fecha_generacion: new Date().toISOString()
      };

      const tituloGenerado = titulo || 
        `Informe ${resultado.aptitudes.nombre} - ${resultado.pacientes.nombre} ${resultado.pacientes.apellido}`;

      // Insertar el informe en la base de datos
      const { data: informeId, error: insertError } = await supabase
        .from('informes_generados')
        .insert({
          paciente_id: resultado.paciente_id,
          tipo_informe: 'individual',
          titulo: tituloGenerado,
          descripcion: descripcion || `Informe individual para test ${resultado.aptitudes.nombre}`,
          contenido: contenidoInforme,
          metadatos: {
            resultado_id: resultadoId,
            aptitud_codigo: resultado.aptitudes.codigo,
            metodo_generacion: 'manual'
          }
        })
        .select('id')
        .single();

      if (insertError) throw insertError;

      console.log('✅ [InformesService] Informe individual generado:', informeId.id);
      toast.success('Informe individual generado exitosamente');
      
      return informeId.id;
    } catch (error) {
      console.error('❌ [InformesService] Error generando informe individual:', error);
      toast.error('Error al generar el informe individual: ' + error.message);
      throw error;
    }
  }

  /**
   * Obtener todos los informes de un paciente
   */
  async obtenerInformesPaciente(pacienteId) {
    try {
      console.log('📋 [InformesService] ACCESO DIRECTO - Obteniendo informes para paciente:', pacienteId);

      // MÉTODO EXTREMO: Acceso directo a la tabla sin RPC
      const { data, error } = await supabase
        .from('informes_generados')
        .select(`
          id,
          titulo,
          descripcion,
          tipo_informe,
          estado,
          fecha_generacion,
          fecha_archivado,
          metadatos
        `)
        .eq('paciente_id', pacienteId)
        .neq('estado', 'eliminado')
        .order('fecha_generacion', { ascending: false });

      if (error) {
        console.error('❌ [InformesService] Error acceso directo:', error);
        throw error;
      }

      console.log('✅ [InformesService] ACCESO DIRECTO - Informes obtenidos:', data?.length || 0);

      // Si no hay informes, generar uno automáticamente con datos reales
      if (!data || data.length === 0) {
        console.log('🔄 [InformesService] No hay informes, generando automáticamente...');
        await this.generarInformeAutomaticoConDatosReales(pacienteId);

        // Volver a consultar después de generar
        const { data: newData, error: newError } = await supabase
          .from('informes_generados')
          .select(`
            id,
            titulo,
            descripcion,
            tipo_informe,
            estado,
            fecha_generacion,
            fecha_archivado,
            metadatos
          `)
          .eq('paciente_id', pacienteId)
          .neq('estado', 'eliminado')
          .order('fecha_generacion', { ascending: false });

        if (newError) throw newError;
        return newData || [];
      }

      return data || [];
    } catch (error) {
      console.error('❌ [InformesService] Error obteniendo informes:', error);
      toast.error('Error al cargar los informes: ' + error.message);
      throw error;
    }
  }

  /**
   * Obtener contenido completo de un informe
   */
  async obtenerInforme(informeId) {
    try {
      console.log('📄 [InformesService] Obteniendo informe:', informeId);

      const { data, error } = await supabase
        .from('informes_generados')
        .select('*')
        .eq('id', informeId)
        .single();

      if (error) throw error;

      if (!data) {
        throw new Error('Informe no encontrado');
      }

      console.log('✅ [InformesService] Informe obtenido exitosamente');
      return data;
    } catch (error) {
      console.error('❌ [InformesService] Error obteniendo informe:', error);
      toast.error('Error al cargar el informe');
      throw error;
    }
  }

  /**
   * Archivar un informe
   */
  async archivarInforme(informeId) {
    try {
      console.log('📦 [InformesService] Archivando informe:', informeId);

      const { error } = await supabase
        .from('informes_generados')
        .update({
          estado: 'archivado',
          fecha_archivado: new Date().toISOString()
        })
        .eq('id', informeId);

      if (error) throw error;

      console.log('✅ [InformesService] Informe archivado exitosamente');
      toast.success('Informe archivado exitosamente');
      
      return true;
    } catch (error) {
      console.error('❌ [InformesService] Error archivando informe:', error);
      toast.error('Error al archivar el informe');
      throw error;
    }
  }

  /**
   * Eliminar un informe (soft delete)
   */
  async eliminarInforme(informeId) {
    try {
      console.log('🗑️ [InformesService] Eliminando informe:', informeId);

      const { error } = await supabase
        .from('informes_generados')
        .update({
          estado: 'eliminado'
        })
        .eq('id', informeId);

      if (error) throw error;

      console.log('✅ [InformesService] Informe eliminado exitosamente');
      toast.success('Informe eliminado exitosamente');
      
      return true;
    } catch (error) {
      console.error('❌ [InformesService] Error eliminando informe:', error);
      toast.error('Error al eliminar el informe');
      throw error;
    }
  }

  /**
   * Obtener interpretación de un percentil
   */
  obtenerInterpretacion(percentil) {
    if (!percentil) return { nivel: 'N/A', descripcion: 'Sin datos suficientes' };

    if (percentil >= 90) {
      return {
        nivel: 'Muy Alto',
        descripcion: 'Rendimiento excepcional, muy por encima del promedio',
        color: 'green'
      };
    } else if (percentil >= 75) {
      return {
        nivel: 'Alto',
        descripcion: 'Rendimiento superior al promedio',
        color: 'blue'
      };
    } else if (percentil >= 25) {
      return {
        nivel: 'Promedio',
        descripcion: 'Rendimiento dentro del rango normal',
        color: 'yellow'
      };
    } else if (percentil >= 10) {
      return {
        nivel: 'Bajo',
        descripcion: 'Rendimiento por debajo del promedio',
        color: 'orange'
      };
    } else {
      return {
        nivel: 'Muy Bajo',
        descripción: 'Rendimiento significativamente por debajo del promedio',
        color: 'red'
      };
    }
  }

  /**
   * Generar informe comparativo entre múltiples pacientes
   */
  async generarInformeComparativo(pacienteIds, titulo = null, descripcion = null) {
    try {
      console.log('📊 [InformesService] Generando informe comparativo para pacientes:', pacienteIds);

      // Obtener datos de todos los pacientes
      const { data: pacientes, error: pacientesError } = await supabase
        .from('pacientes')
        .select(`
          id, nombre, apellido, documento, nivel_educativo, institucion,
          resultados:resultados!inner(
            id, puntaje_directo, percentil, errores, tiempo_segundos,
            aptitudes:aptitud_id(codigo, nombre)
          )
        `)
        .in('id', pacienteIds);

      if (pacientesError) throw pacientesError;

      const contenidoInforme = {
        tipo: 'comparativo',
        pacientes: pacientes,
        estadisticas_comparativas: this.calcularEstadisticasComparativas(pacientes),
        fecha_generacion: new Date().toISOString()
      };

      const tituloGenerado = titulo || `Informe Comparativo - ${pacientes.length} Estudiantes`;

      // Insertar el informe (usando el primer paciente como referencia)
      const { data: informeId, error: insertError } = await supabase
        .from('informes_generados')
        .insert({
          paciente_id: pacienteIds[0], // Usar el primer paciente como referencia
          tipo_informe: 'comparativo',
          titulo: tituloGenerado,
          descripcion: descripcion || 'Informe comparativo generado manualmente',
          contenido: contenidoInforme,
          metadatos: {
            pacientes_incluidos: pacienteIds,
            total_pacientes: pacienteIds.length,
            metodo_generacion: 'manual'
          }
        })
        .select('id')
        .single();

      if (insertError) throw insertError;

      console.log('✅ [InformesService] Informe comparativo generado:', informeId.id);
      toast.success('Informe comparativo generado exitosamente');
      
      return informeId.id;
    } catch (error) {
      console.error('❌ [InformesService] Error generando informe comparativo:', error);
      toast.error('Error al generar el informe comparativo');
      throw error;
    }
  }

  /**
   * Calcular estadísticas comparativas
   */
  calcularEstadisticasComparativas(pacientes) {
    const estadisticas = {};
    
    pacientes.forEach(paciente => {
      paciente.resultados.forEach(resultado => {
        const aptitud = resultado.aptitudes.codigo;
        if (!estadisticas[aptitud]) {
          estadisticas[aptitud] = {
            nombre: resultado.aptitudes.nombre,
            percentiles: [],
            puntajes_directos: []
          };
        }
        
        if (resultado.percentil) estadisticas[aptitud].percentiles.push(resultado.percentil);
        if (resultado.puntaje_directo) estadisticas[aptitud].puntajes_directos.push(resultado.puntaje_directo);
      });
    });

    // Calcular promedios y estadísticas
    Object.keys(estadisticas).forEach(aptitud => {
      const data = estadisticas[aptitud];
      data.percentil_promedio = data.percentiles.length > 0 
        ? data.percentiles.reduce((a, b) => a + b, 0) / data.percentiles.length 
        : 0;
      data.pd_promedio = data.puntajes_directos.length > 0
        ? data.puntajes_directos.reduce((a, b) => a + b, 0) / data.puntajes_directos.length
        : 0;
    });

    return estadisticas;
  }
  /**
   * MÉTODO DIRECTO: Generar informe usando función RPC que funciona
   */
  async generarInformeAutomaticoConDatosReales(pacienteId) {
    try {
      console.log('🚨 [InformesService] MÉTODO DIRECTO - Generando informe automático para:', pacienteId);

      // Usar la función RPC directa que SÍ funciona
      const { data, error } = await supabase.rpc('generar_informe_directo', {
        p_paciente_id: pacienteId,
        p_titulo: `Informe Automático BAT-7 - ${new Date().toLocaleDateString('es-ES')}`
      });

      if (error) {
        console.error('❌ [InformesService] Error con función directa:', error);
        throw error;
      }

      console.log('🎉 [InformesService] INFORME AUTOMÁTICO GENERADO:', data);
      toast.success('Informe generado automáticamente con datos reales');

      return data;
    } catch (error) {
      console.error('❌ [InformesService] Error generando informe automático:', error);
      toast.error('Error generando informe automático: ' + error.message);
      throw error;
    }
  }
}

export default new InformesService();
