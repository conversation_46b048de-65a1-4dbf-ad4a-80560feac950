/**
 * @file InterpretacionService.js
 * @description Servicio para generar interpretaciones cualitativas de aptitudes BAT-7
 */

class InterpretacionService {
  
  /**
   * Obtener interpretación cualitativa completa basada en resultados reales
   */
  static generarInterpretacionCualitativa(resultados) {
    if (!resultados || resultados.length === 0) {
      return {
        resumen_general: "No se encontraron resultados de evaluación para generar una interpretación.",
        aptitudes_destacadas: [],
        areas_mejora: [],
        recomendaciones: []
      };
    }

    // Analizar resultados por aptitud
    const analisisAptitudes = this.analizarAptitudes(resultados);
    
    // Generar interpretación general
    const resumenGeneral = this.generarResumenGeneral(analisisAptitudes);
    
    // Identificar aptitudes destacadas (percentil >= 75)
    const aptitudesDestacadas = this.identificarAptitudesDestacadas(analisisAptitudes);
    
    // Identificar áreas de mejora (percentil <= 25)
    const areasMejora = this.identificarAreasMejora(analisisAptitudes);
    
    // Generar recomendaciones
    const recomendaciones = this.generarRecomendaciones(analisisAptitudes);

    return {
      resumen_general: resumenGeneral,
      aptitudes_destacadas: aptitudesDestacadas,
      areas_mejora: areasMejora,
      recomendaciones: recomendaciones,
      analisis_detallado: analisisAptitudes
    };
  }

  /**
   * Analizar cada aptitud individualmente
   */
  static analizarAptitudes(resultados) {
    return resultados.map(resultado => {
      const aptitud = resultado.aptitud;
      const percentil = resultado.percentil;
      const puntajeDirecto = resultado.puntaje_directo;
      const errores = resultado.errores || 0;
      
      // Determinar nivel de rendimiento
      const nivel = this.determinarNivel(percentil);
      
      // Generar interpretación específica por aptitud
      const interpretacion = this.generarInterpretacionPorAptitud(aptitud.codigo, percentil, nivel);
      
      return {
        codigo: aptitud.codigo,
        nombre: aptitud.nombre,
        descripcion: aptitud.descripcion,
        percentil: percentil,
        puntaje_directo: puntajeDirecto,
        errores: errores,
        nivel: nivel,
        interpretacion: interpretacion
      };
    });
  }

  /**
   * Determinar nivel de rendimiento basado en percentil
   */
  static determinarNivel(percentil) {
    if (percentil >= 90) return { nivel: 'Muy Alto', color: 'text-green-700', bg: 'bg-green-100' };
    if (percentil >= 75) return { nivel: 'Alto', color: 'text-blue-700', bg: 'bg-blue-100' };
    if (percentil >= 50) return { nivel: 'Medio Alto', color: 'text-teal-700', bg: 'bg-teal-100' };
    if (percentil >= 25) return { nivel: 'Medio', color: 'text-yellow-700', bg: 'bg-yellow-100' };
    if (percentil >= 10) return { nivel: 'Bajo', color: 'text-orange-700', bg: 'bg-orange-100' };
    return { nivel: 'Muy Bajo', color: 'text-red-700', bg: 'bg-red-100' };
  }

  /**
   * Generar interpretación específica por aptitud
   */
  static generarInterpretacionPorAptitud(codigo, percentil, nivel) {
    const interpretaciones = {
      'V': {
        'Muy Alto': 'Excelente capacidad de comprensión verbal y manejo del lenguaje. Demuestra un vocabulario amplio y habilidades superiores para el razonamiento verbal.',
        'Alto': 'Buena capacidad verbal y de comprensión del lenguaje. Maneja adecuadamente conceptos verbales y puede expresarse con claridad.',
        'Medio Alto': 'Capacidad verbal por encima del promedio. Comprende bien las instrucciones verbales y puede comunicarse efectivamente.',
        'Medio': 'Capacidad verbal dentro del rango normal. Comprende instrucciones básicas y se comunica de manera adecuada.',
        'Bajo': 'Capacidad verbal por debajo del promedio. Puede presentar dificultades en la comprensión de conceptos verbales complejos.',
        'Muy Bajo': 'Capacidad verbal significativamente por debajo del promedio. Requiere apoyo adicional en el desarrollo de habilidades verbales.'
      },
      'E': {
        'Muy Alto': 'Excelente capacidad de visualización espacial y manipulación mental de objetos tridimensionales. Ideal para actividades que requieren pensamiento espacial.',
        'Alto': 'Buena capacidad espacial. Puede visualizar y manipular objetos mentalmente con facilidad.',
        'Medio Alto': 'Capacidad espacial por encima del promedio. Maneja bien las relaciones espaciales y la orientación.',
        'Medio': 'Capacidad espacial dentro del rango normal. Comprende relaciones espaciales básicas.',
        'Bajo': 'Capacidad espacial por debajo del promedio. Puede presentar dificultades con tareas que requieren visualización espacial.',
        'Muy Bajo': 'Capacidad espacial significativamente limitada. Requiere apoyo en el desarrollo de habilidades espaciales.'
      },
      'A': {
        'Muy Alto': 'Excelente capacidad de atención y concentración. Puede mantener el foco en tareas durante períodos prolongados sin distraerse.',
        'Alto': 'Buena capacidad atencional. Mantiene la concentración en tareas importantes y filtra distracciones efectivamente.',
        'Medio Alto': 'Capacidad atencional por encima del promedio. Generalmente mantiene el foco en las tareas.',
        'Medio': 'Capacidad atencional dentro del rango normal. Puede concentrarse en tareas de duración moderada.',
        'Bajo': 'Capacidad atencional por debajo del promedio. Puede distraerse con facilidad y tener dificultades para mantener el foco.',
        'Muy Bajo': 'Capacidad atencional significativamente limitada. Requiere estrategias específicas para mejorar la concentración.'
      },
      'R': {
        'Muy Alto': 'Excelente capacidad de razonamiento lógico y resolución de problemas. Puede analizar situaciones complejas y encontrar soluciones innovadoras.',
        'Alto': 'Buena capacidad de razonamiento. Analiza problemas de manera lógica y encuentra soluciones efectivas.',
        'Medio Alto': 'Capacidad de razonamiento por encima del promedio. Puede resolver problemas de complejidad moderada.',
        'Medio': 'Capacidad de razonamiento dentro del rango normal. Resuelve problemas básicos de manera adecuada.',
        'Bajo': 'Capacidad de razonamiento por debajo del promedio. Puede presentar dificultades con problemas complejos.',
        'Muy Bajo': 'Capacidad de razonamiento significativamente limitada. Requiere apoyo en el desarrollo del pensamiento lógico.'
      },
      'N': {
        'Muy Alto': 'Excelente capacidad numérica y de cálculo. Maneja operaciones matemáticas complejas con facilidad y precisión.',
        'Alto': 'Buena capacidad numérica. Realiza cálculos con precisión y comprende conceptos matemáticos.',
        'Medio Alto': 'Capacidad numérica por encima del promedio. Maneja operaciones matemáticas básicas con confianza.',
        'Medio': 'Capacidad numérica dentro del rango normal. Realiza cálculos básicos de manera adecuada.',
        'Bajo': 'Capacidad numérica por debajo del promedio. Puede presentar dificultades con operaciones matemáticas.',
        'Muy Bajo': 'Capacidad numérica significativamente limitada. Requiere apoyo adicional en el desarrollo de habilidades matemáticas.'
      },
      'M': {
        'Muy Alto': 'Excelente capacidad mecánica y comprensión de principios físicos. Ideal para actividades técnicas y de ingeniería.',
        'Alto': 'Buena capacidad mecánica. Comprende principios físicos y puede trabajar con herramientas y maquinaria.',
        'Medio Alto': 'Capacidad mecánica por encima del promedio. Maneja conceptos técnicos básicos.',
        'Medio': 'Capacidad mecánica dentro del rango normal. Comprende principios mecánicos básicos.',
        'Bajo': 'Capacidad mecánica por debajo del promedio. Puede presentar dificultades con conceptos técnicos.',
        'Muy Bajo': 'Capacidad mecánica significativamente limitada. Requiere apoyo en el desarrollo de habilidades técnicas.'
      },
      'O': {
        'Muy Alto': 'Excelente capacidad ortográfica y dominio del lenguaje escrito. Demuestra precisión en la escritura y conocimiento de reglas gramaticales.',
        'Alto': 'Buena capacidad ortográfica. Escribe con precisión y conoce las reglas básicas de ortografía.',
        'Medio Alto': 'Capacidad ortográfica por encima del promedio. Comete pocos errores en la escritura.',
        'Medio': 'Capacidad ortográfica dentro del rango normal. Maneja las reglas básicas de ortografía.',
        'Bajo': 'Capacidad ortográfica por debajo del promedio. Puede presentar errores frecuentes en la escritura.',
        'Muy Bajo': 'Capacidad ortográfica significativamente limitada. Requiere apoyo en el desarrollo de habilidades de escritura.'
      }
    };

    return interpretaciones[codigo]?.[nivel.nivel] || 'Interpretación no disponible para esta aptitud.';
  }

  /**
   * Generar resumen general
   */
  static generarResumenGeneral(analisisAptitudes) {
    const promedioPercentil = analisisAptitudes.reduce((sum, apt) => sum + apt.percentil, 0) / analisisAptitudes.length;
    const aptitudesAltas = analisisAptitudes.filter(apt => apt.percentil >= 75).length;
    const aptitudesBajas = analisisAptitudes.filter(apt => apt.percentil <= 25).length;

    let resumen = `El evaluado ha completado ${analisisAptitudes.length} pruebas de la batería BAT-7, `;
    resumen += `obteniendo un percentil promedio de ${Math.round(promedioPercentil)}. `;

    if (promedioPercentil >= 75) {
      resumen += "Los resultados indican un rendimiento general superior al promedio, ";
    } else if (promedioPercentil >= 50) {
      resumen += "Los resultados indican un rendimiento general dentro del rango normal-alto, ";
    } else if (promedioPercentil >= 25) {
      resumen += "Los resultados indican un rendimiento general dentro del rango normal, ";
    } else {
      resumen += "Los resultados indican un rendimiento general por debajo del promedio, ";
    }

    if (aptitudesAltas > 0) {
      resumen += `destacándose en ${aptitudesAltas} aptitud${aptitudesAltas > 1 ? 'es' : ''}. `;
    }

    if (aptitudesBajas > 0) {
      resumen += `Se identifican ${aptitudesBajas} área${aptitudesBajas > 1 ? 's' : ''} que requiere${aptitudesBajas > 1 ? 'n' : ''} atención especial.`;
    }

    return resumen;
  }

  /**
   * Identificar aptitudes destacadas
   */
  static identificarAptitudesDestacadas(analisisAptitudes) {
    return analisisAptitudes
      .filter(apt => apt.percentil >= 75)
      .sort((a, b) => b.percentil - a.percentil)
      .map(apt => ({
        nombre: apt.nombre,
        percentil: apt.percentil,
        interpretacion: apt.interpretacion
      }));
  }

  /**
   * Identificar áreas de mejora
   */
  static identificarAreasMejora(analisisAptitudes) {
    return analisisAptitudes
      .filter(apt => apt.percentil <= 25)
      .sort((a, b) => a.percentil - b.percentil)
      .map(apt => ({
        nombre: apt.nombre,
        percentil: apt.percentil,
        interpretacion: apt.interpretacion,
        recomendacion: this.generarRecomendacionEspecifica(apt.codigo)
      }));
  }

  /**
   * Generar recomendaciones específicas
   */
  static generarRecomendacionEspecifica(codigo) {
    const recomendaciones = {
      'V': 'Fomentar la lectura, ampliar vocabulario y practicar ejercicios de comprensión verbal.',
      'E': 'Realizar actividades de visualización espacial, puzzles 3D y ejercicios de orientación.',
      'A': 'Implementar técnicas de concentración, mindfulness y ejercicios de atención sostenida.',
      'R': 'Practicar resolución de problemas lógicos, acertijos y ejercicios de razonamiento.',
      'N': 'Reforzar conceptos matemáticos básicos y practicar cálculo mental.',
      'M': 'Explorar actividades técnicas, experimentos prácticos y manipulación de herramientas.',
      'O': 'Practicar escritura, dictados y revisar reglas ortográficas sistemáticamente.'
    };

    return recomendaciones[codigo] || 'Consultar con un especialista para recomendaciones específicas.';
  }

  /**
   * Generar recomendaciones generales
   */
  static generarRecomendaciones(analisisAptitudes) {
    const recomendaciones = [];
    const promedioPercentil = analisisAptitudes.reduce((sum, apt) => sum + apt.percentil, 0) / analisisAptitudes.length;

    // Recomendaciones basadas en el perfil general
    if (promedioPercentil >= 75) {
      recomendaciones.push("Considerar programas de enriquecimiento académico o actividades desafiantes.");
      recomendaciones.push("Explorar áreas de especialización basadas en las aptitudes más destacadas.");
    } else if (promedioPercentil <= 25) {
      recomendaciones.push("Implementar un plan de apoyo académico personalizado.");
      recomendaciones.push("Considerar evaluaciones adicionales para identificar necesidades específicas.");
    }

    // Recomendaciones específicas por aptitudes
    const aptitudesAltas = analisisAptitudes.filter(apt => apt.percentil >= 75);
    const aptitudesBajas = analisisAptitudes.filter(apt => apt.percentil <= 25);

    if (aptitudesAltas.length > 0) {
      recomendaciones.push(`Potenciar las fortalezas en: ${aptitudesAltas.map(apt => apt.nombre).join(', ')}.`);
    }

    if (aptitudesBajas.length > 0) {
      recomendaciones.push(`Brindar apoyo adicional en: ${aptitudesBajas.map(apt => apt.nombre).join(', ')}.`);
    }

    // Recomendación general
    recomendaciones.push("Realizar seguimiento periódico para monitorear el progreso y ajustar estrategias.");

    return recomendaciones;
  }
}

export default InterpretacionService;
