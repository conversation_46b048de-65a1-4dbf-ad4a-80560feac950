/**
 * @file InterpretationCustomizationService.js
 * @description Servicio para personalizar interpretaciones según necesidades específicas
 * Permite adaptar las interpretaciones por institución, edad, contexto, etc.
 */

import { interpretacionesAptitudes } from '../data/interpretacionesAptitudes';

class InterpretationCustomizationService {
  constructor() {
    this.customInterpretations = new Map();
    this.institutionProfiles = new Map();
    this.ageGroupProfiles = new Map();
    this.contextualModifiers = new Map();
  }

  /**
   * Registrar interpretaciones personalizadas por institución
   */
  registerInstitutionProfile(institutionId, profile) {
    this.institutionProfiles.set(institutionId, {
      ...profile,
      createdAt: new Date(),
      active: true
    });
    
    console.log(`📋 [InterpretationCustomization] Perfil registrado para institución: ${institutionId}`);
  }

  /**
   * Registrar interpretaciones por grupo de edad
   */
  registerAgeGroupProfile(ageGroup, profile) {
    this.ageGroupProfiles.set(ageGroup, {
      ...profile,
      createdAt: new Date(),
      active: true
    });
    
    console.log(`📋 [InterpretationCustomization] Perfil registrado para grupo de edad: ${ageGroup}`);
  }

  /**
   * Obtener interpretación personalizada
   */
  getCustomInterpretation(aptitud, percentil, context = {}) {
    const {
      institutionId,
      edad,
      genero,
      nivelEducativo,
      contextoEvaluacion = 'clinico'
    } = context;

    // Obtener interpretación base
    let baseInterpretation = this.getBaseInterpretation(aptitud, percentil);
    
    // Aplicar personalizaciones por institución
    if (institutionId && this.institutionProfiles.has(institutionId)) {
      baseInterpretation = this.applyInstitutionCustomization(
        baseInterpretation, 
        this.institutionProfiles.get(institutionId),
        aptitud,
        percentil
      );
    }

    // Aplicar personalizaciones por edad
    const ageGroup = this.getAgeGroup(edad);
    if (this.ageGroupProfiles.has(ageGroup)) {
      baseInterpretation = this.applyAgeGroupCustomization(
        baseInterpretation,
        this.ageGroupProfiles.get(ageGroup),
        aptitud,
        percentil
      );
    }

    // Aplicar modificadores contextuales
    baseInterpretation = this.applyContextualModifiers(
      baseInterpretation,
      { genero, nivelEducativo, contextoEvaluacion },
      aptitud,
      percentil
    );

    return baseInterpretation;
  }

  /**
   * Obtener interpretación base del sistema original
   */
  getBaseInterpretation(aptitud, percentil) {
    const aptitudData = interpretacionesAptitudes[aptitud];
    if (!aptitudData) {
      return {
        nivel: this.getNivelFromPercentile(percentil),
        descripcion: 'Interpretación no disponible para esta aptitud.',
        caracteristicas: [],
        recomendaciones: []
      };
    }

    // Determinar nivel y obtener interpretación correspondiente
    const nivel = this.getNivelFromPercentile(percentil);
    const interpretacion = aptitudData.interpretaciones?.[nivel.toLowerCase().replace('-', '_')] || 
                          aptitudData.interpretaciones?.medio;

    return {
      nivel,
      descripcion: interpretacion?.descripcion || aptitudData.descripcion,
      caracteristicas: interpretacion?.caracteristicas || [],
      recomendaciones: interpretacion?.recomendaciones || [],
      percentil
    };
  }

  /**
   * Aplicar personalización por institución
   */
  applyInstitutionCustomization(interpretation, institutionProfile, aptitud, percentil) {
    const customizations = institutionProfile.aptitudeCustomizations?.[aptitud];
    if (!customizations) return interpretation;

    return {
      ...interpretation,
      descripcion: customizations.descripcionOverride || interpretation.descripcion,
      caracteristicas: [
        ...interpretation.caracteristicas,
        ...(customizations.caracteristicasAdicionales || [])
      ],
      recomendaciones: [
        ...interpretation.recomendaciones,
        ...(customizations.recomendacionesAdicionales || [])
      ],
      contextoInstitucional: customizations.contextoEspecifico
    };
  }

  /**
   * Aplicar personalización por grupo de edad
   */
  applyAgeGroupCustomization(interpretation, ageProfile, aptitud, percentil) {
    const ageCustomizations = ageProfile.aptitudeAdjustments?.[aptitud];
    if (!ageCustomizations) return interpretation;

    // Ajustar descripción según edad
    let adjustedDescription = interpretation.descripcion;
    if (ageCustomizations.descripcionAjustada) {
      adjustedDescription = ageCustomizations.descripcionAjustada;
    }

    return {
      ...interpretation,
      descripcion: adjustedDescription,
      caracteristicas: [
        ...interpretation.caracteristicas,
        ...(ageCustomizations.caracteristicasEspecificas || [])
      ],
      recomendaciones: this.adjustRecommendationsForAge(
        interpretation.recomendaciones,
        ageCustomizations.recomendacionesEspecificas || []
      ),
      consideracionesEdad: ageCustomizations.consideracionesEspeciales
    };
  }

  /**
   * Aplicar modificadores contextuales
   */
  applyContextualModifiers(interpretation, context, aptitud, percentil) {
    const { genero, nivelEducativo, contextoEvaluacion } = context;
    
    let modifiedInterpretation = { ...interpretation };

    // Modificadores por género (si aplica)
    if (genero && this.hasGenderSpecificModifiers(aptitud)) {
      modifiedInterpretation = this.applyGenderModifiers(
        modifiedInterpretation, 
        genero, 
        aptitud
      );
    }

    // Modificadores por nivel educativo
    if (nivelEducativo) {
      modifiedInterpretation = this.applyEducationalLevelModifiers(
        modifiedInterpretation,
        nivelEducativo,
        aptitud
      );
    }

    // Modificadores por contexto de evaluación
    modifiedInterpretation = this.applyEvaluationContextModifiers(
      modifiedInterpretation,
      contextoEvaluacion,
      aptitud
    );

    return modifiedInterpretation;
  }

  /**
   * Determinar grupo de edad
   */
  getAgeGroup(edad) {
    if (!edad) return 'general';
    if (edad <= 12) return 'infantil';
    if (edad <= 17) return 'adolescente';
    if (edad <= 25) return 'joven_adulto';
    if (edad <= 65) return 'adulto';
    return 'adulto_mayor';
  }

  /**
   * Obtener nivel desde percentil
   */
  getNivelFromPercentile(percentil) {
    if (percentil >= 90) return 'Muy Alto';
    if (percentil >= 75) return 'Alto';
    if (percentil >= 60) return 'Medio-Alto';
    if (percentil >= 40) return 'Medio';
    if (percentil >= 25) return 'Medio-Bajo';
    if (percentil >= 10) return 'Bajo';
    return 'Muy Bajo';
  }

  /**
   * Verificar si una aptitud tiene modificadores específicos por género
   */
  hasGenderSpecificModifiers(aptitud) {
    // Algunas aptitudes pueden tener consideraciones específicas por género
    const genderSensitiveAptitudes = ['E', 'M', 'V']; // Espacial, Mecánico, Verbal
    return genderSensitiveAptitudes.includes(aptitud);
  }

  /**
   * Aplicar modificadores por género
   */
  applyGenderModifiers(interpretation, genero, aptitud) {
    const genderModifiers = {
      'E': {
        'Masculino': {
          nota: 'Los hombres tienden a mostrar ventajas en tareas espaciales.'
        },
        'Femenino': {
          nota: 'Las mujeres pueden compensar con estrategias verbales en tareas espaciales.'
        }
      },
      'V': {
        'Femenino': {
          nota: 'Las mujeres suelen mostrar ventajas en habilidades verbales.'
        }
      }
    };

    const modifier = genderModifiers[aptitud]?.[genero];
    if (modifier) {
      return {
        ...interpretation,
        consideracionesGenero: modifier.nota
      };
    }

    return interpretation;
  }

  /**
   * Aplicar modificadores por nivel educativo
   */
  applyEducationalLevelModifiers(interpretation, nivelEducativo, aptitud) {
    // Ajustar expectativas según nivel educativo
    const educationalModifiers = {
      'universidad': {
        expectativaAjustada: 'Se esperan puntuaciones más altas dado el nivel educativo.',
        recomendacionesAdicionales: ['Considerar estudios de posgrado en áreas afines.']
      },
      'primaria': {
        expectativaAjustada: 'Resultados apropiados para el nivel educativo actual.',
        recomendacionesAdicionales: ['Fomentar el desarrollo continuo de esta aptitud.']
      }
    };

    const modifier = educationalModifiers[nivelEducativo];
    if (modifier) {
      return {
        ...interpretation,
        expectativaEducativa: modifier.expectativaAjustada,
        recomendaciones: [
          ...interpretation.recomendaciones,
          ...modifier.recomendacionesAdicionales
        ]
      };
    }

    return interpretation;
  }

  /**
   * Aplicar modificadores por contexto de evaluación
   */
  applyEvaluationContextModifiers(interpretation, contexto, aptitud) {
    const contextModifiers = {
      'clinico': {
        enfoque: 'diagnóstico y tratamiento',
        lenguaje: 'técnico-profesional'
      },
      'educativo': {
        enfoque: 'desarrollo académico',
        lenguaje: 'educativo-pedagógico'
      },
      'laboral': {
        enfoque: 'competencias profesionales',
        lenguaje: 'organizacional'
      }
    };

    const modifier = contextModifiers[contexto];
    if (modifier) {
      return {
        ...interpretation,
        contextoEvaluacion: modifier.enfoque,
        estiloLenguaje: modifier.lenguaje
      };
    }

    return interpretation;
  }

  /**
   * Ajustar recomendaciones según edad
   */
  adjustRecommendationsForAge(baseRecommendations, ageSpecificRecommendations) {
    return [
      ...baseRecommendations.map(rec => this.adaptLanguageForAge(rec)),
      ...ageSpecificRecommendations
    ];
  }

  /**
   * Adaptar lenguaje según edad
   */
  adaptLanguageForAge(text, ageGroup = 'general') {
    // Simplificar lenguaje para grupos más jóvenes
    if (ageGroup === 'infantil') {
      return text.replace(/desarrollar/g, 'mejorar')
                 .replace(/implementar/g, 'usar')
                 .replace(/estrategias/g, 'formas');
    }
    return text;
  }

  /**
   * Exportar configuración de personalización
   */
  exportCustomizationConfig() {
    return {
      institutionProfiles: Object.fromEntries(this.institutionProfiles),
      ageGroupProfiles: Object.fromEntries(this.ageGroupProfiles),
      contextualModifiers: Object.fromEntries(this.contextualModifiers),
      exportedAt: new Date().toISOString()
    };
  }

  /**
   * Importar configuración de personalización
   */
  importCustomizationConfig(config) {
    try {
      if (config.institutionProfiles) {
        this.institutionProfiles = new Map(Object.entries(config.institutionProfiles));
      }
      if (config.ageGroupProfiles) {
        this.ageGroupProfiles = new Map(Object.entries(config.ageGroupProfiles));
      }
      if (config.contextualModifiers) {
        this.contextualModifiers = new Map(Object.entries(config.contextualModifiers));
      }
      
      console.log('✅ [InterpretationCustomization] Configuración importada exitosamente');
      return true;
    } catch (error) {
      console.error('❌ [InterpretationCustomization] Error importando configuración:', error);
      return false;
    }
  }
}

// Instancia singleton
const interpretationCustomizationService = new InterpretationCustomizationService();

// Configuraciones predefinidas
interpretationCustomizationService.registerInstitutionProfile('colegio_san_jose', {
  nombre: 'Colegio San José',
  enfoque: 'educativo',
  aptitudeCustomizations: {
    'V': {
      caracteristicasAdicionales: [
        'Excelente base para el programa de literatura del colegio',
        'Potencial para participar en debates y oratoria'
      ],
      recomendacionesAdicionales: [
        'Inscribir en el club de debate del colegio',
        'Considerar materias electivas de literatura avanzada'
      ]
    }
  }
});

interpretationCustomizationService.registerAgeGroupProfile('adolescente', {
  nombre: 'Adolescentes (13-17 años)',
  aptitudeAdjustments: {
    'A': {
      consideracionesEspeciales: 'Durante la adolescencia, la atención puede verse afectada por cambios hormonales y desarrollo cerebral.',
      recomendacionesEspecificas: [
        'Implementar técnicas de mindfulness apropiadas para la edad',
        'Establecer rutinas de estudio estructuradas'
      ]
    }
  }
});

export default interpretationCustomizationService;
