/**
 * @file PDFExportService.js
 * @description Servicio para exportar informes individuales de BAT-7 a PDF
 * Utiliza jsPDF y html2canvas para generar reportes profesionales
 */

// import jsPDF from 'jspdf'; // Comentado temporalmente
// import html2canvas from 'html2canvas'; // Comentado temporalmente
import { interpretacionesAptitudes, obtenerInterpretacion } from '../data/interpretacionesAptitudes';

class PDFExportService {
  constructor() {
    this.doc = null;
    this.pageHeight = 297; // A4 height in mm
    this.pageWidth = 210; // A4 width in mm
    this.margin = 20;
    this.currentY = this.margin;
  }

  /**
   * Exportar informe individual completo a PDF (Implementación temporal)
   */
  async exportIndividualReport(patientData, options = {}) {
    try {
      console.log('📄 [PDFExport] Simulando exportación de informe individual...');

      // Simular tiempo de procesamiento
      await new Promise(resolve => setTimeout(resolve, 2000));

      const fileName = `Informe_BAT7_${patientData.nombre}_${patientData.apellido}_${new Date().toISOString().split('T')[0]}.pdf`;

      // Crear contenido de texto simple para descargar
      const reportContent = this.generateTextReport(patientData);
      const blob = new Blob([reportContent], { type: 'text/plain' });
      const url = URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.href = url;
      a.download = fileName.replace('.pdf', '.txt');
      a.click();
      URL.revokeObjectURL(url);

      console.log('✅ [PDFExport] Informe exportado como texto:', fileName);
      return { success: true, fileName: fileName.replace('.pdf', '.txt') };

    } catch (error) {
      console.error('❌ [PDFExport] Error exportando informe:', error);
      throw error;
    }
  }

  /**
   * Generar reporte en formato texto
   */
  generateTextReport(patientData) {
    return `
INFORME PSICOLÓGICO BAT-7
========================

DATOS DEL EVALUADO
------------------
Nombre: ${patientData.nombre} ${patientData.apellido}
Documento: ${patientData.documento}
Edad: ${patientData.edad} años
Género: ${patientData.genero}
Institución: ${patientData.institucion}
Fecha de Evaluación: ${patientData.fechaEvaluacion}
Psicólogo: ${patientData.psicologo}

PUNTUACIONES OBTENIDAS
----------------------
${Object.entries(patientData.puntuaciones).map(([apt, scores]) =>
  `${apt}: PD=${scores.pd}, Pc=${scores.pc}, Nivel=${scores.nivel}`
).join('\n')}

INTERPRETACIÓN
--------------
Este informe fue generado por el sistema BAT-7.
Para obtener el informe completo en PDF, instale las dependencias jsPDF y html2canvas.

Fecha de generación: ${new Date().toLocaleString('es-ES')}
`;
  }

  /**
   * Agregar header del documento
   */
  async addHeader(patientData) {
    // Logo y título (simulado)
    this.doc.setFontSize(20);
    this.doc.setFont('helvetica', 'bold');
    this.doc.text('INFORME PSICOLÓGICO BAT-7', this.pageWidth / 2, this.currentY, { align: 'center' });
    
    this.currentY += 10;
    this.doc.setFontSize(12);
    this.doc.setFont('helvetica', 'normal');
    this.doc.text('Batería de Aptitudes Diferenciales y Generales', this.pageWidth / 2, this.currentY, { align: 'center' });
    
    this.currentY += 15;
    
    // Línea separadora
    this.doc.setLineWidth(0.5);
    this.doc.line(this.margin, this.currentY, this.pageWidth - this.margin, this.currentY);
    this.currentY += 10;
  }

  /**
   * Agregar información del paciente
   */
  async addPatientInfo(patientData) {
    this.doc.setFontSize(14);
    this.doc.setFont('helvetica', 'bold');
    this.doc.text('DATOS DEL EVALUADO', this.margin, this.currentY);
    this.currentY += 8;

    this.doc.setFontSize(10);
    this.doc.setFont('helvetica', 'normal');

    const info = [
      [`Nombre:`, `${patientData.nombre} ${patientData.apellido}`],
      [`Documento:`, patientData.documento],
      [`Edad:`, `${patientData.edad} años`],
      [`Género:`, patientData.genero],
      [`Institución:`, patientData.institucion],
      [`Curso/Grado:`, patientData.curso],
      [`Fecha de Evaluación:`, patientData.fechaEvaluacion],
      [`Psicólogo Evaluador:`, patientData.psicologo],
      [`Nivel BAT-7:`, patientData.nivelBat7]
    ];

    info.forEach(([label, value]) => {
      this.doc.setFont('helvetica', 'bold');
      this.doc.text(label, this.margin, this.currentY);
      this.doc.setFont('helvetica', 'normal');
      this.doc.text(value, this.margin + 40, this.currentY);
      this.currentY += 5;
    });

    this.currentY += 10;
  }

  /**
   * Agregar tabla de puntuaciones
   */
  async addScoresTable(patientData) {
    this.checkPageBreak(60);

    this.doc.setFontSize(14);
    this.doc.setFont('helvetica', 'bold');
    this.doc.text('PUNTUACIONES OBTENIDAS', this.margin, this.currentY);
    this.currentY += 10;

    // Headers de la tabla
    const headers = ['Aptitud', 'Descripción', 'PD', 'Pc', 'Nivel'];
    const colWidths = [20, 80, 15, 15, 30];
    let startX = this.margin;

    this.doc.setFontSize(9);
    this.doc.setFont('helvetica', 'bold');
    
    // Dibujar headers
    headers.forEach((header, index) => {
      this.doc.rect(startX, this.currentY - 4, colWidths[index], 6);
      this.doc.text(header, startX + 2, this.currentY);
      startX += colWidths[index];
    });

    this.currentY += 6;

    // Datos de la tabla
    this.doc.setFont('helvetica', 'normal');
    
    const aptitudeNames = {
      V: 'Verbal',
      E: 'Espacial',
      A: 'Atención',
      R: 'Razonamiento',
      N: 'Numérico',
      M: 'Mecánico',
      O: 'Ortografía'
    };

    Object.entries(patientData.puntuaciones).forEach(([aptitud, scores]) => {
      this.checkPageBreak(8);
      
      startX = this.margin;
      const rowData = [
        aptitud,
        aptitudeNames[aptitud] || aptitud,
        scores.pd.toString(),
        scores.pc.toString(),
        scores.nivel
      ];

      rowData.forEach((data, index) => {
        this.doc.rect(startX, this.currentY - 4, colWidths[index], 6);
        this.doc.text(data, startX + 2, this.currentY);
        startX += colWidths[index];
      });

      this.currentY += 6;
    });

    this.currentY += 10;
  }

  /**
   * Agregar sección de gráficos (placeholder)
   */
  async addChartsSection(patientData) {
    this.checkPageBreak(40);

    this.doc.setFontSize(14);
    this.doc.setFont('helvetica', 'bold');
    this.doc.text('PERFIL APTITUDINAL', this.margin, this.currentY);
    this.currentY += 10;

    // Placeholder para gráfico
    this.doc.setFontSize(10);
    this.doc.setFont('helvetica', 'italic');
    this.doc.text('Gráfico de perfil aptitudinal (se incluirá en versión completa)', this.margin, this.currentY);
    
    // Dibujar un rectángulo como placeholder
    this.doc.setLineWidth(0.5);
    this.doc.rect(this.margin, this.currentY + 5, this.pageWidth - 2 * this.margin, 60);
    
    this.currentY += 70;
  }

  /**
   * Agregar sección de interpretaciones
   */
  async addInterpretationsSection(patientData) {
    this.checkPageBreak(80);

    this.doc.setFontSize(14);
    this.doc.setFont('helvetica', 'bold');
    this.doc.text('INTERPRETACIÓN CUALITATIVA', this.margin, this.currentY);
    this.currentY += 10;

    this.doc.setFontSize(10);
    this.doc.setFont('helvetica', 'normal');

    // Resumen general
    const fortalezas = Object.entries(patientData.puntuaciones).filter(([_, scores]) => scores.pc >= 70).length;
    const promedio = Object.entries(patientData.puntuaciones).filter(([_, scores]) => scores.pc >= 30 && scores.pc < 70).length;
    const aDesarrollar = Object.entries(patientData.puntuaciones).filter(([_, scores]) => scores.pc < 30).length;

    this.doc.text(`Resumen del perfil: ${fortalezas} fortalezas, ${promedio} aptitudes promedio, ${aDesarrollar} áreas a desarrollar.`, this.margin, this.currentY);
    this.currentY += 8;

    // Interpretaciones por aptitud
    Object.entries(patientData.puntuaciones).forEach(([aptitud, scores]) => {
      this.checkPageBreak(15);
      
      const interpretacion = obtenerInterpretacion(aptitud, scores.pc);
      if (interpretacion) {
        this.doc.setFont('helvetica', 'bold');
        this.doc.text(`${aptitud} - ${interpretacion.nivel} (Pc ${scores.pc}):`, this.margin, this.currentY);
        this.currentY += 5;
        
        this.doc.setFont('helvetica', 'normal');
        const lines = this.doc.splitTextToSize(interpretacion.descripcion, this.pageWidth - 2 * this.margin);
        lines.forEach(line => {
          this.checkPageBreak(5);
          this.doc.text(line, this.margin + 5, this.currentY);
          this.currentY += 4;
        });
        
        this.currentY += 3;
      }
    });
  }

  /**
   * Agregar sección de recomendaciones
   */
  async addRecommendationsSection(patientData) {
    this.checkPageBreak(40);

    this.doc.setFontSize(14);
    this.doc.setFont('helvetica', 'bold');
    this.doc.text('RECOMENDACIONES', this.margin, this.currentY);
    this.currentY += 10;

    this.doc.setFontSize(10);
    this.doc.setFont('helvetica', 'normal');

    const recomendaciones = [
      'Potenciar las fortalezas identificadas mediante actividades específicas.',
      'Implementar estrategias de apoyo en las áreas que requieren desarrollo.',
      'Realizar seguimiento periódico para monitorear el progreso.',
      'Considerar el perfil completo al diseñar intervenciones educativas.',
      'Adaptar las metodologías de enseñanza al estilo atencional identificado.'
    ];

    recomendaciones.forEach((recomendacion, index) => {
      this.checkPageBreak(8);
      this.doc.text(`${index + 1}. ${recomendacion}`, this.margin, this.currentY);
      this.currentY += 6;
    });

    this.currentY += 10;
  }

  /**
   * Agregar footer del documento
   */
  async addFooter() {
    const footerY = this.pageHeight - 15;
    
    this.doc.setFontSize(8);
    this.doc.setFont('helvetica', 'italic');
    this.doc.text(`Informe generado el ${new Date().toLocaleDateString('es-ES')}`, this.margin, footerY);
    this.doc.text('Sistema BAT-7 - Evaluación Psicológica', this.pageWidth - this.margin, footerY, { align: 'right' });
    
    // Línea separadora
    this.doc.setLineWidth(0.3);
    this.doc.line(this.margin, footerY - 3, this.pageWidth - this.margin, footerY - 3);
  }

  /**
   * Verificar si se necesita un salto de página
   */
  checkPageBreak(requiredSpace) {
    if (this.currentY + requiredSpace > this.pageHeight - 30) {
      this.doc.addPage();
      this.currentY = this.margin;
    }
  }

  /**
   * Exportar solo la tabla de puntuaciones (Implementación temporal)
   */
  async exportScoresOnly(patientData) {
    try {
      console.log('📄 [PDFExport] Simulando exportación de puntuaciones...');

      await new Promise(resolve => setTimeout(resolve, 1000));

      const fileName = `Puntuaciones_BAT7_${patientData.nombre}_${patientData.apellido}.txt`;
      const scoresContent = this.generateScoresReport(patientData);

      const blob = new Blob([scoresContent], { type: 'text/plain' });
      const url = URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.href = url;
      a.download = fileName;
      a.click();
      URL.revokeObjectURL(url);

      return { success: true, fileName };
    } catch (error) {
      console.error('❌ [PDFExport] Error exportando puntuaciones:', error);
      throw error;
    }
  }

  /**
   * Generar reporte de puntuaciones
   */
  generateScoresReport(patientData) {
    return `
PUNTUACIONES BAT-7
==================

Evaluado: ${patientData.nombre} ${patientData.apellido}
Documento: ${patientData.documento}
Fecha: ${patientData.fechaEvaluacion}

TABLA DE PUNTUACIONES
---------------------
${Object.entries(patientData.puntuaciones).map(([apt, scores]) =>
  `${apt.padEnd(12)} | PD: ${scores.pd.toString().padStart(3)} | Pc: ${scores.pc.toString().padStart(3)} | ${scores.nivel}`
).join('\n')}

Generado: ${new Date().toLocaleString('es-ES')}
`;
  }

  /**
   * Exportar desde elemento HTML (Implementación temporal)
   */
  async exportFromHTML(elementId, fileName) {
    try {
      console.log(`📄 [PDFExport] Simulando exportación de elemento ${elementId}...`);

      const element = document.getElementById(elementId);
      if (!element) {
        throw new Error(`Elemento ${elementId} no encontrado`);
      }

      await new Promise(resolve => setTimeout(resolve, 1500));

      // Crear un archivo de texto con información del gráfico
      const chartInfo = `
EXPORTACIÓN DE GRÁFICO BAT-7
============================

Elemento: ${elementId}
Fecha: ${new Date().toLocaleString('es-ES')}

Nota: Para exportar gráficos como imagen, instale las dependencias:
- html2canvas
- jsPDF

El gráfico se encuentra disponible en la interfaz web.
`;

      const blob = new Blob([chartInfo], { type: 'text/plain' });
      const url = URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.href = url;
      a.download = fileName.replace('.pdf', '.txt');
      a.click();
      URL.revokeObjectURL(url);

      return { success: true, fileName: fileName.replace('.pdf', '.txt') };
    } catch (error) {
      console.error('❌ [PDFExport] Error exportando desde HTML:', error);
      throw error;
    }
  }
}

// Instancia singleton
const pdfExportService = new PDFExportService();

export default pdfExportService;
