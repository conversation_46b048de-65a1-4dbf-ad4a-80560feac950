/**
 * @file ReportTemplateService.js
 * @description Servicio para gestionar plantillas de informe personalizables
 * Permite crear, modificar y aplicar diferentes formatos de informe según necesidades
 */

class ReportTemplateService {
  constructor() {
    this.templates = new Map();
    this.activeTemplate = 'standard';
    this.customFields = new Map();
    this.initializeDefaultTemplates();
  }

  /**
   * Inicializar plantillas predeterminadas
   */
  initializeDefaultTemplates() {
    // Plantilla estándar
    this.registerTemplate('standard', {
      name: 'Informe Estándar BAT-7',
      description: 'Plantilla estándar para informes psicológicos',
      sections: [
        {
          id: 'header',
          name: '<PERSON><PERSON>be<PERSON><PERSON>',
          enabled: true,
          required: true,
          content: {
            showLogo: true,
            showInstitution: true,
            showDate: true,
            title: 'INFORME PSICOLÓGICO BAT-7'
          }
        },
        {
          id: 'patient_info',
          name: '<PERSON><PERSON> del Evaluado',
          enabled: true,
          required: true,
          content: {
            fields: ['nombre', 'apellido', 'documento', 'edad', 'genero', 'institucion', 'curso'],
            layout: 'table'
          }
        },
        {
          id: 'evaluation_info',
          name: 'Información de la Evaluación',
          enabled: true,
          required: false,
          content: {
            fields: ['fechaEvaluacion', 'psicologo', 'nivelBat7', 'duracion'],
            layout: 'list'
          }
        },
        {
          id: 'scores_table',
          name: 'Tabla de Puntuaciones',
          enabled: true,
          required: true,
          content: {
            showPD: true,
            showPC: true,
            showLevel: true,
            showInterpretation: false,
            groupByFactor: false
          }
        },
        {
          id: 'profile_chart',
          name: 'Gráfico de Perfil',
          enabled: true,
          required: false,
          content: {
            chartType: 'line',
            showPercentiles: true,
            showDirectScores: false,
            includeNorms: true
          }
        },
        {
          id: 'attention_analysis',
          name: 'Análisis Atencional',
          enabled: true,
          required: false,
          content: {
            showQuadrant: true,
            showDescription: true,
            showRecommendations: true
          }
        },
        {
          id: 'qualitative_interpretation',
          name: 'Interpretación Cualitativa',
          enabled: true,
          required: true,
          content: {
            showByAptitude: true,
            showSummary: true,
            showStrengths: true,
            showWeaknesses: true,
            detailLevel: 'medium'
          }
        },
        {
          id: 'recommendations',
          name: 'Recomendaciones',
          enabled: true,
          required: false,
          content: {
            showGeneral: true,
            showSpecific: true,
            showEducational: true,
            showTherapeutic: false
          }
        },
        {
          id: 'signature',
          name: 'Firma Digital',
          enabled: true,
          required: true,
          content: {
            showPsychologist: true,
            showLicense: true,
            showTimestamp: true,
            showValidation: true
          }
        }
      ],
      styling: {
        fontFamily: 'Arial',
        fontSize: 11,
        lineHeight: 1.4,
        margins: { top: 20, bottom: 20, left: 20, right: 20 },
        colors: {
          primary: '#1f2937',
          secondary: '#6b7280',
          accent: '#3b82f6',
          success: '#10b981',
          warning: '#f59e0b',
          danger: '#ef4444'
        }
      }
    });

    // Plantilla clínica
    this.registerTemplate('clinical', {
      name: 'Informe Clínico Detallado',
      description: 'Plantilla para uso clínico con mayor detalle diagnóstico',
      sections: [
        {
          id: 'header',
          name: 'Encabezado Clínico',
          enabled: true,
          required: true,
          content: {
            showLogo: true,
            showInstitution: true,
            showDate: true,
            title: 'EVALUACIÓN NEUROPSICOLÓGICA BAT-7',
            subtitle: 'Informe Clínico Especializado'
          }
        },
        {
          id: 'patient_info',
          name: 'Historia Clínica',
          enabled: true,
          required: true,
          content: {
            fields: ['nombre', 'apellido', 'documento', 'edad', 'genero', 'escolaridad', 'ocupacion', 'motivoConsulta'],
            layout: 'detailed'
          }
        },
        {
          id: 'clinical_background',
          name: 'Antecedentes Clínicos',
          enabled: true,
          required: false,
          content: {
            showMedicalHistory: true,
            showFamilyHistory: true,
            showPreviousEvaluations: true
          }
        },
        {
          id: 'scores_table',
          name: 'Resultados Cuantitativos',
          enabled: true,
          required: true,
          content: {
            showPD: true,
            showPC: true,
            showLevel: true,
            showInterpretation: true,
            showConfidenceIntervals: true,
            groupByFactor: true
          }
        },
        {
          id: 'cognitive_profile',
          name: 'Perfil Cognitivo',
          enabled: true,
          required: true,
          content: {
            showFactorAnalysis: true,
            showCognitiveStrengths: true,
            showCognitiveWeaknesses: true,
            showProcessingSpeed: true
          }
        },
        {
          id: 'clinical_interpretation',
          name: 'Interpretación Clínica',
          enabled: true,
          required: true,
          content: {
            showDiagnosticImpression: true,
            showDifferentialDiagnosis: true,
            showPrognosticFactors: true,
            detailLevel: 'high'
          }
        },
        {
          id: 'treatment_recommendations',
          name: 'Recomendaciones Terapéuticas',
          enabled: true,
          required: true,
          content: {
            showTherapeuticApproach: true,
            showInterventionPlan: true,
            showFollowUpPlan: true,
            showReferrals: true
          }
        }
      ],
      styling: {
        fontFamily: 'Times New Roman',
        fontSize: 12,
        lineHeight: 1.5,
        margins: { top: 25, bottom: 25, left: 25, right: 25 },
        colors: {
          primary: '#1f2937',
          secondary: '#4b5563',
          accent: '#7c3aed',
          success: '#059669',
          warning: '#d97706',
          danger: '#dc2626'
        }
      }
    });

    // Plantilla educativa
    this.registerTemplate('educational', {
      name: 'Informe Educativo',
      description: 'Plantilla orientada al ámbito educativo y pedagógico',
      sections: [
        {
          id: 'header',
          name: 'Encabezado Educativo',
          enabled: true,
          required: true,
          content: {
            showLogo: true,
            showInstitution: true,
            showDate: true,
            title: 'EVALUACIÓN DE APTITUDES ACADÉMICAS',
            subtitle: 'Informe Psicopedagógico BAT-7'
          }
        },
        {
          id: 'student_info',
          name: 'Información del Estudiante',
          enabled: true,
          required: true,
          content: {
            fields: ['nombre', 'apellido', 'edad', 'curso', 'institucion', 'modalidad'],
            layout: 'educational'
          }
        },
        {
          id: 'academic_context',
          name: 'Contexto Académico',
          enabled: true,
          required: false,
          content: {
            showCurrentPerformance: true,
            showAcademicHistory: true,
            showLearningStyle: true
          }
        },
        {
          id: 'aptitude_analysis',
          name: 'Análisis de Aptitudes',
          enabled: true,
          required: true,
          content: {
            focusOnAcademic: true,
            showLearningPotential: true,
            showAcademicStrengths: true,
            showSupportNeeds: true
          }
        },
        {
          id: 'educational_recommendations',
          name: 'Recomendaciones Pedagógicas',
          enabled: true,
          required: true,
          content: {
            showTeachingStrategies: true,
            showAccommodations: true,
            showEnrichmentActivities: true,
            showParentGuidance: true
          }
        }
      ],
      styling: {
        fontFamily: 'Calibri',
        fontSize: 11,
        lineHeight: 1.3,
        margins: { top: 20, bottom: 20, left: 20, right: 20 },
        colors: {
          primary: '#1e40af',
          secondary: '#64748b',
          accent: '#0ea5e9',
          success: '#16a34a',
          warning: '#ea580c',
          danger: '#e11d48'
        }
      }
    });

    console.log('📋 [ReportTemplate] Plantillas predeterminadas inicializadas');
  }

  /**
   * Registrar nueva plantilla
   */
  registerTemplate(id, template) {
    this.templates.set(id, {
      ...template,
      id,
      createdAt: new Date().toISOString(),
      version: '1.0',
      active: true
    });
    
    console.log(`📋 [ReportTemplate] Plantilla registrada: ${template.name}`);
  }

  /**
   * Obtener plantilla por ID
   */
  getTemplate(id) {
    return this.templates.get(id);
  }

  /**
   * Listar todas las plantillas disponibles
   */
  listTemplates() {
    return Array.from(this.templates.values())
      .filter(template => template.active)
      .map(template => ({
        id: template.id,
        name: template.name,
        description: template.description,
        sectionsCount: template.sections.length,
        createdAt: template.createdAt
      }));
  }

  /**
   * Establecer plantilla activa
   */
  setActiveTemplate(templateId) {
    if (this.templates.has(templateId)) {
      this.activeTemplate = templateId;
      console.log(`📋 [ReportTemplate] Plantilla activa: ${templateId}`);
      return true;
    }
    return false;
  }

  /**
   * Personalizar sección de plantilla
   */
  customizeSection(templateId, sectionId, customization) {
    const template = this.templates.get(templateId);
    if (!template) return false;

    const sectionIndex = template.sections.findIndex(s => s.id === sectionId);
    if (sectionIndex === -1) return false;

    template.sections[sectionIndex] = {
      ...template.sections[sectionIndex],
      ...customization,
      lastModified: new Date().toISOString()
    };

    console.log(`📋 [ReportTemplate] Sección personalizada: ${sectionId} en ${templateId}`);
    return true;
  }

  /**
   * Agregar campo personalizado
   */
  addCustomField(templateId, sectionId, field) {
    const customFieldId = `custom_${Date.now()}`;
    
    if (!this.customFields.has(templateId)) {
      this.customFields.set(templateId, new Map());
    }
    
    this.customFields.get(templateId).set(customFieldId, {
      ...field,
      id: customFieldId,
      sectionId,
      createdAt: new Date().toISOString()
    });

    console.log(`📋 [ReportTemplate] Campo personalizado agregado: ${field.name}`);
    return customFieldId;
  }

  /**
   * Generar informe usando plantilla
   */
  generateReport(patientData, templateId = null, customizations = {}) {
    const template = this.templates.get(templateId || this.activeTemplate);
    if (!template) {
      throw new Error('Plantilla no encontrada');
    }

    const report = {
      templateId: template.id,
      templateName: template.name,
      generatedAt: new Date().toISOString(),
      patientData,
      sections: [],
      styling: template.styling,
      metadata: {
        version: template.version,
        generator: 'BAT-7 Report System'
      }
    };

    // Procesar cada sección habilitada
    template.sections
      .filter(section => section.enabled)
      .forEach(section => {
        const processedSection = this.processSection(section, patientData, customizations);
        if (processedSection) {
          report.sections.push(processedSection);
        }
      });

    // Aplicar campos personalizados
    this.applyCustomFields(report, templateId);

    console.log(`📋 [ReportTemplate] Informe generado usando plantilla: ${template.name}`);
    return report;
  }

  /**
   * Procesar sección individual
   */
  processSection(section, patientData, customizations) {
    const sectionCustomization = customizations[section.id] || {};
    
    return {
      id: section.id,
      name: section.name,
      type: section.id,
      content: this.generateSectionContent(section, patientData),
      styling: sectionCustomization.styling || {},
      metadata: {
        required: section.required,
        processedAt: new Date().toISOString()
      }
    };
  }

  /**
   * Generar contenido específico de sección
   */
  generateSectionContent(section, patientData) {
    switch (section.id) {
      case 'header':
        return this.generateHeaderContent(section.content, patientData);
      
      case 'patient_info':
      case 'student_info':
        return this.generatePatientInfoContent(section.content, patientData);
      
      case 'scores_table':
        return this.generateScoresTableContent(section.content, patientData);
      
      case 'profile_chart':
        return this.generateProfileChartContent(section.content, patientData);
      
      case 'qualitative_interpretation':
      case 'clinical_interpretation':
        return this.generateInterpretationContent(section.content, patientData);
      
      case 'recommendations':
      case 'educational_recommendations':
      case 'treatment_recommendations':
        return this.generateRecommendationsContent(section.content, patientData);
      
      case 'signature':
        return this.generateSignatureContent(section.content, patientData);
      
      default:
        return { type: 'placeholder', message: `Contenido para ${section.name} en desarrollo` };
    }
  }

  /**
   * Generar contenido del encabezado
   */
  generateHeaderContent(config, patientData) {
    return {
      title: config.title,
      subtitle: config.subtitle || '',
      showLogo: config.showLogo,
      showInstitution: config.showInstitution,
      showDate: config.showDate,
      institution: patientData.institucion || 'Centro de Evaluación Psicológica',
      date: new Date().toLocaleDateString('es-ES')
    };
  }

  /**
   * Generar contenido de información del paciente
   */
  generatePatientInfoContent(config, patientData) {
    const fields = config.fields.reduce((acc, field) => {
      if (patientData[field] !== undefined) {
        acc[field] = patientData[field];
      }
      return acc;
    }, {});

    return {
      layout: config.layout,
      fields,
      labels: this.getFieldLabels()
    };
  }

  /**
   * Generar contenido de tabla de puntuaciones
   */
  generateScoresTableContent(config, patientData) {
    return {
      puntuaciones: patientData.puntuaciones,
      showPD: config.showPD,
      showPC: config.showPC,
      showLevel: config.showLevel,
      showInterpretation: config.showInterpretation,
      groupByFactor: config.groupByFactor
    };
  }

  /**
   * Obtener etiquetas de campos
   */
  getFieldLabels() {
    return {
      nombre: 'Nombre',
      apellido: 'Apellido',
      documento: 'Documento',
      edad: 'Edad',
      genero: 'Género',
      institucion: 'Institución',
      curso: 'Curso/Grado',
      fechaEvaluacion: 'Fecha de Evaluación',
      psicologo: 'Psicólogo Evaluador',
      nivelBat7: 'Nivel BAT-7'
    };
  }

  /**
   * Aplicar campos personalizados
   */
  applyCustomFields(report, templateId) {
    const customFields = this.customFields.get(templateId);
    if (!customFields) return;

    customFields.forEach((field, fieldId) => {
      const section = report.sections.find(s => s.id === field.sectionId);
      if (section) {
        if (!section.customFields) {
          section.customFields = [];
        }
        section.customFields.push(field);
      }
    });
  }

  /**
   * Exportar plantilla
   */
  exportTemplate(templateId) {
    const template = this.templates.get(templateId);
    if (!template) return null;

    const customFields = this.customFields.get(templateId);
    
    return {
      template,
      customFields: customFields ? Object.fromEntries(customFields) : {},
      exportedAt: new Date().toISOString()
    };
  }

  /**
   * Importar plantilla
   */
  importTemplate(templateData) {
    try {
      const { template, customFields } = templateData;
      
      this.templates.set(template.id, template);
      
      if (customFields) {
        this.customFields.set(template.id, new Map(Object.entries(customFields)));
      }
      
      console.log(`📋 [ReportTemplate] Plantilla importada: ${template.name}`);
      return true;
    } catch (error) {
      console.error('❌ [ReportTemplate] Error importando plantilla:', error);
      return false;
    }
  }

  /**
   * Clonar plantilla
   */
  cloneTemplate(sourceId, newName) {
    const sourceTemplate = this.templates.get(sourceId);
    if (!sourceTemplate) return null;

    const newId = `${sourceId}_clone_${Date.now()}`;
    const clonedTemplate = {
      ...JSON.parse(JSON.stringify(sourceTemplate)),
      id: newId,
      name: newName,
      createdAt: new Date().toISOString(),
      clonedFrom: sourceId
    };

    this.templates.set(newId, clonedTemplate);
    
    // Clonar campos personalizados si existen
    const customFields = this.customFields.get(sourceId);
    if (customFields) {
      this.customFields.set(newId, new Map(customFields));
    }

    console.log(`📋 [ReportTemplate] Plantilla clonada: ${newName}`);
    return newId;
  }
}

// Instancia singleton
const reportTemplateService = new ReportTemplateService();

export default reportTemplateService;
