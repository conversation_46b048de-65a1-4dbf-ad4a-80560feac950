/**
 * Servicio de exportación para el Dashboard BAT-7
 * Permite exportar datos y gráficos en diferentes formatos
 */

import supabase from '../api/supabaseClient';

class DashboardExportService {
  /**
   * Exportar datos del dashboard a CSV
   */
  async exportToCSV(viewName, filename) {
    try {
      console.log(`📊 [Export] Exportando ${viewName} a CSV...`);
      
      const { data, error } = await supabase
        .from(viewName)
        .select('*');

      if (error) {
        throw new Error(`Error al obtener datos: ${error.message}`);
      }

      if (!data || data.length === 0) {
        throw new Error('No hay datos para exportar');
      }

      // Convertir datos a CSV
      const headers = Object.keys(data[0]);
      const csvContent = [
        headers.join(','),
        ...data.map(row => 
          headers.map(header => {
            const value = row[header];
            // Escapar comillas y envolver en comillas si contiene comas
            if (typeof value === 'string' && (value.includes(',') || value.includes('"'))) {
              return `"${value.replace(/"/g, '""')}"`;
            }
            return value || '';
          }).join(',')
        )
      ].join('\n');

      // Crear y descargar archivo
      const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
      const link = document.createElement('a');
      const url = URL.createObjectURL(blob);
      
      link.setAttribute('href', url);
      link.setAttribute('download', `${filename}_${new Date().toISOString().split('T')[0]}.csv`);
      link.style.visibility = 'hidden';
      
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      
      console.log('✅ [Export] CSV exportado exitosamente');
      return true;
      
    } catch (error) {
      console.error('❌ [Export] Error al exportar CSV:', error);
      throw error;
    }
  }

  /**
   * Exportar datos del dashboard a JSON
   */
  async exportToJSON(viewName, filename) {
    try {
      console.log(`📊 [Export] Exportando ${viewName} a JSON...`);
      
      const { data, error } = await supabase
        .from(viewName)
        .select('*');

      if (error) {
        throw new Error(`Error al obtener datos: ${error.message}`);
      }

      if (!data || data.length === 0) {
        throw new Error('No hay datos para exportar');
      }

      // Crear objeto de exportación con metadatos
      const exportData = {
        metadata: {
          exportDate: new Date().toISOString(),
          source: 'BAT-7 Dashboard',
          view: viewName,
          recordCount: data.length
        },
        data: data
      };

      // Crear y descargar archivo
      const blob = new Blob([JSON.stringify(exportData, null, 2)], { 
        type: 'application/json;charset=utf-8;' 
      });
      const link = document.createElement('a');
      const url = URL.createObjectURL(blob);
      
      link.setAttribute('href', url);
      link.setAttribute('download', `${filename}_${new Date().toISOString().split('T')[0]}.json`);
      link.style.visibility = 'hidden';
      
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      
      console.log('✅ [Export] JSON exportado exitosamente');
      return true;
      
    } catch (error) {
      console.error('❌ [Export] Error al exportar JSON:', error);
      throw error;
    }
  }

  /**
   * Exportar reporte completo del dashboard
   */
  async exportCompleteReport(format = 'json') {
    try {
      console.log(`📊 [Export] Generando reporte completo en formato ${format}...`);
      
      // Obtener datos de todas las vistas
      const views = [
        'dashboard_estadisticas_generales',
        'dashboard_estudiantes_por_nivel',
        'dashboard_perfil_institucional',
        'dashboard_perfil_por_nivel',
        'dashboard_distribucion_rendimiento',
        'dashboard_comparativa_genero',
        'dashboard_correlacion_aptitudes'
      ];

      const reportData = {
        metadata: {
          exportDate: new Date().toISOString(),
          source: 'BAT-7 Dashboard',
          type: 'Complete Report',
          version: '1.0'
        },
        sections: {}
      };

      // Obtener datos de cada vista
      for (const view of views) {
        try {
          const { data, error } = await supabase
            .from(view)
            .select('*');

          if (error) {
            console.warn(`⚠️ [Export] Error en vista ${view}:`, error);
            reportData.sections[view] = { error: error.message };
          } else {
            reportData.sections[view] = {
              recordCount: data?.length || 0,
              data: data || []
            };
          }
        } catch (viewError) {
          console.warn(`⚠️ [Export] Error al procesar vista ${view}:`, viewError);
          reportData.sections[view] = { error: viewError.message };
        }
      }

      // Exportar según el formato
      if (format === 'csv') {
        // Para CSV, exportar cada sección como archivo separado
        for (const [viewName, viewData] of Object.entries(reportData.sections)) {
          if (viewData.data && viewData.data.length > 0) {
            await this.exportToCSV(viewName, `reporte_${viewName}`);
          }
        }
      } else {
        // Exportar como JSON
        const blob = new Blob([JSON.stringify(reportData, null, 2)], { 
          type: 'application/json;charset=utf-8;' 
        });
        const link = document.createElement('a');
        const url = URL.createObjectURL(blob);
        
        link.setAttribute('href', url);
        link.setAttribute('download', `reporte_completo_bat7_${new Date().toISOString().split('T')[0]}.json`);
        link.style.visibility = 'hidden';
        
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
      }
      
      console.log('✅ [Export] Reporte completo exportado exitosamente');
      return true;
      
    } catch (error) {
      console.error('❌ [Export] Error al exportar reporte completo:', error);
      throw error;
    }
  }

  /**
   * Exportar gráfico como imagen (usando canvas)
   */
  async exportChartAsImage(chartElement, filename, format = 'png') {
    try {
      console.log(`📊 [Export] Exportando gráfico como ${format}...`);
      
      // Usar html2canvas para capturar el elemento
      const html2canvas = await import('html2canvas');
      const canvas = await html2canvas.default(chartElement, {
        backgroundColor: '#ffffff',
        scale: 2, // Mayor resolución
        logging: false
      });

      // Convertir canvas a blob
      canvas.toBlob((blob) => {
        const link = document.createElement('a');
        const url = URL.createObjectURL(blob);
        
        link.setAttribute('href', url);
        link.setAttribute('download', `${filename}_${new Date().toISOString().split('T')[0]}.${format}`);
        link.style.visibility = 'hidden';
        
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
        
        console.log('✅ [Export] Gráfico exportado exitosamente');
      }, `image/${format}`);
      
      return true;
      
    } catch (error) {
      console.error('❌ [Export] Error al exportar gráfico:', error);
      throw error;
    }
  }

  /**
   * Generar URL para compartir dashboard
   */
  generateShareableURL(filters = {}) {
    const baseURL = window.location.origin + '/dashboard';
    const params = new URLSearchParams();
    
    // Agregar filtros como parámetros de URL
    Object.entries(filters).forEach(([key, value]) => {
      if (value) {
        params.append(key, value);
      }
    });
    
    const shareURL = params.toString() ? `${baseURL}?${params.toString()}` : baseURL;
    
    // Copiar al portapapeles
    if (navigator.clipboard) {
      navigator.clipboard.writeText(shareURL);
      console.log('✅ [Export] URL copiada al portapapeles');
    }
    
    return shareURL;
  }
}

export default new DashboardExportService();
