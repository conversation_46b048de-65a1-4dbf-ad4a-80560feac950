import { saveAs } from 'file-saver';
import { utils, write } from 'xlsx';
import jsPDF from 'jspdf';
import 'jspdf-autotable';

/**
 * Servicio para exportar datos del dashboard en diferentes formatos
 */
class ExportService {
  /**
   * Exportar datos a PDF
   * @param {Object} data - Datos para exportar
   * @param {Object} options - Opciones de configuración
   */
  static async exportToPDF(data, options = {}) {
    try {
      console.log('📄 [ExportService] Exportando a PDF...');
      
      const {
        title = 'Reporte Ejecutivo BAT-7',
        includeGraphics = true,
        includeTrends = true,
        includeRecommendations = true,
        includeDetailedData = false
      } = options;
      
      // Crear documento PDF
      const doc = new jsPDF();
      
      // Título
      doc.setFontSize(20);
      doc.setTextColor(0, 51, 153);
      doc.text(title, 20, 20);
      
      // Fecha
      doc.setFontSize(10);
      doc.setTextColor(100, 100, 100);
      doc.text(`Generado: ${new Date().toLocaleDateString()}`, 20, 30);
      
      // Estadísticas generales
      doc.setFontSize(16);
      doc.setTextColor(0, 0, 0);
      doc.text('Estadísticas Generales', 20, 40);
      
      const estadisticas = [
        ['Total Pacientes', data.total_pacientes || 0],
        ['Pacientes Evaluados', data.pacientes_evaluados || 0],
        ['Total Evaluaciones', data.total_evaluaciones || 0],
        ['Percentil Promedio', `${data.percentil_promedio_general || 0}`],
        ['Evaluaciones Último Mes', data.evaluaciones_ultimo_mes || 0],
        ['Evaluaciones Última Semana', data.evaluaciones_ultima_semana || 0]
      ];
      
      doc.autoTable({
        startY: 45,
        head: [['Métrica', 'Valor']],
        body: estadisticas,
        theme: 'grid',
        headStyles: { fillColor: [0, 102, 204] }
      });
      
      // Incluir recomendaciones si se solicita
      if (includeRecommendations) {
        const currentY = doc.lastAutoTable.finalY + 10;
        doc.setFontSize(16);
        doc.text('Recomendaciones', 20, currentY);
        
        const recomendaciones = [
          ['Alta', 'Implementar programa de refuerzo en aptitud mecánica', '30 días'],
          ['Media', 'Expandir programa de excelencia para estudiantes destacados', '60 días'],
          ['Media', 'Desarrollar estrategias diferenciadas por género', '45 días']
        ];
        
        doc.autoTable({
          startY: currentY + 5,
          head: [['Prioridad', 'Acción', 'Plazo']],
          body: recomendaciones,
          theme: 'grid',
          headStyles: { fillColor: [0, 102, 204] }
        });
      }
      
      // Guardar PDF
      doc.save('reporte_ejecutivo_bat7.pdf');
      
      return true;
    } catch (error) {
      console.error('❌ [ExportService] Error al exportar a PDF:', error);
      throw error;
    }
  }
  
  /**
   * Exportar datos a Excel
   * @param {Object} data - Datos para exportar
   * @param {Object} options - Opciones de configuración
   */
  static async exportToExcel(data, options = {}) {
    try {
      console.log('📊 [ExportService] Exportando a Excel...');
      
      // Crear libro de trabajo
      const wb = utils.book_new();
      
      // Hoja de estadísticas generales
      const estadisticasWS = utils.json_to_sheet([
        { Métrica: 'Total Pacientes', Valor: data.total_pacientes || 0 },
        { Métrica: 'Pacientes Evaluados', Valor: data.pacientes_evaluados || 0 },
        { Métrica: 'Total Evaluaciones', Valor: data.total_evaluaciones || 0 },
        { Métrica: 'Percentil Promedio', Valor: data.percentil_promedio_general || 0 },
        { Métrica: 'Evaluaciones Último Mes', Valor: data.evaluaciones_ultimo_mes || 0 },
        { Métrica: 'Evaluaciones Última Semana', Valor: data.evaluaciones_ultima_semana || 0 }
      ]);
      
      utils.book_append_sheet(wb, estadisticasWS, 'Estadísticas Generales');
      
      // Hoja de KPIs
      if (data.kpis) {
        const kpisData = Object.entries(data.kpis).map(([key, value]) => ({
          KPI: key,
          Actual: value.current,
          Anterior: value.previous,
          Objetivo: value.target,
          Variación: ((value.current - value.previous) / value.previous * 100).toFixed(1) + '%'
        }));
        
        const kpisWS = utils.json_to_sheet(kpisData);
        utils.book_append_sheet(wb, kpisWS, 'KPIs');
      }
      
      // Hoja de tendencias
      if (data.trends) {
        const trendsWS = utils.json_to_sheet(data.trends);
        utils.book_append_sheet(wb, trendsWS, 'Tendencias');
      }
      
      // Generar archivo Excel
      const excelBuffer = write(wb, { bookType: 'xlsx', type: 'array' });
      const blob = new Blob([excelBuffer], { type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' });
      
      // Guardar archivo
      saveAs(blob, 'datos_bat7.xlsx');
      
      return true;
    } catch (error) {
      console.error('❌ [ExportService] Error al exportar a Excel:', error);
      throw error;
    }
  }
  
  /**
   * Exportar datos a PowerPoint (simulado)
   * @param {Object} data - Datos para exportar
   * @param {Object} options - Opciones de configuración
   */
  static async exportToPowerPoint(data, options = {}) {
    try {
      console.log('🎯 [ExportService] Exportando a PowerPoint...');
      
      // En una implementación real, aquí se generaría un archivo PPTX
      // Actualmente simulamos la exportación
      
      // Simular tiempo de procesamiento
      await new Promise(resolve => setTimeout(resolve, 1500));
      
      // Crear un archivo de texto como placeholder
      const content = `BAT-7 Presentación Ejecutiva
      
Fecha: ${new Date().toLocaleDateString()}

Estadísticas Generales:
- Total Pacientes: ${data.total_pacientes || 0}
- Pacientes Evaluados: ${data.pacientes_evaluados || 0}
- Total Evaluaciones: ${data.total_evaluaciones || 0}
- Percentil Promedio: ${data.percentil_promedio_general || 0}

Nota: Este es un archivo de texto simulado. En una implementación real, 
se generaría un archivo PowerPoint (.pptx) con gráficos y datos completos.`;
      
      const blob = new Blob([content], { type: 'text/plain' });
      saveAs(blob, 'presentacion_bat7.txt');
      
      return true;
    } catch (error) {
      console.error('❌ [ExportService] Error al exportar a PowerPoint:', error);
      throw error;
    }
  }
  
  /**
   * Enviar reporte por email (simulado)
   * @param {Object} data - Datos para enviar
   * @param {Object} options - Opciones de configuración
   */
  static async sendByEmail(data, options = {}) {
    try {
      console.log('📧 [ExportService] Enviando por email...');
      
      // En una implementación real, aquí se enviaría un email
      // Actualmente simulamos el envío
      
      // Simular tiempo de procesamiento
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      // Mostrar confirmación
      alert('Simulación: El reporte ha sido enviado por email correctamente.');
      
      return true;
    } catch (error) {
      console.error('❌ [ExportService] Error al enviar por email:', error);
      throw error;
    }
  }
}

export default ExportService;