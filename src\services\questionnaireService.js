// src/services/questionnaireService.js
import supabase from './supabaseService';

/**
 * Guarda las respuestas de un cuestionario en la base de datos.
 * @param {object} responsesData - El objeto con los datos a insertar.
 * @returns {Promise<object>} Los datos insertados.
 * @throws {Error} Si la inserción en Supabase falla.
 */
export const saveQuestionnaireResponses = async (responsesData) => {
  try {
    const { data, error } = await supabase
      .from('respuestas_cuestionario')
      .insert([responsesData])
      .select()
      .single();

    if (error) {
      throw error;
    }

    return data;
  } catch (error) {
    console.error('Error saving questionnaire responses:', error.message);
    throw new Error('Failed to save questionnaire responses.');
  }
};