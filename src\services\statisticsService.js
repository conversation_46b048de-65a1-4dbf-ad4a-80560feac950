import supabase from '../api/supabaseClient';

/**
 * Servicio para análisis estadístico avanzado de datos BAT-7
 * Implementa medidas de tendencia central, dispersión y distribución de frecuencias
 */
class StatisticsService {
  /**
   * Obtener medidas de tendencia central y dispersión para una aptitud específica
   * @param {string} aptitudCodigo - Código de la aptitud (V, E, A, R, N, M, O)
   * @param {Object} filters - Filtros opcionales (nivel, género, institución, etc.)
   */
  static async getMedidasTendenciaCentral(aptitudCodigo, filters = {}) {
    try {
      console.log(`📊 [StatisticsService] Calculando medidas para aptitud ${aptitudCodigo}...`);
      
      // Construir la consulta base
      let query = supabase
        .from('resultados')
        .select(`
          id, percentil, puntaje_directo,
          aptitudes:aptitud_id (codigo, nombre),
          pacientes:paciente_id (id, nivel_educativo, genero, institucion_id)
        `)
        .eq('aptitudes.codigo', aptitudCodigo);
      
      // Aplicar filtros si existen
      if (filters.nivel) {
        query = query.eq('pacientes.nivel_educativo', filters.nivel);
      }
      
      if (filters.genero) {
        query = query.eq('pacientes.genero', filters.genero);
      }
      
      if (filters.institucion) {
        query = query.eq('pacientes.institucion', filters.institucion);
      }
      
      if (filters.dateRange) {
        query = query.gte('created_at', filters.dateRange.start);
        query = query.lte('created_at', filters.dateRange.end);
      }
      
      // Ejecutar la consulta
      const { data: resultados, error } = await query;
      
      if (error) throw error;
      
      // Si no hay datos, devolver valores por defecto
      if (!resultados || resultados.length === 0) {
        return {
          aptitud: aptitudCodigo,
          nombre_aptitud: this.getNombreAptitud(aptitudCodigo),
          n_muestras: 0,
          percentil: {
            media: 0,
            mediana: 0,
            moda: 0,
            desviacion_estandar: 0,
            rango_intercuartilico: 0,
            minimo: 0,
            maximo: 0,
            q1: 0,
            q3: 0
          },
          puntuacion_directa: {
            media: 0,
            mediana: 0,
            moda: 0,
            desviacion_estandar: 0,
            rango_intercuartilico: 0,
            minimo: 0,
            maximo: 0,
            q1: 0,
            q3: 0
          }
        };
      }
      
      // Extraer valores de percentil y puntuación directa
      const percentiles = resultados
        .map(r => r.percentil)
        .filter(p => p !== null && p !== undefined);
      
      const puntuacionesDirectas = resultados
        .map(r => r.puntaje_directo)
        .filter(p => p !== null && p !== undefined);
      
      // Calcular medidas para percentiles
      const medidasPercentil = this.calcularMedidasEstadisticas(percentiles);
      
      // Calcular medidas para puntuaciones directas
      const medidasPD = this.calcularMedidasEstadisticas(puntuacionesDirectas);
      
      return {
        aptitud: aptitudCodigo,
        nombre_aptitud: this.getNombreAptitud(aptitudCodigo),
        n_muestras: resultados.length,
        percentil: medidasPercentil,
        puntuacion_directa: medidasPD
      };
      
    } catch (error) {
      console.error(`❌ [StatisticsService] Error al calcular medidas para ${aptitudCodigo}:`, error);
      throw error;
    }
  }
  
  /**
   * Obtener medidas de tendencia central y dispersión para todas las aptitudes
   * @param {Object} filters - Filtros opcionales (nivel, género, institución, etc.)
   */
  static async getMedidasTodasAptitudes(filters = {}) {
    try {
      console.log('📊 [StatisticsService] Calculando medidas para todas las aptitudes...');
      
      // Códigos de aptitudes BAT-7
      const codigosAptitudes = ['V', 'E', 'A', 'R', 'N', 'M', 'O'];
      
      // Obtener medidas para cada aptitud en paralelo
      const resultados = await Promise.all(
        codigosAptitudes.map(codigo => this.getMedidasTendenciaCentral(codigo, filters))
      );
      
      return {
        medidas_por_aptitud: resultados,
        fecha_calculo: new Date().toISOString()
      };
      
    } catch (error) {
      console.error('❌ [StatisticsService] Error al calcular medidas para todas las aptitudes:', error);
      throw error;
    }
  }
  
  /**
   * Obtener distribución de frecuencias para una aptitud específica
   * @param {string} aptitudCodigo - Código de la aptitud (V, E, A, R, N, M, O)
   * @param {Object} filters - Filtros opcionales (nivel, género, institución, etc.)
   * @param {number} numBins - Número de intervalos para la distribución (default: 5)
   */
  static async getDistribucionFrecuencias(aptitudCodigo, filters = {}, numBins = 5) {
    try {
      console.log(`📊 [StatisticsService] Calculando distribución para aptitud ${aptitudCodigo}...`);
      
      // Construir la consulta base
      let query = supabase
        .from('resultados')
        .select(`
          id, percentil, puntaje_directo,
          aptitudes:aptitud_id (codigo, nombre),
          pacientes:paciente_id (id, nivel_educativo, genero, institucion_id)
        `)
        .eq('aptitudes.codigo', aptitudCodigo);
      
      // Aplicar filtros si existen
      if (filters.nivel) {
        query = query.eq('pacientes.nivel_educativo', filters.nivel);
      }
      
      if (filters.genero) {
        query = query.eq('pacientes.genero', filters.genero);
      }
      
      if (filters.institucion) {
        query = query.eq('pacientes.institucion', filters.institucion);
      }
      
      if (filters.dateRange) {
        query = query.gte('created_at', filters.dateRange.start);
        query = query.lte('created_at', filters.dateRange.end);
      }
      
      // Ejecutar la consulta
      const { data: resultados, error } = await query;
      
      if (error) throw error;
      
      // Si no hay datos, devolver valores por defecto
      if (!resultados || resultados.length === 0) {
        return {
          aptitud: aptitudCodigo,
          nombre_aptitud: this.getNombreAptitud(aptitudCodigo),
          n_muestras: 0,
          distribucion_percentil: [],
          distribucion_pd: []
        };
      }
      
      // Extraer valores de percentil y puntuación directa
      const percentiles = resultados
        .map(r => r.percentil)
        .filter(p => p !== null && p !== undefined);
      
      const puntuacionesDirectas = resultados
        .map(r => r.puntaje_directo)
        .filter(p => p !== null && p !== undefined);
      
      // Calcular distribución de frecuencias para percentiles
      const distribucionPercentil = this.calcularDistribucionFrecuencias(percentiles, numBins, 0, 100);
      
      // Calcular distribución de frecuencias para puntuaciones directas
      // Para PD, calculamos el rango dinámicamente
      const minPD = Math.min(...puntuacionesDirectas);
      const maxPD = Math.max(...puntuacionesDirectas);
      const distribucionPD = this.calcularDistribucionFrecuencias(puntuacionesDirectas, numBins, minPD, maxPD);
      
      // Calcular percentiles específicos (25, 50, 75)
      const percentiles_clave = {
        p25: this.calcularPercentil(percentiles, 25),
        p50: this.calcularPercentil(percentiles, 50),
        p75: this.calcularPercentil(percentiles, 75)
      };
      
      return {
        aptitud: aptitudCodigo,
        nombre_aptitud: this.getNombreAptitud(aptitudCodigo),
        n_muestras: resultados.length,
        distribucion_percentil: distribucionPercentil,
        distribucion_pd: distribucionPD,
        percentiles_clave
      };
      
    } catch (error) {
      console.error(`❌ [StatisticsService] Error al calcular distribución para ${aptitudCodigo}:`, error);
      throw error;
    }
  }
  
  /**
   * Obtener distribución de frecuencias para todas las aptitudes
   * @param {Object} filters - Filtros opcionales (nivel, género, institución, etc.)
   * @param {number} numBins - Número de intervalos para la distribución (default: 5)
   */
  static async getDistribucionTodasAptitudes(filters = {}, numBins = 5) {
    try {
      console.log('📊 [StatisticsService] Calculando distribución para todas las aptitudes...');
      
      // Códigos de aptitudes BAT-7
      const codigosAptitudes = ['V', 'E', 'A', 'R', 'N', 'M', 'O'];
      
      // Obtener distribución para cada aptitud en paralelo
      const resultados = await Promise.all(
        codigosAptitudes.map(codigo => this.getDistribucionFrecuencias(codigo, filters, numBins))
      );
      
      return {
        distribuciones_por_aptitud: resultados,
        fecha_calculo: new Date().toISOString()
      };
      
    } catch (error) {
      console.error('❌ [StatisticsService] Error al calcular distribución para todas las aptitudes:', error);
      throw error;
    }
  }
  
  /**
   * Obtener análisis estadístico completo (tendencia central + distribución)
   * @param {Object} filters - Filtros opcionales (nivel, género, institución, etc.)
   */
  static async getAnalisisEstadisticoCompleto(filters = {}) {
    try {
      console.log('📊 [StatisticsService] Generando análisis estadístico completo...');
      
      // Obtener ambos análisis en paralelo
      const [medidasTendencia, distribucionesFrecuencia] = await Promise.all([
        this.getMedidasTodasAptitudes(filters),
        this.getDistribucionTodasAptitudes(filters)
      ]);
      
      // Combinar resultados
      const resultadosPorAptitud = {};
      
      // Códigos de aptitudes BAT-7
      const codigosAptitudes = ['V', 'E', 'A', 'R', 'N', 'M', 'O'];
      
      // Combinar datos por aptitud
      codigosAptitudes.forEach(codigo => {
        const medidas = medidasTendencia.medidas_por_aptitud.find(m => m.aptitud === codigo);
        const distribucion = distribucionesFrecuencia.distribuciones_por_aptitud.find(d => d.aptitud === codigo);
        
        resultadosPorAptitud[codigo] = {
          aptitud: codigo,
          nombre_aptitud: this.getNombreAptitud(codigo),
          medidas_tendencia_central: medidas,
          distribucion_frecuencias: distribucion
        };
      });
      
      return {
        resultados_por_aptitud: resultadosPorAptitud,
        fecha_analisis: new Date().toISOString(),
        filtros_aplicados: filters
      };
      
    } catch (error) {
      console.error('❌ [StatisticsService] Error al generar análisis estadístico completo:', error);
      throw error;
    }
  }
  
  /**
   * Obtener análisis de correlación entre aptitudes
   * @param {Object} filters - Filtros opcionales (nivel, género, institución, etc.)
   */
  static async getAnalisisCorrelacion(filters = {}) {
    try {
      console.log('📊 [StatisticsService] Calculando correlaciones entre aptitudes...');
      
      // Obtener todos los resultados con filtros aplicados
      let query = supabase
        .from('resultados')
        .select(`
          id, percentil, aptitud_id, paciente_id,
          aptitudes:aptitud_id (codigo, nombre),
          pacientes:paciente_id (id, nivel_educativo, genero, institucion_id)
        `);
      
      // Aplicar filtros si existen
      if (filters.nivel) {
        query = query.eq('pacientes.nivel_educativo', filters.nivel);
      }
      
      if (filters.genero) {
        query = query.eq('pacientes.genero', filters.genero);
      }
      
      if (filters.institucion) {
        query = query.eq('pacientes.institucion', filters.institucion);
      }
      
      if (filters.dateRange) {
        query = query.gte('created_at', filters.dateRange.start);
        query = query.lte('created_at', filters.dateRange.end);
      }
      
      // Ejecutar la consulta
      const { data: resultados, error } = await query;
      
      if (error) throw error;
      
      // Si no hay datos, devolver matriz vacía
      if (!resultados || resultados.length === 0) {
        return {
          matriz_correlacion: {},
          interpretacion: "No hay datos suficientes para calcular correlaciones"
        };
      }
      
      // Códigos de aptitudes BAT-7
      const codigosAptitudes = ['V', 'E', 'A', 'R', 'N', 'M', 'O'];
      
      // Agrupar resultados por paciente y aptitud
      const resultadosPorPaciente = {};
      
      resultados.forEach(r => {
        const pacienteId = r.paciente_id;
        const aptitudCodigo = r.aptitudes?.codigo;
        
        if (!pacienteId || !aptitudCodigo) return;
        
        if (!resultadosPorPaciente[pacienteId]) {
          resultadosPorPaciente[pacienteId] = {};
        }
        
        resultadosPorPaciente[pacienteId][aptitudCodigo] = r.percentil;
      });
      
      // Calcular matriz de correlación
      const matrizCorrelacion = {};
      
      codigosAptitudes.forEach(codigo1 => {
        matrizCorrelacion[codigo1] = {};
        
        codigosAptitudes.forEach(codigo2 => {
          // Extraer pares de valores para las dos aptitudes
          const pares = Object.values(resultadosPorPaciente)
            .filter(p => p[codigo1] !== undefined && p[codigo2] !== undefined)
            .map(p => [p[codigo1], p[codigo2]]);
          
          // Calcular correlación de Pearson
          const correlacion = pares.length > 5 ? this.calcularCorrelacionPearson(pares) : null;
          
          matrizCorrelacion[codigo1][codigo2] = correlacion;
        });
      });
      
      // Generar interpretaciones para correlaciones significativas
      const interpretaciones = [];
      
      codigosAptitudes.forEach((codigo1, i) => {
        codigosAptitudes.slice(i + 1).forEach(codigo2 => {
          const correlacion = matrizCorrelacion[codigo1][codigo2];
          
          if (correlacion !== null) {
            const absCorrelacion = Math.abs(correlacion);
            let fuerza = '';
            let interpretacion = '';
            
            if (absCorrelacion >= 0.7) {
              fuerza = 'fuerte';
            } else if (absCorrelacion >= 0.5) {
              fuerza = 'moderada';
            } else if (absCorrelacion >= 0.3) {
              fuerza = 'débil';
            } else {
              fuerza = 'muy débil';
            }
            
            if (correlacion > 0) {
              interpretacion = `Correlación ${fuerza} positiva (${correlacion.toFixed(2)}) entre ${this.getNombreAptitud(codigo1)} y ${this.getNombreAptitud(codigo2)}`;
            } else {
              interpretacion = `Correlación ${fuerza} negativa (${correlacion.toFixed(2)}) entre ${this.getNombreAptitud(codigo1)} y ${this.getNombreAptitud(codigo2)}`;
            }
            
            interpretaciones.push({
              aptitud1: codigo1,
              aptitud2: codigo2,
              correlacion: correlacion,
              interpretacion: interpretacion
            });
          }
        });
      });
      
      // Ordenar interpretaciones por fuerza de correlación (absoluta)
      interpretaciones.sort((a, b) => Math.abs(b.correlacion) - Math.abs(a.correlacion));
      
      return {
        matriz_correlacion: matrizCorrelacion,
        interpretaciones: interpretaciones
      };
      
    } catch (error) {
      console.error('❌ [StatisticsService] Error al calcular correlaciones:', error);
      throw error;
    }
  }
  
  /**
   * Obtener análisis de grupos de riesgo y talento
   * @param {Object} filters - Filtros opcionales (nivel, género, institución, etc.)
   * @param {number} umbralRiesgo - Percentil por debajo del cual se considera riesgo (default: 25)
   * @param {number} umbralTalento - Percentil por encima del cual se considera talento (default: 75)
   */
  static async getAnalisisGrupos(filters = {}, umbralRiesgo = 25, umbralTalento = 75) {
    try {
      console.log('📊 [StatisticsService] Analizando grupos de riesgo y talento...');
      
      // Obtener todos los resultados con filtros aplicados
      let query = supabase
        .from('resultados')
        .select(`
          id, percentil, aptitud_id, paciente_id,
          aptitudes:aptitud_id (codigo, nombre),
          pacientes:paciente_id (id, nombre, apellido, nivel_educativo, genero, institucion_id)
        `);
      
      // Aplicar filtros si existen
      if (filters.nivel) {
        query = query.eq('pacientes.nivel_educativo', filters.nivel);
      }
      
      if (filters.genero) {
        query = query.eq('pacientes.genero', filters.genero);
      }
      
      if (filters.institucion) {
        query = query.eq('pacientes.institucion_id', filters.institucion);
      }
      
      if (filters.dateRange) {
        query = query.gte('created_at', filters.dateRange.start);
        query = query.lte('created_at', filters.dateRange.end);
      }
      
      // Ejecutar la consulta
      const { data: resultados, error } = await query;
      
      if (error) throw error;
      
      // Si no hay datos, devolver análisis vacío
      if (!resultados || resultados.length === 0) {
        return {
          grupos_riesgo: {},
          grupos_talento: {},
          resumen: {
            total_pacientes: 0,
            pacientes_riesgo: 0,
            pacientes_talento: 0,
            porcentaje_riesgo: 0,
            porcentaje_talento: 0
          }
        };
      }
      
      // Códigos de aptitudes BAT-7
      const codigosAptitudes = ['V', 'E', 'A', 'R', 'N', 'M', 'O'];
      
      // Inicializar grupos por aptitud
      const gruposRiesgo = {};
      const gruposTalento = {};
      
      codigosAptitudes.forEach(codigo => {
        gruposRiesgo[codigo] = [];
        gruposTalento[codigo] = [];
      });
      
      // Conjunto de pacientes únicos para estadísticas
      const pacientesUnicos = new Set();
      const pacientesRiesgo = new Set();
      const pacientesTalento = new Set();
      
      // Clasificar resultados por grupos
      resultados.forEach(r => {
        const pacienteId = r.paciente_id;
        const aptitudCodigo = r.aptitudes?.codigo;
        const percentil = r.percentil;
        
        if (!pacienteId || !aptitudCodigo || percentil === null || percentil === undefined) return;
        
        // Registrar paciente único
        pacientesUnicos.add(pacienteId);
        
        // Datos del paciente para incluir en los grupos
        const pacienteData = {
          id: pacienteId,
          nombre: r.pacientes?.nombre || 'Sin nombre',
          apellido: r.pacientes?.apellido || '',
          nivel: r.pacientes?.nivel_educativo || 'Sin definir',
          genero: r.pacientes?.genero || 'No especificado',
          percentil: percentil
        };
        
        // Clasificar en grupo de riesgo
        if (percentil <= umbralRiesgo) {
          gruposRiesgo[aptitudCodigo].push(pacienteData);
          pacientesRiesgo.add(pacienteId);
        }
        
        // Clasificar en grupo de talento
        if (percentil >= umbralTalento) {
          gruposTalento[aptitudCodigo].push(pacienteData);
          pacientesTalento.add(pacienteId);
        }
      });
      
      // Calcular estadísticas de resumen
      const totalPacientes = pacientesUnicos.size;
      const totalRiesgo = pacientesRiesgo.size;
      const totalTalento = pacientesTalento.size;
      
      const resumen = {
        total_pacientes: totalPacientes,
        pacientes_riesgo: totalRiesgo,
        pacientes_talento: totalTalento,
        porcentaje_riesgo: totalPacientes > 0 ? (totalRiesgo / totalPacientes * 100).toFixed(1) : 0,
        porcentaje_talento: totalPacientes > 0 ? (totalTalento / totalPacientes * 100).toFixed(1) : 0
      };
      
      // Ordenar grupos por percentil (ascendente para riesgo, descendente para talento)
      codigosAptitudes.forEach(codigo => {
        gruposRiesgo[codigo].sort((a, b) => a.percentil - b.percentil);
        gruposTalento[codigo].sort((a, b) => b.percentil - a.percentil);
      });
      
      return {
        grupos_riesgo: gruposRiesgo,
        grupos_talento: gruposTalento,
        resumen: resumen
      };
      
    } catch (error) {
      console.error('❌ [StatisticsService] Error al analizar grupos:', error);
      throw error;
    }
  }
  
  // ===== MÉTODOS AUXILIARES =====
  
  /**
   * Calcular medidas estadísticas básicas para un conjunto de datos
   * @param {Array<number>} datos - Array de valores numéricos
   * @returns {Object} Objeto con las medidas calculadas
   */
  static calcularMedidasEstadisticas(datos) {
    // Si no hay datos, devolver valores por defecto
    if (!datos || datos.length === 0) {
      return {
        media: 0,
        mediana: 0,
        moda: 0,
        desviacion_estandar: 0,
        rango_intercuartilico: 0,
        minimo: 0,
        maximo: 0,
        q1: 0,
        q3: 0
      };
    }
    
    // Ordenar datos para cálculos
    const datosOrdenados = [...datos].sort((a, b) => a - b);
    const n = datosOrdenados.length;
    
    // Media
    const media = datosOrdenados.reduce((sum, val) => sum + val, 0) / n;
    
    // Mediana
    const mediana = this.calcularPercentil(datosOrdenados, 50);
    
    // Moda
    const moda = this.calcularModa(datosOrdenados);
    
    // Desviación estándar
    const varianza = datosOrdenados.reduce((sum, val) => sum + Math.pow(val - media, 2), 0) / n;
    const desviacionEstandar = Math.sqrt(varianza);
    
    // Cuartiles
    const q1 = this.calcularPercentil(datosOrdenados, 25);
    const q3 = this.calcularPercentil(datosOrdenados, 75);
    
    // Rango intercuartílico
    const rangoIntercuartilico = q3 - q1;
    
    // Mínimo y máximo
    const minimo = datosOrdenados[0];
    const maximo = datosOrdenados[n - 1];
    
    return {
      media: parseFloat(media.toFixed(2)),
      mediana: parseFloat(mediana.toFixed(2)),
      moda: parseFloat(moda.toFixed(2)),
      desviacion_estandar: parseFloat(desviacionEstandar.toFixed(2)),
      rango_intercuartilico: parseFloat(rangoIntercuartilico.toFixed(2)),
      minimo: parseFloat(minimo.toFixed(2)),
      maximo: parseFloat(maximo.toFixed(2)),
      q1: parseFloat(q1.toFixed(2)),
      q3: parseFloat(q3.toFixed(2))
    };
  }
  
  /**
   * Calcular la moda de un conjunto de datos
   * @param {Array<number>} datos - Array de valores numéricos ordenados
   * @returns {number} La moda (valor más frecuente)
   */
  static calcularModa(datos) {
    if (!datos || datos.length === 0) return 0;
    
    const frecuencias = {};
    let maxFrecuencia = 0;
    let moda = datos[0];
    
    datos.forEach(valor => {
      frecuencias[valor] = (frecuencias[valor] || 0) + 1;
      
      if (frecuencias[valor] > maxFrecuencia) {
        maxFrecuencia = frecuencias[valor];
        moda = valor;
      }
    });
    
    return moda;
  }
  
  /**
   * Calcular un percentil específico de un conjunto de datos
   * @param {Array<number>} datos - Array de valores numéricos
   * @param {number} percentil - Percentil a calcular (0-100)
   * @returns {number} El valor del percentil
   */
  static calcularPercentil(datos, percentil) {
    if (!datos || datos.length === 0) return 0;
    
    // Asegurar que los datos estén ordenados
    const datosOrdenados = [...datos].sort((a, b) => a - b);
    const n = datosOrdenados.length;
    
    // Calcular la posición
    const posicion = (percentil / 100) * (n - 1);
    const posicionEntera = Math.floor(posicion);
    const fraccion = posicion - posicionEntera;
    
    // Si la posición es entera, devolver el valor en esa posición
    if (fraccion === 0) {
      return datosOrdenados[posicionEntera];
    }
    
    // Si no, interpolar entre los dos valores más cercanos
    const valorInferior = datosOrdenados[posicionEntera];
    const valorSuperior = datosOrdenados[posicionEntera + 1];
    
    return valorInferior + fraccion * (valorSuperior - valorInferior);
  }
  
  /**
   * Calcular distribución de frecuencias para un conjunto de datos
   * @param {Array<number>} datos - Array de valores numéricos
   * @param {number} numBins - Número de intervalos
   * @param {number} min - Valor mínimo para el rango (opcional)
   * @param {number} max - Valor máximo para el rango (opcional)
   * @returns {Array<Object>} Array con la distribución de frecuencias
   */
  static calcularDistribucionFrecuencias(datos, numBins = 5, min = null, max = null) {
    if (!datos || datos.length === 0) {
      return [];
    }
    
    // Si no se especifican min y max, calcularlos de los datos
    const minValor = min !== null ? min : Math.min(...datos);
    const maxValor = max !== null ? max : Math.max(...datos);
    
    // Calcular el ancho de cada intervalo
    const rango = maxValor - minValor;
    const anchoBin = rango / numBins;
    
    // Inicializar bins
    const bins = Array(numBins).fill(0).map((_, i) => {
      const limiteInferior = minValor + i * anchoBin;
      const limiteSuperior = i === numBins - 1 ? maxValor : minValor + (i + 1) * anchoBin;
      
      return {
        bin: i + 1,
        limite_inferior: parseFloat(limiteInferior.toFixed(2)),
        limite_superior: parseFloat(limiteSuperior.toFixed(2)),
        frecuencia: 0,
        porcentaje: 0
      };
    });
    
    // Contar frecuencias
    datos.forEach(valor => {
      for (let i = 0; i < numBins; i++) {
        const limiteInferior = bins[i].limite_inferior;
        const limiteSuperior = bins[i].limite_superior;
        
        // Para el último bin, incluir el valor máximo
        if (i === numBins - 1) {
          if (valor >= limiteInferior && valor <= limiteSuperior) {
            bins[i].frecuencia++;
            break;
          }
        } else {
          if (valor >= limiteInferior && valor < limiteSuperior) {
            bins[i].frecuencia++;
            break;
          }
        }
      }
    });
    
    // Calcular porcentajes
    const total = datos.length;
    bins.forEach(bin => {
      bin.porcentaje = parseFloat(((bin.frecuencia / total) * 100).toFixed(1));
    });
    
    return bins;
  }
  
  /**
   * Calcular correlación de Pearson entre dos variables
   * @param {Array<Array<number>>} pares - Array de pares [x, y]
   * @returns {number} Coeficiente de correlación de Pearson
   */
  static calcularCorrelacionPearson(pares) {
    if (!pares || pares.length < 2) return null;
    
    const n = pares.length;
    
    // Extraer valores x e y
    const x = pares.map(par => par[0]);
    const y = pares.map(par => par[1]);
    
    // Calcular medias
    const mediaX = x.reduce((sum, val) => sum + val, 0) / n;
    const mediaY = y.reduce((sum, val) => sum + val, 0) / n;
    
    // Calcular covarianza y desviaciones estándar
    let covarianza = 0;
    let varianzaX = 0;
    let varianzaY = 0;
    
    for (let i = 0; i < n; i++) {
      const difX = x[i] - mediaX;
      const difY = y[i] - mediaY;
      
      covarianza += difX * difY;
      varianzaX += difX * difX;
      varianzaY += difY * difY;
    }
    
    // Evitar división por cero
    if (varianzaX === 0 || varianzaY === 0) return 0;
    
    // Calcular correlación
    const correlacion = covarianza / (Math.sqrt(varianzaX) * Math.sqrt(varianzaY));
    
    // Limitar a rango [-1, 1] por precisión numérica
    return Math.max(-1, Math.min(1, correlacion));
  }
  
  /**
   * Obtener el nombre completo de una aptitud a partir de su código
   * @param {string} codigo - Código de la aptitud
   * @returns {string} Nombre completo de la aptitud
   */
  static getNombreAptitud(codigo) {
    const nombresAptitudes = {
      'V': 'Aptitud Verbal',
      'E': 'Aptitud Espacial',
      'A': 'Atención',
      'R': 'Razonamiento',
      'N': 'Aptitud Numérica',
      'M': 'Aptitud Mecánica',
      'O': 'Ortografía'
    };
    
    return nombresAptitudes[codigo] || `Aptitud ${codigo}`;
  }
}

export default StatisticsService;