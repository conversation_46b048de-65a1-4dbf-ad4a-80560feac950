-- =====================================================
-- VISTAS SQL PARA DASHBOARD BAT-7
-- =====================================================

-- 1. Vista: Estadísticas Generales del Dashboard
CREATE OR REPLACE VIEW dashboard_estadisticas_generales AS
SELECT 
    COUNT(DISTINCT p.id) as total_pacientes,
    COUNT(DISTINCT CASE WHEN r.id IS NOT NULL THEN p.id END) as pacientes_evaluados,
    COUNT(r.id) as total_evaluaciones,
    ROUND(AVG(r.percentil_general), 2) as percentil_promedio_general,
    COUNT(CASE WHEN r.created_at >= CURRENT_DATE - INTERVAL '30 days' THEN 1 END) as evaluaciones_ultimo_mes,
    COUNT(CASE WHEN r.created_at >= CURRENT_DATE - INTERVAL '7 days' THEN 1 END) as evaluaciones_ultima_semana,
    CURRENT_TIMESTAMP as ultima_actualizacion
FROM pacientes p
LEFT JOIN resultados r ON p.id = r.paciente_id;

-- 2. Vista: Distribución de Estudiantes por Nivel
CREATE OR REPLACE VIEW dashboard_estudiantes_por_nivel AS
SELECT 
    p.nivel_educativo,
    CASE 
        WHEN p.nivel_educativo = 'E' THEN 'Elemental'
        WHEN p.nivel_educativo = 'M' THEN 'Medio'
        WHEN p.nivel_educativo = 'S' THEN 'Superior'
        ELSE 'No Especificado'
    END as nivel_nombre,
    COUNT(DISTINCT p.id) as total_estudiantes,
    COUNT(DISTINCT CASE WHEN r.id IS NOT NULL THEN p.id END) as estudiantes_evaluados,
    ROUND(
        (COUNT(DISTINCT CASE WHEN r.id IS NOT NULL THEN p.id END) * 100.0) / 
        NULLIF(COUNT(DISTINCT p.id), 0), 2
    ) as porcentaje_evaluados
FROM pacientes p
LEFT JOIN resultados r ON p.id = r.paciente_id
WHERE p.nivel_educativo IS NOT NULL
GROUP BY p.nivel_educativo
ORDER BY p.nivel_educativo;

-- 3. Vista: Perfil de Aptitud Institucional
CREATE OR REPLACE VIEW dashboard_perfil_institucional AS
SELECT 
    'V' as aptitud_codigo,
    'Verbal' as aptitud_nombre,
    ROUND(AVG(r.percentil_v), 2) as percentil_promedio,
    COUNT(r.id) as total_evaluaciones,
    STDDEV(r.percentil_v) as desviacion_estandar
FROM resultados r
WHERE r.percentil_v IS NOT NULL

UNION ALL

SELECT 
    'E' as aptitud_codigo,
    'Espacial' as aptitud_nombre,
    ROUND(AVG(r.percentil_e), 2) as percentil_promedio,
    COUNT(r.id) as total_evaluaciones,
    STDDEV(r.percentil_e) as desviacion_estandar
FROM resultados r
WHERE r.percentil_e IS NOT NULL

UNION ALL

SELECT 
    'A' as aptitud_codigo,
    'Atención' as aptitud_nombre,
    ROUND(AVG(r.percentil_a), 2) as percentil_promedio,
    COUNT(r.id) as total_evaluaciones,
    STDDEV(r.percentil_a) as desviacion_estandar
FROM resultados r
WHERE r.percentil_a IS NOT NULL

UNION ALL

SELECT 
    'R' as aptitud_codigo,
    'Razonamiento' as aptitud_nombre,
    ROUND(AVG(r.percentil_r), 2) as percentil_promedio,
    COUNT(r.id) as total_evaluaciones,
    STDDEV(r.percentil_r) as desviacion_estandar
FROM resultados r
WHERE r.percentil_r IS NOT NULL

UNION ALL

SELECT 
    'N' as aptitud_codigo,
    'Numérica' as aptitud_nombre,
    ROUND(AVG(r.percentil_n), 2) as percentil_promedio,
    COUNT(r.id) as total_evaluaciones,
    STDDEV(r.percentil_n) as desviacion_estandar
FROM resultados r
WHERE r.percentil_n IS NOT NULL

UNION ALL

SELECT 
    'M' as aptitud_codigo,
    'Mecánica' as aptitud_nombre,
    ROUND(AVG(r.percentil_m), 2) as percentil_promedio,
    COUNT(r.id) as total_evaluaciones,
    STDDEV(r.percentil_m) as desviacion_estandar
FROM resultados r
WHERE r.percentil_m IS NOT NULL

UNION ALL

SELECT 
    'O' as aptitud_codigo,
    'Ortografía' as aptitud_nombre,
    ROUND(AVG(r.percentil_o), 2) as percentil_promedio,
    COUNT(r.id) as total_evaluaciones,
    STDDEV(r.percentil_o) as desviacion_estandar
FROM resultados r
WHERE r.percentil_o IS NOT NULL

ORDER BY aptitud_codigo;

-- 4. Vista: Perfil de Aptitud por Nivel Educativo
CREATE OR REPLACE VIEW dashboard_perfil_por_nivel AS
SELECT 
    p.nivel_educativo,
    CASE 
        WHEN p.nivel_educativo = 'E' THEN 'Elemental'
        WHEN p.nivel_educativo = 'M' THEN 'Medio'
        WHEN p.nivel_educativo = 'S' THEN 'Superior'
        ELSE 'No Especificado'
    END as nivel_nombre,
    'V' as aptitud_codigo,
    'Verbal' as aptitud_nombre,
    ROUND(AVG(r.percentil_v), 2) as percentil_promedio,
    COUNT(r.id) as total_evaluaciones
FROM pacientes p
JOIN resultados r ON p.id = r.paciente_id
WHERE p.nivel_educativo IS NOT NULL AND r.percentil_v IS NOT NULL
GROUP BY p.nivel_educativo

UNION ALL

SELECT 
    p.nivel_educativo,
    CASE 
        WHEN p.nivel_educativo = 'E' THEN 'Elemental'
        WHEN p.nivel_educativo = 'M' THEN 'Medio'
        WHEN p.nivel_educativo = 'S' THEN 'Superior'
        ELSE 'No Especificado'
    END as nivel_nombre,
    'E' as aptitud_codigo,
    'Espacial' as aptitud_nombre,
    ROUND(AVG(r.percentil_e), 2) as percentil_promedio,
    COUNT(r.id) as total_evaluaciones
FROM pacientes p
JOIN resultados r ON p.id = r.paciente_id
WHERE p.nivel_educativo IS NOT NULL AND r.percentil_e IS NOT NULL
GROUP BY p.nivel_educativo

UNION ALL

SELECT 
    p.nivel_educativo,
    CASE 
        WHEN p.nivel_educativo = 'E' THEN 'Elemental'
        WHEN p.nivel_educativo = 'M' THEN 'Medio'
        WHEN p.nivel_educativo = 'S' THEN 'Superior'
        ELSE 'No Especificado'
    END as nivel_nombre,
    'A' as aptitud_codigo,
    'Atención' as aptitud_nombre,
    ROUND(AVG(r.percentil_a), 2) as percentil_promedio,
    COUNT(r.id) as total_evaluaciones
FROM pacientes p
JOIN resultados r ON p.id = r.paciente_id
WHERE p.nivel_educativo IS NOT NULL AND r.percentil_a IS NOT NULL
GROUP BY p.nivel_educativo

UNION ALL

SELECT 
    p.nivel_educativo,
    CASE 
        WHEN p.nivel_educativo = 'E' THEN 'Elemental'
        WHEN p.nivel_educativo = 'M' THEN 'Medio'
        WHEN p.nivel_educativo = 'S' THEN 'Superior'
        ELSE 'No Especificado'
    END as nivel_nombre,
    'R' as aptitud_codigo,
    'Razonamiento' as aptitud_nombre,
    ROUND(AVG(r.percentil_r), 2) as percentil_promedio,
    COUNT(r.id) as total_evaluaciones
FROM pacientes p
JOIN resultados r ON p.id = r.paciente_id
WHERE p.nivel_educativo IS NOT NULL AND r.percentil_r IS NOT NULL
GROUP BY p.nivel_educativo

UNION ALL

SELECT 
    p.nivel_educativo,
    CASE 
        WHEN p.nivel_educativo = 'E' THEN 'Elemental'
        WHEN p.nivel_educativo = 'M' THEN 'Medio'
        WHEN p.nivel_educativo = 'S' THEN 'Superior'
        ELSE 'No Especificado'
    END as nivel_nombre,
    'N' as aptitud_codigo,
    'Numérica' as aptitud_nombre,
    ROUND(AVG(r.percentil_n), 2) as percentil_promedio,
    COUNT(r.id) as total_evaluaciones
FROM pacientes p
JOIN resultados r ON p.id = r.paciente_id
WHERE p.nivel_educativo IS NOT NULL AND r.percentil_n IS NOT NULL
GROUP BY p.nivel_educativo

UNION ALL

SELECT 
    p.nivel_educativo,
    CASE 
        WHEN p.nivel_educativo = 'E' THEN 'Elemental'
        WHEN p.nivel_educativo = 'M' THEN 'Medio'
        WHEN p.nivel_educativo = 'S' THEN 'Superior'
        ELSE 'No Especificado'
    END as nivel_nombre,
    'M' as aptitud_codigo,
    'Mecánica' as aptitud_nombre,
    ROUND(AVG(r.percentil_m), 2) as percentil_promedio,
    COUNT(r.id) as total_evaluaciones
FROM pacientes p
JOIN resultados r ON p.id = r.paciente_id
WHERE p.nivel_educativo IS NOT NULL AND r.percentil_m IS NOT NULL
GROUP BY p.nivel_educativo

UNION ALL

SELECT 
    p.nivel_educativo,
    CASE 
        WHEN p.nivel_educativo = 'E' THEN 'Elemental'
        WHEN p.nivel_educativo = 'M' THEN 'Medio'
        WHEN p.nivel_educativo = 'S' THEN 'Superior'
        ELSE 'No Especificado'
    END as nivel_nombre,
    'O' as aptitud_codigo,
    'Ortografía' as aptitud_nombre,
    ROUND(AVG(r.percentil_o), 2) as percentil_promedio,
    COUNT(r.id) as total_evaluaciones
FROM pacientes p
JOIN resultados r ON p.id = r.paciente_id
WHERE p.nivel_educativo IS NOT NULL AND r.percentil_o IS NOT NULL
GROUP BY p.nivel_educativo

ORDER BY nivel_educativo, aptitud_codigo;

-- 5. Vista: Distribución de Rendimiento por Aptitud y Nivel
CREATE OR REPLACE VIEW dashboard_distribucion_rendimiento AS
WITH rendimiento_categorizado AS (
    SELECT
        p.nivel_educativo,
        CASE
            WHEN p.nivel_educativo = 'E' THEN 'Elemental'
            WHEN p.nivel_educativo = 'M' THEN 'Medio'
            WHEN p.nivel_educativo = 'S' THEN 'Superior'
            ELSE 'No Especificado'
        END as nivel_nombre,
        'V' as aptitud_codigo,
        'Verbal' as aptitud_nombre,
        CASE
            WHEN r.percentil_v < 30 THEN 'Bajo'
            WHEN r.percentil_v >= 30 AND r.percentil_v <= 70 THEN 'Promedio'
            WHEN r.percentil_v > 70 THEN 'Alto'
            ELSE 'Sin Datos'
        END as categoria_rendimiento,
        COUNT(*) as cantidad
    FROM pacientes p
    JOIN resultados r ON p.id = r.paciente_id
    WHERE p.nivel_educativo IS NOT NULL AND r.percentil_v IS NOT NULL
    GROUP BY p.nivel_educativo,
             CASE
                WHEN r.percentil_v < 30 THEN 'Bajo'
                WHEN r.percentil_v >= 30 AND r.percentil_v <= 70 THEN 'Promedio'
                WHEN r.percentil_v > 70 THEN 'Alto'
                ELSE 'Sin Datos'
             END

    UNION ALL

    SELECT
        p.nivel_educativo,
        CASE
            WHEN p.nivel_educativo = 'E' THEN 'Elemental'
            WHEN p.nivel_educativo = 'M' THEN 'Medio'
            WHEN p.nivel_educativo = 'S' THEN 'Superior'
            ELSE 'No Especificado'
        END as nivel_nombre,
        'R' as aptitud_codigo,
        'Razonamiento' as aptitud_nombre,
        CASE
            WHEN r.percentil_r < 30 THEN 'Bajo'
            WHEN r.percentil_r >= 30 AND r.percentil_r <= 70 THEN 'Promedio'
            WHEN r.percentil_r > 70 THEN 'Alto'
            ELSE 'Sin Datos'
        END as categoria_rendimiento,
        COUNT(*) as cantidad
    FROM pacientes p
    JOIN resultados r ON p.id = r.paciente_id
    WHERE p.nivel_educativo IS NOT NULL AND r.percentil_r IS NOT NULL
    GROUP BY p.nivel_educativo,
             CASE
                WHEN r.percentil_r < 30 THEN 'Bajo'
                WHEN r.percentil_r >= 30 AND r.percentil_r <= 70 THEN 'Promedio'
                WHEN r.percentil_r > 70 THEN 'Alto'
                ELSE 'Sin Datos'
             END
)
SELECT
    nivel_educativo,
    nivel_nombre,
    aptitud_codigo,
    aptitud_nombre,
    categoria_rendimiento,
    cantidad,
    ROUND(
        (cantidad * 100.0) / SUM(cantidad) OVER (PARTITION BY nivel_educativo, aptitud_codigo), 2
    ) as porcentaje
FROM rendimiento_categorizado
ORDER BY nivel_educativo, aptitud_codigo,
         CASE categoria_rendimiento
             WHEN 'Alto' THEN 1
             WHEN 'Promedio' THEN 2
             WHEN 'Bajo' THEN 3
             ELSE 4
         END;

-- 6. Vista: Comparativa por Género
CREATE OR REPLACE VIEW dashboard_comparativa_genero AS
SELECT
    p.genero,
    CASE
        WHEN p.genero = 'M' THEN 'Masculino'
        WHEN p.genero = 'F' THEN 'Femenino'
        ELSE 'No Especificado'
    END as genero_nombre,
    p.nivel_educativo,
    CASE
        WHEN p.nivel_educativo = 'E' THEN 'Elemental'
        WHEN p.nivel_educativo = 'M' THEN 'Medio'
        WHEN p.nivel_educativo = 'S' THEN 'Superior'
        ELSE 'No Especificado'
    END as nivel_nombre,
    'V' as aptitud_codigo,
    'Verbal' as aptitud_nombre,
    ROUND(AVG(r.percentil_v), 2) as percentil_promedio,
    COUNT(r.id) as total_evaluaciones
FROM pacientes p
JOIN resultados r ON p.id = r.paciente_id
WHERE p.genero IS NOT NULL AND p.nivel_educativo IS NOT NULL AND r.percentil_v IS NOT NULL
GROUP BY p.genero, p.nivel_educativo

UNION ALL

SELECT
    p.genero,
    CASE
        WHEN p.genero = 'M' THEN 'Masculino'
        WHEN p.genero = 'F' THEN 'Femenino'
        ELSE 'No Especificado'
    END as genero_nombre,
    p.nivel_educativo,
    CASE
        WHEN p.nivel_educativo = 'E' THEN 'Elemental'
        WHEN p.nivel_educativo = 'M' THEN 'Medio'
        WHEN p.nivel_educativo = 'S' THEN 'Superior'
        ELSE 'No Especificado'
    END as nivel_nombre,
    'E' as aptitud_codigo,
    'Espacial' as aptitud_nombre,
    ROUND(AVG(r.percentil_e), 2) as percentil_promedio,
    COUNT(r.id) as total_evaluaciones
FROM pacientes p
JOIN resultados r ON p.id = r.paciente_id
WHERE p.genero IS NOT NULL AND p.nivel_educativo IS NOT NULL AND r.percentil_e IS NOT NULL
GROUP BY p.genero, p.nivel_educativo

UNION ALL

SELECT
    p.genero,
    CASE
        WHEN p.genero = 'M' THEN 'Masculino'
        WHEN p.genero = 'F' THEN 'Femenino'
        ELSE 'No Especificado'
    END as genero_nombre,
    p.nivel_educativo,
    CASE
        WHEN p.nivel_educativo = 'E' THEN 'Elemental'
        WHEN p.nivel_educativo = 'M' THEN 'Medio'
        WHEN p.nivel_educativo = 'S' THEN 'Superior'
        ELSE 'No Especificado'
    END as nivel_nombre,
    'N' as aptitud_codigo,
    'Numérica' as aptitud_nombre,
    ROUND(AVG(r.percentil_n), 2) as percentil_promedio,
    COUNT(r.id) as total_evaluaciones
FROM pacientes p
JOIN resultados r ON p.id = r.paciente_id
WHERE p.genero IS NOT NULL AND p.nivel_educativo IS NOT NULL AND r.percentil_n IS NOT NULL
GROUP BY p.genero, p.nivel_educativo

ORDER BY genero, nivel_educativo, aptitud_codigo;

-- 7. Vista: Matriz de Correlación de Aptitudes
CREATE OR REPLACE VIEW dashboard_correlacion_aptitudes AS
WITH correlaciones AS (
    SELECT
        'V-E' as par_aptitudes,
        'Verbal vs Espacial' as descripcion,
        CORR(r.percentil_v, r.percentil_e) as coeficiente_correlacion,
        COUNT(*) as n_observaciones
    FROM resultados r
    WHERE r.percentil_v IS NOT NULL AND r.percentil_e IS NOT NULL

    UNION ALL

    SELECT
        'V-R' as par_aptitudes,
        'Verbal vs Razonamiento' as descripcion,
        CORR(r.percentil_v, r.percentil_r) as coeficiente_correlacion,
        COUNT(*) as n_observaciones
    FROM resultados r
    WHERE r.percentil_v IS NOT NULL AND r.percentil_r IS NOT NULL

    UNION ALL

    SELECT
        'V-N' as par_aptitudes,
        'Verbal vs Numérica' as descripcion,
        CORR(r.percentil_v, r.percentil_n) as coeficiente_correlacion,
        COUNT(*) as n_observaciones
    FROM resultados r
    WHERE r.percentil_v IS NOT NULL AND r.percentil_n IS NOT NULL

    UNION ALL

    SELECT
        'R-N' as par_aptitudes,
        'Razonamiento vs Numérica' as descripcion,
        CORR(r.percentil_r, r.percentil_n) as coeficiente_correlacion,
        COUNT(*) as n_observaciones
    FROM resultados r
    WHERE r.percentil_r IS NOT NULL AND r.percentil_n IS NOT NULL

    UNION ALL

    SELECT
        'E-M' as par_aptitudes,
        'Espacial vs Mecánica' as descripcion,
        CORR(r.percentil_e, r.percentil_m) as coeficiente_correlacion,
        COUNT(*) as n_observaciones
    FROM resultados r
    WHERE r.percentil_e IS NOT NULL AND r.percentil_m IS NOT NULL
)
SELECT
    par_aptitudes,
    descripcion,
    ROUND(coeficiente_correlacion, 3) as correlacion,
    n_observaciones,
    CASE
        WHEN ABS(coeficiente_correlacion) >= 0.7 THEN 'Fuerte'
        WHEN ABS(coeficiente_correlacion) >= 0.4 THEN 'Moderada'
        WHEN ABS(coeficiente_correlacion) >= 0.2 THEN 'Débil'
        ELSE 'Muy Débil'
    END as intensidad_correlacion,
    CASE
        WHEN coeficiente_correlacion > 0 THEN 'Positiva'
        WHEN coeficiente_correlacion < 0 THEN 'Negativa'
        ELSE 'Nula'
    END as direccion_correlacion
FROM correlaciones
ORDER BY ABS(coeficiente_correlacion) DESC;
