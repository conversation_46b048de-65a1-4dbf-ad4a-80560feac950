/**
 * @file chartAnimations.css
 * @description CSS animations for chart components
 */

@keyframes fadeInScale {
  0% {
    opacity: 0;
    transform: scale(0.8);
  }
  100% {
    opacity: 1;
    transform: scale(1);
  }
}

@keyframes slideInFromLeft {
  0% {
    opacity: 0;
    transform: translateX(-20px);
  }
  100% {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes pulseGlow {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.7;
  }
}

/* Chart-specific animations */
.chart-segment {
  animation: fadeInScale 0.6s ease-out both;
}

.chart-legend-item {
  animation: slideInFromLeft 0.4s ease-out both;
}

.chart-center-total {
  animation: pulseGlow 2s ease-in-out infinite;
}