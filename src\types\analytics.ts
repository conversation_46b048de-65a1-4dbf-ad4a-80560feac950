/**
 * Tipos TypeScript para el sistema de analytics mejorado
 * Define interfaces para datos, configuraciones y componentes
 */

// Tipos base para datos de analytics
export interface TimeSeriesData {
  timestamp: Date;
  value: number;
  category?: string;
  metadata?: Record<string, any>;
}

export interface AggregatedMetrics {
  daily: TimeSeriesData[];
  weekly: TimeSeriesData[];
  monthly: TimeSeriesData[];
  yearly: TimeSeriesData[];
}

// Interfaces para métricas institucionales
export interface InstitutionalMetrics {
  totalAssessments: number;
  completionRate: number;
  averageScores: AptitudeScores;
  trendsOverTime: TimeSeries[];
  psychologistPerformance: PsychologistMetrics[];
}

export interface AptitudeScores {
  V: number; // Verbal
  E: number; // Espacial
  A: number; // Atención
  R: number; // Razonamiento
  N: number; // Numérico
  M: number; // Mecánico
  O: number; // Ortografía
}

export interface TimeSeries {
  date: string;
  value: number;
  label?: string;
}

export interface PsychologistMetrics {
  psychologistId: string;
  psychologistName: string;
  totalAssessments: number;
  averageCompletionTime: number;
  averageScores: AptitudeScores;
  patientSatisfaction: number;
}

// Interfaces para progreso de pacientes
export interface PatientProgressData {
  patientId: string;
  patientName: string;
  assessmentHistory: AssessmentResult[];
  scoreProgression: AptitudeProgression[];
  percentileComparisons: PercentileData;
  significantChanges: ChangeIndicator[];
}

export interface AssessmentResult {
  evaluationId: string;
  date: Date;
  aptitudeResults: Record<string, AptitudeResult>;
  overallPercentile: number;
  completionTime: number;
  psychologistId: string;
}

export interface AptitudeResult {
  aptitudeCode: string;
  aptitudeName: string;
  directScore: number;
  percentile: number;
  previousPercentile?: number;
  changeFromPrevious?: number;
}

export interface AptitudeProgression {
  aptitudeCode: string;
  aptitudeName: string;
  progressionData: TimeSeries[];
  trend: 'improving' | 'declining' | 'stable';
  trendStrength: number; // -1 a 1
}

export interface PercentileData {
  institutional: Record<string, number>;
  national: Record<string, number>;
  patientRanking: {
    institutional: number;
    national: number;
  };
}

export interface ChangeIndicator {
  aptitudeCode: string;
  changeType: 'significant_improvement' | 'significant_decline' | 'notable_change';
  magnitude: number;
  fromDate: Date;
  toDate: Date;
  description: string;
}

// Interfaces para comparación de grupos
export interface ComparisonGroup {
  groupId: string;
  groupName: string;
  sampleSize: number;
  statistics: GroupStatistics;
  aptitudeBreakdown: Record<string, number>;
}

export interface GroupStatistics {
  mean: number;
  median: number;
  standardDeviation: number;
  percentiles: Record<number, number>; // P25, P50, P75, etc.
  confidenceInterval: {
    lower: number;
    upper: number;
    level: number; // 95, 99, etc.
  };
}

export interface ComparisonResult {
  groups: ComparisonGroup[];
  statisticalSignificance: {
    pValue: number;
    isSignificant: boolean;
    effectSize: number;
    effectSizeInterpretation: 'small' | 'medium' | 'large';
  };
  recommendations: string[];
}

// Interfaces para configuración de gráficos
export interface ChartConfiguration {
  title: string;
  subtitle?: string;
  xAxis: AxisConfig;
  yAxis: AxisConfig;
  colors: ColorScheme;
  interactivity: InteractivityOptions;
  exportOptions: ExportConfig;
  responsive: boolean;
}

export interface AxisConfig {
  label: string;
  dataKey: string;
  domain?: [number, number];
  tickFormatter?: (value: any) => string;
  scale?: 'linear' | 'log' | 'time';
}

export interface ColorScheme {
  primary: string;
  secondary: string;
  accent: string;
  background: string;
  text: string;
  grid: string;
  palette: string[];
}

export interface InteractivityOptions {
  tooltip: boolean;
  zoom: boolean;
  brush: boolean;
  legend: boolean;
  clickable: boolean;
  hoverable: boolean;
}

export interface ExportConfig {
  formats: ('png' | 'jpg' | 'svg' | 'pdf')[];
  quality: number;
  dimensions: {
    width: number;
    height: number;
  };
}

// Interfaces para dashboard personalizable
export interface DashboardLayout {
  layoutId: string;
  layoutName: string;
  userId: string;
  widgets: WidgetInstance[];
  gridConfig: GridConfiguration;
  isDefault: boolean;
  createdAt: Date;
  updatedAt: Date;
}

export interface WidgetInstance {
  widgetId: string;
  widgetType: string;
  position: WidgetPosition;
  size: WidgetSize;
  configuration: Record<string, any>;
  dataFilters: Record<string, any>;
}

export interface WidgetPosition {
  x: number;
  y: number;
}

export interface WidgetSize {
  width: number;
  height: number;
}

export interface GridConfiguration {
  columns: number;
  rowHeight: number;
  margin: [number, number];
  containerPadding: [number, number];
}

export interface WidgetDefinition {
  widgetType: string;
  displayName: string;
  description: string;
  category: 'kpi' | 'chart' | 'table' | 'metric';
  defaultSize: WidgetSize;
  configurationSchema: Record<string, any>;
  previewImage?: string;
}

// Interfaces para reportes programados
export interface ScheduledReport {
  reportId: string;
  reportName: string;
  description: string;
  schedule: ReportSchedule;
  recipients: ReportRecipient[];
  template: ReportTemplate;
  filters: Record<string, any>;
  isActive: boolean;
  createdBy: string;
  createdAt: Date;
  lastRun?: Date;
  nextRun: Date;
}

export interface ReportSchedule {
  frequency: 'daily' | 'weekly' | 'monthly' | 'quarterly' | 'custom';
  interval: number;
  dayOfWeek?: number; // 0-6, domingo = 0
  dayOfMonth?: number; // 1-31
  time: string; // HH:MM format
  timezone: string;
}

export interface ReportRecipient {
  email: string;
  name: string;
  role: string;
  deliveryMethod: 'email' | 'system_notification';
}

export interface ReportTemplate {
  templateId: string;
  templateName: string;
  format: 'pdf' | 'excel' | 'html';
  sections: ReportSection[];
  styling: ReportStyling;
}

export interface ReportSection {
  sectionId: string;
  sectionType: 'header' | 'chart' | 'table' | 'text' | 'kpi';
  title: string;
  configuration: Record<string, any>;
  order: number;
}

export interface ReportStyling {
  theme: 'light' | 'dark' | 'corporate';
  primaryColor: string;
  secondaryColor: string;
  fontFamily: string;
  fontSize: number;
  logo?: string;
}

// Interfaces para métricas del sistema
export interface SystemMetrics {
  usageStatistics: UsageStatistics;
  performanceMetrics: PerformanceMetrics;
  errorMetrics: ErrorMetrics;
  userActivity: UserActivity[];
}

export interface UsageStatistics {
  totalUsers: number;
  activeUsers: {
    daily: number;
    weekly: number;
    monthly: number;
  };
  assessmentVolume: {
    total: number;
    thisMonth: number;
    thisWeek: number;
    today: number;
  };
  peakUsageTimes: TimeUsageData[];
}

export interface PerformanceMetrics {
  averageResponseTime: number;
  databaseQueryTime: number;
  pageLoadTimes: Record<string, number>;
  errorRate: number;
  uptime: number;
}

export interface ErrorMetrics {
  totalErrors: number;
  errorsByType: Record<string, number>;
  criticalErrors: number;
  recentErrors: ErrorLog[];
}

export interface ErrorLog {
  timestamp: Date;
  errorType: string;
  message: string;
  userId?: string;
  stackTrace?: string;
  resolved: boolean;
}

export interface UserActivity {
  userId: string;
  userName: string;
  lastLogin: Date;
  sessionsThisMonth: number;
  averageSessionDuration: number;
  featuresUsed: string[];
}

export interface TimeUsageData {
  hour: number;
  dayOfWeek: number;
  usage: number;
}

// Tipos para manejo de errores
export interface AnalyticsError {
  type: 'NETWORK_ERROR' | 'DATA_PARSING_ERROR' | 'PERMISSION_ERROR' | 'CALCULATION_ERROR';
  message: string;
  retryable: boolean;
  fallbackData?: any;
  timestamp: Date;
  context?: Record<string, any>;
}

// Tipos para filtros avanzados
export interface AdvancedFilters {
  dateRange: {
    start: Date;
    end: Date;
    preset?: 'last_7_days' | 'last_30_days' | 'last_3_months' | 'last_6_months' | 'last_year' | 'custom';
  };
  institutions?: string[];
  psychologists?: string[];
  educationLevels?: string[];
  genders?: string[];
  ageRanges?: AgeRange[];
  aptitudes?: string[];
  scoreRanges?: ScoreRange[];
  customCriteria?: CustomFilterCriteria[];
}

export interface AgeRange {
  min: number;
  max: number;
  label: string;
}

export interface ScoreRange {
  aptitude: string;
  min: number;
  max: number;
  type: 'percentile' | 'direct_score';
}

export interface CustomFilterCriteria {
  field: string;
  operator: 'equals' | 'not_equals' | 'greater_than' | 'less_than' | 'contains' | 'in' | 'not_in';
  value: any;
  label: string;
}