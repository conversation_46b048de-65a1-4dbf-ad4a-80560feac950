/**
 * Configuración de temas y colores para gráficos analytics
 * Mantiene consistencia visual en todo el dashboard
 */

// Paleta de colores principal para aptitudes BAT-7
export const APTITUDE_COLORS = {
  V: '#3B82F6', // Azul - Verbal
  E: '#10B981', // Verde - Espacial  
  A: '#F59E0B', // Naranja - Atención
  R: '#8B5CF6', // Púrpura - Razonamiento
  N: '#EC4899', // Rosa - Numérico
  M: '#EF4444', // Rojo - Mecánico
  O: '#0EA5E9'  // Azul claro - Ortografía
};

// Paleta de colores para niveles educativos
export const EDUCATION_LEVEL_COLORS = {
  E: '#3B82F6', // Azul - Elemental
  M: '#10B981', // Verde - Medio
  S: '#F59E0B', // Naranja - Superior
  'Sin definir': '#9CA3AF' // Gris
};

// Paleta de colores para géneros
export const GENDER_COLORS = {
  masculino: '#3B82F6',    // Azul
  femenino: '#EC4899',     // Rosa
  otro: '#8B5CF6',         // Púrpura
  'No especificado': '#9CA3AF' // Gris
};

// Paleta de colores general para gráficos
export const CHART_COLORS = [
  '#3B82F6', // Azul
  '#10B981', // Verde
  '#F59E0B', // Naranja
  '#EF4444', // Rojo
  '#8B5CF6', // Púrpura
  '#EC4899', // Rosa
  '#0EA5E9', // Azul claro
  '#84CC16', // Verde lima
  '#F97316', // Naranja oscuro
  '#6366F1'  // Índigo
];

// Configuración de temas
export const CHART_THEMES = {
  light: {
    background: '#FFFFFF',
    surface: '#F8FAFC',
    text: {
      primary: '#1F2937',
      secondary: '#6B7280',
      muted: '#9CA3AF'
    },
    grid: '#E5E7EB',
    border: '#D1D5DB',
    colors: CHART_COLORS
  },
  dark: {
    background: '#1F2937',
    surface: '#374151',
    text: {
      primary: '#F9FAFB',
      secondary: '#D1D5DB',
      muted: '#9CA3AF'
    },
    grid: '#4B5563',
    border: '#6B7280',
    colors: CHART_COLORS
  }
};

// Configuraciones predefinidas para diferentes tipos de gráficos
export const CHART_CONFIGS = {
  lineChart: {
    strokeWidth: 2,
    dot: {
      r: 4,
      strokeWidth: 2
    },
    activeDot: {
      r: 6,
      strokeWidth: 0
    }
  },
  barChart: {
    barSize: 40,
    radius: [4, 4, 0, 0]
  },
  areaChart: {
    strokeWidth: 2,
    fillOpacity: 0.6
  },
  pieChart: {
    innerRadius: 60,
    outerRadius: 120,
    paddingAngle: 2
  },
  radarChart: {
    outerRadius: 120,
    strokeWidth: 2,
    fillOpacity: 0.3
  }
};

// Configuración de tooltips
export const TOOLTIP_CONFIG = {
  contentStyle: {
    backgroundColor: 'rgba(255, 255, 255, 0.95)',
    border: '1px solid #E5E7EB',
    borderRadius: '8px',
    boxShadow: '0 10px 15px -3px rgba(0, 0, 0, 0.1)',
    fontSize: '14px',
    padding: '12px'
  },
  labelStyle: {
    color: '#374151',
    fontWeight: '600',
    marginBottom: '4px'
  },
  itemStyle: {
    color: '#6B7280',
    padding: '2px 0'
  }
};

// Configuración de leyendas
export const LEGEND_CONFIG = {
  wrapperStyle: {
    paddingTop: '20px',
    fontSize: '14px'
  },
  iconType: 'circle'
};

// Configuración de ejes
export const AXIS_CONFIG = {
  xAxis: {
    axisLine: true,
    tickLine: true,
    tick: {
      fontSize: 12,
      fill: '#6B7280'
    }
  },
  yAxis: {
    axisLine: false,
    tickLine: false,
    tick: {
      fontSize: 12,
      fill: '#6B7280'
    },
    width: 60
  }
};

// Configuración de grillas
export const GRID_CONFIG = {
  strokeDasharray: '3 3',
  stroke: '#E5E7EB',
  strokeOpacity: 0.5
};

// Configuración responsiva
export const RESPONSIVE_CONFIG = {
  mobile: {
    width: '100%',
    height: 250,
    margin: { top: 20, right: 20, bottom: 20, left: 20 }
  },
  tablet: {
    width: '100%',
    height: 300,
    margin: { top: 20, right: 30, bottom: 20, left: 30 }
  },
  desktop: {
    width: '100%',
    height: 400,
    margin: { top: 20, right: 40, bottom: 20, left: 40 }
  }
};

// Utilidades para obtener colores
export const getAptitudeColor = (aptitudeCode) => {
  return APTITUDE_COLORS[aptitudeCode] || CHART_COLORS[0];
};

export const getEducationLevelColor = (level) => {
  return EDUCATION_LEVEL_COLORS[level] || CHART_COLORS[0];
};

export const getGenderColor = (gender) => {
  return GENDER_COLORS[gender] || CHART_COLORS[0];
};

export const getChartColor = (index) => {
  return CHART_COLORS[index % CHART_COLORS.length];
};

// Función para generar paleta de colores personalizada
export const generateColorPalette = (count, baseColor = '#3B82F6') => {
  const colors = [];
  const hue = parseInt(baseColor.slice(1), 16);
  
  for (let i = 0; i < count; i++) {
    const variation = (i * 360) / count;
    const newHue = (hue + variation) % 360;
    colors.push(`hsl(${newHue}, 70%, 50%)`);
  }
  
  return colors;
};

// Configuración de animaciones
export const ANIMATION_CONFIG = {
  duration: 750,
  easing: 'ease-out',
  delay: 0
};

export default {
  APTITUDE_COLORS,
  EDUCATION_LEVEL_COLORS,
  GENDER_COLORS,
  CHART_COLORS,
  CHART_THEMES,
  CHART_CONFIGS,
  TOOLTIP_CONFIG,
  LEGEND_CONFIG,
  AXIS_CONFIG,
  GRID_CONFIG,
  RESPONSIVE_CONFIG,
  ANIMATION_CONFIG,
  getAptitudeColor,
  getEducationLevelColor,
  getGenderColor,
  getChartColor,
  generateColorPalette
};