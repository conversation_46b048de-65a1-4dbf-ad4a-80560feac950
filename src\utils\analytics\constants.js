/**
 * Constantes para el sistema de analytics mejorado
 * Define valores constantes utilizados en todo el sistema
 */

// Códigos de aptitudes BAT-7
export const APTITUDE_CODES = {
  V: 'Verbal',
  E: 'Espacial', 
  A: 'Atención',
  R: 'Razonamiento',
  N: 'Numérico',
  M: 'Mecánico',
  O: 'Ortografía'
};

// Niveles educativos
export const EDUCATION_LEVELS = {
  E: 'Elemental',
  M: 'Medio',
  S: 'Superior'
};

// Rangos de tiempo predefinidos
export const TIME_RANGES = {
  LAST_7_DAYS: {
    key: 'last_7_days',
    label: 'Últimos 7 días',
    days: 7
  },
  LAST_30_DAYS: {
    key: 'last_30_days', 
    label: 'Últimos 30 días',
    days: 30
  },
  LAST_3_MONTHS: {
    key: 'last_3_months',
    label: 'Últimos 3 meses',
    days: 90
  },
  LAST_6_MONTHS: {
    key: 'last_6_months',
    label: 'Últimos 6 meses', 
    days: 180
  },
  LAST_YEAR: {
    key: 'last_year',
    label: 'Último año',
    days: 365
  },
  CUSTOM: {
    key: 'custom',
    label: 'Personalizado',
    days: null
  }
};

// Tipos de gráficos disponibles
export const CHART_TYPES = {
  LINE: 'line',
  BAR: 'bar',
  AREA: 'area',
  PIE: 'pie',
  RADAR: 'radar',
  SCATTER: 'scatter',
  COMPOSED: 'composed'
};

// Tipos de análisis estadístico
export const STATISTICAL_ANALYSIS_TYPES = {
  DESCRIPTIVE: 'descriptive',
  COMPARATIVE: 'comparative',
  CORRELATION: 'correlation',
  TREND: 'trend',
  DISTRIBUTION: 'distribution'
};

// Niveles de significancia estadística
export const SIGNIFICANCE_LEVELS = {
  VERY_HIGH: { threshold: 0.001, label: 'Muy alta (p < 0.001)' },
  HIGH: { threshold: 0.01, label: 'Alta (p < 0.01)' },
  MODERATE: { threshold: 0.05, label: 'Moderada (p < 0.05)' },
  LOW: { threshold: 0.1, label: 'Baja (p < 0.1)' },
  NOT_SIGNIFICANT: { threshold: 1, label: 'No significativa (p ≥ 0.1)' }
};

// Interpretación del tamaño del efecto (Cohen's d)
export const EFFECT_SIZE_INTERPRETATION = {
  SMALL: { min: 0.2, max: 0.5, label: 'Pequeño' },
  MEDIUM: { min: 0.5, max: 0.8, label: 'Mediano' },
  LARGE: { min: 0.8, max: Infinity, label: 'Grande' }
};

// Percentiles de referencia
export const PERCENTILE_RANGES = {
  VERY_LOW: { min: 0, max: 10, label: 'Muy bajo', color: '#EF4444' },
  LOW: { min: 10, max: 25, label: 'Bajo', color: '#F97316' },
  BELOW_AVERAGE: { min: 25, max: 40, label: 'Bajo promedio', color: '#F59E0B' },
  AVERAGE: { min: 40, max: 60, label: 'Promedio', color: '#10B981' },
  ABOVE_AVERAGE: { min: 60, max: 75, label: 'Sobre promedio', color: '#0EA5E9' },
  HIGH: { min: 75, max: 90, label: 'Alto', color: '#3B82F6' },
  VERY_HIGH: { min: 90, max: 100, label: 'Muy alto', color: '#8B5CF6' }
};

// Configuración de exportación
export const EXPORT_FORMATS = {
  PDF: {
    key: 'pdf',
    label: 'PDF',
    mimeType: 'application/pdf',
    extension: '.pdf'
  },
  EXCEL: {
    key: 'excel',
    label: 'Excel',
    mimeType: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
    extension: '.xlsx'
  },
  CSV: {
    key: 'csv',
    label: 'CSV',
    mimeType: 'text/csv',
    extension: '.csv'
  },
  PNG: {
    key: 'png',
    label: 'PNG',
    mimeType: 'image/png',
    extension: '.png'
  },
  SVG: {
    key: 'svg',
    label: 'SVG',
    mimeType: 'image/svg+xml',
    extension: '.svg'
  }
};

// Frecuencias de reportes programados
export const REPORT_FREQUENCIES = {
  DAILY: {
    key: 'daily',
    label: 'Diario',
    cronPattern: '0 0 * * *'
  },
  WEEKLY: {
    key: 'weekly',
    label: 'Semanal',
    cronPattern: '0 0 * * 1'
  },
  MONTHLY: {
    key: 'monthly',
    label: 'Mensual',
    cronPattern: '0 0 1 * *'
  },
  QUARTERLY: {
    key: 'quarterly',
    label: 'Trimestral',
    cronPattern: '0 0 1 */3 *'
  }
};

// Tipos de widgets para dashboard personalizable
export const WIDGET_TYPES = {
  KPI_CARD: {
    key: 'kpi_card',
    label: 'Tarjeta KPI',
    category: 'kpi',
    defaultSize: { width: 2, height: 1 }
  },
  LINE_CHART: {
    key: 'line_chart',
    label: 'Gráfico de Líneas',
    category: 'chart',
    defaultSize: { width: 4, height: 2 }
  },
  BAR_CHART: {
    key: 'bar_chart',
    label: 'Gráfico de Barras',
    category: 'chart',
    defaultSize: { width: 4, height: 2 }
  },
  PIE_CHART: {
    key: 'pie_chart',
    label: 'Gráfico Circular',
    category: 'chart',
    defaultSize: { width: 3, height: 2 }
  },
  DATA_TABLE: {
    key: 'data_table',
    label: 'Tabla de Datos',
    category: 'table',
    defaultSize: { width: 6, height: 3 }
  },
  METRIC_SUMMARY: {
    key: 'metric_summary',
    label: 'Resumen de Métricas',
    category: 'metric',
    defaultSize: { width: 3, height: 2 }
  }
};

// Configuración de alertas del sistema
export const ALERT_TYPES = {
  CRITICAL: {
    key: 'critical',
    label: 'Crítica',
    color: '#EF4444',
    icon: 'exclamation-triangle'
  },
  WARNING: {
    key: 'warning',
    label: 'Advertencia',
    color: '#F59E0B',
    icon: 'exclamation-circle'
  },
  INFO: {
    key: 'info',
    label: 'Información',
    color: '#3B82F6',
    icon: 'info-circle'
  },
  SUCCESS: {
    key: 'success',
    label: 'Éxito',
    color: '#10B981',
    icon: 'check-circle'
  }
};

// Umbrales para alertas automáticas
export const ALERT_THRESHOLDS = {
  LOW_COMPLETION_RATE: 0.7, // 70%
  HIGH_ERROR_RATE: 0.05, // 5%
  SLOW_RESPONSE_TIME: 3000, // 3 segundos
  LOW_PERCENTILE_THRESHOLD: 25,
  HIGH_PERCENTILE_THRESHOLD: 75,
  SIGNIFICANT_CHANGE_THRESHOLD: 10 // 10 puntos percentiles
};

// Configuración de paginación
export const PAGINATION_CONFIG = {
  DEFAULT_PAGE_SIZE: 20,
  PAGE_SIZE_OPTIONS: [10, 20, 50, 100],
  MAX_PAGE_SIZE: 1000
};

// Configuración de cache
export const CACHE_CONFIG = {
  DEFAULT_TTL: 300000, // 5 minutos
  LONG_TTL: 3600000, // 1 hora
  SHORT_TTL: 60000, // 1 minuto
  KEYS: {
    INSTITUTIONAL_METRICS: 'institutional_metrics',
    PATIENT_PROGRESS: 'patient_progress',
    SYSTEM_METRICS: 'system_metrics',
    COMPARISON_DATA: 'comparison_data'
  }
};

// Mensajes de error comunes
export const ERROR_MESSAGES = {
  NETWORK_ERROR: 'Error de conexión. Por favor, verifica tu conexión a internet.',
  DATA_NOT_FOUND: 'No se encontraron datos para los criterios especificados.',
  PERMISSION_DENIED: 'No tienes permisos para acceder a esta información.',
  INVALID_DATE_RANGE: 'El rango de fechas seleccionado no es válido.',
  EXPORT_FAILED: 'Error al exportar los datos. Inténtalo nuevamente.',
  CALCULATION_ERROR: 'Error en el cálculo estadístico. Verifica los datos.',
  INSUFFICIENT_DATA: 'Datos insuficientes para realizar el análisis solicitado.'
};

// Configuración de validación
export const VALIDATION_RULES = {
  MIN_SAMPLE_SIZE: 5,
  MAX_DATE_RANGE_DAYS: 1095, // 3 años
  MIN_DATE_RANGE_DAYS: 1,
  MAX_EXPORT_RECORDS: 10000,
  MIN_PERCENTILE: 0,
  MAX_PERCENTILE: 100
};

// Configuración de formato de números
export const NUMBER_FORMATS = {
  DECIMAL_PLACES: 2,
  PERCENTAGE_DECIMAL_PLACES: 1,
  LARGE_NUMBER_THRESHOLD: 1000,
  CURRENCY_SYMBOL: '$',
  THOUSAND_SEPARATOR: ',',
  DECIMAL_SEPARATOR: '.'
};

export default {
  APTITUDE_CODES,
  EDUCATION_LEVELS,
  TIME_RANGES,
  CHART_TYPES,
  STATISTICAL_ANALYSIS_TYPES,
  SIGNIFICANCE_LEVELS,
  EFFECT_SIZE_INTERPRETATION,
  PERCENTILE_RANGES,
  EXPORT_FORMATS,
  REPORT_FREQUENCIES,
  WIDGET_TYPES,
  ALERT_TYPES,
  ALERT_THRESHOLDS,
  PAGINATION_CONFIG,
  CACHE_CONFIG,
  ERROR_MESSAGES,
  VALIDATION_RULES,
  NUMBER_FORMATS
};