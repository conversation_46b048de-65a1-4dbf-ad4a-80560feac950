/**
 * Utilidades para agregación y procesamiento de datos analytics
 * Funciones auxiliares para manipular y transformar datos
 */

import { format, startOfWeek, startOfMonth, startOfYear, eachDayOfInterval, eachWeekOfInterval, eachMonthOfInterval } from 'date-fns';
import { es } from 'date-fns/locale';
import { APTITUDE_CODES, TIME_RANGES } from './constants.js';

/**
 * Agregar datos por período temporal
 */
export const aggregateByTimePeriod = (data, period = 'day', dateField = 'created_at') => {
  if (!data || data.length === 0) return [];

  const aggregated = new Map();

  data.forEach(item => {
    const date = new Date(item[dateField]);
    let key;

    switch (period) {
      case 'day':
        key = format(date, 'yyyy-MM-dd');
        break;
      case 'week':
        key = format(startOfWeek(date, { locale: es }), 'yyyy-MM-dd');
        break;
      case 'month':
        key = format(startOfMonth(date), 'yyyy-MM');
        break;
      case 'year':
        key = format(startOfYear(date), 'yyyy');
        break;
      default:
        key = format(date, 'yyyy-MM-dd');
    }

    if (!aggregated.has(key)) {
      aggregated.set(key, {
        period: key,
        date: key,
        count: 0,
        items: []
      });
    }

    const periodData = aggregated.get(key);
    periodData.count++;
    periodData.items.push(item);
  });

  return Array.from(aggregated.values()).sort((a, b) => a.period.localeCompare(b.period));
};

/**
 * Agregar datos por aptitud
 */
export const aggregateByAptitude = (data, valueField = 'percentil') => {
  const aggregated = {};

  // Inicializar con todas las aptitudes
  Object.keys(APTITUDE_CODES).forEach(code => {
    aggregated[code] = {
      aptitudeCode: code,
      aptitudeName: APTITUDE_CODES[code],
      count: 0,
      values: [],
      sum: 0,
      average: 0,
      min: null,
      max: null
    };
  });

  data.forEach(item => {
    const code = item.aptitudes?.codigo || item.aptitude_code;
    const value = item[valueField];

    if (code && aggregated[code] && value !== null && value !== undefined) {
      const aptData = aggregated[code];
      aptData.count++;
      aptData.values.push(value);
      aptData.sum += value;
      
      if (aptData.min === null || value < aptData.min) aptData.min = value;
      if (aptData.max === null || value > aptData.max) aptData.max = value;
    }
  });

  // Calcular promedios
  Object.keys(aggregated).forEach(code => {
    const aptData = aggregated[code];
    if (aptData.count > 0) {
      aptData.average = Math.round((aptData.sum / aptData.count) * 10) / 10;
    }
  });

  return aggregated;
};

/**
 * Agregar datos por nivel educativo
 */
export const aggregateByEducationLevel = (data, valueField = 'percentil') => {
  const aggregated = {
    E: { level: 'E', name: 'Elemental', count: 0, values: [], sum: 0, average: 0 },
    M: { level: 'M', name: 'Medio', count: 0, values: [], sum: 0, average: 0 },
    S: { level: 'S', name: 'Superior', count: 0, values: [], sum: 0, average: 0 },
    'Sin definir': { level: 'Sin definir', name: 'Sin definir', count: 0, values: [], sum: 0, average: 0 }
  };

  data.forEach(item => {
    const level = normalizeEducationLevel(item.nivel_educativo || item.education_level);
    const value = item[valueField];

    if (aggregated[level] && value !== null && value !== undefined) {
      const levelData = aggregated[level];
      levelData.count++;
      levelData.values.push(value);
      levelData.sum += value;
    }
  });

  // Calcular promedios
  Object.keys(aggregated).forEach(level => {
    const levelData = aggregated[level];
    if (levelData.count > 0) {
      levelData.average = Math.round((levelData.sum / levelData.count) * 10) / 10;
    }
  });

  return aggregated;
};

/**
 * Agregar datos por género
 */
export const aggregateByGender = (data, valueField = 'percentil') => {
  const aggregated = {
    masculino: { gender: 'masculino', name: 'Masculino', count: 0, values: [], sum: 0, average: 0 },
    femenino: { gender: 'femenino', name: 'Femenino', count: 0, values: [], sum: 0, average: 0 },
    otro: { gender: 'otro', name: 'Otro', count: 0, values: [], sum: 0, average: 0 },
    'No especificado': { gender: 'No especificado', name: 'No especificado', count: 0, values: [], sum: 0, average: 0 }
  };

  data.forEach(item => {
    const gender = normalizeGender(item.genero || item.gender);
    const value = item[valueField];

    if (aggregated[gender] && value !== null && value !== undefined) {
      const genderData = aggregated[gender];
      genderData.count++;
      genderData.values.push(value);
      genderData.sum += value;
    }
  });

  // Calcular promedios
  Object.keys(aggregated).forEach(gender => {
    const genderData = aggregated[gender];
    if (genderData.count > 0) {
      genderData.average = Math.round((genderData.sum / genderData.count) * 10) / 10;
    }
  });

  return aggregated;
};

/**
 * Crear series temporales completas (rellenar gaps)
 */
export const createCompleteTimeSeries = (data, startDate, endDate, period = 'day') => {
  const intervals = {
    day: eachDayOfInterval,
    week: eachWeekOfInterval,
    month: eachMonthOfInterval
  };

  const intervalFn = intervals[period] || intervals.day;
  const dates = intervalFn({ start: startDate, end: endDate });

  const dataMap = new Map();
  data.forEach(item => {
    const key = item.period || item.date;
    dataMap.set(key, item);
  });

  return dates.map(date => {
    let key;
    switch (period) {
      case 'day':
        key = format(date, 'yyyy-MM-dd');
        break;
      case 'week':
        key = format(startOfWeek(date, { locale: es }), 'yyyy-MM-dd');
        break;
      case 'month':
        key = format(startOfMonth(date), 'yyyy-MM');
        break;
      default:
        key = format(date, 'yyyy-MM-dd');
    }

    return dataMap.get(key) || {
      period: key,
      date: key,
      count: 0,
      value: 0,
      items: []
    };
  });
};

/**
 * Calcular métricas de tendencia
 */
export const calculateTrendMetrics = (timeSeries, valueField = 'value') => {
  if (!timeSeries || timeSeries.length < 2) {
    return {
      trend: 'stable',
      slope: 0,
      correlation: 0,
      changePercent: 0,
      isSignificant: false
    };
  }

  const values = timeSeries.map(item => item[valueField] || 0);
  const n = values.length;
  const x = Array.from({ length: n }, (_, i) => i);

  // Calcular regresión lineal simple
  const meanX = x.reduce((sum, val) => sum + val, 0) / n;
  const meanY = values.reduce((sum, val) => sum + val, 0) / n;

  let numerator = 0;
  let denominatorX = 0;
  let denominatorY = 0;

  for (let i = 0; i < n; i++) {
    const deltaX = x[i] - meanX;
    const deltaY = values[i] - meanY;
    
    numerator += deltaX * deltaY;
    denominatorX += deltaX * deltaX;
    denominatorY += deltaY * deltaY;
  }

  const slope = denominatorX === 0 ? 0 : numerator / denominatorX;
  const correlation = Math.sqrt(denominatorX * denominatorY) === 0 ? 0 : 
    numerator / Math.sqrt(denominatorX * denominatorY);

  // Calcular cambio porcentual
  const firstValue = values[0];
  const lastValue = values[n - 1];
  const changePercent = firstValue === 0 ? 0 : ((lastValue - firstValue) / firstValue) * 100;

  // Determinar tendencia
  let trend = 'stable';
  if (Math.abs(slope) > 0.1) {
    trend = slope > 0 ? 'increasing' : 'decreasing';
  }

  // Determinar si es significativo (simplificado)
  const isSignificant = Math.abs(correlation) > 0.5 && Math.abs(changePercent) > 5;

  return {
    trend,
    slope: Math.round(slope * 1000) / 1000,
    correlation: Math.round(correlation * 1000) / 1000,
    changePercent: Math.round(changePercent * 10) / 10,
    isSignificant,
    firstValue,
    lastValue,
    meanValue: Math.round(meanY * 10) / 10
  };
};

/**
 * Agrupar datos por rangos de percentiles
 */
export const groupByPercentileRanges = (data, percentileField = 'percentil') => {
  const ranges = {
    'Muy bajo (0-10)': { min: 0, max: 10, count: 0, items: [] },
    'Bajo (10-25)': { min: 10, max: 25, count: 0, items: [] },
    'Bajo promedio (25-40)': { min: 25, max: 40, count: 0, items: [] },
    'Promedio (40-60)': { min: 40, max: 60, count: 0, items: [] },
    'Sobre promedio (60-75)': { min: 60, max: 75, count: 0, items: [] },
    'Alto (75-90)': { min: 75, max: 90, count: 0, items: [] },
    'Muy alto (90-100)': { min: 90, max: 100, count: 0, items: [] }
  };

  data.forEach(item => {
    const percentile = item[percentileField];
    if (percentile !== null && percentile !== undefined) {
      for (const [rangeName, range] of Object.entries(ranges)) {
        if (percentile >= range.min && percentile < range.max) {
          range.count++;
          range.items.push(item);
          break;
        }
      }
    }
  });

  return ranges;
};

/**
 * Calcular estadísticas de distribución
 */
export const calculateDistributionStats = (data, valueField = 'percentil') => {
  if (!data || data.length === 0) {
    return {
      count: 0,
      mean: 0,
      median: 0,
      mode: null,
      standardDeviation: 0,
      min: 0,
      max: 0,
      quartiles: { q1: 0, q2: 0, q3: 0 }
    };
  }

  const values = data.map(item => item[valueField]).filter(val => val !== null && val !== undefined);
  if (values.length === 0) return { count: 0, mean: 0, median: 0, mode: null, standardDeviation: 0, min: 0, max: 0, quartiles: { q1: 0, q2: 0, q3: 0 } };

  const sortedValues = [...values].sort((a, b) => a - b);
  const n = values.length;

  // Media
  const mean = values.reduce((sum, val) => sum + val, 0) / n;

  // Mediana
  const median = n % 2 === 0 
    ? (sortedValues[n / 2 - 1] + sortedValues[n / 2]) / 2
    : sortedValues[Math.floor(n / 2)];

  // Moda
  const frequency = {};
  let maxFreq = 0;
  let mode = null;

  values.forEach(val => {
    frequency[val] = (frequency[val] || 0) + 1;
    if (frequency[val] > maxFreq) {
      maxFreq = frequency[val];
      mode = val;
    }
  });

  if (maxFreq === 1) mode = null; // No hay moda si todos los valores aparecen una vez

  // Desviación estándar
  const variance = values.reduce((sum, val) => sum + Math.pow(val - mean, 2), 0) / n;
  const standardDeviation = Math.sqrt(variance);

  // Cuartiles
  const q1 = calculatePercentile(sortedValues, 25);
  const q2 = median;
  const q3 = calculatePercentile(sortedValues, 75);

  return {
    count: n,
    mean: Math.round(mean * 100) / 100,
    median: Math.round(median * 100) / 100,
    mode,
    standardDeviation: Math.round(standardDeviation * 100) / 100,
    min: sortedValues[0],
    max: sortedValues[n - 1],
    quartiles: {
      q1: Math.round(q1 * 100) / 100,
      q2: Math.round(q2 * 100) / 100,
      q3: Math.round(q3 * 100) / 100
    }
  };
};

/**
 * Calcular percentil específico
 */
export const calculatePercentile = (sortedData, percentile) => {
  if (sortedData.length === 0) return 0;
  
  const index = (percentile / 100) * (sortedData.length - 1);
  const lower = Math.floor(index);
  const upper = Math.ceil(index);
  
  if (lower === upper) {
    return sortedData[lower];
  }
  
  const weight = index - lower;
  return sortedData[lower] * (1 - weight) + sortedData[upper] * weight;
};

/**
 * Transformar datos para gráficos de Recharts
 */
export const transformForRecharts = (data, config = {}) => {
  const {
    xField = 'name',
    yField = 'value',
    groupField = null,
    sortBy = null,
    limit = null
  } = config;

  let transformed = data.map(item => ({
    [xField]: item[xField] || item.name || item.label,
    [yField]: item[yField] || item.value || item.count,
    ...item
  }));

  // Agrupar si es necesario
  if (groupField) {
    const grouped = {};
    transformed.forEach(item => {
      const group = item[groupField];
      if (!grouped[group]) {
        grouped[group] = [];
      }
      grouped[group].push(item);
    });
    transformed = Object.entries(grouped).map(([group, items]) => ({
      [xField]: group,
      [yField]: items.reduce((sum, item) => sum + (item[yField] || 0), 0),
      items
    }));
  }

  // Ordenar si es necesario
  if (sortBy) {
    transformed.sort((a, b) => {
      if (sortBy === 'asc') return a[yField] - b[yField];
      if (sortBy === 'desc') return b[yField] - a[yField];
      return a[xField].localeCompare(b[xField]);
    });
  }

  // Limitar si es necesario
  if (limit && limit > 0) {
    transformed = transformed.slice(0, limit);
  }

  return transformed;
};

/**
 * Funciones auxiliares de normalización
 */
export const normalizeEducationLevel = (nivel) => {
  if (!nivel) return 'Sin definir';
  
  const nivelLower = nivel.toLowerCase();
  if (nivelLower.includes('elemental') || nivel === 'E') return 'E';
  if (nivelLower.includes('medio') || nivel === 'M') return 'M';
  if (nivelLower.includes('superior') || nivelLower.includes('bachillerato') || nivel === 'S') return 'S';
  
  return 'Sin definir';
};

export const normalizeGender = (genero) => {
  if (!genero) return 'No especificado';
  
  const generoLower = genero.toLowerCase();
  if (generoLower.includes('masculino') || generoLower === 'm') return 'masculino';
  if (generoLower.includes('femenino') || generoLower === 'f') return 'femenino';
  if (generoLower.includes('otro')) return 'otro';
  
  return 'No especificado';
};

/**
 * Generar datos de ejemplo para testing
 */
export const generateSampleData = (type = 'timeSeries', count = 30) => {
  const data = [];
  const today = new Date();

  switch (type) {
    case 'timeSeries':
      for (let i = 0; i < count; i++) {
        const date = new Date(today);
        date.setDate(date.getDate() - (count - i));
        data.push({
          date: format(date, 'yyyy-MM-dd'),
          value: Math.floor(Math.random() * 50) + 25 + Math.sin(i / 5) * 10,
          count: Math.floor(Math.random() * 20) + 5
        });
      }
      break;

    case 'aptitudes':
      Object.keys(APTITUDE_CODES).forEach(code => {
        data.push({
          aptitudeCode: code,
          aptitudeName: APTITUDE_CODES[code],
          value: Math.floor(Math.random() * 40) + 40,
          count: Math.floor(Math.random() * 100) + 20
        });
      });
      break;

    case 'distribution':
      for (let i = 0; i < count; i++) {
        data.push({
          id: i + 1,
          percentil: Math.floor(Math.random() * 100),
          aptitude_code: Object.keys(APTITUDE_CODES)[Math.floor(Math.random() * 7)],
          nivel_educativo: ['E', 'M', 'S'][Math.floor(Math.random() * 3)],
          genero: ['masculino', 'femenino'][Math.floor(Math.random() * 2)]
        });
      }
      break;

    default:
      return [];
  }

  return data;
};

export default {
  aggregateByTimePeriod,
  aggregateByAptitude,
  aggregateByEducationLevel,
  aggregateByGender,
  createCompleteTimeSeries,
  calculateTrendMetrics,
  groupByPercentileRanges,
  calculateDistributionStats,
  calculatePercentile,
  transformForRecharts,
  normalizeEducationLevel,
  normalizeGender,
  generateSampleData
};