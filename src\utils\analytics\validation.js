/**
 * Utilidades de validación para datos analytics
 * Asegura la integridad y consistencia de los datos antes del procesamiento
 */

import { APTITUDE_CODES, PERCENTILE_RANGES, VALIDATION_RULES } from './constants.js';

/**
 * Validar estructura básica de datos de evaluación
 * @param {Object} evaluationData - Datos de evaluación a validar
 * @returns {Object} Resultado de validación con errores si los hay
 */
export const validateEvaluationData = (evaluationData) => {
  const errors = [];
  const warnings = [];

  if (!evaluationData) {
    errors.push('Los datos de evaluación son requeridos');
    return { isValid: false, errors, warnings };
  }

  // Validar campos requeridos
  const requiredFields = ['paciente_id', 'psicologo_id', 'fecha_inicio'];
  requiredFields.forEach(field => {
    if (!evaluationData[field]) {
      errors.push(`Campo requerido faltante: ${field}`);
    }
  });

  // Validar fechas
  if (evaluationData.fecha_inicio) {
    const startDate = new Date(evaluationData.fecha_inicio);
    if (isNaN(startDate.getTime())) {
      errors.push('Fecha de inicio inválida');
    } else if (startDate > new Date()) {
      warnings.push('La fecha de inicio es futura');
    }
  }

  if (evaluationData.fecha_fin) {
    const endDate = new Date(evaluationData.fecha_fin);
    const startDate = new Date(evaluationData.fecha_inicio);
    
    if (isNaN(endDate.getTime())) {
      errors.push('Fecha de fin inválida');
    } else if (endDate < startDate) {
      errors.push('La fecha de fin no puede ser anterior a la fecha de inicio');
    }
  }

  // Validar resultados si están presentes
  if (evaluationData.resultados && Array.isArray(evaluationData.resultados)) {
    evaluationData.resultados.forEach((resultado, index) => {
      const resultValidation = validateResultData(resultado);
      if (!resultValidation.isValid) {
        errors.push(`Resultado ${index + 1}: ${resultValidation.errors.join(', ')}`);
      }
      warnings.push(...resultValidation.warnings.map(w => `Resultado ${index + 1}: ${w}`));
    });
  }

  return {
    isValid: errors.length === 0,
    errors,
    warnings
  };
};

/**
 * Validar datos de resultado individual
 * @param {Object} resultData - Datos de resultado a validar
 * @returns {Object} Resultado de validación
 */
export const validateResultData = (resultData) => {
  const errors = [];
  const warnings = [];

  if (!resultData) {
    errors.push('Los datos de resultado son requeridos');
    return { isValid: false, errors, warnings };
  }

  // Validar aptitud
  if (!resultData.aptitud_id && !resultData.aptitude_code) {
    errors.push('ID o código de aptitud es requerido');
  }

  if (resultData.aptitude_code && !APTITUDE_CODES[resultData.aptitude_code]) {
    errors.push(`Código de aptitud inválido: ${resultData.aptitude_code}`);
  }

  // Validar puntuación directa
  if (resultData.puntuacion_directa !== undefined) {
    if (typeof resultData.puntuacion_directa !== 'number' || resultData.puntuacion_directa < 0) {
      errors.push('La puntuación directa debe ser un número positivo');
    }
  }

  // Validar percentil
  if (resultData.percentil !== undefined) {
    if (typeof resultData.percentil !== 'number' || 
        resultData.percentil < VALIDATION_RULES.MIN_PERCENTILE || 
        resultData.percentil > VALIDATION_RULES.MAX_PERCENTILE) {
      errors.push(`El percentil debe estar entre ${VALIDATION_RULES.MIN_PERCENTILE} y ${VALIDATION_RULES.MAX_PERCENTILE}`);
    }
  }

  // Validar consistencia entre puntuación directa y percentil
  if (resultData.puntuacion_directa !== undefined && resultData.percentil !== undefined) {
    if (resultData.puntuacion_directa === 0 && resultData.percentil > 10) {
      warnings.push('Puntuación directa de 0 con percentil alto puede indicar un error');
    }
  }

  return {
    isValid: errors.length === 0,
    errors,
    warnings
  };
};

/**
 * Validar datos de paciente
 * @param {Object} patientData - Datos de paciente a validar
 * @returns {Object} Resultado de validación
 */
export const validatePatientData = (patientData) => {
  const errors = [];
  const warnings = [];

  if (!patientData) {
    errors.push('Los datos de paciente son requeridos');
    return { isValid: false, errors, warnings };
  }

  // Validar campos requeridos
  const requiredFields = ['nombre', 'apellido', 'email'];
  requiredFields.forEach(field => {
    if (!patientData[field] || patientData[field].trim() === '') {
      errors.push(`Campo requerido faltante: ${field}`);
    }
  });

  // Validar email
  if (patientData.email) {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (!emailRegex.test(patientData.email)) {
      errors.push('Formato de email inválido');
    }
  }

  // Validar fecha de nacimiento
  if (patientData.fecha_nacimiento) {
    const birthDate = new Date(patientData.fecha_nacimiento);
    const today = new Date();
    const age = today.getFullYear() - birthDate.getFullYear();
    
    if (isNaN(birthDate.getTime())) {
      errors.push('Fecha de nacimiento inválida');
    } else if (birthDate > today) {
      errors.push('La fecha de nacimiento no puede ser futura');
    } else if (age > 100) {
      warnings.push('Edad superior a 100 años, verificar fecha de nacimiento');
    } else if (age < 5) {
      warnings.push('Edad inferior a 5 años, verificar si es apropiado para BAT-7');
    }
  }

  // Validar género
  if (patientData.genero) {
    const validGenders = ['masculino', 'femenino', 'otro'];
    if (!validGenders.includes(patientData.genero.toLowerCase())) {
      warnings.push('Género no estándar, verificar consistencia');
    }
  }

  // Validar nivel educativo
  if (patientData.nivel_educativo) {
    const validLevels = ['E', 'M', 'S', 'elemental', 'medio', 'superior'];
    if (!validLevels.includes(patientData.nivel_educativo)) {
      warnings.push('Nivel educativo no estándar');
    }
  }

  return {
    isValid: errors.length === 0,
    errors,
    warnings
  };
};

/**
 * Validar conjunto de datos para análisis estadístico
 * @param {Array} dataset - Conjunto de datos a validar
 * @param {Object} options - Opciones de validación
 * @returns {Object} Resultado de validación
 */
export const validateDatasetForAnalysis = (dataset, options = {}) => {
  const errors = [];
  const warnings = [];
  const {
    minSampleSize = VALIDATION_RULES.MIN_SAMPLE_SIZE,
    requiredFields = [],
    allowMissingValues = true
  } = options;

  if (!Array.isArray(dataset)) {
    errors.push('El dataset debe ser un array');
    return { isValid: false, errors, warnings };
  }

  if (dataset.length === 0) {
    errors.push('El dataset no puede estar vacío');
    return { isValid: false, errors, warnings };
  }

  if (dataset.length < minSampleSize) {
    warnings.push(`Tamaño de muestra pequeño (${dataset.length}), se recomienda al menos ${minSampleSize} registros`);
  }

  // Validar estructura de datos
  const firstItem = dataset[0];
  const expectedKeys = Object.keys(firstItem);

  dataset.forEach((item, index) => {
    // Verificar campos requeridos
    requiredFields.forEach(field => {
      if (!(field in item)) {
        errors.push(`Campo requerido '${field}' faltante en registro ${index + 1}`);
      }
    });

    // Verificar consistencia de estructura
    const itemKeys = Object.keys(item);
    const missingKeys = expectedKeys.filter(key => !itemKeys.includes(key));
    const extraKeys = itemKeys.filter(key => !expectedKeys.includes(key));

    if (missingKeys.length > 0) {
      warnings.push(`Registro ${index + 1}: campos faltantes: ${missingKeys.join(', ')}`);
    }

    if (extraKeys.length > 0) {
      warnings.push(`Registro ${index + 1}: campos adicionales: ${extraKeys.join(', ')}`);
    }
  });

  // Validar valores numéricos si están presentes
  const numericFields = ['percentil', 'puntuacion_directa', 'edad'];
  numericFields.forEach(field => {
    const values = dataset.map(item => item[field]).filter(val => val !== null && val !== undefined);
    
    if (values.length > 0) {
      const invalidValues = values.filter(val => typeof val !== 'number' || isNaN(val));
      if (invalidValues.length > 0) {
        errors.push(`Campo '${field}' contiene valores no numéricos`);
      }

      // Detectar outliers extremos
      if (field === 'percentil') {
        const outOfRange = values.filter(val => val < 0 || val > 100);
        if (outOfRange.length > 0) {
          errors.push(`Campo '${field}' contiene valores fuera del rango válido (0-100)`);
        }
      }
    }
  });

  return {
    isValid: errors.length === 0,
    errors,
    warnings,
    stats: {
      totalRecords: dataset.length,
      validRecords: dataset.filter(item => 
        requiredFields.every(field => field in item && item[field] !== null)
      ).length
    }
  };
};

/**
 * Validar filtros de dashboard
 * @param {Object} filters - Filtros a validar
 * @returns {Object} Resultado de validación
 */
export const validateDashboardFilters = (filters) => {
  const errors = [];
  const warnings = [];

  if (!filters || typeof filters !== 'object') {
    return { isValid: true, errors, warnings }; // Filtros vacíos son válidos
  }

  // Validar rango de fechas
  if (filters.dateRange) {
    const { start, end } = filters.dateRange;
    
    if (start && end) {
      const startDate = new Date(start);
      const endDate = new Date(end);
      
      if (isNaN(startDate.getTime()) || isNaN(endDate.getTime())) {
        errors.push('Fechas de filtro inválidas');
      } else if (startDate > endDate) {
        errors.push('La fecha de inicio no puede ser posterior a la fecha de fin');
      } else {
        const daysDiff = Math.ceil((endDate - startDate) / (1000 * 60 * 60 * 24));
        if (daysDiff > VALIDATION_RULES.MAX_DATE_RANGE_DAYS) {
          warnings.push(`Rango de fechas muy amplio (${daysDiff} días), puede afectar el rendimiento`);
        }
      }
    }
  }

  // Validar códigos de aptitud
  if (filters.aptitudes && Array.isArray(filters.aptitudes)) {
    const invalidAptitudes = filters.aptitudes.filter(code => !APTITUDE_CODES[code]);
    if (invalidAptitudes.length > 0) {
      errors.push(`Códigos de aptitud inválidos: ${invalidAptitudes.join(', ')}`);
    }
  }

  // Validar niveles educativos
  if (filters.educationLevels && Array.isArray(filters.educationLevels)) {
    const validLevels = ['E', 'M', 'S'];
    const invalidLevels = filters.educationLevels.filter(level => !validLevels.includes(level));
    if (invalidLevels.length > 0) {
      errors.push(`Niveles educativos inválidos: ${invalidLevels.join(', ')}`);
    }
  }

  // Validar rango de edad
  if (filters.ageRange) {
    const { min, max } = filters.ageRange;
    if (min !== undefined && max !== undefined) {
      if (min < 0 || max < 0) {
        errors.push('Las edades no pueden ser negativas');
      } else if (min > max) {
        errors.push('La edad mínima no puede ser mayor que la máxima');
      } else if (max > 100) {
        warnings.push('Edad máxima superior a 100 años');
      }
    }
  }

  return {
    isValid: errors.length === 0,
    errors,
    warnings
  };
};

/**
 * Sanitizar datos de entrada para prevenir inyecciones
 * @param {any} input - Datos a sanitizar
 * @returns {any} Datos sanitizados
 */
export const sanitizeInput = (input) => {
  if (typeof input === 'string') {
    return input
      .replace(/<script\b[^<]*(?:(?!<\/script>)<[^<]*)*<\/script>/gi, '') // Remover scripts
      .replace(/javascript:/gi, '') // Remover javascript:
      .replace(/on\w+\s*=/gi, '') // Remover event handlers
      .trim();
  }
  
  if (Array.isArray(input)) {
    return input.map(sanitizeInput);
  }
  
  if (input && typeof input === 'object') {
    const sanitized = {};
    Object.keys(input).forEach(key => {
      sanitized[key] = sanitizeInput(input[key]);
    });
    return sanitized;
  }
  
  return input;
};

/**
 * Validar permisos de usuario para operaciones analytics
 * @param {Object} user - Usuario a validar
 * @param {string} operation - Operación solicitada
 * @returns {boolean} Si el usuario tiene permisos
 */
export const validateUserPermissions = (user, operation) => {
  if (!user) return false;
  
  // En el contexto actual sin autenticación, permitir todas las operaciones
  // En producción, esto debería verificar roles y permisos reales
  const allowedOperations = [
    'view_dashboard',
    'view_analytics',
    'export_data',
    'view_patient_progress'
  ];
  
  return allowedOperations.includes(operation);
};

export default {
  validateEvaluationData,
  validateResultData,
  validatePatientData,
  validateDatasetForAnalysis,
  validateDashboardFilters,
  sanitizeInput,
  validateUserPermissions
};