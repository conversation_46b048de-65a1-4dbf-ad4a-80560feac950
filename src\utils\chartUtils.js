/**
 * @file chartUtils.js
 * @description Utility functions for chart calculations
 */

import { PIE_CHART_CONFIG } from '../constants/chartConstants.js';

/**
 * Validates and sanitizes student data
 */
export const validateStudentData = (data) => {
  if (!data || !Array.isArray(data)) {
    return [];
  }

  return data
    .map((item, index) => {
      const totalEstudiantes = parseInt(item?.total_estudiantes) || 0;
      const porcentaje = parseFloat(item?.porcentaje) || 0;
      const nivel = item?.nivel || `N${index + 1}`;
      const nivelNombre = item?.nivel_nombre || `Nivel ${index + 1}`;

      return {
        total_estudiantes: totalEstudiantes,
        porcentaje: isNaN(porcentaje) || !isFinite(porcentaje) 
          ? 0 
          : Math.max(0, Math.min(100, porcentaje)),
        nivel: nivel,
        nivel_nombre: nivelNombre
      };
    })
    .filter(item => item.total_estudiantes > 0 && item.porcentaje > 0);
};

/**
 * Calculates pie chart segments with angles and coordinates
 */
export const calculatePieSegments = (validatedData) => {
  if (!validatedData.length) return [];

  let cumulativePercentage = 0;
  
  return validatedData.map((item) => {
    const safePercentage = Math.max(0, Math.min(100, item.porcentaje));
    const startAngle = cumulativePercentage * 3.6;
    const endAngle = (cumulativePercentage + safePercentage) * 3.6;
    cumulativePercentage += safePercentage;

    return {
      ...item,
      startAngle: isNaN(startAngle) ? 0 : startAngle,
      endAngle: isNaN(endAngle) ? 0 : endAngle,
      safePercentage
    };
  });
};

/**
 * Calculates SVG path coordinates for a pie segment
 */
export const calculateSegmentPath = (segment) => {
  const { RADIUS, CENTER_X, CENTER_Y } = PIE_CHART_CONFIG;
  
  const startAngleRad = (segment.startAngle * Math.PI) / 180;
  const endAngleRad = (segment.endAngle * Math.PI) / 180;

  // Validate angles
  if (isNaN(startAngleRad) || isNaN(endAngleRad) || 
      !isFinite(startAngleRad) || !isFinite(endAngleRad)) {
    return null;
  }

  const x1 = CENTER_X + RADIUS * Math.cos(startAngleRad);
  const y1 = CENTER_Y + RADIUS * Math.sin(startAngleRad);
  const x2 = CENTER_X + RADIUS * Math.cos(endAngleRad);
  const y2 = CENTER_Y + RADIUS * Math.sin(endAngleRad);

  // Validate coordinates
  if (isNaN(x1) || isNaN(y1) || isNaN(x2) || isNaN(y2) || 
      !isFinite(x1) || !isFinite(y1) || !isFinite(x2) || !isFinite(y2)) {
    return null;
  }

  const largeArcFlag = segment.safePercentage > 50 ? 1 : 0;

  return [
    `M ${CENTER_X} ${CENTER_Y}`,
    `L ${x1.toFixed(2)} ${y1.toFixed(2)}`,
    `A ${RADIUS} ${RADIUS} 0 ${largeArcFlag} 1 ${x2.toFixed(2)} ${y2.toFixed(2)}`,
    'Z'
  ].join(' ');
};