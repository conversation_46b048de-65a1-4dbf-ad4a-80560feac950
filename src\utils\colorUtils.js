/**
 * @file colorUtils.js
 * @description Color utilities for charts and UI components
 */

import { EDUCATION_LEVEL_COLORS } from '../constants/chartConstants.js';

/**
 * Gets color configuration for education level
 */
export const getEducationLevelColors = (nivel) => {
  return EDUCATION_LEVEL_COLORS[nivel] || EDUCATION_LEVEL_COLORS.default;
};

/**
 * Gets fill color for education level (for SVG)
 */
export const getEducationLevelFill = (nivel) => {
  const colors = getEducationLevelColors(nivel);
  return colors.fill;
};