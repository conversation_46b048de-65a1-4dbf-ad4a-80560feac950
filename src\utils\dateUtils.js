/**
 * Utilidades para manejo de fechas en el sistema BAT-7
 */

/**
 * Formatea una fecha para mostrar la hora de última actualización
 * @param {Date|string|null|undefined} dateValue - Valor de fecha a formatear
 * @returns {string} Fecha formateada o mensaje por defecto
 */
export const formatLastUpdated = (dateValue) => {
  if (!dateValue) {
    return 'Cargando datos...';
  }

  try {
    const date = dateValue instanceof Date ? dateValue : new Date(dateValue);
    
    // Verificar si la fecha es válida
    if (isNaN(date.getTime())) {
      console.warn('Fecha inválida recibida:', dateValue);
      return 'Fecha inválida';
    }

    return date.toLocaleTimeString('es-ES', {
      hour: '2-digit',
      minute: '2-digit',
      second: '2-digit'
    });
  } catch (error) {
    console.error('Error al formatear fecha:', error);
    return 'Error en fecha';
  }
};

/**
 * Formatea una fecha completa con fecha y hora
 * @param {Date|string} dateValue - Valor de fecha a formatear
 * @returns {string} Fecha y hora formateadas
 */
export const formatDateTime = (dateValue) => {
  if (!dateValue) return '';

  try {
    const date = dateValue instanceof Date ? dateValue : new Date(dateValue);
    
    if (isNaN(date.getTime())) {
      return 'Fecha inválida';
    }

    return date.toLocaleString('es-ES', {
      year: 'numeric',
      month: '2-digit',
      day: '2-digit',
      hour: '2-digit',
      minute: '2-digit'
    });
  } catch (error) {
    console.error('Error al formatear fecha completa:', error);
    return 'Error en fecha';
  }
};

/**
 * Calcula el tiempo transcurrido desde una fecha
 * @param {Date|string} dateValue - Fecha de referencia
 * @returns {string} Tiempo transcurrido en formato legible
 */
export const getTimeAgo = (dateValue) => {
  if (!dateValue) return '';

  try {
    const date = dateValue instanceof Date ? dateValue : new Date(dateValue);
    const now = new Date();
    const diffMs = now - date;
    const diffMinutes = Math.floor(diffMs / (1000 * 60));
    const diffHours = Math.floor(diffMs / (1000 * 60 * 60));
    const diffDays = Math.floor(diffMs / (1000 * 60 * 60 * 24));

    if (diffMinutes < 1) return 'Hace un momento';
    if (diffMinutes < 60) return `Hace ${diffMinutes} minuto${diffMinutes > 1 ? 's' : ''}`;
    if (diffHours < 24) return `Hace ${diffHours} hora${diffHours > 1 ? 's' : ''}`;
    if (diffDays < 7) return `Hace ${diffDays} día${diffDays > 1 ? 's' : ''}`;
    
    return formatDateTime(date);
  } catch (error) {
    console.error('Error al calcular tiempo transcurrido:', error);
    return 'Error en fecha';
  }
};

export default {
  formatLastUpdated,
  formatDateTime,
  getTimeAgo
};