/**
 * @file propTypes.js
 * @description Common PropTypes definitions for the BAT-7 system
 */

import PropTypes from 'prop-types';

// Patient PropType
export const PatientPropType = PropTypes.shape({
  id: PropTypes.oneOfType([PropTypes.string, PropTypes.number]).isRequired,
  nombre: PropTypes.string.isRequired,
  apellido: PropTypes.string.isRequired,
  email: PropTypes.string,
  telefono: PropTypes.string,
  fecha_nacimiento: PropTypes.string,
  genero: PropTypes.oneOf(['masculino', 'femenino', 'otro']),
  nivel_educativo: PropTypes.oneOf(['E', 'M', 'S']),
  institucion_id: PropTypes.oneOfType([PropTypes.string, PropTypes.number]),
  created_at: PropTypes.string,
  updated_at: PropTypes.string
});

// Institution PropType
export const InstitutionPropType = PropTypes.shape({
  id: PropTypes.oneOfType([PropTypes.string, PropTypes.number]).isRequired,
  nombre: PropTypes.string.isRequired,
  direccion: PropTypes.string,
  telefono: PropTypes.string,
  email: PropTypes.string,
  created_at: PropTypes.string
});

// Aptitude PropType
export const AptitudePropType = PropTypes.shape({
  id: PropTypes.oneOfType([PropTypes.string, PropTypes.number]).isRequired,
  codigo: PropTypes.string.isRequired,
  nombre: PropTypes.string.isRequired,
  descripcion: PropTypes.string,
  total_preguntas: PropTypes.number
});

// Filter PropType
export const FilterPropType = PropTypes.shape({
  dateRange: PropTypes.shape({
    start: PropTypes.string,
    end: PropTypes.string
  }),
  institution: PropTypes.oneOfType([PropTypes.string, PropTypes.number]),
  educationalLevel: PropTypes.oneOf(['E', 'M', 'S']),
  gender: PropTypes.oneOf(['M', 'F']),
  aptitude: PropTypes.string,
  nivelAplicacion: PropTypes.oneOf(['E', 'M', 'S']),
  categoriaEvaluado: PropTypes.oneOf(['escolares', 'adultos']),
  rangoEdad: PropTypes.shape({
    min: PropTypes.number,
    max: PropTypes.number
  }),
  cursoGrado: PropTypes.string,
  psicologo: PropTypes.oneOfType([PropTypes.string, PropTypes.number]),
  pacienteEspecifico: PropTypes.oneOfType([PropTypes.string, PropTypes.number])
});

// KPI PropType
export const KPIPropType = PropTypes.shape({
  value: PropTypes.number.isRequired,
  target: PropTypes.number.isRequired,
  status: PropTypes.oneOf(['excellent', 'good', 'warning', 'critical']).isRequired,
  trend: PropTypes.oneOf(['up', 'down', 'stable']),
  description: PropTypes.string
});

// Insight PropType
export const InsightPropType = PropTypes.shape({
  type: PropTypes.oneOf(['positive', 'concern', 'neutral']).isRequired,
  title: PropTypes.string.isRequired,
  description: PropTypes.string.isRequired,
  impact: PropTypes.oneOf(['high', 'medium', 'low']).isRequired,
  category: PropTypes.string.isRequired,
  value: PropTypes.number
});

// Recommendation PropType
export const RecommendationPropType = PropTypes.shape({
  priority: PropTypes.oneOf(['high', 'medium', 'low']).isRequired,
  category: PropTypes.string.isRequired,
  title: PropTypes.string.isRequired,
  description: PropTypes.string.isRequired,
  expectedImpact: PropTypes.string,
  resources: PropTypes.string,
  timeline: PropTypes.string
});

// Chart Data PropType
export const ChartDataPropType = PropTypes.shape({
  labels: PropTypes.arrayOf(PropTypes.string),
  datasets: PropTypes.arrayOf(PropTypes.object)
});