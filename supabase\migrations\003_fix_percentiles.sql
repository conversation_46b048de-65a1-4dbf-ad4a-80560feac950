-- =====================================================
-- CORRECCIÓN MASIVA DE PERCENTILES INCONSISTENTES
-- =====================================================

-- 1. FUNCIÓN MEJORADA PARA CONVERSIÓN PD A PC
CREATE OR REPLACE FUNCTION convertir_pd_a_pc_mejorada()
RETURNS TRIGGER AS $$
DECLARE
    aptitud_codigo TEXT;
    percentil_calculado INTEGER;
BEGIN
    -- Solo procesar si el percentil está vacío o es NULL
    IF NEW.percentil IS NULL THEN
        -- Obtener el código de la aptitud
        SELECT a.codigo INTO aptitud_codigo
        FROM aptitudes a
        WHERE a.id = NEW.aptitud_id;
        
        -- Si encontramos el código de aptitud, buscar el percentil
        IF aptitud_codigo IS NOT NULL AND NEW.puntaje_directo IS NOT NULL THEN
            SELECT b.percentil INTO percentil_calculado
            FROM baremos b
            WHERE b.factor = aptitud_codigo
            AND NEW.puntaje_directo >= b.puntaje_min
            AND NEW.puntaje_directo <= b.puntaje_max
            ORDER BY b.puntaje_min DESC
            LIMIT 1;
            
            -- Si encontramos un percentil, actualizar el registro
            IF percentil_calculado IS NOT NULL THEN
                NEW.percentil := percentil_calculado;
                
                -- Log para debugging
                RAISE NOTICE 'Conversión automática: PD % (%) → PC %', 
                    NEW.puntaje_directo, aptitud_codigo, percentil_calculado;
            ELSE
                -- Si no se encuentra en baremos, asignar percentil por defecto
                NEW.percentil := 50;
                RAISE WARNING 'No se encontró baremo para PD % en aptitud %. Asignando PC 50.', 
                    NEW.puntaje_directo, aptitud_codigo;
            END IF;
        END IF;
    END IF;
    
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- 2. REEMPLAZAR EL TRIGGER EXISTENTE
DROP TRIGGER IF EXISTS trigger_convertir_pd_a_pc ON resultados;
CREATE TRIGGER trigger_convertir_pd_a_pc
    BEFORE INSERT OR UPDATE ON resultados
    FOR EACH ROW
    EXECUTE FUNCTION convertir_pd_a_pc_mejorada();

-- 3. FUNCIÓN PARA CORRECCIÓN MASIVA DE PERCENTILES EXISTENTES
CREATE OR REPLACE FUNCTION corregir_percentiles_masivo()
RETURNS TABLE(
    resultado_id UUID,
    aptitud_codigo TEXT,
    puntaje_directo INTEGER,
    percentil_anterior INTEGER,
    percentil_nuevo INTEGER,
    estado TEXT
) AS $$
DECLARE
    resultado RECORD;
    aptitud_codigo_var TEXT;
    percentil_correcto INTEGER;
    contador INTEGER := 0;
BEGIN
    -- Iterar sobre todos los resultados
    FOR resultado IN 
        SELECT r.id, r.puntaje_directo, r.percentil, r.aptitud_id, a.codigo as codigo_aptitud
        FROM resultados r
        JOIN aptitudes a ON r.aptitud_id = a.id
        WHERE r.puntaje_directo IS NOT NULL
        ORDER BY r.created_at DESC
    LOOP
        -- Buscar el percentil correcto según baremos
        SELECT b.percentil INTO percentil_correcto
        FROM baremos b
        WHERE b.factor = resultado.codigo_aptitud
        AND resultado.puntaje_directo >= b.puntaje_min
        AND resultado.puntaje_directo <= b.puntaje_max
        ORDER BY b.puntaje_min DESC
        LIMIT 1;
        
        -- Si encontramos un percentil correcto y es diferente al actual
        IF percentil_correcto IS NOT NULL AND 
           (resultado.percentil IS NULL OR resultado.percentil != percentil_correcto) THEN
            
            -- Actualizar el resultado
            UPDATE resultados 
            SET 
                percentil = percentil_correcto,
                updated_at = CURRENT_TIMESTAMP
            WHERE id = resultado.id;
            
            contador := contador + 1;
            
            -- Retornar información de la corrección
            resultado_id := resultado.id;
            aptitud_codigo := resultado.codigo_aptitud;
            puntaje_directo := resultado.puntaje_directo;
            percentil_anterior := resultado.percentil;
            percentil_nuevo := percentil_correcto;
            estado := 'CORREGIDO';
            
            RETURN NEXT;
            
        ELSIF percentil_correcto IS NULL THEN
            -- No se encontró baremo para este PD
            resultado_id := resultado.id;
            aptitud_codigo := resultado.codigo_aptitud;
            puntaje_directo := resultado.puntaje_directo;
            percentil_anterior := resultado.percentil;
            percentil_nuevo := NULL;
            estado := 'SIN_BAREMO';
            
            RETURN NEXT;
        END IF;
    END LOOP;
    
    RAISE NOTICE 'Corrección masiva completada. % resultados corregidos.', contador;
END;
$$ LANGUAGE plpgsql;

-- 4. FUNCIÓN PARA VERIFICAR CONSISTENCIA DE PERCENTILES
CREATE OR REPLACE FUNCTION verificar_consistencia_percentiles()
RETURNS TABLE(
    aptitud TEXT,
    puntaje_directo INTEGER,
    percentil_actual INTEGER,
    percentil_esperado INTEGER,
    paciente_nombre TEXT,
    fecha_evaluacion TIMESTAMP WITH TIME ZONE,
    inconsistente BOOLEAN
) AS $$
BEGIN
    RETURN QUERY
    SELECT 
        a.codigo as aptitud,
        r.puntaje_directo,
        r.percentil as percentil_actual,
        b.percentil as percentil_esperado,
        CONCAT(p.nombre, ' ', p.apellido) as paciente_nombre,
        r.created_at as fecha_evaluacion,
        (r.percentil != b.percentil OR r.percentil IS NULL) as inconsistente
    FROM resultados r
    JOIN aptitudes a ON r.aptitud_id = a.id
    JOIN pacientes p ON r.paciente_id = p.id
    LEFT JOIN baremos b ON (
        b.factor = a.codigo AND
        r.puntaje_directo >= b.puntaje_min AND
        r.puntaje_directo <= b.puntaje_max
    )
    WHERE r.puntaje_directo IS NOT NULL
    ORDER BY r.created_at DESC;
END;
$$ LANGUAGE plpgsql;

-- 5. FUNCIÓN PARA GENERAR REPORTE DE INCONSISTENCIAS
CREATE OR REPLACE FUNCTION generar_reporte_inconsistencias()
RETURNS TABLE(
    resumen_inconsistencias JSONB
) AS $$
DECLARE
    total_resultados INTEGER;
    resultados_inconsistentes INTEGER;
    resultados_sin_percentil INTEGER;
    aptitudes_afectadas JSONB;
BEGIN
    -- Contar totales
    SELECT COUNT(*) INTO total_resultados
    FROM resultados WHERE puntaje_directo IS NOT NULL;
    
    -- Contar inconsistencias
    SELECT COUNT(*) INTO resultados_inconsistentes
    FROM resultados r
    JOIN aptitudes a ON r.aptitud_id = a.id
    LEFT JOIN baremos b ON (
        b.factor = a.codigo AND
        r.puntaje_directo >= b.puntaje_min AND
        r.puntaje_directo <= b.puntaje_max
    )
    WHERE r.puntaje_directo IS NOT NULL
    AND (r.percentil != b.percentil OR r.percentil IS NULL);
    
    -- Contar sin percentil
    SELECT COUNT(*) INTO resultados_sin_percentil
    FROM resultados WHERE puntaje_directo IS NOT NULL AND percentil IS NULL;
    
    -- Aptitudes más afectadas
    SELECT jsonb_agg(
        jsonb_build_object(
            'aptitud', aptitud_codigo,
            'inconsistencias', total_inconsistencias
        )
    ) INTO aptitudes_afectadas
    FROM (
        SELECT 
            a.codigo as aptitud_codigo,
            COUNT(*) as total_inconsistencias
        FROM resultados r
        JOIN aptitudes a ON r.aptitud_id = a.id
        LEFT JOIN baremos b ON (
            b.factor = a.codigo AND
            r.puntaje_directo >= b.puntaje_min AND
            r.puntaje_directo <= b.puntaje_max
        )
        WHERE r.puntaje_directo IS NOT NULL
        AND (r.percentil != b.percentil OR r.percentil IS NULL)
        GROUP BY a.codigo
        ORDER BY COUNT(*) DESC
        LIMIT 10
    ) sub;
    
    -- Retornar resumen
    RETURN QUERY
    SELECT jsonb_build_object(
        'fecha_reporte', CURRENT_TIMESTAMP,
        'total_resultados', total_resultados,
        'resultados_inconsistentes', resultados_inconsistentes,
        'resultados_sin_percentil', resultados_sin_percentil,
        'porcentaje_inconsistencias', 
            ROUND((resultados_inconsistentes::DECIMAL / total_resultados) * 100, 2),
        'aptitudes_mas_afectadas', aptitudes_afectadas
    );
END;
$$ LANGUAGE plpgsql;

-- 6. CREAR VISTA PARA MONITOREO CONTINUO
CREATE OR REPLACE VIEW vista_monitoreo_percentiles AS
SELECT 
    a.codigo as aptitud,
    r.puntaje_directo,
    r.percentil as percentil_actual,
    b.percentil as percentil_esperado,
    CASE 
        WHEN r.percentil IS NULL THEN 'SIN_PERCENTIL'
        WHEN r.percentil != b.percentil THEN 'INCONSISTENTE'
        ELSE 'CORRECTO'
    END as estado,
    CONCAT(p.nombre, ' ', p.apellido) as paciente,
    r.created_at as fecha_evaluacion,
    r.id as resultado_id
FROM resultados r
JOIN aptitudes a ON r.aptitud_id = a.id
JOIN pacientes p ON r.paciente_id = p.id
LEFT JOIN baremos b ON (
    b.factor = a.codigo AND
    r.puntaje_directo >= b.puntaje_min AND
    r.puntaje_directo <= b.puntaje_max
)
WHERE r.puntaje_directo IS NOT NULL
ORDER BY r.created_at DESC;

-- 7. ÍNDICES PARA OPTIMIZAR CONSULTAS
CREATE INDEX IF NOT EXISTS idx_resultados_puntaje_percentil 
ON resultados(puntaje_directo, percentil);

CREATE INDEX IF NOT EXISTS idx_baremos_factor_puntaje 
ON baremos(factor, puntaje_min, puntaje_max);

-- 8. COMENTARIOS PARA DOCUMENTACIÓN
COMMENT ON FUNCTION corregir_percentiles_masivo() IS 
'Función para corregir masivamente todos los percentiles inconsistentes en la tabla resultados';

COMMENT ON FUNCTION verificar_consistencia_percentiles() IS 
'Función para verificar la consistencia entre percentiles actuales y esperados según baremos';

COMMENT ON VIEW vista_monitoreo_percentiles IS 
'Vista para monitoreo continuo del estado de los percentiles en resultados';
