-- =====================================================
-- TABLA PARA INFORMES GENERADOS MANUALMENTE
-- =====================================================

-- Tabla para almacenar informes generados
CREATE TABLE IF NOT EXISTS informes_generados (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    paciente_id UUID NOT NULL REFERENCES pacientes(id) ON DELETE CASCADE,
    tipo_informe VARCHAR(50) NOT NULL, -- 'completo', 'individual', 'comparativo'
    titulo VARCHAR(255) NOT NULL,
    descripcion TEXT,
    contenido JSONB NOT NULL, -- Contenido del informe en formato JSON
    archivo_url TEXT, -- URL del archivo PDF generado (opcional)
    estado VARCHAR(20) DEFAULT 'generado', -- 'generado', 'archivado', 'eliminado'
    generado_por UUID REFERENCES auth.users(id),
    fecha_generacion TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    fecha_archivado TIMESTAMP WITH TIME ZONE,
    metadatos JSONB DEFAULT '{}', -- Información adicional del informe
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Índices para optimizar consultas
CREATE INDEX IF NOT EXISTS idx_informes_paciente_id ON informes_generados(paciente_id);
CREATE INDEX IF NOT EXISTS idx_informes_tipo ON informes_generados(tipo_informe);
CREATE INDEX IF NOT EXISTS idx_informes_estado ON informes_generados(estado);
CREATE INDEX IF NOT EXISTS idx_informes_fecha_generacion ON informes_generados(fecha_generacion);
CREATE INDEX IF NOT EXISTS idx_informes_generado_por ON informes_generados(generado_por);

-- RLS (Row Level Security)
ALTER TABLE informes_generados ENABLE ROW LEVEL SECURITY;

-- Política para que los psicólogos solo vean informes de sus pacientes
CREATE POLICY "psicologo_ve_informes_sus_pacientes" ON informes_generados
FOR SELECT USING (
    auth.jwt() ->> 'tipo_usuario' = 'psicologo' AND
    paciente_id IN (
        SELECT id FROM pacientes 
        WHERE psicologo_asignado_id = (auth.jwt() ->> 'user_id')::uuid
    )
);

-- Política para que los administradores vean todos los informes
CREATE POLICY "admin_ve_todos_informes" ON informes_generados
FOR ALL USING (auth.jwt() ->> 'tipo_usuario' = 'administrador');

-- Política para que los psicólogos puedan crear informes de sus pacientes
CREATE POLICY "psicologo_crea_informes_sus_pacientes" ON informes_generados
FOR INSERT WITH CHECK (
    auth.jwt() ->> 'tipo_usuario' IN ('psicologo', 'administrador') AND
    paciente_id IN (
        SELECT id FROM pacientes 
        WHERE psicologo_asignado_id = (auth.jwt() ->> 'user_id')::uuid
        OR auth.jwt() ->> 'tipo_usuario' = 'administrador'
    )
);

-- Política para actualizar informes
CREATE POLICY "usuario_actualiza_sus_informes" ON informes_generados
FOR UPDATE USING (
    generado_por = (auth.jwt() ->> 'user_id')::uuid OR
    auth.jwt() ->> 'tipo_usuario' = 'administrador'
);

-- Función para actualizar timestamp de updated_at
CREATE OR REPLACE FUNCTION update_informes_updated_at()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Trigger para actualizar updated_at automáticamente
CREATE TRIGGER trigger_update_informes_updated_at
    BEFORE UPDATE ON informes_generados
    FOR EACH ROW
    EXECUTE FUNCTION update_informes_updated_at();

-- Función para generar informe completo de un paciente
CREATE OR REPLACE FUNCTION generar_informe_completo(
    p_paciente_id UUID,
    p_titulo VARCHAR(255) DEFAULT NULL,
    p_descripcion TEXT DEFAULT NULL
)
RETURNS UUID AS $$
DECLARE
    informe_id UUID;
    paciente_data JSONB;
    resultados_data JSONB;
    contenido_informe JSONB;
    titulo_generado VARCHAR(255);
BEGIN
    -- Obtener datos del paciente
    SELECT jsonb_build_object(
        'id', p.id,
        'nombre', p.nombre,
        'apellido', p.apellido,
        'documento', p.documento,
        'fecha_nacimiento', p.fecha_nacimiento,
        'sexo', p.sexo,
        'nivel_educativo', p.nivel_educativo,
        'institucion', p.institucion
    ) INTO paciente_data
    FROM pacientes p
    WHERE p.id = p_paciente_id;
    
    IF paciente_data IS NULL THEN
        RAISE EXCEPTION 'Paciente no encontrado con ID: %', p_paciente_id;
    END IF;
    
    -- Obtener resultados del paciente
    SELECT jsonb_agg(
        jsonb_build_object(
            'id', r.id,
            'puntaje_directo', r.puntaje_directo,
            'percentil', r.percentil,
            'errores', r.errores,
            'tiempo_segundos', r.tiempo_segundos,
            'concentracion', r.concentracion,
            'fecha_evaluacion', r.created_at,
            'aptitud', jsonb_build_object(
                'codigo', a.codigo,
                'nombre', a.nombre,
                'descripcion', a.descripcion
            )
        )
    ) INTO resultados_data
    FROM resultados r
    JOIN aptitudes a ON r.aptitud_id = a.id
    WHERE r.paciente_id = p_paciente_id
    ORDER BY r.created_at DESC;
    
    -- Generar título si no se proporciona
    titulo_generado := COALESCE(
        p_titulo, 
        'Informe Completo - ' || (paciente_data->>'nombre') || ' ' || (paciente_data->>'apellido')
    );
    
    -- Construir contenido del informe
    contenido_informe := jsonb_build_object(
        'tipo', 'completo',
        'paciente', paciente_data,
        'resultados', COALESCE(resultados_data, '[]'::jsonb),
        'estadisticas', jsonb_build_object(
            'total_tests', COALESCE(jsonb_array_length(resultados_data), 0),
            'percentil_promedio', (
                SELECT AVG(r.percentil)
                FROM resultados r
                WHERE r.paciente_id = p_paciente_id AND r.percentil IS NOT NULL
            ),
            'fecha_primera_evaluacion', (
                SELECT MIN(r.created_at)
                FROM resultados r
                WHERE r.paciente_id = p_paciente_id
            ),
            'fecha_ultima_evaluacion', (
                SELECT MAX(r.created_at)
                FROM resultados r
                WHERE r.paciente_id = p_paciente_id
            )
        ),
        'fecha_generacion', NOW(),
        'generado_por', (auth.jwt() ->> 'user_id')::uuid
    );
    
    -- Insertar el informe
    INSERT INTO informes_generados (
        paciente_id,
        tipo_informe,
        titulo,
        descripcion,
        contenido,
        generado_por,
        metadatos
    ) VALUES (
        p_paciente_id,
        'completo',
        titulo_generado,
        COALESCE(p_descripcion, 'Informe completo generado automáticamente'),
        contenido_informe,
        (auth.jwt() ->> 'user_id')::uuid,
        jsonb_build_object(
            'total_resultados', COALESCE(jsonb_array_length(resultados_data), 0),
            'metodo_generacion', 'manual'
        )
    ) RETURNING id INTO informe_id;
    
    RETURN informe_id;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Función para obtener informes de un paciente
CREATE OR REPLACE FUNCTION obtener_informes_paciente(p_paciente_id UUID)
RETURNS TABLE(
    id UUID,
    tipo_informe VARCHAR(50),
    titulo VARCHAR(255),
    descripcion TEXT,
    estado VARCHAR(20),
    fecha_generacion TIMESTAMP WITH TIME ZONE,
    fecha_archivado TIMESTAMP WITH TIME ZONE,
    total_resultados INTEGER,
    generado_por_email TEXT
) AS $$
BEGIN
    RETURN QUERY
    SELECT 
        ig.id,
        ig.tipo_informe,
        ig.titulo,
        ig.descripcion,
        ig.estado,
        ig.fecha_generacion,
        ig.fecha_archivado,
        COALESCE((ig.metadatos->>'total_resultados')::INTEGER, 0) as total_resultados,
        COALESCE(u.email, 'Sistema') as generado_por_email
    FROM informes_generados ig
    LEFT JOIN auth.users u ON ig.generado_por = u.id
    WHERE ig.paciente_id = p_paciente_id
    AND ig.estado != 'eliminado'
    ORDER BY ig.fecha_generacion DESC;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Comentarios para documentación
COMMENT ON TABLE informes_generados IS 'Tabla para almacenar informes generados manualmente a partir de resultados de tests';
COMMENT ON FUNCTION generar_informe_completo(UUID, VARCHAR, TEXT) IS 'Genera un informe completo para un paciente específico';
COMMENT ON FUNCTION obtener_informes_paciente(UUID) IS 'Obtiene todos los informes generados para un paciente específico';
