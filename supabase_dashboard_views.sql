-- Vistas SQL para el Dashboard BAT-7

-- Vista para estadísticas generales
CREATE OR REPLACE VIEW dashboard_estadisticas_generales AS
SELECT
  COUNT(DISTINCT p.id) AS total_pacientes,
  COUNT(DISTINCT r.paciente_id) AS pacientes_evaluados,
  COUNT(r.id) AS total_evaluaciones,
  AVG(r.percentil) AS percentil_promedio_general,
  COUNT(CASE WHEN r.created_at >= NOW() - INTERVAL '30 days' THEN 1 END) AS evaluaciones_ultimo_mes,
  COUNT(CASE WHEN r.created_at >= NOW() - INTERVAL '7 days' THEN 1 END) AS evaluaciones_ultima_semana
FROM
  pacientes p
LEFT JOIN
  resultados r ON p.id = r.paciente_id;

-- Vista para distribución por nivel educativo
CREATE OR REPLACE VIEW dashboard_distribucion_nivel AS
SELECT
  nivel_educativo,
  COUNT(*) AS cantidad
FROM
  pacientes
GROUP BY
  nivel_educativo;

-- Vista para perfil institucional por aptitudes
CREATE OR REPLACE VIEW dashboard_perfil_institucional AS
SELECT
  a.codigo,
  a.nombre AS aptitud,
  AVG(r.percentil) AS percentil_promedio
FROM
  resultados r
JOIN
  aptitudes a ON r.aptitud_id = a.id
GROUP BY
  a.id, a.codigo, a.nombre
ORDER BY
  a.codigo;

-- Vista para comparativa por nivel educativo
CREATE OR REPLACE VIEW dashboard_comparativa_nivel AS
SELECT
  p.nivel_educativo,
  a.codigo,
  a.nombre AS aptitud,
  AVG(r.percentil) AS percentil_promedio
FROM
  resultados r
JOIN
  pacientes p ON r.paciente_id = p.id
JOIN
  aptitudes a ON r.aptitud_id = a.id
GROUP BY
  p.nivel_educativo, a.id, a.codigo, a.nombre
ORDER BY
  p.nivel_educativo, a.codigo;

-- Vista para comparativa por género
CREATE OR REPLACE VIEW dashboard_comparativa_genero AS
SELECT
  p.genero,
  a.codigo,
  a.nombre AS aptitud,
  AVG(r.percentil) AS percentil_promedio
FROM
  resultados r
JOIN
  pacientes p ON r.paciente_id = p.id
JOIN
  aptitudes a ON r.aptitud_id = a.id
GROUP BY
  p.genero, a.id, a.codigo, a.nombre
ORDER BY
  p.genero, a.codigo;

-- Vista para tendencias temporales (por mes)
CREATE OR REPLACE VIEW dashboard_tendencias_mensuales AS
SELECT
  DATE_TRUNC('month', r.created_at) AS mes,
  AVG(r.percentil) AS rendimiento_promedio,
  COUNT(DISTINCT r.paciente_id) / COUNT(DISTINCT p.id)::float * 100 AS tasa_participacion
FROM
  resultados r
JOIN
  pacientes p ON TRUE
WHERE
  r.created_at >= NOW() - INTERVAL '6 months'
GROUP BY
  DATE_TRUNC('month', r.created_at)
ORDER BY
  mes;

-- Vista para estudiantes en riesgo (percentil < 30)
CREATE OR REPLACE VIEW dashboard_estudiantes_riesgo AS
SELECT
  p.id,
  p.nombre,
  p.apellido,
  p.nivel_educativo,
  AVG(r.percentil) AS percentil_promedio
FROM
  pacientes p
JOIN
  resultados r ON p.id = r.paciente_id
GROUP BY
  p.id, p.nombre, p.apellido, p.nivel_educativo
HAVING
  AVG(r.percentil) < 30
ORDER BY
  percentil_promedio;