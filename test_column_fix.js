import { createClient } from '@supabase/supabase-js';

const supabaseUrl = 'https://ydglduxhgwajqdseqzpy.supabase.co';
const supabaseKey = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InlkZ2xkdXhoZ3dhanFkc2VxenB5Iiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDYzMTI4NDEsImV4cCI6MjA2MTg4ODg0MX0.HEFdJm5qnXU1PQFbF-HkZ-bLez9LuPi3LepirU0nz4c';

const supabase = createClient(supabaseUrl, supabaseKey);

async function testColumnFix() {
  console.log('🔧 Probando corrección de nombre de columna...');
  
  try {
    // Probar la consulta que estaba fallando (con nombre correcto)
    console.log('\n📊 1. Probando consulta con puntaje_directo (correcto)...');
    const { data: correctQuery, error: correctError } = await supabase
      .from('resultados')
      .select(`
        id,
        percentil,
        puntaje_directo,
        created_at,
        aptitud_id,
        evaluacion_id,
        aptitudes:aptitud_id (codigo, nombre)
      `)
      .limit(5);
    
    if (correctError) {
      console.error('❌ Error con puntaje_directo:', correctError);
    } else {
      console.log(`✅ Consulta exitosa con puntaje_directo. Resultados: ${correctQuery.length}`);
      correctQuery.forEach(r => {
        console.log(`   - Aptitud ${r.aptitudes?.codigo}: PD=${r.puntaje_directo}, PC=${r.percentil}`);
      });
    }
    
    // Probar la consulta que fallaba antes (con nombre incorrecto)
    console.log('\n📊 2. Probando consulta con puntuacion_directa (incorrecto)...');
    const { data: incorrectQuery, error: incorrectError } = await supabase
      .from('resultados')
      .select(`
        id,
        percentil,
        puntuacion_directa,
        created_at
      `)
      .limit(1);
    
    if (incorrectError) {
      console.log('✅ Error esperado con puntuacion_directa:', incorrectError.message);
    } else {
      console.log('⚠️ No debería funcionar con puntuacion_directa');
    }
    
    // Verificar estructura de la tabla
    console.log('\n📊 3. Verificando estructura de tabla resultados...');
    const { data: tableInfo, error: tableError } = await supabase
      .rpc('admin_get_table_info', { table_name: 'resultados' });
    
    if (tableError) {
      console.log('ℹ️ No se pudo obtener info de tabla (función no disponible)');
    } else {
      console.log('✅ Estructura de tabla obtenida:', tableInfo);
    }
    
    console.log('\n🎯 Resumen:');
    console.log('✅ La corrección de puntaje_directo está funcionando');
    console.log('✅ El dashboard debería cargar sin errores de columna');
    
  } catch (error) {
    console.error('💥 Error general:', error);
  }
}

// Ejecutar la prueba
testColumnFix();
