/**
 * @file test_dashboard_integration.js
 * @description Script de prueba final para verificar que no haya errores NaN
 * en todos los componentes del dashboard y que la integración funcione correctamente.
 */

import { createClient } from '@supabase/supabase-js';

// Configuración de Supabase
const supabaseUrl = 'https://your-supabase-url.supabase.co'; // !!! REEMPLAZA CON TU URL DE SUPABASE !!!
const supabaseKey = 'YOUR_SUPABASE_ANON_KEY'; // !!! REEMPLAZA CON TU CLAVE ANON DE SUPABASE !!!

const supabase = createClient(supabaseUrl, supabaseKey);

console.log('🔧 [TEST] Verificando la integración del dashboard y la ausencia de NaN...');

/**
 * Simula la validación del OverviewModule.
 * Asegura que los datos del perfil institucional y de distribución estén limpios de NaN/Infinity.
 * @param {object} data - Datos a validar para el OverviewModule.
 * @returns {object} Resultados de la validación.
 */
function validateOverviewModule(data) {
    console.log('📋 [TEST] Validando OverviewModule...');

    // Abreviaciones de aptitudes para el perfil institucional
    const aptitudeAbbreviations = {
        'Aptitud Verbal': 'V',
        'Aptitud Espacial': 'E',
        'Atención': 'A',
        'Razonamiento Lógico': 'R',
        'Aptitud Numérica': 'N',
        'Aptitud Mecánica': 'M',
        'Ortografía': 'O'
    };

    // Validar datos del perfil institucional (BarChart)
    const institutionalProfileData = data.institutionalProfile?.datasets?.[0]?.data?.map((value, index) => {
        const numericValue = parseFloat(value);
        // Asumiendo que las etiquetas del perfil institucional están en data.institutionalProfile.labels
        const fullName = data.institutionalProfile.labels?.[index] || `Aptitud ${index + 1}`;
        const abbreviation = aptitudeAbbreviations[fullName] || fullName.charAt(0).toUpperCase();

        return {
            aptitud: abbreviation,
            percentil: isNaN(numericValue) || !isFinite(numericValue) ? 0 : Math.round(numericValue * 100) / 100
        };
    }).filter(item => item.aptitud && typeof item.percentil === 'number' && isFinite(item.percentil)) || [];

    // Validar datos de distribución (PieChart)
    // const distributionColors = ['#3B82F6', '#EF4444', '#10B981', '#F59E0B', '#6366F1', '#EC4899', '#84CC16']; // No usada en la lógica de validación actual
    const validDistribution = data.distributionData?.map(item => {
        let numericValue;
        const rawValue = item?.value;

        if (typeof rawValue === 'number') {
            numericValue = rawValue;
        } else if (typeof rawValue === 'string') {
            numericValue = parseFloat(rawValue);
        } else {
            numericValue = 0; // Valor por defecto si no es un número o string válido
        }

        const safeValue = (isNaN(numericValue) || !isFinite(numericValue)) ? 0 : numericValue;
        const safeName = (item?.name && typeof item.name === 'string') ? item.name : `Sin Nombre ${Math.random()}`;
        const safeColor = (item?.color && typeof item.color === 'string') ? item.color : '#CCCCCC'; // Color por defecto

        return {
            name: safeName,
            value: safeValue,
            color: safeColor
        };
    }).filter(item => item.name && typeof item.value === 'number' && isFinite(item.value)) || [];

    console.log('   ✅ Perfil institucional validado. Total de items:', institutionalProfileData.length);
    console.log('   ✅ Distribución validada. Total de items:', validDistribution.length);

    // Verificar siglas (que todas las siglas sean válidas o que haya al menos una para el caso de prueba)
    const siglasCorrectas = institutionalProfileData.every(item =>
        ['V', 'E', 'A', 'R', 'N', 'M', 'O'].includes(item.aptitud) || item.aptitud.length === 1
    );

    console.log('   ✅ Siglas de aptitudes verificadas:', siglasCorrectas);

    return {
        institutionalProfileData,
        validDistribution,
        siglasCorrectas
    };
}

/**
 * Simula la validación del StudentsByLevelModule.
 * Asegura que los datos de estudiantes por nivel y los cálculos SVG estén limpios de NaN/Infinity.
 * @param {Array<object>} data - Datos de estudiantes por nivel.
 * @returns {object} Resultados de la validación.
 */
function validateStudentsByLevel(data) {
    console.log('📋 [TEST] Validando StudentsByLevelModule...');

    const validatedData = data.map(estudiante => {
        const totalEstudiantes = isNaN(parseFloat(estudiante.total_estudiantes)) || !isFinite(parseFloat(estudiante.total_estudiantes)) ? 0 : parseFloat(estudiante.total_estudiantes);
        const porcentaje = isNaN(parseFloat(estudiante.porcentaje)) || !isFinite(parseFloat(estudiante.porcentaje)) ? 0 : parseFloat(estudiante.porcentaje);
        const nivel = estudiante.nivel || 0;
        const nivelNombre = estudiante.nivel_nombre || `Nivel ${nivel}`;

        return {
            total_estudiantes: totalEstudiantes,
            porcentaje: porcentaje,
            nivel: nivel,
            nivel_nombre: nivelNombre
        };
    }).filter(item => item.total_estudiantes > 0 && item.porcentaje >= 0 && item.porcentaje <= 100);

    // Simular cálculo de ángulos para SVG (como en un Pie Chart)
    let cumulativePercentage = 0;
    const segments = validatedData.map(item => {
        const safePercentage = Math.max(0, Math.min(100, item.porcentaje));
        const startAngle = cumulativePercentage * 3.6;
        const endAngle = (cumulativePercentage + safePercentage) * 3.6;
        cumulativePercentage += safePercentage;

        return {
            ...item,
            startAngle: isNaN(startAngle) ? 0 : startAngle,
            endAngle: isNaN(endAngle) ? 0 : endAngle,
            safePercentage // Para verificación
        };
    });

    // Verificar cálculos SVG más a fondo (coordenadas básicas para un círculo)
    let hasInvalidSVG = false;
    segments.forEach((segment) => {
        const radius = 80;
        const centerX = 100;
        const centerY = 100;

        const startAngleRad = (segment.startAngle * Math.PI) / 180;
        const endAngleRad = (segment.endAngle * Math.PI) / 180;

        // Comprobar si los ángulos son NaN o infinitos
        if (isNaN(startAngleRad) || !isFinite(startAngleRad) || isNaN(endAngleRad) || !isFinite(endAngleRad)) {
            hasInvalidSVG = true;
            return; // Salir de la iteración actual
        }

        // Simular cálculo de puntos para el SVG path (no se usan directamente en el test, solo para verificar si son válidos)
        const x1 = centerX + radius * Math.cos(startAngleRad);
        const y1 = centerY + radius * Math.sin(startAngleRad);
        const x2 = centerX + radius * Math.cos(endAngleRad);
        const y2 = centerY + radius * Math.sin(endAngleRad);

        // Verificar si las coordenadas calculadas son NaN o infinitas
        if (isNaN(x1) || !isFinite(x1) || isNaN(y1) || !isFinite(y1) ||
            isNaN(x2) || !isFinite(x2) || isNaN(y2) || !isFinite(y2)) {
            hasInvalidSVG = true;
        }
    });

    console.log('   ✅ Datos validados para StudentsByLevel. Total de segmentos:', validatedData.length);
    console.log('   ✅ Cálculos SVG verificados. Sin NaN/Infinity en coordenadas:', !hasInvalidSVG);

    return {
        validatedData,
        segments,
        hasValidSVG: !hasInvalidSVG
    };
}

/**
 * Ejecuta la prueba de integración completa obteniendo datos de Supabase y validándolos.
 * @returns {Promise<boolean>} True si todas las pruebas pasan, false en caso contrario.
 */
async function testCompleteIntegration() {
    try {
        console.log('📊 [TEST] Obteniendo datos de Supabase para la prueba completa...');

        // Obtener datos de perfil institucional
        const { data: perfilData, error: perfilError } = await supabase
            .from('dashboard_perfil_institucional')
            .select('*')
            .order('codigo'); // Asumiendo que 'codigo' es una columna para ordenar

        if (perfilError) {
            console.error('❌ Error al obtener datos de perfil institucional:', perfilError.message);
            return false;
        }

        // Obtener datos de distribución de estudiantes por nivel
        const { data: distribucionData, error: distribucionError } = await supabase
            .from('dashboard_estudiantes_por_nivel')
            .select('*');

        if (distribucionError) {
            console.error('❌ Error al obtener datos de distribución:', distribucionError.message);
            return false;
        }

        console.log('✅ Datos obtenidos exitosamente de Supabase.');

        // Preparar datos para validateOverviewModule
        const overviewModuleData = {
            institutionalProfile: {
                labels: perfilData.map(p => p.nombre), // Asumiendo que 'nombre' es la etiqueta de la aptitud
                datasets: [{
                    data: perfilData.map(p => p.valor_percentil) // Asumiendo que 'valor_percentil' es el valor
                }]
            },
            distributionData: distribucionData.map(nivel => ({
                name: nivel.nivel_nombre, // Asumiendo que 'nivel_nombre' es el nombre
                value: nivel.total_estudiantes // Asumiendo que 'total_estudiantes' es el valor
            }))
        };

        const overviewResults = validateOverviewModule(overviewModuleData);

        // Preparar datos para StudentsByLevelModule
        const studentsByLevelData = distribucionData.map(nivel => ({
            total_estudiantes: nivel.total_estudiantes,
            porcentaje: parseFloat(nivel.porcentaje), // Asegurarse de que el porcentaje sea un número
            nivel: nivel.nivel,
            nivel_nombre: nivel.nivel_nombre
        }));

        const studentsByLevelResults = validateStudentsByLevel(studentsByLevelData);

        // Reportar resultados
        const allTestsPassed =
            overviewResults.institutionalProfileData.length > 0 &&
            overviewResults.validDistribution.length > 0 &&
            overviewResults.siglasCorrectas &&
            studentsByLevelResults.validatedData.length > 0 &&
            studentsByLevelResults.hasValidSVG;

        console.log('\n--- Resumen de resultados de la prueba de integración ---');
        console.log(`   - OverviewModule (Perfil institucional y Distribución): ${overviewResults.institutionalProfileData.length > 0 && overviewResults.validDistribution.length > 0 && overviewResults.siglasCorrectas ? '✅ PASÓ' : '❌ FALLÓ'}`);
        console.log(`   - StudentsByLevelModule (Estudiantes por nivel y SVG): ${studentsByLevelResults.validatedData.length > 0 && studentsByLevelResults.hasValidSVG ? '✅ PASÓ' : '❌ FALLÓ'}`);

        if (allTestsPassed) {
            console.log('\n🎉 [TEST] ¡Todas las validaciones de integración pasaron exitosamente!');
            console.log('   ✅ Sin errores NaN o Infinity detectados en los datos procesados.');
            console.log('   ✅ Integración de datos con Supabase funcionando correctamente.');
            console.log('   🚀 El dashboard debería renderizar los datos de manera fiable.');

            console.log('\n📊 Datos procesados (ejemplos):');
            console.log(`   - Aptitudes con siglas: ${JSON.stringify(overviewResults.institutionalProfileData.slice(0, 2))}${overviewResults.institutionalProfileData.length > 2 ? '...' : ''}`);
            console.log(`   - Distribución de estudiantes: ${JSON.stringify(overviewResults.validDistribution.slice(0, 2))}${overviewResults.validDistribution.length > 2 ? '...' : ''}`);
            console.log(`   - Niveles de estudiantes (primeros 2 segmentos): ${JSON.stringify(studentsByLevelResults.segments.slice(0, 2))}${studentsByLevelResults.segments.length > 2 ? '...' : ''}`);

            return true;
        } else {
            console.log('\n⚠️ [TEST] Algunas validaciones de integración fallaron. Revisa los logs anteriores.');
            return false;
        }

    } catch (error) {
        console.error('❌ Error crítico durante la prueba de integración:', error.message);
        return false;
    }
}

/**
 * Prueba casos extremos para la validación de módulos, como datos vacíos, NaN o Infinity.
 * @returns {Promise<boolean>} True si los módulos manejan correctamente los casos extremos, false en caso contrario.
 */
async function testEdgeCasesIntegration() {
    console.log('\n🧪 [TEST] Probando casos extremos de integración...');

    const edgeCases = [
        {
            name: 'Datos completamente vacíos',
            overviewData: { institutionalProfile: { labels: [], datasets: [{ data: [] }] }, distributionData: [] },
            studentsByLevelData: [],
            expectedOverviewPass: false, // Esperamos que falle la "validez" de los datos si están vacíos
            expectedStudentsByLevelPass: false
        },
        {
            name: 'Datos con valores NaN',
            overviewData: {
                institutionalProfile: {
                    labels: ['Aptitud Test'],
                    datasets: [{ data: [NaN, 10] }]
                },
                distributionData: [{ name: 'Test', value: NaN }, { name: 'Test2', value: 50 }]
            },
            studentsByLevelData: [{ total_estudiantes: NaN, porcentaje: 50, nivel: 1, nivel_nombre: 'Nivel 1' }],
            expectedOverviewPass: false, // Aunque los NaN se convierten a 0, si el input original tiene NaN, se considera un "fallo" del test
            expectedStudentsByLevelPass: false
        },
        {
            name: 'Datos con valores Infinity',
            overviewData: {
                institutionalProfile: {
                    labels: ['Aptitud Inf'],
                    datasets: [{ data: [Infinity, 20] }]
                },
                distributionData: [{ name: 'Test Inf', value: Infinity }, { name: 'Test2', value: 30 }]
            },
            studentsByLevelData: [{ total_estudiantes: 10, porcentaje: Infinity, nivel: 2, nivel_nombre: 'Nivel 2' }],
            expectedOverviewPass: false,
            expectedStudentsByLevelPass: false
        },
        {
            name: 'Datos con strings no numéricos',
            overviewData: {
                institutionalProfile: {
                    labels: ['Aptitud String'],
                    datasets: [{ data: ['abc', 40] }]
                },
                distributionData: [{ name: 'Test String', value: 'xyz' }, { name: 'Test2', value: 70 }]
            },
            studentsByLevelData: [{ total_estudiantes: 'foo', porcentaje: 'bar', nivel: 3, nivel_nombre: 'Nivel 3' }],
            expectedOverviewPass: false,
            expectedStudentsByLevelPass: false
        }
    ];

    let allEdgeCasesPassed = true;

    for (const testCase of edgeCases) {
        console.log(`\n   🔍 Probando: ${testCase.name}`);

        try {
            const overviewResults = validateOverviewModule(testCase.overviewData);
            const studentsByLevelResults = validateStudentsByLevel(testCase.studentsByLevelData);

            // Verificar si hay NaN o Infinity en los resultados después de la validación
            const hasNaNInOverview = JSON.stringify(overviewResults).includes('NaN') || JSON.stringify(overviewResults).includes('Infinity');
            const hasNaNInStudentsByLevel = JSON.stringify(studentsByLevelResults).includes('NaN') || JSON.stringify(studentsByLevelResults).includes('Infinity');

            // Las validaciones internas convierten NaN/Infinity a 0 o los filtran.
            // Para el propósito de este test de "caso extremo", esperamos que se manejen sin producir NaN/Infinity en la salida final.
            const currentCasePassed = !hasNaNInOverview && !hasNaNInStudentsByLevel;

            if (currentCasePassed) {
                console.log(`   ✅ El caso extremo "${testCase.name}" fue manejado correctamente (sin NaN/Infinity en la salida).`);
            } else {
                console.log(`   ❌ El caso extremo "${testCase.name}" NO fue manejado correctamente (se encontraron NaN/Infinity en la salida).`);
                allEdgeCasesPassed = false;
            }

        } catch (error) {
            console.log(`   ❌ Caso extremo "${testCase.name}" falló con un error inesperado: ${error.message}`);
            allEdgeCasesPassed = false;
        }
    }

    return allEdgeCasesPassed;
}

/**
 * Función principal para ejecutar todas las pruebas de integración.
 */
async function runCompleteIntegrationTest() {
    console.log('🚀 [TEST] Ejecutando prueba completa de integración de dashboard...');

    const integrationTestPassed = await testCompleteIntegration();
    const edgeCasesTestPassed = await testEdgeCasesIntegration();

    console.log('\n--- Resumen final de todas las pruebas de integración ---');
    console.log(`   - Prueba de integración de datos de Supabase: ${integrationTestPassed ? '✅ PASÓ' : '❌ FALLÓ'}`);
    console.log(`   - Prueba de casos extremos (NaN/Infinity/Vacío): ${edgeCasesTestPassed ? '✅ PASÓ' : '❌ FALLÓ'}`);

    if (integrationTestPassed && edgeCasesTestPassed) {
        console.log('\n🎉 [TEST] ¡TODAS LAS PRUEBAS DE INTEGRACIÓN PASARON EXITOSAMENTE!');
        console.log('   🛡️ Cero NaN/Infinity detectados en todos los componentes y escenarios.');
        console.log('   ✨ Validación robusta en todos los componentes del dashboard.');
        console.log('   ✅ La integración con Supabase es estable y los datos se procesan sin errores.');

        console.log('\n🔧 Componentes verificados y estables:');
        console.log('   ✅ OverviewModule - Gráficos de barra y pastel sin NaN/Infinity');
        console.log('   ✅ StudentsByLevelModule - Datos de tabla y cálculos SVG sin NaN/Infinity');
        console.log('   ✅ Validación de casos extremos');
        console.log('   ✅ Flujo de integración de datos global');
    } else {
        console.log('\n⚠️ [TEST] ¡ALGUNAS PRUEBAS DE INTEGRACIÓN FALLARON! Revisa los logs para más detalles.');
    }
}

// Ejecutar la prueba completa
runCompleteIntegrationTest().catch(console.error);