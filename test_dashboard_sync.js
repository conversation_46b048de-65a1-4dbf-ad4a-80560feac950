import { createClient } from '@supabase/supabase-js';

const supabaseUrl = 'https://ydglduxhgwajqdseqzpy.supabase.co';
const supabaseKey = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InlkZ2xkdXhoZ3dhanFkc2VxenB5Iiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDYzMTI4NDEsImV4cCI6MjA2MTg4ODg0MX0.HEFdJm5qnXU1PQFbF-HkZ-bLez9LuPi3LepirU0nz4c';

const supabase = createClient(supabaseUrl, supabaseKey);

async function testDashboardSync() {
  console.log('🔧 Probando sincronización del Dashboard con Supabase...');
  
  try {
    // 1. Verificar conexión básica
    console.log('\n📊 1. Verificando conexión básica...');
    const { data: connectionTest, error: connectionError } = await supabase
      .from('pacientes')
      .select('count', { count: 'exact', head: true });
    
    if (connectionError) {
      console.error('❌ Error de conexión:', connectionError);
      return;
    }
    
    console.log(`✅ Conexión exitosa. Total pacientes: ${connectionTest.count || 0}`);
    
    // 2. Verificar datos para dashboard
    console.log('\n📊 2. Verificando datos para dashboard...');
    
    // Pacientes con instituciones
    const { data: patients, error: patientsError } = await supabase
      .from('pacientes')
      .select(`
        id,
        nombre,
        apellido,
        created_at,
        instituciones:institucion_id (
          id,
          nombre
        )
      `)
      .limit(5);
    
    if (patientsError) {
      console.error('❌ Error al obtener pacientes:', patientsError);
    } else {
      console.log(`✅ Pacientes obtenidos: ${patients.length}`);
      patients.forEach(p => {
        console.log(`   - ${p.nombre} ${p.apellido} (${p.instituciones?.nombre || 'Sin institución'})`);
      });
    }
    
    // 3. Verificar resultados con aptitudes
    console.log('\n📊 3. Verificando resultados con aptitudes...');
    const { data: results, error: resultsError } = await supabase
      .from('resultados')
      .select(`
        id,
        puntaje_directo,
        percentil,
        created_at,
        aptitudes:aptitud_id (
          codigo,
          nombre
        ),
        pacientes:paciente_id (
          nombre,
          apellido
        )
      `)
      .limit(10);
    
    if (resultsError) {
      console.error('❌ Error al obtener resultados:', resultsError);
    } else {
      console.log(`✅ Resultados obtenidos: ${results.length}`);
      
      // Agrupar por aptitud
      const byAptitude = {};
      results.forEach(r => {
        const code = r.aptitudes?.codigo || 'N/A';
        if (!byAptitude[code]) {
          byAptitude[code] = {
            name: r.aptitudes?.nombre || 'Desconocida',
            count: 0,
            avgPercentile: 0,
            scores: []
          };
        }
        byAptitude[code].count++;
        if (r.percentil) {
          byAptitude[code].scores.push(r.percentil);
        }
      });
      
      // Calcular promedios
      Object.keys(byAptitude).forEach(code => {
        const apt = byAptitude[code];
        if (apt.scores.length > 0) {
          apt.avgPercentile = Math.round(apt.scores.reduce((sum, s) => sum + s, 0) / apt.scores.length);
        }
        console.log(`   - ${code} (${apt.name}): ${apt.count} resultados, promedio PC: ${apt.avgPercentile}`);
      });
    }
    
    // 4. Verificar aptitudes disponibles
    console.log('\n📊 4. Verificando aptitudes disponibles...');
    const { data: aptitudes, error: aptitudesError } = await supabase
      .from('aptitudes')
      .select('*')
      .order('codigo');
    
    if (aptitudesError) {
      console.error('❌ Error al obtener aptitudes:', aptitudesError);
    } else {
      console.log(`✅ Aptitudes disponibles: ${aptitudes.length}`);
      aptitudes.forEach(a => {
        console.log(`   - ${a.codigo}: ${a.nombre}`);
      });
    }
    
    // 5. Verificar instituciones
    console.log('\n📊 5. Verificando instituciones...');
    const { data: institutions, error: institutionsError } = await supabase
      .from('instituciones')
      .select('*')
      .order('nombre');
    
    if (institutionsError) {
      console.error('❌ Error al obtener instituciones:', institutionsError);
    } else {
      console.log(`✅ Instituciones disponibles: ${institutions.length}`);
      institutions.forEach(i => {
        console.log(`   - ${i.nombre} (ID: ${i.id})`);
      });
    }
    
    // 6. Simular consulta de dashboard - Métricas generales
    console.log('\n📊 6. Simulando consulta de métricas generales...');
    
    // Total de evaluaciones por mes
    const { data: monthlyEvaluations, error: monthlyError } = await supabase
      .from('resultados')
      .select('created_at')
      .gte('created_at', new Date(Date.now() - 90 * 24 * 60 * 60 * 1000).toISOString()); // Últimos 3 meses
    
    if (monthlyError) {
      console.error('❌ Error en métricas mensuales:', monthlyError);
    } else {
      const monthlyStats = {};
      monthlyEvaluations.forEach(r => {
        const month = new Date(r.created_at).toISOString().substring(0, 7); // YYYY-MM
        monthlyStats[month] = (monthlyStats[month] || 0) + 1;
      });
      
      console.log('✅ Evaluaciones por mes (últimos 3 meses):');
      Object.keys(monthlyStats).sort().forEach(month => {
        console.log(`   - ${month}: ${monthlyStats[month]} evaluaciones`);
      });
    }
    
    // 7. Verificar datos para gráficos
    console.log('\n📊 7. Verificando datos para gráficos...');
    
    // Distribución por percentiles
    const { data: percentileData, error: percentileError } = await supabase
      .from('resultados')
      .select('percentil')
      .not('percentil', 'is', null);
    
    if (percentileError) {
      console.error('❌ Error en datos de percentiles:', percentileError);
    } else {
      const percentiles = percentileData.map(r => r.percentil);
      const ranges = {
        'Bajo (0-25)': percentiles.filter(p => p <= 25).length,
        'Medio-Bajo (26-50)': percentiles.filter(p => p > 25 && p <= 50).length,
        'Medio-Alto (51-75)': percentiles.filter(p => p > 50 && p <= 75).length,
        'Alto (76-100)': percentiles.filter(p => p > 75).length
      };
      
      console.log('✅ Distribución de percentiles:');
      Object.keys(ranges).forEach(range => {
        const percentage = Math.round((ranges[range] / percentiles.length) * 100);
        console.log(`   - ${range}: ${ranges[range]} (${percentage}%)`);
      });
    }
    
    console.log('\n🎯 Resumen de Sincronización:');
    console.log('✅ Conexión con Supabase: OK');
    console.log('✅ Estructura de datos: OK');
    console.log('✅ Datos para dashboard: Disponibles');
    console.log('✅ Métricas calculables: OK');
    console.log('\n🚀 El dashboard debería funcionar correctamente con estos datos.');
    
  } catch (error) {
    console.error('💥 Error general:', error);
  }
}

// Ejecutar la prueba
testDashboardSync();
