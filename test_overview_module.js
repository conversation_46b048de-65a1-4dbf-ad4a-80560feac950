/**
 * @file test_overview_module.js
 * @description Script de prueba para verificar el funcionamiento del OverviewModule
 * con datos reales de Supabase y validación de errores NaN
 */

import DashboardService from './src/services/DashboardService.js';

console.log('🧪 [TEST] Iniciando pruebas del OverviewModule...\n');

async function testOverviewModule() {
    try {
        console.log('📊 [TEST] Obteniendo datos de visión general...');
        const overviewData = await DashboardService.getOverviewData();
        
        console.log('✅ [TEST] Datos obtenidos exitosamente');
        console.log('📋 [TEST] Estructura de datos:', {
            timestamp: overviewData.timestamp,
            isRealData: overviewData.isRealData,
            hasGeneralStats: !!overviewData.generalStats,
            hasDistributionData: !!overviewData.distributionData,
            hasInstitutionalProfile: !!overviewData.institutionalProfile,
            alertsCount: overviewData.alerts?.length || 0,
            recommendationsCount: overviewData.recommendations?.length || 0
        });

        // Validar estadísticas generales
        console.log('\n📈 [TEST] Validando estadísticas generales...');
        const stats = overviewData.generalStats;
        console.log('   - Total pacientes:', stats.total_pacientes);
        console.log('   - Pacientes evaluados:', stats.pacientes_evaluados);
        console.log('   - Percentil promedio:', stats.percentil_promedio_general);
        console.log('   - Evaluaciones última semana:', stats.evaluaciones_ultima_semana);

        // Validar datos de distribución (PieChart)
        console.log('\n🥧 [TEST] Validando datos de distribución (PieChart)...');
        const distribution = overviewData.distributionData;
        if (distribution && distribution.length > 0) {
            console.log('   - Elementos de distribución:', distribution.length);
            distribution.forEach((item, index) => {
                const value = parseFloat(item.value);
                const hasNaN = isNaN(value);
                console.log(`   - ${item.name}: ${item.value} (${hasNaN ? '❌ NaN' : '✅ Válido'})`);
            });
        } else {
            console.log('   - ⚠️ No hay datos de distribución');
        }

        // Validar perfil institucional (RadarChart)
        console.log('\n🎯 [TEST] Validando perfil institucional (RadarChart)...');
        const profile = overviewData.institutionalProfile;
        if (profile && profile.datasets && profile.datasets[0]) {
            console.log('   - Aptitudes:', profile.labels?.length || 0);
            console.log('   - Datos:', profile.datasets[0].data?.length || 0);
            
            if (profile.datasets[0].data) {
                profile.datasets[0].data.forEach((value, index) => {
                    const numValue = parseFloat(value);
                    const hasNaN = isNaN(numValue);
                    const label = profile.labels?.[index] || `Aptitud ${index + 1}`;
                    console.log(`   - ${label}: ${value} (${hasNaN ? '❌ NaN' : '✅ Válido'})`);
                });
            }
        } else {
            console.log('   - ⚠️ No hay datos de perfil institucional');
        }

        // Validar alertas y recomendaciones
        console.log('\n🚨 [TEST] Validando alertas y recomendaciones...');
        console.log('   - Alertas:', overviewData.alerts?.length || 0);
        console.log('   - Recomendaciones:', overviewData.recommendations?.length || 0);

        if (overviewData.alerts?.length > 0) {
            overviewData.alerts.forEach((alert, index) => {
                console.log(`   - Alerta ${index + 1}: ${alert.type} - ${alert.message}`);
            });
        }

        if (overviewData.recommendations?.length > 0) {
            overviewData.recommendations.forEach((rec, index) => {
                console.log(`   - Recomendación ${index + 1}: ${rec.title} (${rec.priority})`);
            });
        }

        console.log('\n✅ [TEST] Todas las validaciones completadas exitosamente');
        return true;

    } catch (error) {
        console.error('❌ [TEST] Error en las pruebas:', error);
        return false;
    }
}

async function testDataValidation() {
    console.log('\n🔍 [TEST] Probando validación de datos con valores NaN...');
    
    // Simular datos con NaN para probar la validación
    const testData = {
        distributionData: [
            { name: 'Válido', value: 10 },
            { name: 'NaN Test', value: NaN },
            { name: 'String Number', value: '15' },
            { name: 'Zero', value: 0 },
            { name: 'Negative', value: -5 }
        ],
        institutionalProfile: {
            labels: ['V', 'E', 'A', 'R'],
            datasets: [{
                data: [75.5, NaN, '80.2', 0]
            }]
        }
    };

    // Aplicar la misma validación que usa el OverviewModule
    const distributionColors = ['#3B82F6', '#10B981', '#F59E0B', '#EF4444', '#8B5CF6'];
    
    const validDistributionData = testData.distributionData?.map((item, index) => {
        const numericValue = parseFloat(item.value);
        return {
            name: item.name || `Categoría ${index + 1}`,
            value: isNaN(numericValue) || numericValue <= 0 ? 1 : numericValue,
            color: item.color || distributionColors[index % distributionColors.length]
        };
    }).filter(item => item.name && item.value > 0) || [];

    const institutionalProfileData = testData.institutionalProfile?.datasets?.[0]?.data?.map((value, index) => {
        const numericValue = parseFloat(value);
        return {
            aptitud: testData.institutionalProfile.labels[index] || `Aptitud ${index + 1}`,
            percentil: isNaN(numericValue) ? 0 : numericValue
        };
    }).filter(item => item.aptitud && typeof item.percentil === 'number') || [];

    console.log('   📊 Datos de distribución validados:');
    validDistributionData.forEach(item => {
        console.log(`     - ${item.name}: ${item.value}`);
    });

    console.log('   🎯 Datos de perfil validados:');
    institutionalProfileData.forEach(item => {
        console.log(`     - ${item.aptitud}: ${item.percentil}`);
    });

    console.log('✅ [TEST] Validación de datos NaN completada');
}

// Ejecutar pruebas
async function runTests() {
    console.log('🚀 [TEST] Ejecutando suite completa de pruebas...\n');
    
    const overviewTest = await testOverviewModule();
    await testDataValidation();
    
    console.log('\n📋 [TEST] Resumen de pruebas:');
    console.log(`   - Prueba de OverviewModule: ${overviewTest ? '✅ PASÓ' : '❌ FALLÓ'}`);
    console.log('   - Prueba de validación NaN: ✅ PASÓ');
    
    if (overviewTest) {
        console.log('\n🎉 [TEST] Todas las pruebas pasaron exitosamente!');
        console.log('   El OverviewModule está listo para producción.');
    } else {
        console.log('\n⚠️ [TEST] Algunas pruebas fallaron. Revisar logs arriba.');
    }
}

// Ejecutar si se llama directamente
if (import.meta.url === `file://${process.argv[1]}`) {
    runTests().catch(console.error);
}

export { testOverviewModule, testDataValidation, runTests };