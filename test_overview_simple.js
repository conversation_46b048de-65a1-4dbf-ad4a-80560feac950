/**
 * @file test_overview_simple.js
 * @description Script de prueba simplificado para verificar el OverviewModule
 */

import { createClient } from '@supabase/supabase-js';

// Configuración directa de Supabase
const supabaseUrl = 'https://ydglduxhgwajqdseqzpy.supabase.co';
const supabaseKey = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InlkZ2xkdXhoZ3dhanFkc2VxenB5Iiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDYzMTI4NDEsImV4cCI6MjA2MTg4ODg0MX0.HEFdJm5qnXU1PQFbF-HkZ-bLez9LuPi3LepirU0nz4c';

const supabase = createClient(supabaseUrl, supabaseKey);

console.log('🧪 [TEST] Iniciando pruebas del OverviewModule...\n');

async function testDashboardViews() {
    try {
        console.log('📊 [TEST] Probando vista dashboard_estadisticas_generales...');
        const { data: stats, error: statsError } = await supabase
            .from('dashboard_estadisticas_generales')
            .select('*')
            .single();

        if (statsError) {
            console.error('❌ Error en estadísticas:', statsError);
            return false;
        }

        console.log('✅ Estadísticas obtenidas:', {
            total_pacientes: stats.total_pacientes,
            pacientes_evaluados: stats.pacientes_evaluados,
            percentil_promedio: stats.percentil_promedio_general
        });

        console.log('\n🎯 [TEST] Probando vista dashboard_perfil_institucional...');
        const { data: perfil, error: perfilError } = await supabase
            .from('dashboard_perfil_institucional')
            .select('*')
            .order('codigo');

        if (perfilError) {
            console.error('❌ Error en perfil:', perfilError);
            return false;
        }

        console.log('✅ Perfil institucional obtenido:', perfil.length, 'aptitudes');
        
        // Validar datos para NaN
        console.log('\n🔍 [TEST] Validando datos para NaN...');
        let hasNaN = false;
        
        perfil.forEach(apt => {
            const percentil = parseFloat(apt.percentil_promedio);
            if (isNaN(percentil)) {
                console.log(`❌ NaN encontrado en ${apt.aptitud_nombre}: ${apt.percentil_promedio}`);
                hasNaN = true;
            } else {
                console.log(`✅ ${apt.codigo} (${apt.aptitud_nombre}): ${percentil}%`);
            }
        });

        console.log('\n🥧 [TEST] Probando vista dashboard_estudiantes_por_nivel...');
        const { data: distribucion, error: distribucionError } = await supabase
            .from('dashboard_estudiantes_por_nivel')
            .select('*');

        if (distribucionError) {
            console.error('❌ Error en distribución:', distribucionError);
            return false;
        }

        console.log('✅ Distribución por nivel obtenida:', distribucion.length, 'niveles');
        
        // Validar datos de distribución
        distribucion.forEach(nivel => {
            const estudiantes = parseInt(nivel.total_estudiantes);
            if (isNaN(estudiantes)) {
                console.log(`❌ NaN en distribución ${nivel.nivel_nombre}: ${nivel.total_estudiantes}`);
                hasNaN = true;
            } else {
                console.log(`✅ ${nivel.nivel_nombre}: ${estudiantes} estudiantes`);
            }
        });

        if (!hasNaN) {
            console.log('\n🎉 [TEST] ¡Todos los datos son válidos! No se encontraron valores NaN.');
        }

        return !hasNaN;

    } catch (error) {
        console.error('❌ [TEST] Error general:', error);
        return false;
    }
}

async function testDataProcessing() {
    console.log('\n🔧 [TEST] Probando procesamiento de datos como en OverviewModule...');
    
    try {
        // Simular el procesamiento del DashboardService
        const { data: perfilData } = await supabase
            .from('dashboard_perfil_institucional')
            .select('*')
            .order('codigo');

        const { data: distribucionData } = await supabase
            .from('dashboard_estudiantes_por_nivel')
            .select('*');

        // Procesar perfil institucional con siglas (como en DashboardService)
        const aptitudeAbbreviations = {
            'Aptitud Verbal': 'V',
            'Aptitud Espacial': 'E', 
            'Atención': 'A',
            'Razonamiento': 'R',
            'Aptitud Numérica': 'N',
            'Aptitud Mecánica': 'M',
            'Ortografía': 'O'
        };

        const institutionalProfileData = perfilData?.map((apt, index) => {
            const numericValue = parseFloat(apt.percentil_promedio);
            return {
                aptitud: aptitudeAbbreviations[apt.aptitud_nombre] || apt.codigo,
                percentil: isNaN(numericValue) ? 0 : numericValue
            };
        }).filter(item => item.aptitud && typeof item.percentil === 'number') || [];

        console.log('✅ Datos de perfil procesados:', institutionalProfileData.length, 'aptitudes');
        institutionalProfileData.forEach(item => {
            console.log(`   - ${item.aptitud}: ${item.percentil}%`);
        });

        // Procesar distribución por nivel
        const distributionColors = ['#3B82F6', '#10B981', '#F59E0B', '#EF4444', '#8B5CF6'];
        const validDistributionData = distribucionData?.map((nivel, index) => {
            const numericValue = parseFloat(nivel.total_estudiantes);
            return {
                name: nivel.nivel_nombre || `Nivel ${index + 1}`,
                value: isNaN(numericValue) || numericValue <= 0 ? 1 : numericValue,
                color: distributionColors[index % distributionColors.length]
            };
        }).filter(item => item.name && item.value > 0) || [];

        console.log('✅ Datos de distribución procesados:', validDistributionData.length, 'niveles');
        validDistributionData.forEach(item => {
            console.log(`   - ${item.name}: ${item.value} estudiantes`);
        });

        return true;

    } catch (error) {
        console.error('❌ Error en procesamiento:', error);
        return false;
    }
}

async function runTests() {
    console.log('🚀 [TEST] Ejecutando suite de pruebas...\n');
    
    const viewsTest = await testDashboardViews();
    const processingTest = await testDataProcessing();
    
    console.log('\n📋 [TEST] Resumen de pruebas:');
    console.log(`   - Prueba de vistas: ${viewsTest ? '✅ PASÓ' : '❌ FALLÓ'}`);
    console.log(`   - Prueba de procesamiento: ${processingTest ? '✅ PASÓ' : '❌ FALLÓ'}`);
    
    if (viewsTest && processingTest) {
        console.log('\n🎉 [TEST] ¡Todas las pruebas pasaron exitosamente!');
        console.log('   El OverviewModule está listo y los datos no contienen valores NaN.');
    } else {
        console.log('\n⚠️ [TEST] Algunas pruebas fallaron. Revisar logs arriba.');
    }
}

runTests().catch(console.error);