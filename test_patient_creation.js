import { createClient } from '@supabase/supabase-js';

const supabaseUrl = 'https://ydglduxhgwajqdseqzpy.supabase.co';
const supabaseKey = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.*******************************************************************************************************************************.HEFdJm5qnXU1PQFbF-HkZ-bLez9LuPi3LepirU0nz4c';

const supabase = createClient(supabaseUrl, supabaseKey);

async function testPatientCreation() {
  console.log('🧪 Probando creación de pacientes...');
  
  try {
    // 1. Obtener instituciones disponibles
    console.log('\n📊 1. Obteniendo instituciones...');
    const { data: instituciones, error: instError } = await supabase
      .from('instituciones')
      .select('id, nombre')
      .order('nombre');
    
    if (instError) {
      console.error('❌ Error al obtener instituciones:', instError);
      return;
    }
    
    console.log(`✅ Instituciones disponibles: ${instituciones.length}`);
    instituciones.forEach(inst => {
      console.log(`   - ${inst.nombre} (ID: ${inst.id})`);
    });
    
    if (instituciones.length === 0) {
      console.log('❌ No hay instituciones disponibles');
      return;
    }
    
    // 2. Crear paciente de prueba
    console.log('\n📊 2. Creando paciente de prueba...');
    const nuevoPaciente = {
      nombre: 'Paciente',
      apellido: 'De Prueba',
      email: '<EMAIL>',
      telefono: '************',
      genero: 'masculino', // Usar valor en minúsculas
      fecha_nacimiento: '1990-01-01',
      nivel_educativo: 'Universitario',
      institucion_id: instituciones[0].id, // Usar primera institución
      created_at: new Date().toISOString()
    };
    
    console.log('📝 Datos del paciente:', nuevoPaciente);
    
    const { data: pacienteCreado, error: createError } = await supabase
      .from('pacientes')
      .insert([nuevoPaciente])
      .select();
    
    if (createError) {
      console.error('❌ Error al crear paciente:', createError);
      return;
    }
    
    console.log('✅ Paciente creado exitosamente:', pacienteCreado[0]);
    
    // 3. Verificar que el paciente se guardó
    console.log('\n📊 3. Verificando paciente guardado...');
    const { data: pacienteVerificado, error: verifyError } = await supabase
      .from('pacientes')
      .select('*')
      .eq('id', pacienteCreado[0].id)
      .single();
    
    if (verifyError) {
      console.error('❌ Error al verificar paciente:', verifyError);
      return;
    }
    
    console.log('✅ Paciente verificado:', {
      id: pacienteVerificado.id,
      nombre: pacienteVerificado.nombre,
      apellido: pacienteVerificado.apellido,
      email: pacienteVerificado.email,
      genero: pacienteVerificado.genero,
      institucion_id: pacienteVerificado.institucion_id
    });
    
    // 4. Limpiar - eliminar paciente de prueba
    console.log('\n📊 4. Limpiando paciente de prueba...');
    const { error: deleteError } = await supabase
      .from('pacientes')
      .delete()
      .eq('id', pacienteCreado[0].id);
    
    if (deleteError) {
      console.error('❌ Error al eliminar paciente de prueba:', deleteError);
    } else {
      console.log('✅ Paciente de prueba eliminado correctamente');
    }
    
  } catch (error) {
    console.error('💥 Error general:', error);
  }
}

// Ejecutar la prueba
testPatientCreation();
