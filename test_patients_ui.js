/**
 * Script de prueba para verificar los cambios en la página de pacientes
 */

// Función para simular datos de pacientes con diferentes géneros
function generateTestPatients() {
  return [
    {
      id: '1',
      nombre: '<PERSON>',
      apellido: '<PERSON>',
      genero: 'femenino',
      email: '<EMAIL>',
      documento: '12345678',
      fecha_nacimiento: '2010-05-15',
      nivel_educativo: 'Secundaria'
    },
    {
      id: '2',
      nombre: '<PERSON>',
      apellido: '<PERSON>',
      genero: 'masculino',
      email: '<EMAIL>',
      documento: '87654321',
      fecha_nacimiento: '2009-08-22',
      nivel_educativo: 'Secundaria'
    },
    {
      id: '3',
      nombre: 'Ana',
      apellido: '<PERSON>',
      genero: 'femenino',
      email: '<EMAIL>',
      documento: '11223344',
      fecha_nacimiento: '2011-03-10',
      nivel_educativo: 'Primaria'
    },
    {
      id: '4',
      nombre: '<PERSON>',
      apellid<PERSON>: '<PERSON>',
      genero: 'masculino',
      email: '<EMAIL>',
      documento: '44332211',
      fecha_nacimiento: '2008-12-05',
      nivel_educativo: 'Secundaria'
    }
  ];
}

// Función para generar el CSS de los círculos según género
function generateAvatarCSS(patient) {
  let bgColor;
  
  switch (patient.genero) {
    case 'femenino':
      bgColor = 'bg-pink-500';
      break;
    case 'masculino':
      bgColor = 'bg-blue-500';
      break;
    default:
      bgColor = 'bg-gray-500';
  }
  
  return {
    className: `w-8 h-8 rounded-full flex items-center justify-center mr-3 ${bgColor}`,
    initial: patient.nombre?.charAt(0)?.toUpperCase() || '?'
  };
}

// Función para verificar los cambios
function testPatientsUIChanges() {
  console.log('🧪 Probando cambios en la UI de pacientes...');
  
  const testPatients = generateTestPatients();
  
  console.log('\n📋 Pacientes de prueba:');
  testPatients.forEach(patient => {
    const avatar = generateAvatarCSS(patient);
    console.log(`👤 ${patient.nombre} ${patient.apellido}:`);
    console.log(`   - Género: ${patient.genero}`);
    console.log(`   - Inicial: ${avatar.initial}`);
    console.log(`   - CSS: ${avatar.className}`);
    console.log(`   - Color esperado: ${patient.genero === 'femenino' ? 'Rosa' : patient.genero === 'masculino' ? 'Azul' : 'Gris'}`);
    console.log('');
  });
  
  console.log('✅ Verificaciones:');
  console.log('1. ❌ Pestañas eliminadas: Instituciones, Psicólogos, Conversión PD→PC, Supabase');
  console.log('2. ✅ Círculos con color por género:');
  console.log('   - 👩 Femenino: Rosa (bg-pink-500)');
  console.log('   - 👨 Masculino: Azul (bg-blue-500)');
  console.log('   - ❓ Sin género: Gris (bg-gray-500)');
  
  console.log('\n🎯 Para verificar en el navegador:');
  console.log('1. Ir a /admin/patients');
  console.log('2. Verificar que NO aparezcan las pestañas de navegación');
  console.log('3. Verificar que los círculos tengan colores según género');
  console.log('4. Crear pacientes de prueba con diferentes géneros');
}

// Ejecutar las pruebas
testPatientsUIChanges();
