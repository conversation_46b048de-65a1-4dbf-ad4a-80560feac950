// Importar el cliente de Supabase
import { createClient } from '@supabase/supabase-js';
import fs from 'fs';

// Configuración de Supabase
const supabaseUrl = 'https://ydglduxhgwajqdseqzpy.supabase.co';
const supabaseAnonKey = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InlkZ2xkdXhoZ3dhanFkc2VxenB5Iiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDYzMTI4NDEsImV4cCI6MjA2MTg4ODg0MX0.HEFdJm5qnXU1PQFbF-HkZ-bLez9LuPi3LepirU0nz4c';
const supabaseServiceKey = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InlkZ2xkdXhoZ3dhanFkc2VxenB5Iiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImlhdCI6MTc0NjMxMjg0MSwiZXhwIjoyMDYxODg4ODQxfQ.zwk3Wiay5jjeYOrg8B1M6T98B2esCqRFI43At-AFV3A';

// Crear el cliente de Supabase con la clave de servicio para operaciones administrativas
const supabase = createClient(supabaseUrl, supabaseServiceKey);

// Función para ejecutar consultas SQL
async function ejecutarSQL(sql) {
  try {
    console.log(`Ejecutando SQL: ${sql.substring(0, 50)}...`);
    
    const { data, error } = await supabase.rpc('exec', { sql });
    
    if (error) {
      console.error('Error al ejecutar SQL:', error);
      return false;
    }
    
    console.log('SQL ejecutado correctamente');
    console.log('Resultado:', data);
    return true;
  } catch (error) {
    console.error('Error inesperado:', error);
    return false;
  }
}

// Función para sincronizar las vistas del dashboard
async function sincronizarVistas() {
  try {
    console.log('Sincronizando vistas del dashboard...');
    
    // Leer el archivo SQL
    const sql = fs.readFileSync('supabase_dashboard_views.sql', 'utf8');
    
    // Dividir el archivo en consultas individuales
    const consultas = sql.split(';').filter(q => q.trim());
    
    // Ejecutar cada consulta
    for (const consulta of consultas) {
      if (consulta.trim()) {
        const exito = await ejecutarSQL(consulta);
        if (!exito) {
          console.error('Error al ejecutar consulta:', consulta);
        }
      }
    }
    
    console.log('Sincronización completada');
  } catch (error) {
    console.error('Error en la sincronización:', error);
  }
}

// Función para probar las vistas
async function probarVistas() {
  try {
    console.log('Probando vistas del dashboard...');
    
    // Probar cada vista
    const vistas = [
      'dashboard_estadisticas_generales',
      'dashboard_distribucion_nivel',
      'dashboard_perfil_institucional',
      'dashboard_comparativa_nivel',
      'dashboard_comparativa_genero',
      'dashboard_tendencias_mensuales',
      'dashboard_estudiantes_riesgo'
    ];
    
    for (const vista of vistas) {
      console.log(`\nProbando vista: ${vista}`);
      const { data, error } = await supabase.from(vista).select('*').limit(5);
      
      if (error) {
        console.error(`Error al consultar vista ${vista}:`, error);
      } else {
        console.log(`Vista ${vista} consultada correctamente`);
        console.log('Datos de muestra:', data);
      }
    }
    
    console.log('\nPruebas completadas');
  } catch (error) {
    console.error('Error en las pruebas:', error);
  }
}

// Ejecutar sincronización y pruebas
async function main() {
  await sincronizarVistas();
  await probarVistas();
}

main();